package manage

import (
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type staticReq struct {
	Start model.JsonTime `json:"start"`
	End   model.JsonTime `json:"end"`
}

func GenStatistic(c *gin.Context) {
	var code int
	var req staticReq
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if req.Start.Time().Format("2006-01-01") != req.End.Time().Format("2006-01-01") {
		errmsg.Abort(c, errmsg.FAIL, "统计时间不在同一天")
		return
	}

	statistic, err := service.StatisticSerivce.StatisticAll(req.Start.Time(), req.End.Time())
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, err.Error())
		return
	}
	result := make(map[string]interface{})
	result["statistic"] = statistic
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}

}
