package v1

import (
	"crypto/md5"
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/myhttp"
	"design-ai/utils/myimg"
	"design-ai/utils/tools"
	"encoding/hex"
	"fmt"
	"net/http"
	"os"
	"path"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type initImageApi struct {
}

func (obj initImageApi) Upload(c *gin.Context) {
	var code int

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	userId := claims.UserId

	f, err := c.FormFile("file")
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}

	var user model.User
	if err := user.GetById(userId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	//20221218/user-cd/cd6fa037da4e59ee94b8db4fc87c163e.base64
	//20221218/out-cd/cd6fa037da4e59ee94b8db4fc87c163e.base64
	//https://aigc.cyuai.com/output/20221218/out-cd/cd6fa037da4e59ee94b8db4fc87c163e.base64

	oMd5Str := fmt.Sprintf("%d,%s", user.ID, time.Now().Format("2006-01-02 15:04:05.000"))
	has := md5.Sum([]byte(oMd5Str))
	md5Str := hex.EncodeToString(has[:])
	if len(md5Str) != 32 {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参考图名称生成失败")
		return
	}

	ext := path.Ext(f.Filename) // 输出 .html

	filename := md5Str + strings.ToLower(ext)

	pre2 := md5Str
	pre2 = pre2[0:1]

	//filepath := config.TempImgFilePath + filename
	//relativePath := fmt.Sprintf("%s/user-%s/%s", time.Now().Format("20060102"), pre2, filename)
	//relativePath := fmt.Sprintf("%s/user/", time.Now().Format("20060102"))
	relativePath := service.ImgService.GetSaveRelativePath(false)
	if config.Env == enums.EnvEnum.DEV || config.Env == enums.EnvEnum.TEST {
		relativePath = service.ImgService.GetSaveRelativePath(true)
	}
	relativePathFile := relativePath + filename

	dirPath := config.DiffusionFilePath + relativePath
	filepath := config.DiffusionFilePath + relativePathFile

	tempImg := model.UploadImg{
		UserId:    userId,
		OrigWhere: 1,
		Md5:       md5Str,
		Path:      relativePathFile,
		State:     0,
	}
	//if err := tempImg.Save(); err != nil {
	//	logger.Error(err)
	//	errmsg.Abort(c, errmsg.FAIL, "图片数据生成失败")
	//	return
	//}

	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		// mkdir 创建目录，mkdirAll 可创建多层级目录
		if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
			logger.Error(err, dirPath)
			errmsg.Abort(c, errmsg.FAIL, "创建路径失败")
			return
		}
	}

	if err := c.SaveUploadedFile(f, filepath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}

	if err := tempImg.SetState(enums.UploadImgStateEnum.Uploaded); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "设置图片状态失败")
		return
	}

	img, _, err := myimg.FileToImg(filepath)
	width := img.Bounds().Size().X
	height := img.Bounds().Size().Y
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片解析失败")
		return
	}

	if err := tempImg.SetWidthHeight(width, height, 0); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片信息失败")
		return
	}

	//small := myimg.ResizeImg(156, 156, img, true)
	//smallFilePath := service.ImgService.GetSmallImagePath(filepath)
	//if err := myimg.ImgToFile(small, smallFilePath); err != nil {
	//	logger.Error(err)
	//	errmsg.Abort(c, errmsg.FAIL, "缩略图保存失败")
	//}
	//
	//imgSecCheckResp, _, err := weixin.ImgSecCheck(smallFilePath)
	//if err != nil {
	//	logger.Error(err)
	//	if err := tempImg.SetState(enums.TempImgStateEnum.SecFail); err != nil {
	//		logger.Error(err)
	//	}
	//	errmsg.Abort(c, errmsg.FAIL, "图片校验失败")
	//	return
	//}
	//if imgSecCheckResp.ErrCode == 87014 {
	//	if err := tempImg.SetState(enums.TempImgStateEnum.SecRisky); err != nil {
	//		logger.Error(err)
	//	}
	//	if err := os.Remove(filepath); err != nil {
	//		logger.Error(err)
	//	}
	//	if err := os.Remove(smallFilePath); err != nil {
	//		logger.Error(err)
	//	}
	//	errmsg.Abort(c, errmsg.FAIL, "图片内容校验未通过，请更换图片")
	//	return
	//}
	//
	//if imgSecCheckResp.ErrCode != 0 {
	//	logger.Error("图片校验出错", imgSecCheckResp, " userId:", userId)
	//	if err := tempImg.SetState(enums.TempImgStateEnum.SecFail); err != nil {
	//		logger.Error(err)
	//	}
	//	errmsg.Abort(c, errmsg.FAIL, "图片内容校验失败，请重试")
	//	return
	//}
	//
	//if imgSecCheckResp.ErrCode == 0 {
	//	if err := tempImg.SetState(enums.TempImgStateEnum.SecPass); err != nil {
	//		logger.Error(err)
	//	}
	//}

	result := make(map[string]interface{})
	//result["init_image_name"] = filename
	//result["init_image_url"] = fmt.Sprintf("/api/v1/anime/init_img/%s", filename)

	result["init_image_md5"] = md5Str
	result["init_image_url"] = config.DiffusionDomain + relativePathFile

	if config.Env == enums.EnvEnum.DEV { //如果是本地调试 要把文件上传到服务器
		url := "https://dev.cyuai.com/api/v1/sys/trans_upload"
		//url := "http://127.0.0.1:5002/api/v1/sys/trans_upload"
		fields := map[string]string{
			"relative_path_file": relativePathFile,
		}
		headers := map[string]string{
			"Authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJtb2JpbGUiOiIxMzAqKioqMzI3OSIsInVzZXJuYW1lIjoidmJlbiIsImlkIjoxLCJleHAiOjMzMjE2MTQ1NzUwLCJpc3MiOiJkZXNpZ24iLCJuYmYiOjE2ODAxNDU2NTB9.GBfjCyCz71aJBKysCVODng5L3b2sF4JyqHgZODqw6ck",
		}
		strJson, err := myhttp.UploadFile(url, filepath, "file", fields, headers)
		if err != nil {
			errmsg.Abort(c, errmsg.FAIL, "上传图片到服务器失败")
			return
		}

		mapRes := tools.GetMapFromJson(strJson)
		if mapRes == nil {
			errmsg.Abort(c, errmsg.FAIL, "上传图片到服务器失败")
			return
		}
		if mapRes["code"].(float64) != 0 {
			errmsg.Abort(c, errmsg.FAIL, mapRes["msg"].(string))
			return
		}

	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "添加成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
	}
}

var InitImageApi initImageApi
