package v1

import (
	"design-ai/middleware"
	"design-ai/service"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type mapApi_ struct{}
type locationReq struct {
	Lng float64 `json:"lng"`
	Lat float64 `json:"lat"`
}

type locationIpReq struct {
	Ip string `json:"ip"`
}

const mapKey = "YEGBZ-RSK6H-4WDDH-W4H4M-DUU3V-PQBSA"
const sKey = "MgyZS6jx8OQe227Q5oaOepz2UitMbfwY"
const mapBaseUrl = "https://apis.map.qq.com"

type location struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type address struct {
	Nation       string `json:"nation"`
	Province     string `json:"province"`
	City         string `json:"city"`
	District     string `json:"district"`
	Street       string `json:"street"`
	StreetNumber string `json:"street_number"`
	Adcode       int    `json:"adcode"`
}

func (obj mapApi_) GetLocationByIp(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req locationIpReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	location, err := service.LocationService.GetLocationByIp(req.Ip)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, err.Error())
		return
	}
	result := make(map[string]interface{})
	result["location"] = location
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "地址获取成功",
			"result": result,
		})
	}
}

func (obj mapApi_) GetLocationByPots(c *gin.Context) {
	var code int

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req locationReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	location, err := service.LocationService.GetLocationByPots(req.Lat, req.Lng)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, err.Error())
		return
	}

	result := make(map[string]interface{})
	result["location"] = location
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "地址获取成功",
			"result": result,
		})
	}
}

var MapApi mapApi_
