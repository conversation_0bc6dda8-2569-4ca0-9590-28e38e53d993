package v1

import (
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/service"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/myredis"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

type sysApi_ struct {
}

type setInsiderUserReq struct {
	Users string `json:"users"`
}

func (obj sysApi_) TransUpload(c *gin.Context) {
	var code int

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if claims.UserId != 1 {
		errmsg.Abort(c, errmsg.FAIL, "无权限")
		return
	}

	f, err := c.FormFile("file")
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}

	relativePathFile, _ := c.GetPostForm("relative_path_file")
	if relativePathFile == "" {
		errmsg.Abort(c, errmsg.FAIL, "relativePath未指定")
		return
	}

	//if !utils.IsFilePath("/" + relativePathFile) {
	//	logger.Error("relativePath文件路径不正确", relativePathFile)
	//	errmsg.Abort(c, errmsg.FAIL, "relativePath文件路径不正确")
	//	return
	//}

	//if strings.HasPrefix(relativePathFile, "debug/") == false {
	//	errmsg.Abort(c, errmsg.FAIL, "relativePath规则不正确")
	//	return
	//}

	if strings.Contains(relativePathFile, "/debug/") == false {
		errmsg.Abort(c, errmsg.FAIL, "relativePath规则不正确")
		return
	}

	relativePath := filepath.Dir(relativePathFile)

	dirPath := config.DiffusionFilePath + relativePath
	filepath := config.DiffusionFilePath + relativePathFile

	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		// mkdir 创建目录，mkdirAll 可创建多层级目录
		if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
			logger.Error(err, dirPath)
			errmsg.Abort(c, errmsg.FAIL, "创建路径失败")
			return
		}
	}

	if err := c.SaveUploadedFile(f, filepath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}

	result := make(map[string]interface{})
	result["filepath"] = filepath
	//result["init_image_name"] = filename
	//result["init_image_url"] = fmt.Sprintf("/api/v1/anime/init_img/%s", filename)

	//result["init_image_md5"] = md5Str
	//result["init_image_url"] = config.DiffusionDomain + relativePathFile

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "文件上传成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
	}
}

func (obj sysApi_) SetInsiderUser(c *gin.Context) {
	var code int
	var msg string
	var oReq setInsiderUserReq
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if claims.UserId != 1 && claims.UserId != 2 && claims.UserId != 3 {
		errmsg.Abort(c, errmsg.FAIL, "无权限")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if !strings.Contains(oReq.Users, ",") {
		hash, _ := myredis.HGetAll(enums.RedisKeyEnum.InsiderUsers)
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  "当前内部用户",
			"val":  hash,
		})
		return
	}

	//myredis.Del(enums.RedisKeyEnum.InsiderUsers)
	ary := strings.Split(oReq.Users, ",")
	for _, val := range ary {
		if val == "" {
			//myredis.HDel(enums.RedisKeyEnum.InsiderUsers, "")
			continue
		}
		tmp, _ := myredis.HGet(enums.RedisKeyEnum.InsiderUsers, val)
		if tmp == "" {
			myredis.HSet(enums.RedisKeyEnum.InsiderUsers, val, "close")
		}
	}

	//if err := myredis.Set(enums.RedisKeyEnum.InsiderUser, oReq.Users, -1); err != nil {
	//	logger.Error(err)
	//	errmsg.Abort(c, errmsg.FAIL, "设置redis失败")
	//	return
	//}

	hash, _ := myredis.HGetAll(enums.RedisKeyEnum.InsiderUsers)
	c.JSON(http.StatusOK, gin.H{
		"code": code,
		"msg":  "设置内部用户完成",
		"val":  hash,
	})

}

func (obj sysApi_) ReSetSiteConf(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if claims.UserId != 1 {
		errmsg.Abort(c, errmsg.FAIL, "无权限")
		return
	}
	service.CacheService.SiteConfResult = nil

	msg = "重置站点配置缓存完成"
	c.JSON(http.StatusOK, gin.H{
		"code":   code,
		"msg":    msg,
		"result": service.CacheService.SiteConfResult,
	})

}

var SysApi sysApi_
