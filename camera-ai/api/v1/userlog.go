package v1

import (
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/tools"
	"github.com/gin-gonic/gin"
	"net/http"
)

type userLogApi_ struct {
}

type userLogReq struct {
	LogType int    `json:"log_type"`
	LogJson string `json:"log_json"`
}

func (obj userLogApi_) Save(c *gin.Context) {
	var code int
	var msg string
	var oReq userLogReq
	//logger.Info("head", c.Request.Header)

	//xForwardedFor := c.Request.Header.Get("X-Forwarded-For")
	//logger.Info("xForwardedFor:", xForwardedFor)

	userId := uint(0)
	tokenHeader := c.Request.Header.Get("Authorization")
	claims, err := middleware.GetClaimsByToken(tokenHeader)
	if err != nil {
		if tokenHeader != "" {
			logger.Error(err, "   tokenHeader:", tokenHeader)
		}
	}
	if claims != nil {
		userId = claims.UserId
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	logJson := oReq.LogJson
	if logJson == "" {
		logJson = "{}"
	}

	userEnvStr := c.Request.Header.Get("User-Env")
	if userEnvStr == "" {
		userEnvStr = "{}"
	}
	userLog := model.UserLog{
		TrackId: tools.Md5(userEnvStr),
		UserId:  userId,
		LogType: oReq.LogType,
		Ip:      tools.GetClientIp(c.Request.Header),
		LogJson: logJson,
		UserEnv: userEnvStr,
	}

	//logger.Info("userEnvStr:", userEnvStr)
	//if userEnvStr != "" {
	//	var userSystem model.UserEnv
	//	if err := tools.GetStructFromJson(&userSystem, userEnvStr); err != nil {
	//		logger.Error(err, userEnvStr)
	//		userLog.UserEnv = userEnvStr
	//	} else {
	//		userSystem.Ip = tools.GetClientIp(c.Request.Header)
	//		jsonStr := tools.GetJsonFromStruct(userSystem)
	//		logger.Info("复制IP userSystem:", jsonStr)
	//		if jsonStr != "" {
	//			userLog.UserEnv = jsonStr
	//		}
	//	}
	//}

	if err := userLog.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存日志出错")
		return
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  "日志保存成功",
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
	if _, err := service.LocationService.PushJson("userlog", userLog.ID); err != nil {
		logger.Error(err)
	}
}

var UserLogApi userLogApi_
