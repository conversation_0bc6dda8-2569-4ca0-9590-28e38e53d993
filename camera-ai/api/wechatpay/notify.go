package wechatpay

import (
	"design-ai/enums"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/tools"
	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/wechat/v3"
	"net/http"
	"time"
)

func Notify(c *gin.Context) {
	logger.Info("已经接收到微信回调v1.3")

	// 从 HTTP 请求头中获取加密算法类型和加密使用的证书序列号
	//nonce := c.Request.Header.Get("Wechatpay-Nonce")
	//serial := c.Request.Header.Get("Wechatpay-Serial")

	//logger.Info("nonce:", nonce, "  ", "serial:", serial)

	userLogId := uint(0)
	drawTypeStr, _ := c.GetPostForm("log_id")
	if drawTypeStr != "" {
		userLogId = tools.ParseUint(drawTypeStr)
		if userLogId == 0 {
			logger.Error("参数错误 drawTypeStr:", drawTypeStr)
			errmsg.Abort(c, errmsg.FAIL, "参数错误 "+drawTypeStr)
			return
		}
	}

	var notifyResource wechat.Resource
	if userLogId > 0 {
		logger.Info("logid大于0 充userLog获取数据")
		var userLog model.UserLog
		if err := userLog.GetByID(userLogId); err != nil {
			logger.Error(err)
		}
		//userLog.LogJson = `{"nonce": "52KW1IfvFpVj", "algorithm": "", "ciphertext": "nliWS3Dq8SLeE/tpiU+1Pnu5Vk3w+DrI4PeoxbDFM6bIon2WUUwgcxjL2McOIX392hVzmbvhDvCnKq/od+T0Wk1iHSJuRoc7yQ/1NpuWwOcsrC1Yy5r+ELLoATqC2D+y9ajXniDUERcO6TvUTlNd6EIkfOzkFAa6xmM4FoTrrEnA1zaPZmT0lTqsgRnxJqrGh34rBOD7eFa9PBmhwj4MfFadfd1jDp91MDujB4GxrIZbUlxR/AvhzhSrbTHJo363sp0Eyhbe1gRo2YzIZLL1mtbioIGuNshdoTRTYrHiLuBWq4L7/hXE76nnkGr8+1Si69ajlkQB5+YAg6+IpNUlXRa3vauFfji8u7lRtKlLuJ3KRvzAQV0nHqUTUiE7lDMGQLc8VNbVI8l2Do2+uQ+7s7bLBbip9YlxuMpaX7N2ZtwBtOGmKRfCcCDyqODu1DcNcGLlymYro1reEhbuOx/+wSokhTufjPZjSaYW4akT0F6yyDW5ZBX04VSy6P8TsIfXfxOZaghkLvt3Soh9vBRZLsDEVz6xROq0DK8M+fs9hzyUAWCv07D/AZO9pXo6rL3D1nUXCwci", "associated_data": "transaction"}`
		if err := tools.GetStructFromJson(&notifyResource, userLog.LogJson); err != nil {
			logger.Error(err)
		}
	} else {
		notifyData, err := wechat.V3ParseNotify(c.Request)
		if err != nil {
			logger.Error(err)
			return
		}
		logger.Info("解密前：Ciphertext:", notifyData.Resource.Ciphertext, "    Nonce:", notifyData.Resource.Nonce, " AssociatedData:", notifyData.Resource.AssociatedData)

		notifyResource = wechat.Resource{
			Ciphertext:     notifyData.Resource.Ciphertext,
			Nonce:          notifyData.Resource.Nonce,
			AssociatedData: notifyData.Resource.AssociatedData,
		}
		userLog := model.UserLog{
			LogType: enums.UserLogTypeEnum.WechatpayNotify,
			Ip:      tools.GetClientIp(c.Request.Header),
			LogJson: tools.GetJsonFromStruct(notifyResource),
			UserEnv: "{}",
		}
		if err := userLog.Save(); err != nil {
			logger.Error(err)
		} else {
			logger.Info("数据保存成功", userLog)
		}
	}

	// 解密加密数据
	result, err := service.WechatpayService.V3DecryptNotifyCipherText(notifyResource.Ciphertext, notifyResource.Nonce, notifyResource.AssociatedData)
	if err != nil {
		// 解密失败，处理错误
		c.String(http.StatusBadRequest, "解密支付通知失败：%v", err)
		return
	}
	logger.Info("解密完成")
	logger.Info("解密支付 result:", tools.GetJsonFromStruct(result))
	logger.Info(result.TransactionId, "  ", result.TradeState, " ", result.OutTradeNo)

	if result.TradeState == "SUCCESS" {

		outTradeNo := result.OutTradeNo
		tradeNo := result.TransactionId

		//payTime, err := time.ParseInLocation("2006-01-02 15:04:05", result.SuccessTime, time.Local) //这里按照当前时区转
		payTime, err := time.Parse(time.RFC3339, result.SuccessTime)
		if err != nil {
			logger.Error(err)
			return
		}
		logger.Info("payTime ", result.SuccessTime, "  ", payTime)
		if err != nil {
			logger.Error(err)
			return
		}
		if err := service.RechargeService.HandlePaySuccessRecharge(outTradeNo, tradeNo, payTime, tools.GetJsonFromStruct(result)); err != nil {
			logger.Error(err)
			return
		} else {
			logger.Info("支付完成")
		}
	} else {
		logger.Error("result:", tools.GetJsonFromStruct(result))
	}

	//c.JSON(http.StatusOK, &wechat.V3NotifyRsp{Code: gopay.SUCCESS, Message: "成功"})
	logger.Info("返回success")
	c.String(http.StatusOK, "success")

}
