package model

import (
	"design-ai/enums"
	"gorm.io/gorm"
)

type UploadImg struct {
	gorm.Model
	UserId     uint    `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	OrigWhere  int     `json:"orig_where" gorm:"type:int;not null;default:0;comment:对应枚举UploadImgOrigWhereEnum 1用户头像"`
	OrigId     uint    `json:"orig_id" gorm:"type:bigint;not null;default:0;comment:数据id  1用户ID"`
	Md5        string  `json:"md5" gorm:"type:varchar(50);not null;default:'';comment:图片md5"`
	SmallPath  string  `json:"small_path" gorm:"type:varchar(100);not null;default:'';comment:图片路径"`
	Path       string  `json:"path" gorm:"type:varchar(100);not null;default:'';comment:图片路径"`
	Width      int     `json:"width" gorm:"type:int;not null;default:0;comment:图片宽度"`
	Height     int     `json:"height" gorm:"type:int;not null;default:0;comment:图片高度"`
	OrderNo    string  `json:"order_no" gorm:"type:varchar(50);comment:订单编号"`
	OrderIndex float32 `json:"order_index" gorm:"type:float;not null;default:0;comment:排序 越大越前面"`
	State      int     `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 1上传成功 2校验失败 3非法图片 4校验通过 5使用中"`
}

func (UploadImg) TableName() string {
	return "T_UploadImg"
}

func (o *UploadImg) GetById(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *UploadImg) GetByMd5(md5Str string) error {
	err := db.First(o, "md5=?", md5Str).Error
	return err
}

func (o *UploadImg) GetByUserOrigWhere(userId uint, origWhere int) error {
	err := db.Debug().First(o, "user_id=? and orig_where=?", userId, origWhere).Order("id desc").Error
	return err
}

func (o *UploadImg) GetArtStyleImg(artStyleId uint, orderIndex int) error {
	err := db.Debug().First(o, "orig_where=? and orig_id=? and order_index=?", enums.UploadImgOrigWhereEnum.ArtStyle, artStyleId, orderIndex).Error
	return err
}

func (o *UploadImg) DeleteByMd5(md5Str string) error {
	err := db.Debug().Where("md5=?", md5Str).Delete(o).Error
	return err
}

func (o *UploadImg) GetList(dest interface{}, userId uint, origWhere int, origId uint, state int, order string, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o)
	if userId > 0 {
		tx.Where("user_id=?", userId)
	}
	if origWhere > 0 {
		tx.Where("orig_where=?", origWhere)
	}
	if origId > 0 {
		tx.Where("orig_id=?", origId)
	}
	if state >= 0 {
		tx.Where("state=?", state)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	if order == "" {
		order = "desc"
	}
	tx.Order("order_index " + order).Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *UploadImg) Save() error {
	return db.Save(o).Error
}

func (o *UploadImg) SetState(state int) error {
	return db.Model(o).Updates(UploadImg{State: state}).Error
}

func (o *UploadImg) SetWidthHeight(width int, height int, size int) error {
	return db.Model(o).Updates(UploadImg{Width: width, Height: height}).Error
}
