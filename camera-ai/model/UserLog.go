package model

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type UserLog struct {
	gorm.Model
	TrackId     string          `json:"track_id" gorm:"type:varchar(50);not null;default:'';comment:跟踪ID"`
	UdId        string          `json:"ud_id" gorm:"type:varchar(50);not null;default:'';comment:设备标识"`
	UserId      uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	LogType     int             `json:"log_type" gorm:"type:int;not null;default:0;comment:日志类型"`
	Ip          string          `json:"ip" gorm:"type:varchar(50);not null;default:'';comment:客户端IP"`
	Lng         decimal.Decimal `json:"lng" gorm:"type:decimal(16,6);not null;default:0;comment:经度"`
	Lat         decimal.Decimal `json:"lat" gorm:"type:decimal(16,6);not null;default:0;comment:纬度"`
	Plat        string          `json:"plat" gorm:"type:varchar(50);not null;default:'';comment:来源平台"`
	Os          string          `json:"os" gorm:"type:varchar(50);not null;default:'';comment:操作系统名称"`
	DeviceBrand string          `json:"device_brand" gorm:"type:varchar(50);not null;default:'';comment:设备品牌"`
	DeviceType  string          `json:"device_type" gorm:"type:varchar(50);not null;default:'';comment:设备类型"`
	Channel     string          `json:"channel" gorm:"type:varchar(50);not null;default:'';comment:渠道"`
	VersionName string          `json:"version_name" gorm:"type:varchar(50);not null;default:'';comment:版本名称"`
	VersionCode int             `json:"version_code" gorm:"type:int;not null;default:0;comment:版本号"`
	Nation      string          `json:"nation" gorm:"type:varchar(50);not null;default:'';comment:国家"`
	Province    string          `json:"province" gorm:"type:varchar(50);not null;default:'';comment:省份"`
	City        string          `json:"city" gorm:"type:varchar(50);not null;default:'';comment:城市"`
	District    string          `json:"district" gorm:"type:varchar(50);not null;default:'';comment:区"`
	Address     string          `json:"address" gorm:"type:varchar(100);not null;default:'';comment:地址"`
	LogJson     string          `json:"log_json" gorm:"type:json;';comment:日志内容"`
	UserEnv     string          `json:"user_env" gorm:"type:json;';comment:用户环境"`
}

func (UserLog) TableName() string {
	return "T_UserLog"
}

func (o *UserLog) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *UserLog) GetList(dest interface{}, state int, page int, pageSize int) error {
	tx := db.Debug().Model(o).Where("state=?", state).Order("order_index asc").Scan(dest)
	return tx.Error
}

func (o *UserLog) Save() error {
	return db.Save(o).Error
}

func (o *PushLog) SetAddr(nation string, province string, city string, district string, addr string) error {
	return db.Debug().Model(o).Updates(UserLog{Nation: nation, Province: province, City: city, District: district, Address: addr}).Error
}

func (o *PushLog) SetUserEnv(envStr string, address string, plat string) error {
	return db.Debug().Model(o).Updates(UserLog{UserEnv: envStr, Address: address, Plat: plat}).Error
}
