package routes

import (
	"design-ai/api/alipay"
	"design-ai/api/appleiap"
	"design-ai/api/manage"
	"design-ai/api/push"
	v1 "design-ai/api/v1"
	"design-ai/api/wechatpay"
	"design-ai/middleware"
	"design-ai/service"
	"design-ai/utils/config"
	"design-ai/utils/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

func InitRouter() {
	r := gin.New()
	//r.Use(middleware.Log())
	r.Use(middleware.Cors())
	//r.Use(middleware.TimeoutMiddleware(time.Second * 15))

	//r.StaticFile("api/testupload", "./chunkfile/testupload.html")
	//r.Static("/", "/Users/<USER>/Downloads/h522")
	//r.Static("/", "/Users/<USER>/Desktop/zcloud/aigc-front/HomeDecoration/unpackage/dist/build/h5")

	router := r.Group("api/v1")
	{
		router.GET("hello/:action", func(c *gin.Context) {

			//http://localhost:5002/api/v1/hello/img.png
			action := c.Param("action")
			result := make(map[string]interface{})
			result["action"] = action
			result["host"] = c.Request.Host
			result["path"] = c.Request.URL.Path

			logger.Info("action:", action)
			msg := "ok"
			if action == "img1.png" {
				c.Header("Content-Type", "application/octet-stream")
				c.Header("Content-Disposition", "attachment; filename=xiaz11.png")
				c.Header("Content-Transfer-Encoding", "binary")
				c.File("/Users/<USER>/Downloads/8e88f96fdcc92553e62e6f2415a23e42.png")
				return
			} else if action == "img2.png" {
				c.Header("Content-Type", "application/octet-stream") //
				//c.Header("Content-Disposition", "attachment; filename=xiaz11.png")
				//c.Header("Content-Transfer-Encoding", "binary")
				c.File("/Users/<USER>/Downloads/15481496764171.png")
				return
			} else if action == "TestOutImg" {
				value, err := service.FacefakeService.PopJson()
				logger.Info("绘图json:", value)
				msg = value
				if err != nil {
					msg += err.Error()
					logger.Error(value, err)
				}
				if err := service.FacefakeService.UpdateToImg(value); err != nil {
					msg += err.Error()
					logger.Error(err)
				}
			} else if action == "push" {
				openId := c.Query("open_id")
				logger.Info("openid:", openId)
				service.PushMessage.SendPhotoCompletedMessage(openId)
				msg = "push 完成" + openId
			} else if action == "GenUrlLink" {
				urlLink := service.WeixinService.GenUrlLink()
				result["url_link"] = urlLink
			}

			c.JSON(http.StatusOK, gin.H{
				"code":   0,
				"msg":    msg,
				"result": result,
			})
		})

		//router.POST("getverifycode", v1.GetVerifyCode)

		//router.POST("recharge/price_list", v1.RechargeApi.GetPriceList)
		//router.POST("conf/get", v1.ConfApi.Get)

		//router.POST("user/reg", v1.Reg)
		router.POST("site/get_conf", v1.SiteApi.GetConf)

		router.POST("user/get_smscode", v1.SendSms)
		router.POST("user/login", v1.Login)
		router.POST("user/login_sms", v1.LoginSms)
		router.POST("user/login_wx", v1.LoginWeixin)
		router.POST("user/login_one", v1.LoginUniOneTouch)
		router.POST("user/log", v1.UserLogApi.Save)
		//router.POST("user/reset_password", v1.UserApi.ChangePasswordByMobile)
		//router.POST("user/logout", v1.Reg)
		//
		//router.POST("weixin/login", weixin.Login)
		//router.POST("weixin/notify", weixin.Notify)
		//

		router.POST("recharge/products", v1.RechargeApi.GetProductList)
		router.GET("alipay/callback", alipay.Callback)
		router.POST("alipay/notify", alipay.Notify)

		router.GET("wechatpay/callback", wechatpay.Callback)
		router.POST("wechatpay/notify", wechatpay.Notify)

		router.POST("appleiap/notify", appleiap.Notify)
		router.POST("appleiap/restore", appleiap.Restore)
		router.POST("appleiap/verify_hand", appleiap.VerifyHand)

		router.GET("push/notify_wx", push.NotifyWx)
		router.POST("push/notify_wx", push.NotifyWx)

	}

	routerAuth := r.Group("api/v1")
	routerAuth.Use(middleware.JwtToken())
	{

		routerAuth.POST("sys/set_insider_user", v1.SysApi.SetInsiderUser)

		routerAuth.POST("user/get_info", v1.UserApi.GetUserInfo)
		routerAuth.POST("user/bind_mobile_by_weixin", v1.UserApi.BindMobileByWeixin)
		routerAuth.POST("user/get_invite_qrcode", v1.UserApi.GetInviteQrCode)
		routerAuth.POST("user/upload_face", v1.UserApi.UploadFaceImg)

		routerAuth.POST("share/invite", v1.ShareApi.Invite)
		routerAuth.POST("share/visit", v1.ShareApi.Visit)

		routerAuth.POST("artstyle/list", v1.ArtstyleApi.GetList)

		routerAuth.POST("digital/reset", v1.DigitalApi.Reset)
		routerAuth.POST("digital/analyze_face", v1.DigitalApi.AnalyzeFace)
		routerAuth.POST("digital/del", v1.DigitalApi.Del)

		routerAuth.POST("facefake/gen", v1.FacefakeApi.Gen)
		routerAuth.POST("facefake/list", v1.FacefakeApi.GetList)
		routerAuth.POST("facefake/del", v1.FacefakeApi.Del)

		routerAuth.POST("outimg/list", v1.OutImgApi.GetOutImgList)
		routerAuth.POST("outimg/get", v1.OutImgApi.GetOutImgItem)
		routerAuth.POST("outimg/del", v1.OutImgApi.DelByMd5)
		routerAuth.POST("outimg/upscale", v1.OutImgApi.UpScale)
		routerAuth.POST("outimg/collect", v1.OutImgApi.Collect)
		routerAuth.POST("outimg/download", v1.OutImgApi.Download)

		routerAuth.POST("user/change_password", v1.UserApi.ChangePassword)
		routerAuth.POST("user/change_username", v1.UserApi.ChangeUsername)
		routerAuth.POST("user/set_wx_sub", v1.UserApi.SetWxSubscribe)
		routerAuth.POST("user/set_insider_state", v1.UserApi.SetInsiderUserState)

		routerAuth.POST("outimg/share", v1.OutImgApi.SetShare)

		routerAuth.POST("coin/get_balance", v1.CoinApi.GetBalance)
		routerAuth.POST("recharge/launch", v1.RechargeApi.Launch)
		routerAuth.POST("recharge/get_balance", v1.RechargeApi.GetBalance)
		routerAuth.POST("recharge/query", v1.RechargeApi.QueryByOutTradeNo)
		routerAuth.POST("appleiap/verify", appleiap.Verify)

		routerAuth.POST("map/get_location_by_ip", v1.MapApi.GetLocationByIp)
		routerAuth.POST("map/get_location_by_pots", v1.MapApi.GetLocationByPots)

	}

	routerAuthManage := r.Group("api/manage")
	routerAuthManage.Use(middleware.JwtTokenCenter())
	{

		routerAuthManage.POST("user/list", manage.UserApi.UserList)
		routerAuthManage.POST("user/set_insider", manage.UserApi.SetInsider)
		routerAuthManage.POST("coin/addquick", manage.CoinApi.AddQuick)
		routerAuthManage.POST("coin/list", manage.CoinApi.GetCoinBalanceListByUser)

		routerAuthManage.POST("artstyle/add", manage.ArtStyleApi.Add)
		routerAuthManage.POST("artstyle/list", manage.ArtStyleApi.GetList)
		routerAuthManage.POST("artstyle/upload_style_img", manage.ArtStyleApi.UploadStyleImg)
		routerAuthManage.POST("artstyle/repair", manage.ArtStyleApi.Repair)
		routerAuthManage.POST("artstyle/repair_passport", manage.ArtStyleApi.RepairPassport)

		routerAuthManage.POST("facefake/list", manage.FacefakeApi.GetList)
		//routerAuthManage.POST("facefake/del", manage.FacefakeApi.Del)

		routerAuthManage.POST("site/get_conf", v1.SiteApi.GetConf)
		routerAuthManage.POST("recharge/list", manage.RechargeList)

		routerAuthManage.POST("statistic/GenStatistic", manage.GenStatistic)

	}
	logger.Info("开启端口监听...", config.HttpPort)
	if err := r.Run(config.HttpPort); err != nil {
		logger.Error("端口监听失败", err)
	} else {
		logger.Info("端口监听中,port:", config.HttpPort)
	}
}
