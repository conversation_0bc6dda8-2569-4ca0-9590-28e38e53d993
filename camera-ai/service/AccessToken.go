package service

import (
	"design-ai/enums"
	"design-ai/utils/config"
	"design-ai/utils/logger"
	"design-ai/utils/myhttp"
	"design-ai/utils/myredis"
	"encoding/json"
	"errors"
	"net/url"
	"time"
)

type accessTokenUpdate_ struct {
	AccessToken string    ` json:"access_token"`
	ExpiresIn   int       `json:"expires_in"`
	StartTime   time.Time `json:"start_time"`
}

func (access *accessTokenUpdate_) Run(force bool) {

	defer func() {
		if e := recover(); e != nil {
			//log.Printf("err:%v", e)
			logger.Error("accessTokenUpdate_奔溃:", e)
		}
	}()
	logger.Info("accessTokenUpdate.Run 开始尝试更新AccessToken 强制更新标记：", force)

	bUpdate, e := access.needUpdate()
	if e != nil {
		logger.Error(e)
		return
	}
	if bUpdate || force {
		if err := access.GetFromWeb(); err != nil {
			logger.Error(err)
			return
		}
		if err := access.saveToRedis(); err != nil {
			logger.Error(err)
			return
		}

	}
}

func (access *accessTokenUpdate_) GetAccessToken() string {
	if config.RunTimer == false {
		access.getFromRedis()
	}
	return access.AccessToken
}

func (access *accessTokenUpdate_) GetFromWeb() error {
	startTime := time.Now()

	if config.AppMode == "debug" {

		//access.StartTime = startTime
		//access.ExpiresIn = 7000
		//access.AccessToken = "adfafdsdfdsdfdf"
		access.getFromRedis()
		return nil
	}

	urlStr := "https://api.weixin.qq.com/cgi-bin/token"
	params := url.Values{}
	params.Add("grant_type", "client_credential")
	params.Add("appid", config.WeixinAppId)
	params.Add("secret", config.WeixinSecret)
	logger.Info("开始获取AccessToken")
	bs, err := myhttp.GetByte(urlStr, params)
	if err != nil {
		logger.Error("获取AccessToken请求失败", err)
		return err
	}
	err = json.Unmarshal(bs, &access)
	if access.ExpiresIn > 0 && len(access.AccessToken) > 0 {
		access.StartTime = startTime
	} else {
		logger.Error("获取到accesstoken", string(bs))
		return errors.New("AccessToken解析失败")
	}
	logger.Info("获取到accesstoken", access.AccessToken, access.ExpiresIn, access.StartTime)
	return nil
}

func (access *accessTokenUpdate_) getFromRedis() error {
	jsonStr := myredis.Get(enums.RedisKeyEnum.WeixinAccessTokenUpdate)
	if err := json.Unmarshal([]byte(jsonStr), &access); err != nil {
		logger.Error(err)
		return err
	}
	if access.ExpiresIn > 0 && len(access.AccessToken) > 0 {
		return nil
	}
	return errors.New("无数据")
}

func (access *accessTokenUpdate_) needUpdate() (bool, error) {
	if len(access.AccessToken) == 0 {
		jsonStr := myredis.Get(enums.RedisKeyEnum.WeixinAccessTokenUpdate)
		if len(jsonStr) == 0 {
			logger.Info("redis字符串为空,需要获取")
			return true, nil
		}
		if err := json.Unmarshal([]byte(jsonStr), &access); err != nil {
			logger.Error(err)
			return false, err
		}
	}

	tip := time.Now().Unix() - access.StartTime.Unix()

	if tip > int64(access.ExpiresIn-300) {
		logger.Info("needUpdate:需要更新", tip, access.ExpiresIn-300)
		return true, nil
	}
	logger.Info("needUpdate:不需要更新", tip, access.ExpiresIn-300)
	return false, nil
}

func (access *accessTokenUpdate_) saveToRedis() error {
	jsonByte, err := json.Marshal(access)
	if err != nil {
		logger.Error(err)
		return err
	}
	if err := myredis.Set(enums.RedisKeyEnum.WeixinAccessTokenUpdate, string(jsonByte), -1); err != nil {
		logger.Error(err)
		return err
	}
	logger.Info("保存accesstoken到redis成功：", string(jsonByte))
	return nil
}

var AccessTokenUpdate accessTokenUpdate_
