package service

import (
	"crypto/md5"
	"design-ai/enums"
	"design-ai/model"
	"design-ai/utils/config"
	"design-ai/utils/logger"
	"design-ai/utils/myimg"
	"design-ai/utils/tools"
	"encoding/hex"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"os"
	"path"
	"strings"
)

type artStyle_ struct {
}

var ArtStyle artStyle_

func (o *artStyle_) GetStyleImagePath(artStyle model.ArtStyle, orderIndex int, faceShapeIndex int) string { //orderIndex 模型序号  //faceShapeIndex 脸型序号1 2 3 4

	artStyleImgPath := "camera/modelimg/"
	if artStyle.ArtType == enums.ArtTypeEnum.Artistic {
		artStyleImgPath += "风格照/"
	} else {
		logger.Error("参数错误", artStyle.ArtType)
		return ""
	}

	if artStyle.Sex == 1 {
		artStyleImgPath += "男/"
	} else if artStyle.Sex == 2 {
		artStyleImgPath += "女/"
	}
	artStyleImgPath += artStyle.Title + "/"

	orderIndexStr := fmt.Sprintf("0%d", orderIndex)
	if orderIndex >= 10 {
		orderIndexStr = fmt.Sprintf("%d", orderIndex)
	}
	artStyleImgPath += fmt.Sprintf("%s-%s/", artStyle.Title, orderIndexStr)
	artStyleImgPath += fmt.Sprintf("%s%d-%d.png", artStyle.Title, orderIndex, faceShapeIndex)
	return artStyleImgPath
}

func (o *artStyle_) GetPassportStyleImagePath(facefake model.Facefake, faceShapeIndex int) string { //orderIndex 模型序号  //faceShapeIndex 脸型序号1 2 3 4

	artStyleImgPath := "camera/modelimg/"
	if facefake.ArtType == enums.ArtTypeEnum.Passport {
		artStyleImgPath += "证件照/"
	} else {
		logger.Error("参数错误", facefake.ArtType)
		return ""
	}

	if facefake.AgeSect == "" || facefake.Dress == "" {
		logger.Error("参数错误", facefake)
		return ""
	}
	if facefake.Sex == 1 {
		artStyleImgPath += "男/"
	} else if facefake.Sex == 2 {
		artStyleImgPath += "女/"
	}

	artStyleImgPath += facefake.AgeSect + "/"
	artStyleImgPath += facefake.Dress + "/"

	orderIndex := facefake.StyleImgIndex

	orderIndexStr := fmt.Sprintf("0%d", orderIndex)
	if orderIndex >= 10 {
		orderIndexStr = fmt.Sprintf("%d", orderIndex)
	}
	artStyleImgPath += fmt.Sprintf("%s-%s/", facefake.Dress, orderIndexStr)
	artStyleImgPath += facefake.PhotoBg + "/"
	artStyleImgPath += facefake.PhotoSize + "/"

	photoSizeNum := o.GetPhotoSizeNum(facefake.PhotoSize)
	if photoSizeNum < 0 {
		logger.Error("参数错误", facefake)
		return ""
	}

	artStyleImgPath += fmt.Sprintf("%s%d-%d-%d%s.png", facefake.Dress, orderIndex, photoSizeNum, faceShapeIndex, facefake.PhotoBg)
	return artStyleImgPath
}

func (o *artStyle_) GetPassportShowStyleImagePath(artStyle model.ArtStyle, orderIndex int) string { //orderIndex 模型序号

	artStyleImgPath := "camera/modelimg/"
	if artStyle.ArtType == enums.ArtTypeEnum.Passport {
		artStyleImgPath += "风格照/"
	} else {
		logger.Error("参数错误", artStyle.ArtType)
		return ""
	}

	if artStyle.AgeSect == "" || artStyle.Title == "" {
		logger.Error("参数错误", artStyle)
		return ""
	}
	if artStyle.Sex == 1 {
		artStyleImgPath += "男/"
	} else if artStyle.Sex == 2 {
		artStyleImgPath += "女/"
	}

	artStyleImgPath += artStyle.AgeSect + "/"
	artStyleImgPath += artStyle.Title + "/"

	orderIndexStr := fmt.Sprintf("0%d", orderIndex)
	if orderIndex >= 10 {
		orderIndexStr = fmt.Sprintf("%d", orderIndex)
	}
	artStyleImgPath += fmt.Sprintf("%s-%s/", artStyle.Title, orderIndexStr)

	artStyleImgPath += fmt.Sprintf("%s-%s.png", artStyle.Title, orderIndexStr)
	return artStyleImgPath
}

func (o artStyle_) RepairArtisticItem(artStyleId uint) error {
	var artStyle model.ArtStyle
	if err := artStyle.GetById(artStyleId); err != nil {
		logger.Error(err)
		return err
	}
	if artStyle.ArtType != enums.ArtTypeEnum.Artistic {
		logger.Error("不是艺术照类型,不处理", artStyle)
		return errors.New("不是艺术照类型,不处理")
	}
	for orderIndex := 1; orderIndex < 20; orderIndex++ {
		for faceShapeIndex := 1; faceShapeIndex <= 4; faceShapeIndex++ {
			artStyleImgPath := o.GetStyleImagePath(artStyle, orderIndex, faceShapeIndex)
			fullPath := config.DiffusionFilePath + artStyleImgPath
			exists, err := tools.PathFileExists(fullPath)
			if err != nil {
				logger.Error(err)
				return err
			}
			if exists == false {
				if faceShapeIndex == 1 && orderIndex == 1 {
					logger.Error("图片模板不存在", fullPath, "  ", artStyle.ID, " ", artStyle.Title)
					return errors.New("图片模板不存在")
				}
				logger.Info("检索完成", artStyle.ID, " ", artStyle.Title)
				return nil
			}
			if faceShapeIndex == 1 { //第一张脸型图，去生成默认图片
				var uploadImg model.UploadImg
				if err := uploadImg.GetArtStyleImg(artStyleId, orderIndex); err != nil {
					if err != gorm.ErrRecordNotFound {
						logger.Error(err)
						return err
					}
				}
				if uploadImg.ID > 0 {
					logger.Info("预览风格图片已经存在，删除图片 artStyleId:", artStyleId)
					//filepath := config.DiffusionFilePath + uploadImg.Path
					//smallFilePath := service.ImgService.GetSmallImagePath(filepath)
					if err := ImgService.DelImage(uploadImg.Path); err != nil {
						logger.Error(err)
						return err
					}
					if err := ImgService.DelImage(ImgService.GetSmallImagePath(uploadImg.Path)); err != nil {
						logger.Error(err)
						return err
					}
				}
				if true { //预览图不存在，生成预览图
					logger.Info("开始处理预览风格图片 artStyleId:", artStyleId)
					//oMd5Str := fmt.Sprintf("%s,%s", artStyleImgPath, time.Now().Format("2006-01-02 15:04:05.000"))
					oMd5Str := artStyleImgPath
					has := md5.Sum([]byte(oMd5Str))
					md5Str := hex.EncodeToString(has[:])
					if len(md5Str) != 32 {
						logger.Error(err)
						return errors.New("len(md5Str) != 32")
					}

					ext := path.Ext(fullPath) // 输出 .html
					ext = ".jpg"
					filename := md5Str + strings.ToLower(ext)

					pre2 := md5Str
					pre2 = pre2[0:1]

					relativePath := fmt.Sprintf("camera/artstyle/")
					relativePathFile := relativePath + filename

					dirPath := config.DiffusionFilePath + relativePath
					filepath := config.DiffusionFilePath + relativePathFile

					//tempImg := model.UploadImg{
					//	OrigWhere:  enums.UploadImgOrigWhereEnum.ArtStyle,
					//	OrigId:     artStyleId,
					//	Md5:        md5Str,
					//	Path:       relativePathFile,
					//	OrderIndex: float32(orderIndex),
					//	State:      0,
					//}
					if uploadImg.ID > 0 {
						uploadImg.Md5 = md5Str
						uploadImg.Path = relativePathFile
					} else {
						uploadImg = model.UploadImg{
							OrigWhere:  enums.UploadImgOrigWhereEnum.ArtStyle,
							OrigId:     artStyleId,
							Md5:        md5Str,
							Path:       relativePathFile,
							OrderIndex: float32(orderIndex),
							State:      0,
						}
					}

					if err := uploadImg.Save(); err != nil {
						logger.Error(err)
						return err
					}

					if _, err := os.Stat(dirPath); os.IsNotExist(err) {
						// mkdir 创建目录，mkdirAll 可创建多层级目录
						if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
							logger.Error(err, dirPath)
							return err
						}
					}

					//if _, err := tools.CopyFile(fullPath, filepath); err != nil {
					//	logger.Error(err, fullPath, "   ", filepath)
					//	return err
					//}

					if err := tools.CopyImgToImg(fullPath, filepath); err != nil {
						logger.Error(err, fullPath, "   ", filepath)
						return err
					}

					if err := uploadImg.SetState(enums.UploadImgStateEnum.Uploaded); err != nil {
						logger.Error(err)
					}
					img, size, _, err := myimg.FileToImgAndSize(filepath)
					if err != nil {
						logger.Error(err)
						return err
					}

					if err := uploadImg.SetWidthHeight(img.Bounds().Size().X, img.Bounds().Size().Y, size); err != nil {
						logger.Error(err)
					}
					small := myimg.ResizeImg(512, 512, img, true)
					smallFilePath := ImgService.GetSmallImagePath(filepath)
					if err := myimg.ImgToFile(small, smallFilePath); err != nil {
						logger.Error(err)
						return err
					}
					logger.Info("处理预览风格图片完成 artStyleId:", artStyleId)
				}
			}
		}
	}
	return nil
}

func (o *artStyle_) RepairPassportArtStyle(repairArtStyleId uint) error { //repairArtStyleId >2修复单条  ==1批量新增  ==2全部修复
	fullPath := config.DiffusionFilePath + fmt.Sprintf("camera/modelimg/证件照/")
	arySex := []int{1, 2}
	arySexTxt := []string{"", "男", "女"}
	//aryAgeSect := []string{"儿童", "青年", "成年"}
	//aryDress := []string{"西装", "白衬衫", "职场"}
	for _, sex := range arySex {
		sexPath := fullPath + arySexTxt[sex] + "/"
		aryAgeSect := tools.GetDirs(sexPath)
		for _, ageSect := range aryAgeSect {
			dressPath := sexPath + ageSect + "/" //modelimg/证件照/男/成年/
			aryDress := tools.GetDirs(dressPath)
			for _, dress := range aryDress {
				posePath := dressPath + dress + "/" //modelimg/证件照/男/成年/白衬衫
				aryPose := tools.GetDirs(posePath)  //西装-01  西装-02
				for _, pose := range aryPose {
					poseIndex := 0
					{
						aryTmp := strings.Split(pose, "-")
						poseIndex = tools.ParseInt(aryTmp[1])
					}
					if poseIndex == 0 {
						logger.Error("解析出错", posePath, pose)
						return errors.New("解析出错")
					}

					imgPath := posePath + pose + "/" + pose + ".png"
					var artStyle model.ArtStyle
					if err := artStyle.GetPassport(enums.ArtTypeEnum.Passport, sex, ageSect, dress, -1, "", ""); err != nil {
						if err != gorm.ErrRecordNotFound {
							logger.Error(err)
							return err
						}
					}

					if repairArtStyleId == 1 { //批量新增
						if artStyle.ID > 0 {
							logger.Info("新增修复 已存在不处理 artStyle.ID:", artStyle.ID)
							continue
						}
					} else if repairArtStyleId > 2 { //单条修复
						if artStyle.ID == repairArtStyleId {
							logger.Info("开始修复风格 artStyle.ID：", artStyle.ID)
						} else {
							continue
						}
					} else if repairArtStyleId == 0 { //参数错误
						logger.Error("参数错误 repairArtStyleId:", repairArtStyleId)
						return errors.New("参数错误")
					} else if repairArtStyleId == 2 { //批量修复
						//logger.Error("参数错误 repairArtStyleId:", repairArtStyleId)
						//return errors.New("参数错误")
					}
					if artStyle.ID == 0 {
						artStyle = model.ArtStyle{
							Title:     dress,
							ArtType:   enums.ArtTypeEnum.Passport,
							Sex:       sex,
							AgeSect:   ageSect,
							Dress:     dress,
							State:     1,
							StyleImgs: "[]",
						}
						if err := artStyle.Save(); err != nil {
							logger.Error(err)
							return err
						}
					}
					if artStyle.ID == 0 {
						logger.Error("出错了", artStyle)
						return errors.New("处理错误")
					}

					var uploadImg model.UploadImg
					if err := uploadImg.GetArtStyleImg(artStyle.ID, poseIndex); err != nil {
						if err != gorm.ErrRecordNotFound {
							logger.Error(err)
							return err
						}
					}
					if uploadImg.ID == 0 {
						uploadImg = model.UploadImg{
							OrigWhere:  enums.UploadImgOrigWhereEnum.ArtStyle,
							OrigId:     artStyle.ID,
							OrderIndex: float32(poseIndex),
						}
						if err := uploadImg.Save(); err != nil {
							logger.Error(err)
							return err
						}
					}
					if uploadImg.ID == 0 {
						logger.Error("出错了", artStyle.ID, "  ", poseIndex, "  ", imgPath)
						return errors.New("处理错误")
					}
					if err := o.UploadImg(uploadImg.ID, imgPath); err != nil {
						logger.Error(err)
						return err
					}

					bgPath := posePath + pose + "/" //camera/modelimg/证件照/男/成年/白衬衫/白衬衫-01
					aryBg := tools.GetDirs(bgPath)
					for _, bg := range aryBg {
						sizePath := bgPath + bg + "/"
						arySize := tools.GetDirs(sizePath)
						for _, size := range arySize {
							sizeNum := o.GetPhotoSizeNum(size) //西装2-0-4白底.png
							for faceIndex := 1; faceIndex <= 4; faceIndex++ {
								imgName := fmt.Sprintf("%s%d-%d-%d%s.png", dress, poseIndex, sizeNum, faceIndex, bg)
								faceImgPath := sizePath + size + "/" + imgName
								if exists, err := tools.PathFileExists(faceImgPath); err != nil || exists == false {
									logger.Error("模板照片不存在", faceImgPath)
									return errors.New("模板照片不存在")
								}

								if 1 == 2 && false { //此段代表准备做背景预览图，暂时先不做
									var artStyle model.ArtStyle
									if err := artStyle.GetPassport(enums.ArtTypeEnum.Passport, sex, ageSect, dress, poseIndex, bg, size); err != nil {
										if err != gorm.ErrRecordNotFound {
											logger.Error(err)
											return err
										}
									}
									if artStyle.ID == 0 {
										artStyle = model.ArtStyle{
											Title:      dress,
											ArtType:    enums.ArtTypeEnum.Passport,
											Sex:        sex,
											AgeSect:    ageSect,
											Dress:      dress,
											PhotoBg:    bg,
											PhotoSize:  size,
											OrderIndex: poseIndex,
											State:      1,
											StyleImgs:  "[]",
										}
										if err := artStyle.Save(); err != nil {
											logger.Error(err)
											return err
										}
									}

									if artStyle.ID == 0 {
										logger.Error("出错了", artStyle)
										return errors.New("处理错误")
									}

									var uploadImg model.UploadImg
									if err := uploadImg.GetArtStyleImg(artStyle.ID, faceIndex); err != nil {
										if err != gorm.ErrRecordNotFound {
											logger.Error(err)
											return err
										}
									}
									if uploadImg.ID == 0 {
										uploadImg = model.UploadImg{
											OrigWhere:  enums.UploadImgOrigWhereEnum.ArtStyle,
											OrigId:     artStyle.ID,
											OrderIndex: float32(faceIndex),
										}
										if err := uploadImg.Save(); err != nil {
											logger.Error(err)
										}
										if err := o.UploadImg(uploadImg.ID, faceImgPath); err != nil {
											logger.Error(err)
											return err
										}
									}
								}

							}
						}
					}

					if repairArtStyleId > 0 && artStyle.ID == uint(repairArtStyleId) {
						logger.Info("单个风格修复完成 artStyleId:", artStyle.ID)
						return nil
					}
				}
			}
		}
	}
	return nil
}

func (o *artStyle_) RepairPassportItem(artStyleId uint) error {
	var artStyle model.ArtStyle
	if err := artStyle.GetById(artStyleId); err != nil {
		logger.Error(err)
		return err
	}
	if artStyle.ArtType != enums.ArtTypeEnum.Passport {
		logger.Error("不是证件照类型,不处理", artStyle)
		return errors.New("不是证件照类型,不处理")
	}
	for orderIndex := 1; orderIndex < 20; orderIndex++ {

		for faceShapeIndex := 1; faceShapeIndex <= 1; faceShapeIndex++ {
			artStyleImgPath := o.GetPassportShowStyleImagePath(artStyle, orderIndex)
			fullPath := config.DiffusionFilePath + artStyleImgPath
			exists, err := tools.PathFileExists(fullPath)
			if err != nil {
				logger.Error(err)
				return err
			}
			if exists == false {
				if faceShapeIndex == 1 && orderIndex == 1 {
					logger.Error("图片模板不存在", fullPath, "  ", artStyle.ID, " ", artStyle.Title)
					return errors.New("图片模板不存在")
				}
				logger.Info("检索完成", artStyle.ID, " ", artStyle.Title)
				return nil
			}
			if faceShapeIndex == 1 { //第一张脸型图，去生成默认图片
				var uploadImg model.UploadImg
				if err := uploadImg.GetArtStyleImg(artStyleId, orderIndex); err != nil {
					if err != gorm.ErrRecordNotFound {
						logger.Error(err)
						return err
					}
				}
				if uploadImg.ID > 0 {
					logger.Info("预览风格图片已经存在，删除图片 artStyleId:", artStyleId)
					//filepath := config.DiffusionFilePath + uploadImg.Path
					//smallFilePath := service.ImgService.GetSmallImagePath(filepath)
					if err := ImgService.DelImage(uploadImg.Path); err != nil {
						logger.Error(err)
						return err
					}
					if err := ImgService.DelImage(ImgService.GetSmallImagePath(uploadImg.Path)); err != nil {
						logger.Error(err)
						return err
					}
				}
				if true { //预览图不存在，生成预览图
					logger.Info("开始处理预览风格图片 artStyleId:", artStyleId)
					//oMd5Str := fmt.Sprintf("%s,%s", artStyleImgPath, time.Now().Format("2006-01-02 15:04:05.000"))
					oMd5Str := artStyleImgPath
					has := md5.Sum([]byte(oMd5Str))
					md5Str := hex.EncodeToString(has[:])
					if len(md5Str) != 32 {
						logger.Error(err)
						return errors.New("len(md5Str) != 32")
					}

					ext := path.Ext(fullPath) // 输出 .html
					ext = ".jpg"
					filename := md5Str + strings.ToLower(ext)

					pre2 := md5Str
					pre2 = pre2[0:1]

					relativePath := fmt.Sprintf("camera/artstyle/")
					relativePathFile := relativePath + filename

					dirPath := config.DiffusionFilePath + relativePath
					filepath := config.DiffusionFilePath + relativePathFile

					if uploadImg.ID > 0 {
						uploadImg.Md5 = md5Str
						uploadImg.Path = relativePathFile
					} else {
						uploadImg = model.UploadImg{
							OrigWhere:  enums.UploadImgOrigWhereEnum.ArtStyle,
							OrigId:     artStyleId,
							Md5:        md5Str,
							Path:       relativePathFile,
							OrderIndex: float32(orderIndex),
							State:      0,
						}
					}

					if err := uploadImg.Save(); err != nil {
						logger.Error(err)
						return err
					}

					if _, err := os.Stat(dirPath); os.IsNotExist(err) {
						// mkdir 创建目录，mkdirAll 可创建多层级目录
						if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
							logger.Error(err, dirPath)
							return err
						}
					}

					if err := tools.CopyImgToImg(fullPath, filepath); err != nil {
						logger.Error(err, fullPath, "   ", filepath)
						return err
					}

					if err := uploadImg.SetState(enums.UploadImgStateEnum.Uploaded); err != nil {
						logger.Error(err)
					}
					img, size, _, err := myimg.FileToImgAndSize(filepath)
					if err != nil {
						logger.Error(err)
						return err
					}

					if err := uploadImg.SetWidthHeight(img.Bounds().Size().X, img.Bounds().Size().Y, size); err != nil {
						logger.Error(err)
					}
					small := myimg.ResizeImg(512, 512, img, true)
					smallFilePath := ImgService.GetSmallImagePath(filepath)
					if err := myimg.ImgToFile(small, smallFilePath); err != nil {
						logger.Error(err)
						return err
					}
					logger.Info("处理预览风格图片完成 artStyleId:", artStyleId)
				}
			}
		}
	}
	return nil
}

func (o *artStyle_) GetPhotoSizeNum(sizeName string) int {
	if sizeName == "1寸" {
		return 1
	} else if sizeName == "2寸" {
		return 2
	} else if sizeName == "签证" {
		return 0
	}
	logger.Error("未解析的照片尺寸", sizeName)
	return -1
}

func (o *artStyle_) UploadImg(uploadImgId uint, photoImgPath string) error {
	var uploadImg model.UploadImg
	if err := uploadImg.GetById(uploadImgId); err != nil {
		logger.Error(err)
		return err
	}
	if uploadImg.ID > 0 {
		//logger.Info("预览风格图片已经存在，删除图片 uploadImgId:", uploadImgId)
		//filepath := config.DiffusionFilePath + uploadImg.Path
		//smallFilePath := service.ImgService.GetSmallImagePath(filepath)
		if err := ImgService.DelImage(uploadImg.Path); err != nil {
			logger.Error(err)
			return err
		}
		if err := ImgService.DelImage(ImgService.GetSmallImagePath(uploadImg.Path)); err != nil {
			logger.Error(err)
			return err
		}
	}
	if true { //预览图不存在，生成预览图
		logger.Info("开始处理预览风格图片 uploadImgId:", uploadImgId)
		oMd5Str := fmt.Sprintf("%d,%s", uploadImgId, uploadImg.CreatedAt.Format("2006-01-02 15:04:05"))
		has := md5.Sum([]byte(oMd5Str))
		md5Str := hex.EncodeToString(has[:])
		if len(md5Str) != 32 {
			logger.Error("md5Str:", md5Str)
			return errors.New("len(md5Str) != 32")
		}

		ext := ".jpg"
		filename := md5Str + strings.ToLower(ext)

		pre2 := md5Str
		pre2 = pre2[0:1]

		relativePath := fmt.Sprintf("camera/artstyle/")
		relativePathFile := relativePath + filename

		dirPath := config.DiffusionFilePath + relativePath
		filepath := config.DiffusionFilePath + relativePathFile

		uploadImg.Md5 = md5Str
		uploadImg.Path = relativePathFile
		if err := uploadImg.Save(); err != nil {
			logger.Error(err)
			return err
		}

		if _, err := os.Stat(dirPath); os.IsNotExist(err) {
			// mkdir 创建目录，mkdirAll 可创建多层级目录
			if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
				logger.Error(err, dirPath)
				return err
			}
		}

		if err := tools.CopyImgToImg(photoImgPath, filepath); err != nil {
			logger.Error(err, photoImgPath, "   ", filepath)
			return err
		}

		if err := uploadImg.SetState(enums.UploadImgStateEnum.Uploaded); err != nil {
			logger.Error(err)
		}
		img, size, _, err := myimg.FileToImgAndSize(filepath)
		if err != nil {
			logger.Error(err)
			return err
		}

		if err := uploadImg.SetWidthHeight(img.Bounds().Size().X, img.Bounds().Size().Y, size); err != nil {
			logger.Error(err)
		}
		small := myimg.ResizeImg(512, 512, img, true)
		smallFilePath := ImgService.GetSmallImagePath(filepath)
		if err := myimg.ImgToFile(small, smallFilePath); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info("处理预览风格图片完成 artStyleId:", uploadImg.OrigId, "  uploadImgId:", uploadImgId)
	}
	return nil
}
