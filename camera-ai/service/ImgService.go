package service

import (
	"design-ai/model"
	"design-ai/utils/config"
	"design-ai/utils/logger"
	"fmt"
	"image"
	"os"
	"path"
	"strings"
	"time"
)

type imgService_ struct {
}

func (imgService_ *imgService_) GetSaveRelativePath(isDebug bool) string {
	//pathStr := "20221221/2e/2ec7f084289bbce350d667a8393a92c8.png"

	//relativePath := fmt.Sprintf("roodesign/%s/", time.Now().Format("20060102"))
	relativePath := fmt.Sprintf("roodesign/%s/", time.Now().Format("200601"))
	if isDebug {
		relativePath = fmt.Sprintf("roodesign/debug/%s/", time.Now().Format("200601"))
	}
	return relativePath
}

func (imgService_ *imgService_) GetSmallImagePath(pathStr string) string {
	//pathStr := "20221221/2e/2ec7f084289bbce350d667a8393a92c8.png"
	ext := path.Ext(pathStr)
	smallPathStr := strings.Replace(pathStr, ext, "_s"+ext, -1)
	return smallPathStr
}

func (imgData *imgService_) GetSmallJpgImagePath(pathStr string) string {
	//pathStr := "20221221/2e/2ec7f084289bbce350d667a8393a92c8.png"
	ext := path.Ext(pathStr)
	smallPathStr := strings.Replace(pathStr, ext, "_s.jpg", -1)
	return smallPathStr
}
func (imgData *imgService_) GetNormalJpgImagePath(pathStr string) string {
	//pathStr := "20221221/2e/2ec7f084289bbce350d667a8393a92c8.png"
	ext := path.Ext(pathStr)
	smallPathStr := strings.Replace(pathStr, ext, ".jpg", -1)
	return smallPathStr
}

func (imgService_ *imgService_) GetAbsoluteImagePath(relativePath string) string {
	//pathStr := "20221221/2e/2ec7f084289bbce350d667a8393a92c8.png"
	absolutePath := config.DiffusionFilePath + relativePath
	return absolutePath
}

func (imgService_ *imgService_) DelOutImg(item model.OutImg) error {

	if item.Path != "" {
		if err := ImgService.DelImage(item.Path); err != nil {
			logger.Error(err)
			return err
		}

		if err := ImgService.DelImage(ImgService.GetSmallJpgImagePath(item.Path)); err != nil {
			logger.Error(err)
			return err
		}

		if err := ImgService.DelImage(ImgService.GetNormalJpgImagePath(item.Path)); err != nil {
			logger.Error(err)
			return err
		}
	}
	if item.Path1 != "" {
		if err := ImgService.DelImage(item.Path1); err != nil {
			logger.Error(err)
			return err
		}
	}
	return nil
}

func (imgService_ *imgService_) DelImage(relativePath string) error {

	if len(relativePath) == 0 {
		return nil
	}
	absolutePath := config.DiffusionFilePath + relativePath
	if err := os.Remove(absolutePath); err != nil && !os.IsNotExist(err) {
		logger.Error(err, absolutePath)
		return err
	}
	return nil
}

func (imgService_ *imgService_) GetImageSize(path string) (int, int, error) {
	file, err := os.Open(path)
	if err != nil {
		logger.Error("Error opening file:", err)
		return 0, 0, err
	}
	defer file.Close()

	img, _, err := image.Decode(file)
	if err != nil {
		logger.Error("Error decoding image:", err)
		return 0, 0, err
	}

	bounds := img.Bounds()
	width := bounds.Max.X - bounds.Min.X
	height := bounds.Max.Y - bounds.Min.Y
	return width, height, nil
}

func (imgService_ *imgService_) CalculateSize(width, height, maxSize int) (int, int) {
	var newWidth, newHeight int

	if width > height {
		if width < maxSize {
			tmp := maxSize/width + 1
			width = width * tmp
			height = height * tmp
		}
		if width > maxSize {
			scale := float64(maxSize) / float64(width)
			newWidth = maxSize
			newHeight = int(float64(height) * scale)
		} else {
			newWidth = width
			newHeight = height
		}
	} else {
		if height < maxSize {
			tmp := maxSize/height + 1
			height = height * tmp
			width = width * tmp
		}

		if height > maxSize {
			scale := float64(maxSize) / float64(height)
			newHeight = maxSize
			newWidth = int(float64(width) * scale)
		} else {
			newWidth = width
			newHeight = height
		}
	}
	return newWidth, newHeight
}

var ImgService imgService_
