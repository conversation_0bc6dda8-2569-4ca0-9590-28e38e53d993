package service

import (
	"design-ai/utils/logger"
)

type pushMessage_ struct{}

var PushMessage pushMessage_

func (o pushMessage_) SendPhotoCompletedMessage(openId string) {
	templateId := "5Fk_pFQfpKy9dAryYdMz1oIPC_IG1f32mZKxq3e0WT4"
	data := make(map[string]map[string]string)
	//data["thing3"]["DATA"] = "您的艺术照制作完成"          //温馨提示
	//data["thing1"]["DATA"] = "抑郁风起"               //作品名称
	//data["thing5"]["DATA"] = "4张照片任你挑选"           //制作信息
	//data["date2"]["DATA"] = "2023-08-09 18:56:31" //完成时间

	data["thing3"] = map[string]string{"value": "您的艺术照制作完成"}
	data["thing1"] = map[string]string{"value": "抑郁风起"}
	data["thing5"] = map[string]string{"value": "4张照片任你挑选"}
	data["date2"] = map[string]string{"value": "2023-08-09 18:56:31"}

	s, err := WeixinService.SendSubscribeMessage(openId, templateId, data)
	if err != nil {
		logger.Error(openId, err)
	}
	logger.Info(openId, " ", s)

	//o.SendSubscribeMessage(openId, templateId, )
}
