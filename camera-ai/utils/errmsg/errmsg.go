package errmsg

import (
	"design-ai/utils/logger"
	"github.com/gin-gonic/gin"
	"io/ioutil"
	"net/http"
)

const (
	SUCCESS       = 0
	FAIL          = 1
	TokenInvalid  = 2
	CoinNotEnough = 3
)

func Success(c *gin.Context, msg string, result map[string]interface{}) {

	c.JSON(http.StatusOK, gin.H{
		"code":   SUCCESS,
		"result": result,
		"msg":    msg,
	})

	c.Abort()
}

func Abort(c *gin.Context, code int, msg string) {
	if code == SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"result": nil,
			"msg":    msg,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"result": nil,
			"msg":    msg,
		})
	}

	c.Abort()
}

func ShouldBindJSON(c *gin.Context, obj any) error {
	if err := c.ShouldBindJSON(obj); err != nil {
		body, _ := ioutil.ReadAll(c.Request.Body)
		logger.Error(err, c.Request.URL.Path, "====", string(body), "===")
		Abort(c, FAIL, "数据获取失败")
		return err
	}
	return nil
}
