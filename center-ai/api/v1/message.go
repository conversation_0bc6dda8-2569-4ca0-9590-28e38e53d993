package v1

import (
	"center-ai/model"
	"center-ai/service"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

type messageApi_ struct {
}

var MessageApi messageApi_

type messageSendReq struct {
	MessageId     uint                    `json:"message_id"`
	ProjectId     uint                    `json:"project_id"`
	ProjectUserId uint                    `json:"project_user_id"`
	AccountId     uint                    `json:"account_id"`
	MessageCode   string                  `json:"message_code"`
	SendTarget    string                  `json:"send_target"`
	SendParm      map[string]any          `json:"send_parm"`
	RegularUnix   int64                   `json:"regular_unix"`
	Method        model.MessageMethodEnum `json:"method"`
	Send          string                  `json:"send"`
}

type messageResp struct {
	ProjectId     uint      `json:"project_id"`
	ProjectUserId uint      `json:"project_user_id"`
	Method        int       `json:"method"`
	Title         string    `json:"title"`
	Content       string    `json:"content"`
	SendTime      time.Time `json:"send_time"`
}

func (obj messageApi_) Add(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq messageSendReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.MessageCode == "" {
		msg = "消息码不正确"
		return
	}

	if oReq.ProjectId <= 0 {
		msg = "请设置项目ID"
		return
	}

	var project model.Project
	if err := project.GetById(oReq.ProjectId); err != nil {
		msg = "获取项目信息失败"
		logger.Error(msg, err)
		return
	}
	if project.MessageToken == "" {
		msg = "该项目未开启消息通知"
		return
	}

	tokenHeader := c.Request.Header.Get("Authorization")
	if project.MessageToken != tokenHeader {
		msg = "消息Token不正确"
		return
	}

	if len(oReq.SendParm) == 0 {
		msg = "请设置参数"
		logger.Error(msg, oReq.SendParm)
		return
	}

	sendTarget := oReq.SendTarget
	if oReq.AccountId > 0 {
		var account model.Account
		if err := account.GetById(oReq.AccountId); err != nil {
			msg = "查询账户失败"
			logger.Error(msg, err)
			return
		}
		if oReq.Method == model.MessageMethodSms {
			sendTarget = account.Mobile
		} else if oReq.Method == model.MessageMethodWechat || oReq.Method == model.MessageMethodWechatSub {
			sendTarget = account.OfficialOpenId
		}
	}

	if sendTarget == "" {
		msg = "发送对象为空"
		logger.Error(msg)
		return
	}

	templateCode := service.MessageService.GetTemplateCode(oReq.Method, oReq.MessageCode)
	if templateCode == "" {
		msg = "未找到模板编码" + oReq.MessageCode
		logger.Error(msg, oReq)
		return
	}

	message := model.Message{
		ProjectId:     oReq.ProjectId,
		ProjectUserId: oReq.ProjectUserId,
		AccountId:     oReq.AccountId,
		Method:        oReq.Method,
		SendTarget:    sendTarget,
		SendParm:      tools.GetJsonFromStruct(oReq.SendParm),
		MessageCode:   oReq.MessageCode,
		TemplateCode:  templateCode,
	}

	if oReq.RegularUnix > 0 {
		message.RegularTime = time.Unix(oReq.RegularUnix, 0)
	}

	if err := message.Save(); err != nil {
		msg = "消息保存失败"
		logger.Error(msg, err, " oReq:", tools.GetJsonFromStruct(oReq))
		return
	}

	msg = "保存成功"
	result["id"] = message.ID

	//if oReq.Send == "s" {
	//	if err := service.MessageService.Send(message.ID); err != nil {
	//		msg = "发送失败"
	//		logger.Error(msg, err)
	//		return
	//	} else {
	//		msg = "已发送"
	//	}
	//}

	if oReq.RegularUnix == 0 {
		if _, err := service.MessageService.PushJson(message.ID); err != nil {
			msg = "添加到发送队列失败"
			logger.Error(msg, err)
			return
		} else {
			msg = "已加入到发送队列"
		}
	}
	code = 0
}

func (obj messageApi_) Send(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq messageSendReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var message model.Message
	if err := message.GetById(oReq.MessageId); err != nil {
		msg = "查询数据失败"
		logger.Error(err)
		return
	}

	if err := service.MessageService.Send(message.ID); err != nil {
		msg = "发送请求失败"
		logger.Error(msg, err)
		return
	} else {
		msg = "发送请求成功"
		code = 0
	}
}
