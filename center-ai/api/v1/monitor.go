package v1

import (
	"center-ai/utils/logger"
	"github.com/gin-gonic/gin"
	"net/http"
	"runtime"
)

type _monitorApi struct {
}

type monitorReq struct {
	Bundle    string `json:"bundle"`
	RoutePath string `json:"route_path"`
	Operate   string `json:"operate"`
}

func (obj _monitorApi) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	//claims := c.Value("center_claims").(*middleware.CenterClaims)
	//
	//var oReq monitorReq
	//if err := c.ShouldBindJ<PERSON>(&oReq); err != nil {
	//	logger.Error(err)
	//	errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
	//	return
	//}
	//
	//msg = "访问成功"
	//result["time"] = jsontime.Now()
	//code = 0
}
