package wechat_official

import (
	"center-ai/enums"
	"center-ai/model"
	"center-ai/service/llmchat"
	wechatofficial "center-ai/service/wechat/official"
	"center-ai/utils/logger"
	"center-ai/utils/myredis"
	"center-ai/utils/tools"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/silenceper/wechat/v2/officialaccount/basic"
	"github.com/silenceper/wechat/v2/officialaccount/material"
	"github.com/silenceper/wechat/v2/officialaccount/message"
	"gorm.io/gorm"
	"net/http"
	"runtime"
	"strings"
	"sync"
	"time"
)

type ScanLogin struct {
	AccountId      uint           `json:"account_id"`
	Ticket         string         `json:"ticket"`
	Mobile         string         `json:"mobile"`
	UnionId        string         `json:"union_id"`
	OfficialOpenId string         `json:"official_open_id"`
	CreatedAt      model.JsonTime `json:"created_at"`
	ErrMsg         string         `json:"err_msg"`
}

type bindMobileReq struct {
	Ticket string `json:"ticket"`
	Mobile string `json:"mobile"`
}

var openidMutex sync.Map

// https://center.cyuai.com/api_online/v1/notify/wechat/official
func Notify(c *gin.Context) {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	logger.Info("接收到微信Official回调")

	server := wechatofficial.WechatOfficialAccount.OfficialAccount.GetServer(c.Request, c.Writer)
	// 设置接收消息的处理方法
	server.SetMessageHandler(func(msg *message.MixMessage) *message.Reply {
		logger.Info("接收到msg:", tools.GetJsonFromStruct(&msg))

		msgType := string(msg.MsgType) // text 用户聊天发的消息  event事件
		event := string(msg.Event)     //subscribe 订阅的消息
		eventKey := msg.EventKey

		openId := string(msg.FromUserName)
		ticket := msg.Ticket

		//EVENT:SCAN msgType:event
		//EVENT:subscribe msgType:event
		//EVENT:unsubscribe msgType:event
		//EVENT:VIEW msgType:event

		if openId != "" {
			// 为每个openId创建或获取一个互斥锁
			if _, ok := openidMutex.Load(openId); !ok {
				if userInfo, err := wechatofficial.WechatOfficialAccount.OfficialAccount.GetUser().GetUserInfo(openId); err != nil {
					logger.Error("获取用户信息失败 err:", err)
				} else {
					logger.Info("用户信息：", tools.GetJsonFromStruct(&userInfo))
					if userInfo.UnionID != "" {
						var account model.Account
						if err := account.GetByUnionId(userInfo.UnionID); err != nil {
							if err == gorm.ErrRecordNotFound {
								account = model.Account{
									UnionId:        userInfo.UnionID,
									OfficialOpenId: userInfo.OpenID,
								}
								if err := account.Save(); err != nil {
									logger.Error("生成账号失败 err:", err)
								}
							}
						}
						if account.ID > 0 {
							openidMutex.Store(openId, account)
						}
					}
				}
			}
		}

		content := ""
		if needLogin(event, eventKey) {
			content = "您正在使用扫码登录"
			logger.Info("进入登录逻辑 ticker:", ticket)
			if ticket != "" {
				var account model.Account
				if err := account.GetByOfficialOpenId(openId); err != nil {
					if err == gorm.ErrRecordNotFound {
						logger.Error("未找到openId:", openId)
					}
				} else {
					scanLogin := ScanLogin{
						Ticket:         ticket,
						AccountId:      account.ID,
						Mobile:         account.Mobile,
						UnionId:        account.UnionId,
						OfficialOpenId: account.OfficialOpenId,
						CreatedAt:      model.JsonTime(time.Now()),
					}
					key := enums.RedisKeyEnum.ScanLogin + ticket

					if exists, _ := myredis.Exists(key); exists {
						scanLogin.ErrMsg = "此二维码已被使用过"
					} else {
						val := tools.GetJsonFromStruct(scanLogin)
						if err := myredis.Set(key, val, time.Minute*10); err != nil {
							logger.Error(val, " err:", err)
						} else {
							logger.Info("redis设置完成", val)
						}
					}
				}
			}
			//accessToken := "82_Rh5Tgz4kVXPrLoYKVg9tYkM8N7WRzvpseGzNiQl3LjxSmg5s2APE6uvC4bespV-Ba51lriPPM_a1ziL4AS48b3F18FYRVCwQjhONJ4cr5Q1gmvVuzRql0_l89xkUXVhADAPSQ"
			//if b, err := wechatofficial.WechatOfficialAccount.OfficialAccount.GetOauth().CheckAccessToken(accessToken, openId); err != nil {
			//	logger.Error("验证AccessToken失败 err:", err, "  accessToken:", accessToken, "  openId:", openId)
			//} else {
			//	logger.Info("验证AccessToken完成 b:", b, "  accessToken:", accessToken, "  openId:", openId)
			//}

			//if currentSelfMenuInfo, err := wechatofficial.WechatOfficialAccount.OfficialAccount.GetMenu().GetCurrentSelfMenuInfo(); err != nil {
			//	logger.Info("currentSelfMenuInfo: err:", err)
			//} else {
			//	logger.Info("currentSelfMenuInfo:", tools.GetJsonFromStruct(currentSelfMenuInfo))
			//}
			//
			//if menuInfo, err := wechatofficial.WechatOfficialAccount.OfficialAccount.GetMenu().GetMenu(); err != nil {
			//	logger.Info("menuInfo: err:", err)
			//} else {
			//	logger.Info("menuInfo:", tools.GetJsonFromStruct(menuInfo))
			//}

			//batchGetUserList := make([]user.BatchGetUserListItem, 0)
			//batchGetUserList = append(batchGetUserList, user.BatchGetUserListItem{OpenID: openId})
			//
			//batchGetUserParam := user.BatchGetUserInfoParams{UserList: batchGetUserList}
			//
			//if infoList, err := wechatofficial.WechatOfficialAccount.OfficialAccount.GetUser().BatchGetUserInfo(batchGetUserParam); err != nil {
			//	logger.Error("获取用户信息列表失败 err:", err)
			//
			//} else {
			//	logger.Info("用户信息列表：", tools.GetJsonFromStruct(&infoList))
			//}
		} else if msgType == "text" {

			if msg.Content == "获取素材列表123" {
				offset := int64(0)
				for i := 0; i < 8; i++ {
					offset += int64(i * 20)
					if list, err := wechatofficial.WechatOfficialAccount.OfficialAccount.GetMaterial().BatchGetMaterial(material.PermanentMaterialTypeImage, offset, 20); err != nil {
						logger.Error("获取素材列表失败 err:", err)
					} else {
						if len(list.Item) == 0 {
							logger.Info("获取图片素材列表:结束", tools.GetJsonFromStruct(list))
							break
						}
						logger.Info("获取图片素材列表:", tools.GetJsonFromStruct(list))
					}
				}

				if list, err := wechatofficial.WechatOfficialAccount.OfficialAccount.GetMaterial().BatchGetMaterial(material.PermanentMaterialTypeVideo, 0, 100); err != nil {
					logger.Error("获取视频素材列表失败 err:", err)
				} else {
					logger.Info("获取视频素材列表:", tools.GetJsonFromStruct(list))
				}
			}

			if resTxt, err := llmchat.Send(openId, msg.Content); err != nil {
				logger.Error(err)
				return &message.Reply{MsgType: message.MsgTypeText, MsgData: "请稍后尝试"}
			} else {
				//resTxt = `Hi~终于等到你了呢！这里是晨羽AI唯一官方微信号，感谢您的关注！欢迎登录我们的官网：https://www.cyuai.com/`
				//logger.Info("resTxt:", resTxt)
				content = resTxt
				//return &message.Reply{MsgType: message.MsgTypeText, MsgData: resTxt}
			}
		} else {
			logger.Info("非用户登录信息 EVENT:", event, " msgType:", msgType, " eventKey:", eventKey, " content:", content)
		}

		// 回复消息：演示回复用户发送的消息

		if content == "" {
			content = msg.Content
		}
		if event == "subscribe" {
			//content = `Hi~终于等到你了呢！这里是晨羽AI唯一官方微信号，感谢您的关注！欢迎登录我们的官网：https://www.cyuai.com/`
			content = `Hi~终于等到你了呢！这里是晨羽AI唯一官方微信号，感谢您的关注！简单问题可以直接输入文字咨询哦`
			//logger.Info("subscribe OpenID:", openId)
			if openId == "oi1S96VMITji6Fv7fcuHe0Db6wIc" || len(openId) > 0 {
				//logger.Info("进入指定OpenID")

				redisKey := fmt.Sprintf(enums.RedisKeyEnum.Kv + "SubscribeMedialID")
				medialId := myredis.Get(redisKey)
				//if medialId == "" {
				//	medialId = "x1AH90Y5E1PAGDwS_ajoBnF1yfmea46926A5ghdElBdj26pyEPbwqRn1p0w6ONQA"
				//}
				if medialId != "" {
					text := message.NewImage(medialId)
					return &message.Reply{MsgType: message.MsgTypeImage, MsgData: text}
				}
				//text := message.NewImage("x1AH90Y5E1PAGDwS_ajoBnF1yfmea46926A5ghdElBdj26pyEPbwqRn1p0w6ONQA")
				//return &message.Reply{MsgType: message.MsgTypeImage, MsgData: text}
			}
		}
		content = strings.ReplaceAll(content, "\n", "")
		logger.Info("EVENT:", event, " msgType:", msgType, " eventKey:", eventKey, " content:", content)
		text := message.NewText(content)
		return &message.Reply{MsgType: message.MsgTypeText, MsgData: text}
	})

	// 处理消息接收以及回复
	if err := server.Serve(); err != nil {
		logger.Error(err)
		return
	}
	logger.Info("开始发送消息")
	// 发送回复的消息
	if err := server.Send(); err != nil {
		logger.Error("消息发送失败 err:", err)
	} else {
		logger.Info("消息发送成功")
	}

}

func getUserInfo(openId string) error {
	if accessToken, err := wechatofficial.WechatOfficialAccount.OfficialAccount.GetAccessToken(); err != nil {
		logger.Error("获取AccessToken失败 err:", err)
		return err
	} else {
		logger.Info("accessToken:", accessToken)
		if userInfo, err := wechatofficial.WechatOfficialAccount.OfficialAccount.GetUser().GetUserInfo(openId); err != nil {
			logger.Error("获取用户信息失败 err:", err)
			return err
		} else {
			logger.Info("用户信息：", tools.GetJsonFromStruct(&userInfo))
			return nil
		}
	}
}

func GetQRCode(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	tq := basic.NewTmpQrRequest(time.Minute*5, "login")

	if ticket, err := wechatofficial.WechatOfficialAccount.OfficialAccount.GetBasic().GetQRTicket(tq); err != nil {
		logger.Error(err)
		msg = err.Error()
		return
	} else {
		if ticket.ErrCode != 0 {
			logger.Error(ticket)
			msg = ticket.ErrMsg
			return
		}
		qrUrl := basic.ShowQRCode(ticket)
		if !strings.HasPrefix(qrUrl, "http") {
			msg = "生成二维码地址失败"
			logger.Error(msg)
			return
		}

		result["qr_ticket"] = ticket.Ticket
		result["qr_code_url"] = qrUrl
		result["qr_expire_seconds"] = ticket.ExpireSeconds
		code = 0
	}
}

func BindMobile(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq bindMobileReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		return
	}

	if oReq.Ticket == "" {
		msg = "Ticket参数错误"
		return
	}

	key := enums.RedisKeyEnum.ScanLogin + oReq.Ticket
	val := myredis.Get(key)
	if val == "" {
		msg = "Ticket缓存已失效"
		return
	}

	var scanLogin ScanLogin
	if err := tools.GetStructFromJson(&scanLogin, val); err != nil {
		msg = "解析失败"
		logger.Error(msg, " ", val, " err:", err)
		return
	}
	if scanLogin.AccountId == 0 {
		msg = "缓存数据错误"
		logger.Error(msg, " ", val)
		return
	}
	var accountMobile model.Account
	if err := accountMobile.GetByMobile(oReq.Mobile); err != nil {
		if err == gorm.ErrRecordNotFound {

		} else {
			msg = "查询手机号码失败"
			logger.Error(msg, " err:", err)
			return
		}

	} else {
		msg = "该手机号码已经被其他账号绑定"
		logger.Error(msg, "  ", tools.GetJsonFromStruct(accountMobile))
		return
	}

	var account model.Account
	if err := account.GetById(scanLogin.AccountId); err != nil {
		msg = "查询账号失败"
		logger.Error(msg, " val:", val, " err:", err)
		return
	}
	if account.Mobile != "" {
		msg = "该账号已绑定了其他手机号码"
		logger.Error(msg, " val:", val, " accountMobile:", account.Mobile)
		return
	}

	if err := account.SetMobile(oReq.Mobile); err != nil {
		msg = "绑定手机号码失败"
		logger.Error(msg, " val:", val, " err:", err)
		return
	} else {
		result["account_id"] = account.ID
		msg = "手机号码绑定成功"
		code = 0
	}

}

/*
// https://center.cyuai.com/api_online/v1/notify/wechat/official
func Notify(c *gin.Context) {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	logger.Info("接收到微信Official回调")
	//logger.Info("初始MemoryCache:", tools.GetJsonFromStruct(wechat_config.MemoryCache))
	var wc = wechat.NewWechat()
	wc.SetCache(wechat_config.MemoryCache)
	officialAccount := wc.GetOfficialAccount(wechat_config.OfficialConfig)
	server := officialAccount.GetServer(c.Request, c.Writer)
	// 设置接收消息的处理方法
	server.SetMessageHandler(func(msg *message.MixMessage) *message.Reply {
		logger.Info("接收到msg:", tools.GetJsonFromStruct(&msg))

		eventType := string(msg.Event)
		eventKey := msg.EventKey

		openId := string(msg.FromUserName)
		//ticket := msg.Ticket

		if needLogin(eventType, eventKey) {

			logger.Info("MemoryCache:", tools.GetJsonFromStruct(wechat_config.MemoryCache))
			if accessToken, err := officialAccount.GetAccessToken(); err != nil {
				logger.Error("获取AccessToken失败 err:", err)
			} else {
				logger.Info("accessToken:", accessToken)
				if userInfo, err := officialAccount.GetOauth().GetUserInfo(accessToken, openId, ""); err != nil {
					logger.Error("获取用户信息失败 err:", err)
				} else {
					logger.Info("用户信息：", tools.GetJsonFromStruct(&userInfo))
				}
			}

		} else {
			logger.Info("用户信息不登录：")
		}

		// 回复消息：演示回复用户发送的消息
		text := message.NewText(msg.Content)
		return &message.Reply{MsgType: message.MsgTypeText, MsgData: text}
	})

	// 处理消息接收以及回复
	if err := server.Serve(); err != nil {
		logger.Error(err)
		return
	}
	logger.Info("开始发送消息")
	// 发送回复的消息
	server.Send()
	logger.Info("消息发送完成")
}

func GetQRCode(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	tq := basic.NewTmpQrRequest(time.Minute*5, "login")

	var wc = wechat.NewWechat()
	officialAccount := wc.GetOfficialAccount(wechat_config.OfficialConfig)
	if ticket, err := officialAccount.GetBasic().GetQRTicket(tq); err != nil {
		logger.Error(err)
		msg = err.Error()
		return
	} else {
		if ticket.ErrCode != 0 {
			logger.Error(ticket)
			msg = ticket.ErrMsg
			return
		}
		qrUrl := basic.ShowQRCode(ticket)
		if !strings.HasPrefix(qrUrl, "http") {
			msg = "生成二维码地址失败"
			logger.Error(msg)
			return
		}

		result["qr_ticket"] = ticket.Ticket
		result["qr_code_url"] = qrUrl
		result["qr_expire_seconds"] = ticket.ExpireSeconds
		code = 0
	}
}*/

func needLogin(event string, eventKey string) bool {
	if event == "SCAN" && eventKey == "login" {
		return true
	}
	if event == "subscribe" && eventKey == "qrscene_login" {
		return true
	}
	return false
}

/*
	//已经关注了扫码进入的公众号的信息
	"Event": "SCAN",
	"EventKey": "login",

	//没有关注扫码关注公众号的信息
	"Event": "subscribe",
	"EventKey": "qrscene_login",

*/

/*
{
	"XMLName": {
		"Space": "",
		"Local": ""
	},
	"ToUserName": "gh_82e213aa0547",    //公众号原始ID 在设置于开发-》公众号设置 页面
	"FromUserName": "oi1S96VMITji6Fv7fcuHe0Db6wIc",  //接收模板消息的用户的openid
	"CreateTime": 1721791168,
	"MsgType": "event",
	"MsgID": 0,
	"TemplateMsgID": 0,
	"Content": "",
	"Recognition": "",
	"PicURL": "",
	"MediaID": "",
	"Format": "",
	"ThumbMediaID": "",
	"LocationX": 0,
	"LocationY": 0,
	"Scale": 0,
	"Label": "",
	"Title": "",
	"Description": "",
	"URL": "",
	"BizMsgMenuID": 0,
	"Event": "SCAN",
	"EventKey": "login",
	"Ticket": "gQH67zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAySC1Ga290YmtlWkYxRFhPdzFDY3cAAgTPcaBmAwQsAQAA",
	"Latitude": "",
	"Longitude": "",
	"Precision": "",
	"MenuID": "",
	"Status": "",
	"SessionFrom": "",
	"TotalCount": 0,
	"FilterCount": 0,
	"SentCount": 0,
	"ErrorCount": 0,
	"ScanCodeInfo": {
		"ScanType": "",
		"ScanResult": ""
	},
	"SendPicsInfo": {
		"Count": 0,
		"PicList": null
	},
	"SendLocationInfo": {
		"LocationX": 0,
		"LocationY": 0,
		"Scale": 0,
		"Label": "",
		"Poiname": ""
	},
	"SubscribeMsgPopupEvent": null,
	"PublishEventInfo": {
		"PublishID": 0,
		"PublishStatus": 0,
		"ArticleID": "",
		"ArticleDetail": {
			"Count": 0,
			"Item": null
		},
		"FailIndex": null
	},
	"InfoType": "",
	"AppID": "",
	"ComponentVerifyTicket": "",
	"AuthorizerAppid": "",
	"AuthorizationCode": "",
	"AuthorizationCodeExpiredTime": 0,
	"PreAuthCode": "",
	"AuthCode": "",
	"Info": {
		"Name": "",
		"Code": "",
		"CodeType": 0,
		"LegalPersonaWechat": "",
		"LegalPersonaName": "",
		"ComponentPhone": ""
	},
	"ResultInfo": {
		"APIName": "",
		"ApplyTime": "",
		"AuditID": "",
		"AuditTime": "",
		"Reason": "",
		"Status": ""
	},
	"CardID": "",
	"RefuseReason": "",
	"IsGiveByFriend": 0,
	"FriendUserName": "",
	"UserCardCode": "",
	"OldUserCardCode": "",
	"OuterStr": "",
	"IsRestoreMemberCard": 0,
	"UnionID": "",
	"IsRisky": false,
	"ExtraInfoJSON": "",
	"TraceID": "",
	"StatusCode": 0,
	"Ret": 0,
	"NickName": "",
	"DeviceType": "",
	"DeviceID": "",
	"SessionID": "",
	"OpenID": "",
	"SuccTime": 0,
	"FailTime": 0,
	"DelayTime": 0,
	"Reason": "",
	"ScreenShot": ""
}

*/

/*

{
    "button": [
        {
            "name": "AI产品",
            "type": "view",
            "url": "https://www.cyuai.com"
        },
        {
            "name": "入门教程",
            "sub_button": [
                {
                    "name": "AI效果图",
                    "type": "media_id",
					"media_id":"x1AH90Y5E1PAGDwS_ajoBqzos6LrVWJfHZmAfVA5i7M4Kv6YH16nPpLHfW2ifebN"
                }
            ]
        },
        {
            "name": "关于我们",
            "sub_button": [
                {
                    "name": "官网入口",
                    "type": "view",
                    "url": "https://suanyun.cn"
                },
                {
                    "name": "下载APP",
                    "type": "media_id",
					"media_id":"x1AH90Y5E1PAGDwS_ajoBj2-rEcqpPIDbSLhZl1Ate1DkHXas-BLtxLGhiZ91NgL"
                },
                {
                    "name": "商务合作",
                    "type": "media_id",
                    "media_id": "x1AH90Y5E1PAGDwS_ajoBnTh0ZZVp1lyo6NdVzXAbq4Dv95yA2fW2hRjpWZ6HaIh"
                }
            ]
        }
    ]
}


*/
