package v1

import (
	"center-ai/enums"
	"center-ai/middleware"
	"center-ai/model"
	"center-ai/service"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"
)

type _projectApi struct {
}

type projectAddReq struct {
	ProjectId uint   `json:"project_id"`
	Title     string `json:"title"`
	Bundle    string `json:"bundle"`
	Remark    string `json:"remark"`
}

type projectListReq struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

type projectItem struct {
	ID       uint           `json:"id"`
	Title    string         `json:"title"`
	Bundle   string         `json:"bundle"`
	Remark   string         `json:"remark"`
	CreateAt model.JsonTime `json:"create_at"`
}

type FileInfo struct {
	Path    string    `json:"path"`
	Name    string    `json:"name"`
	ModTime time.Time `json:"mod_time"`
}

func (obj _projectApi) Add(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	var oReq projectAddReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var project model.Project
	if oReq.ProjectId > 0 {
		if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
			errmsg.Abort(c, errmsg.FAIL, "权限不足")
			return
		}
		if err := project.GetById(oReq.ProjectId); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
			return
		}
		project.Title = oReq.Title
		project.Remark = oReq.Remark
		project.Bundle = oReq.Bundle
		if err := project.Save(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存信息失败")
			return
		}
	} else {
		if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
			errmsg.Abort(c, errmsg.FAIL, "权限不足")
			return
		}
		project = model.Project{
			Title:  oReq.Title,
			Bundle: oReq.Bundle,
			Remark: oReq.Remark,
		}
		if err := project.Save(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存信息失败")
			return
		}
	}

	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  "保存成功",
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _projectApi) List(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	var oReq projectListReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	project := model.Project{}
	ary := make([]projectItem, 0)
	if err := project.GetList(&ary); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询失败")
		return
	}

	result := make(map[string]interface{})
	result["items"] = ary
	result["total"] = len(ary)

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func getFilePathByProjectName(projectName string) string {
	filePath := ""
	if projectName == "aigc_api_4005" {
		filePath = "/www/wwwroot/dev.cyuai.com/api/design_ai.linux"
	} else if projectName == "sheys_api_5021" {
		filePath = "/www/wwwroot/sheys.cn/api/sheys_ai.linux"
	} else if projectName == "design_api_5001" {
		filePath = "/www/wwwroot/design.cyuai.com/api/design_ai.linux"
	} else if projectName == "design_api_online" {
		filePath = "/www/wwwroot/design.cyuai.com/api_online/design_ai.linux"
	} else if projectName == "aigc_api_4003" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api/zcloud_ai.linux"
	} else if projectName == "aigc_api_online" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api_online/zcloud_ai.linux"
	} else if projectName == "center_api_test" {
		filePath = "/www/wwwroot/center.cyuai.com/api_test/center_ai.linux"
	} else if projectName == "center_api_online" {
		filePath = "/www/wwwroot/center.cyuai.com/api_online/center_ai.linux"
	} else if projectName == "center_api" {
		filePath = "/www/wwwroot/center.cyuai.com/api/center_ai.linux"
	} else if projectName == "camera_api" {
		filePath = "/www/wwwroot/camera.cyuai.com/api/camera_ai.linux"
	} else if projectName == "camera_api_online" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_online/camera_ai.linux"
	} else if projectName == "camera_api_test" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_test/camera_ai.linux"
	} else if projectName == "douyin_api_test" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api_test/douyin_ai.linux"
	} else if projectName == "douyin_api" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api/douyin_ai.linux"
	} else if projectName == "sd_api_online" {
		filePath = "/www/wwwroot/sd.cyuai.com/api_online/sd_ai.linux"
	} else if projectName == "sd_api" {
		filePath = "/www/wwwroot/sd.cyuai.com/api/sd_ai.linux"
	} else if projectName == "cpn_sched" {
		filePath = "/www/wwwroot/www.suanyun.cn/api/cpn_sched.linux"
	} else if projectName == "cpn_sched_online" {
		filePath = "/www/wwwroot/www.suanyun.cn/api_online/cpn_sched.linux"
	} else if projectName == "cpn_node_hz01" {
		filePath = "/www/wwwroot/hz01.suanyun.cn/api/cpn_node.linux"
	} else if projectName == "cpn_node_hz02" {
		filePath = "/www/wwwroot/hz02.suanyun.cn/api/cpn_node.linux"
	} else if projectName == "cpn_node_hz03" {
		filePath = "/www/wwwroot/hz03.suanyun.cn/api/cpn_node.linux"
	} else if projectName == "cpn_node_hz05" {
		filePath = "/www/wwwroot/hz05.suanyun.cn/api/cpn_node.linux"
	} else if projectName == "cpn_node_hz06" {
		filePath = "/www/wwwroot/hz06.suanyun.cn/api/cpn_node.linux"
	}
	return filePath
}

func checkFilepath(filePath string) bool {
	if filePath == "" {
		return false
	}
	if !strings.HasPrefix(filePath, "/www/wwwroot/") {
		return false
	}
	if !strings.HasSuffix(filePath, ".linux") {
		return false
	}
	if len(filePath) <= 20 {
		return false
	}
	return true
}

func (obj _projectApi) Publish(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	logger.Info("开始发布项目")

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		msg = "权限不足"
		logger.Error("权限不足")
		return
	}

	justRestart := 0

	if tmp, ok := c.GetPostForm("just_restart"); ok {
		if num, err := strconv.Atoi(tmp); err == nil {
			justRestart = num
		}
	}

	f, err := c.FormFile("file")
	if err != nil {
		if justRestart == 0 || justRestart == 1 {
			msg = "程序上传失败"
			return
		}
	}

	projectName, _ := c.GetPostForm("project_name")

	filePath := getFilePathByProjectName(projectName)
	result["file_path"] = filePath
	if checkFilepath(filePath) == false {
		msg = "程序路径不正确"
		return
	}

	if justRestart == 0 || justRestart == 1 {
		logger.Info("filePath：", filePath)
		if filePath == "" {
			logger.Error("文件路径为空", filePath)
			msg = "文件路径为空"
			return
		}
		if exists, err := tools.PathFileExists(filePath); err != nil {
			msg = "文件不存在"
			logger.Error(msg, err, filePath)
			return
		} else if exists == false {
			msg = "文件不存在"
			logger.Error("文件不存在", filePath)
			return
		}

		//备份运行中的程序文件
		backFilepath := filePath + "_back" + time.Now().Format("20060102150405")
		cmd1 := exec.Command("cp", filePath, backFilepath)
		if err := cmd1.Run(); err != nil {
			msg = "备份执行文件命令失败"
			logger.Error(msg, err)
			return
		}
		logger.Info("备份文件成功", backFilepath)

		//删除运行的程序文件
		cmd := exec.Command("rm", "-f", filePath)
		if err := cmd.Run(); err != nil {
			msg = "执行删除命令失败"
			logger.Error(err)
			return
		}
		logger.Info("删除文件成功", filePath)

		//保存上传的程序文件
		if err := c.SaveUploadedFile(f, filePath); err != nil {
			msg = "程序文件保存失败"
			logger.Error(msg, err)
			return
		}
		logger.Info("文件上传成功并覆盖原文件", filePath)
		//设置程序文件权限
		if err := os.Chmod(filePath, 0777); err != nil {
			msg = "设置文件权限失败"
			logger.Error(msg, err)
			return
		}
	}

	if justRestart == 0 || justRestart == 2 {
		resMap := make(map[string]interface{})
		resMap = service.BaoTaService.RestartProject(projectName)
		if resMap == nil {
			msg = "重启失败，请手动重启," + projectName
			logger.Error(msg, err)
			return
		} else {
			logger.Info("resMap:", resMap)
		}

		time.Sleep(time.Duration(2) * time.Second)

		if _, ok := resMap["data"]; ok {
			if resMap["data"].(string) == "项目未启动" {
				logger.Error("项目未启动,发送启动命令")
				resMap = service.BaoTaService.StartProject(projectName)
				if resMap == nil {
					msg = "启动项目失败，请手动重启," + projectName
					logger.Error(msg, err)
					return
				} else {
					logger.Info("启动resMap:", resMap)
				}
			}
		}
		result["restart"] = resMap
	}

	msg = "发布成功"
	if justRestart == 0 {
		msg = "上传成功(已重启)"
	} else if justRestart == 1 {
		msg = "上传成功(未重启)"
	} else if justRestart == 2 {
		msg = "重启成功"
	}
	result["client_ip"] = tools.GetClientIp(c.Request.Header)
	result["occur_time"] = time.Now().Format("2006-01-02 15:04:05")
	result["just_restart"] = justRestart
	code = 0
	//c.JSON(http.StatusOK, gin.H{
	//	"code":       code,
	//	"msg":        "发布成功",
	//	"client_ip":  tools.GetClientIp(c.Request.Header),
	//	"occur_time": time.Now().Format("2006-01-02 15:04:05"),
	//	"result":     resMap,
	//})
}

func (obj _projectApi) PublishHistorys(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		msg = "权限不足"
		logger.Error("权限不足")
		return
	}

	projectName, _ := c.GetPostForm("project_name")

	filePath := getFilePathByProjectName(projectName)
	result["file_path"] = filePath
	if checkFilepath(filePath) == false {
		msg = "程序路径不正确"
		return
	}

	dir := path.Dir(filePath)
	result["dir"] = dir
	if !strings.HasPrefix(dir, "/www/wwwroot/") {
		msg = "项目目录不正确"
		return
	}
	fileName := path.Base(filePath)
	result["file_name"] = fileName
	if strings.Contains(fileName, "/") || !strings.Contains(fileName, ".") {
		msg = "项目文件名不正确"
		return
	}

	historys := make([]FileInfo, 0)

	entries, err := os.ReadDir(dir)
	if err != nil {
		fmt.Println("Error reading directory:", err)
		return
	}

	// 遍历当前目录中的文件
	for _, entry := range entries {
		// 跳过子目录，只处理文件
		if entry.IsDir() {
			continue
		}

		// 如果是文件，且后缀是目标后缀
		if strings.HasPrefix(entry.Name(), fileName) {
			//fmt.Println("找到 zip 文件:", path)
			// 将目录信息添加到切片
			ary := strings.Split(entry.Name(), "_back")
			if len(ary) == 2 {
				// 定义对应的时间格式
				layout := "20060102150405" // 这个格式是根据字符串 "yyyyMMddHHmmss" 来设置的
				// 使用 time.Parse 将字符串转换为 time.Time 类型
				if t, err := time.ParseInLocation(layout, ary[1], time.Local); err != nil {
					logger.Error(err)
					return
				} else {
					historys = append(historys, FileInfo{
						Name:    entry.Name(),
						ModTime: t,
					})
				}
			}
		}
	}

	sort.Slice(historys, func(i, j int) bool {
		// 进行倒序排序：返回 true 表示 i 应该排在 j 前面
		return historys[i].ModTime.After(historys[j].ModTime)
	})
	result["historys"] = historys

	msg = ""
	result["client_ip"] = tools.GetClientIp(c.Request.Header)
	result["occur_time"] = time.Now().Format("2006-01-02 15:04:05")
	code = 0
}

func (obj _projectApi) PublishRollback(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	logger.Info("开始发布项目")

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		msg = "权限不足"
		logger.Error("权限不足")
		return
	}

	justRestart := 0

	if tmp, ok := c.GetPostForm("just_restart"); ok {
		if num, err := strconv.Atoi(tmp); err == nil {
			justRestart = num
		}
	}

	projectName, _ := c.GetPostForm("project_name")
	backFileName, _ := c.GetPostForm("back_file_name")

	filePath := getFilePathByProjectName(projectName)
	result["file_path"] = filePath
	if checkFilepath(filePath) == false {
		msg = "程序路径不正确"
		return
	}

	dir := path.Dir(filePath)
	result["dir"] = dir
	if !strings.HasPrefix(dir, "/www/wwwroot/") {
		msg = "项目目录不正确"
		return
	}
	fileName := path.Base(filePath)
	result["file_name"] = fileName
	if strings.Contains(fileName, "/") || !strings.Contains(fileName, ".") {
		msg = "项目文件名不正确"
		return
	}
	if strings.Contains(backFileName, "/") || !strings.Contains(backFileName, ".") {
		msg = "备份文件名不正确"
		return
	}
	backFilePath := path.Join(dir, backFileName)

	if justRestart == 0 || justRestart == 1 {
		logger.Info("filePath：", filePath)
		if filePath == "" || !strings.HasSuffix(filePath, ".linux") {
			logger.Error("程序文件路径不正确", filePath)
			msg = "文件路径为空"
			return
		}

		//删除运行的程序文件
		cmd := exec.Command("rm", "-f", filePath)
		if err := cmd.Run(); err != nil {
			msg = "执行删除命令失败"
			logger.Error(err)
			return
		}
		logger.Info("删除程序文件成功", filePath)

		// 打开源文件
		srcFile, err := os.Open(backFilePath)
		if err != nil {
			msg = "打开备份文件失败"
			logger.Error(msg, err)
			return
		}
		defer srcFile.Close()

		// 创建目标文件
		destFile, err := os.Create(filePath)
		if err != nil {
			msg = "打款程序文件失败"
			logger.Error(msg, err)
			return
		}
		defer destFile.Close()

		// 使用 io.Copy 将源文件内容复制到目标文件
		_, err = io.Copy(destFile, srcFile)
		if err != nil {
			msg = "复制文件失败"
			logger.Error(err)
			return
		}
		logger.Info("备份文件成功覆盖原文件", backFilePath, "   ", filePath)
		//设置程序文件权限
		if err := os.Chmod(filePath, 0777); err != nil {
			msg = "设置文件权限失败"
			logger.Error(msg, err)
			return
		}
	}

	if justRestart == 0 || justRestart == 2 {
		resMap := make(map[string]interface{})
		resMap = service.BaoTaService.RestartProject(projectName)
		if resMap == nil {
			msg = "重启失败，请手动重启," + projectName
			err := errors.New(msg)
			logger.Error(msg, err)
			return
		} else {
			logger.Info("resMap:", resMap)
		}

		time.Sleep(time.Duration(2) * time.Second)

		if _, ok := resMap["data"]; ok {
			if resMap["data"].(string) == "项目未启动" {
				logger.Error("项目未启动,发送启动命令")
				resMap = service.BaoTaService.StartProject(projectName)
				if resMap == nil {
					msg = "启动项目失败，请手动重启," + projectName
					err := errors.New(msg)
					logger.Error(msg, err)
					return
				} else {
					logger.Info("启动resMap:", resMap)
				}
			}
		}
		result["restart"] = resMap
	}

	msg = "回滚成功"
	if justRestart == 0 {
		msg = "回滚成功成功(已重启)"
	} else if justRestart == 1 {
		msg = "回滚成功成功(未重启)"
	} else if justRestart == 2 {
		msg = "重启成功"
	}
	result["client_ip"] = tools.GetClientIp(c.Request.Header)
	result["occur_time"] = time.Now().Format("2006-01-02 15:04:05")
	result["just_restart"] = justRestart
	code = 0
}

func (obj _projectApi) Deploy(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	logger.Info("开始部署项目")

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		msg = "权限不足"
		logger.Error("权限不足")
		return
	}

	f, err := c.FormFile("file")
	if err != nil {
		msg = "上传文件失败，" + err.Error()
		logger.Error(err)
		return
	}

	projectName, _ := c.GetPostForm("project_name")

	filePath := ""
	dirPath := ""
	backPath := ""
	if projectName == "aigc_api_4005" {
		filePath = "/www/wwwroot/dev.cyuai.com/api/design_ai.linux"
	} else if projectName == "sheys_api_5021" {
		filePath = "/www/wwwroot/sheys.cn/api/sheys_ai.linux"
	} else if projectName == "design_api_5001" {
		filePath = "/www/wwwroot/design.cyuai.com/api/design_ai.linux"
	} else if projectName == "design_api_online" {
		filePath = "/www/wwwroot/design.cyuai.com/api_online/design_ai.linux"
	} else if projectName == "aigc_api_4003" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api/zcloud_ai.linux"
	} else if projectName == "aigc_api_online" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api_online/zcloud_ai.linux"
	} else if projectName == "center_api_test" {
		filePath = "/www/wwwroot/center.cyuai.com/api_test/center_ai.linux"
	} else if projectName == "center_api_online" {
		filePath = "/www/wwwroot/center.cyuai.com/api_online/center_ai.linux"
	} else if projectName == "center_api" {
		filePath = "/www/wwwroot/center.cyuai.com/api/center_ai.linux"
	} else if projectName == "camera_api" {
		filePath = "/www/wwwroot/camera.cyuai.com/api/camera_ai.linux"
	} else if projectName == "camera_api_online" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_online/camera_ai.linux"
	} else if projectName == "camera_api_test" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_test/camera_ai.linux"
	} else if projectName == "douyin_api_test" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api_test/douyin_ai.linux"
	} else if projectName == "douyin_api" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api/douyin_ai.linux"
	} else if projectName == "sd_api_online" {
		filePath = "/www/wwwroot/sd.cyuai.com/api_online/sd_ai.linux"
	} else if projectName == "sd_api" {
		filePath = "/www/wwwroot/sd.cyuai.com/api/sd_ai.linux"
	} else if projectName == "cpn_sched" {
		filePath = "/www/wwwroot/www.suanyun.cn/api/cpn_sched.linux"
	} else if projectName == "cpn_node_hz01" {
		filePath = "/www/wwwroot/hz01.suanyun.cn/api/cpn_node.linux"
	} else if projectName == "design_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/design.cyuai.com/h5_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/design.cyuai.com/h5"
		backPath = "/www/wwwroot/design.cyuai.com/back/h5"
	} else if projectName == "cpn_node_dist" || projectName == "suanyun_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/www.suanyun.cn/dist_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/www.suanyun.cn/dist"
		backPath = "/www/wwwroot/www.suanyun.cn/back/dist"
	} else if projectName == "suanyun_dist_online" {
		filePath = fmt.Sprintf("/www/wwwroot/online.suanyun.cn/dist_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/online.suanyun.cn/dist"
		backPath = "/www/wwwroot/online.suanyun.cn/back/dist"
	} else if projectName == "docs_suanyun" {
		filePath = fmt.Sprintf("/www/wwwroot/docs.suanyun.cn/docsify_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/docs.suanyun.cn/docsify"
		backPath = "/www/wwwroot/docs.suanyun.cn/back/docsify"
	} else if projectName == "admin_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/center.cyuai.com/admin1_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/center.cyuai.com/admin1"
		backPath = "/www/wwwroot/center.cyuai.com/back/admin1"
	}

	//保存上传的程序文件
	if err := c.SaveUploadedFile(f, filePath); err != nil {
		msg = "程序文件保存失败"
		logger.Error(err)
		return
	}
	logger.Info("文件上传成功", filePath)

	tmpAry := strings.Split(dirPath, "/")
	if strings.HasPrefix(dirPath, "/www/wwwroot/") && len(tmpAry) >= 5 {
		if len(backPath) < len(dirPath) {
			msg = "备份路径不正确"
			logger.Error(msg, backPath)
			return
		}
		newPath := fmt.Sprintf("%s_back%s", backPath, time.Now().Format("20060102150405"))
		if err = os.Rename(dirPath, newPath); err != nil {
			msg = "文件夹重命名出错"
			logger.Error(msg, err, dirPath, "    ", newPath)
			return
		}
		logger.Info("备份文件成功", newPath)
	} else {
		msg = "移除路径不正确"
		logger.Error(msg, dirPath)
		return
	}

	// 执行 unzip 命令解压文件
	cmd := exec.Command("unzip", filePath, "-d", dirPath)
	if err := cmd.Run(); err != nil {
		fmt.Println("解压失败:", err)
		msg = "解压失败"
		logger.Error(msg, err)
		return
	}

	code = 0
	msg = "发布成功"
	result["client_ip"] = tools.GetClientIp(c.Request.Header)
	result["occur_time"] = time.Now().Format("2006-01-02 15:04:05")
}

func (obj _projectApi) Historys(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	logger.Info("开始部署项目")

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		msg = "权限不足"
		logger.Error("权限不足")
		return
	}

	projectName, _ := c.GetPostForm("project_name")

	filePath := ""
	dirPath := ""
	backPath := ""
	if projectName == "aigc_api_4005" {
		filePath = "/www/wwwroot/dev.cyuai.com/api/design_ai.linux"
	} else if projectName == "sheys_api_5021" {
		filePath = "/www/wwwroot/sheys.cn/api/sheys_ai.linux"
	} else if projectName == "design_api_5001" {
		filePath = "/www/wwwroot/design.cyuai.com/api/design_ai.linux"
	} else if projectName == "design_api_online" {
		filePath = "/www/wwwroot/design.cyuai.com/api_online/design_ai.linux"
	} else if projectName == "aigc_api_4003" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api/zcloud_ai.linux"
	} else if projectName == "aigc_api_online" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api_online/zcloud_ai.linux"
	} else if projectName == "center_api_test" {
		filePath = "/www/wwwroot/center.cyuai.com/api_test/center_ai.linux"
	} else if projectName == "center_api_online" {
		filePath = "/www/wwwroot/center.cyuai.com/api_online/center_ai.linux"
	} else if projectName == "center_api" {
		filePath = "/www/wwwroot/center.cyuai.com/api/center_ai.linux"
	} else if projectName == "camera_api" {
		filePath = "/www/wwwroot/camera.cyuai.com/api/camera_ai.linux"
	} else if projectName == "camera_api_online" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_online/camera_ai.linux"
	} else if projectName == "camera_api_test" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_test/camera_ai.linux"
	} else if projectName == "douyin_api_test" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api_test/douyin_ai.linux"
	} else if projectName == "douyin_api" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api/douyin_ai.linux"
	} else if projectName == "sd_api_online" {
		filePath = "/www/wwwroot/sd.cyuai.com/api_online/sd_ai.linux"
	} else if projectName == "sd_api" {
		filePath = "/www/wwwroot/sd.cyuai.com/api/sd_ai.linux"
	} else if projectName == "cpn_sched" {
		filePath = "/www/wwwroot/www.suanyun.cn/api/cpn_sched.linux"
	} else if projectName == "cpn_node_hz01" {
		filePath = "/www/wwwroot/hz01.suanyun.cn/api/cpn_node.linux"
	} else if projectName == "design_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/design.cyuai.com/h5_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/design.cyuai.com/h5"
		backPath = "/www/wwwroot/design.cyuai.com/back/h5"
	} else if projectName == "cpn_node_dist" || projectName == "suanyun_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/www.suanyun.cn/dist_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/www.suanyun.cn/dist"
		backPath = "/www/wwwroot/www.suanyun.cn/back/dist"
	} else if projectName == "suanyun_dist_online" {
		filePath = fmt.Sprintf("/www/wwwroot/online.suanyun.cn/dist_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/online.suanyun.cn/dist"
		backPath = "/www/wwwroot/online.suanyun.cn/back/dist"
	} else if projectName == "docs_suanyun" {
		filePath = fmt.Sprintf("/www/wwwroot/docs.suanyun.cn/docsify_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/docs.suanyun.cn/docsify"
		backPath = "/www/wwwroot/docs.suanyun.cn/back/docsify"
	} else if projectName == "admin_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/center.cyuai.com/admin1_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/center.cyuai.com/admin1"
		backPath = "/www/wwwroot/center.cyuai.com/back/admin1"
	}
	result["project_name"] = projectName
	if dirPath != "" && backPath != "" {

	}

	dir := path.Dir(filePath)
	// 使用 Walk 函数遍历目录
	historys := make([]FileInfo, 0)
	if err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		// 检查是否有错误
		if err != nil {
			return err
		}

		// 检查文件是否是zip文件
		if !info.IsDir() && strings.HasSuffix(info.Name(), ".zip") {
			//fmt.Println("找到 zip 文件:", path)
			// 将目录信息添加到切片
			historys = append(historys, FileInfo{
				Name:    info.Name(),
				ModTime: info.ModTime(),
			})
		}
		return nil
	}); err != nil {
		msg = "获取历史版本包失败"
		logger.Error(msg, err)
		return
	} else {
		sort.Slice(historys, func(i, j int) bool {
			// 进行倒序排序：返回 true 表示 i 应该排在 j 前面
			return historys[i].ModTime.After(historys[j].ModTime)
		})
		result["historys"] = historys
	}
	code = 0
	msg = ""
}

func (obj _projectApi) Rollback(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	logger.Info("开始部署项目")

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		msg = "权限不足"
		logger.Error("权限不足")
		return
	}

	projectName, _ := c.GetPostForm("project_name")
	backName, _ := c.GetPostForm("back_name")
	if backName == "" {
		msg = "请输入回滚文件名称"
		return
	}

	backPw, _ := c.GetPostForm("back_pw")
	if backPw != "3123" {
		msg = "回滚口令不正确"
		return
	}

	filePath := ""
	dirPath := ""
	backPath := ""
	if projectName == "aigc_api_4005" {
		filePath = "/www/wwwroot/dev.cyuai.com/api/design_ai.linux"
	} else if projectName == "sheys_api_5021" {
		filePath = "/www/wwwroot/sheys.cn/api/sheys_ai.linux"
	} else if projectName == "design_api_5001" {
		filePath = "/www/wwwroot/design.cyuai.com/api/design_ai.linux"
	} else if projectName == "design_api_online" {
		filePath = "/www/wwwroot/design.cyuai.com/api_online/design_ai.linux"
	} else if projectName == "aigc_api_4003" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api/zcloud_ai.linux"
	} else if projectName == "aigc_api_online" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api_online/zcloud_ai.linux"
	} else if projectName == "center_api_test" {
		filePath = "/www/wwwroot/center.cyuai.com/api_test/center_ai.linux"
	} else if projectName == "center_api_online" {
		filePath = "/www/wwwroot/center.cyuai.com/api_online/center_ai.linux"
	} else if projectName == "center_api" {
		filePath = "/www/wwwroot/center.cyuai.com/api/center_ai.linux"
	} else if projectName == "camera_api" {
		filePath = "/www/wwwroot/camera.cyuai.com/api/camera_ai.linux"
	} else if projectName == "camera_api_online" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_online/camera_ai.linux"
	} else if projectName == "camera_api_test" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_test/camera_ai.linux"
	} else if projectName == "douyin_api_test" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api_test/douyin_ai.linux"
	} else if projectName == "douyin_api" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api/douyin_ai.linux"
	} else if projectName == "sd_api_online" {
		filePath = "/www/wwwroot/sd.cyuai.com/api_online/sd_ai.linux"
	} else if projectName == "sd_api" {
		filePath = "/www/wwwroot/sd.cyuai.com/api/sd_ai.linux"
	} else if projectName == "cpn_sched" {
		filePath = "/www/wwwroot/www.suanyun.cn/api/cpn_sched.linux"
	} else if projectName == "cpn_node_hz01" {
		filePath = "/www/wwwroot/hz01.suanyun.cn/api/cpn_node.linux"
	} else if projectName == "design_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/design.cyuai.com/h5_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/design.cyuai.com/h5"
		backPath = "/www/wwwroot/design.cyuai.com/back/h5"
	} else if projectName == "cpn_node_dist" || projectName == "suanyun_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/www.suanyun.cn/dist_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/www.suanyun.cn/dist"
		backPath = "/www/wwwroot/www.suanyun.cn/back/dist"
	} else if projectName == "suanyun_dist_online" {
		filePath = fmt.Sprintf("/www/wwwroot/online.suanyun.cn/dist_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/online.suanyun.cn/dist"
		backPath = "/www/wwwroot/online.suanyun.cn/back/dist"
	} else if projectName == "docs_suanyun" {
		filePath = fmt.Sprintf("/www/wwwroot/docs.suanyun.cn/docsify_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/docs.suanyun.cn/docsify"
		backPath = "/www/wwwroot/docs.suanyun.cn/back/docsify"
	} else if projectName == "admin_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/center.cyuai.com/admin1_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/center.cyuai.com/admin1"
		backPath = "/www/wwwroot/center.cyuai.com/back/admin1"
	}

	result["project_name"] = projectName

	dir := path.Dir(filePath)
	rollbackFilepath := path.Join(dir, backName)
	result["rollback_filepath"] = rollbackFilepath

	if !isValidPath(rollbackFilepath) || !isValidPath(dirPath) {
		msg = "路径不正确"
		logger.Error(msg)
		return
	}

	tmpAry := strings.Split(dirPath, "/")
	if strings.HasPrefix(dirPath, "/www/wwwroot/") && len(tmpAry) >= 5 {
		if len(backPath) < len(dirPath) {
			msg = "备份路径不正确"
			logger.Error(msg, backPath)
			return
		}
		newPath := fmt.Sprintf("%s_roll%s", backPath, time.Now().Format("20060102150405"))
		if err := os.Rename(dirPath, newPath); err != nil {
			msg = "文件夹重命名出错"
			logger.Error(msg, err, dirPath, "    ", newPath)
			return
		}
		logger.Info("备份文件成功", newPath)
	} else {
		msg = "移除路径不正确"
		logger.Error(msg, dirPath)
		return
	}

	// 执行 unzip 命令解压文件
	cmd := exec.Command("unzip", rollbackFilepath, "-d", dirPath)
	if err := cmd.Run(); err != nil {
		msg = "解压失败," + err.Error()
		logger.Error(msg, err)
		return
	}

	code = 0
	msg = "回滚成功"
}

func isValidPath(pathStr string) bool {
	if !strings.HasPrefix(pathStr, "/www/wwwroot/") {
		return false
	}
	if len(pathStr) < 20 {
		return false
	}
	return true
}

func (obj _projectApi) Historys111(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	logger.Info("开始部署项目")

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		msg = "权限不足"
		logger.Error("权限不足")
		return
	}

	projectName, _ := c.GetPostForm("project_name")

	filePath := ""
	dirPath := ""
	backPath := ""
	if projectName == "aigc_api_4005" {
		filePath = "/www/wwwroot/dev.cyuai.com/api/design_ai.linux"
	} else if projectName == "sheys_api_5021" {
		filePath = "/www/wwwroot/sheys.cn/api/sheys_ai.linux"
	} else if projectName == "design_api_5001" {
		filePath = "/www/wwwroot/design.cyuai.com/api/design_ai.linux"
	} else if projectName == "design_api_online" {
		filePath = "/www/wwwroot/design.cyuai.com/api_online/design_ai.linux"
	} else if projectName == "aigc_api_4003" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api/zcloud_ai.linux"
	} else if projectName == "aigc_api_online" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api_online/zcloud_ai.linux"
	} else if projectName == "center_api_test" {
		filePath = "/www/wwwroot/center.cyuai.com/api_test/center_ai.linux"
	} else if projectName == "center_api_online" {
		filePath = "/www/wwwroot/center.cyuai.com/api_online/center_ai.linux"
	} else if projectName == "center_api" {
		filePath = "/www/wwwroot/center.cyuai.com/api/center_ai.linux"
	} else if projectName == "camera_api" {
		filePath = "/www/wwwroot/camera.cyuai.com/api/camera_ai.linux"
	} else if projectName == "camera_api_online" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_online/camera_ai.linux"
	} else if projectName == "camera_api_test" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_test/camera_ai.linux"
	} else if projectName == "douyin_api_test" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api_test/douyin_ai.linux"
	} else if projectName == "douyin_api" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api/douyin_ai.linux"
	} else if projectName == "sd_api_online" {
		filePath = "/www/wwwroot/sd.cyuai.com/api_online/sd_ai.linux"
	} else if projectName == "sd_api" {
		filePath = "/www/wwwroot/sd.cyuai.com/api/sd_ai.linux"
	} else if projectName == "cpn_sched" {
		filePath = "/www/wwwroot/www.suanyun.cn/api/cpn_sched.linux"
	} else if projectName == "cpn_node_hz01" {
		filePath = "/www/wwwroot/hz01.suanyun.cn/api/cpn_node.linux"
	} else if projectName == "design_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/design.cyuai.com/h5_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/design.cyuai.com/h5"
		backPath = "/www/wwwroot/design.cyuai.com/back/h5"
	} else if projectName == "cpn_node_dist" || projectName == "suanyun_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/www.suanyun.cn/dist_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/www.suanyun.cn/dist"
		backPath = "/www/wwwroot/www.suanyun.cn/back/dist"
	} else if projectName == "suanyun_dist_online" {
		filePath = fmt.Sprintf("/www/wwwroot/online.suanyun.cn/dist_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/online.suanyun.cn/dist"
		backPath = "/www/wwwroot/online.suanyun.cn/back/dist"
	} else if projectName == "docs_suanyun" {
		filePath = fmt.Sprintf("/www/wwwroot/docs.suanyun.cn/docsify_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/docs.suanyun.cn/docsify"
		backPath = "/www/wwwroot/docs.suanyun.cn/back/docsify"
	} else if projectName == "admin_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/center.cyuai.com/admin1_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/center.cyuai.com/admin1"
		backPath = "/www/wwwroot/center.cyuai.com/back/admin1"
	}
	result["project_name"] = projectName

	if !strings.Contains(backPath, "back") {
		msg = "未获取到备份路径"
		return
	}
	if filePath != "" && dirPath != "" {

	}
	tmpAry := strings.Split(backPath, "back")
	historyPath := tmpAry[0] + "back"
	result["history_path"] = historyPath
	if historys, err := getSubDirs(historyPath); err != nil {
		result["historys_err"] = err.Error()
	} else {
		sort.Slice(historys, func(i, j int) bool {
			// 进行倒序排序：返回 true 表示 i 应该排在 j 前面
			return historys[i].LastModified.After(historys[j].LastModified)
		})
		result["historys"] = historys
	}
	code = 0
	msg = ""
}

func (obj _projectApi) Rollback111(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	logger.Info("开始部署项目")

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		msg = "权限不足"
		logger.Error("权限不足")
		return
	}

	projectName, _ := c.GetPostForm("project_name")
	backName, _ := c.GetPostForm("back_name")
	if backName == "" {
		msg = "请输入回滚文件名称"
		return
	}

	filePath := ""
	dirPath := ""
	backPath := ""
	if projectName == "aigc_api_4005" {
		filePath = "/www/wwwroot/dev.cyuai.com/api/design_ai.linux"
	} else if projectName == "sheys_api_5021" {
		filePath = "/www/wwwroot/sheys.cn/api/sheys_ai.linux"
	} else if projectName == "design_api_5001" {
		filePath = "/www/wwwroot/design.cyuai.com/api/design_ai.linux"
	} else if projectName == "design_api_online" {
		filePath = "/www/wwwroot/design.cyuai.com/api_online/design_ai.linux"
	} else if projectName == "aigc_api_4003" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api/zcloud_ai.linux"
	} else if projectName == "aigc_api_online" {
		filePath = "/www/wwwroot/aigc.cyuai.com/api_online/zcloud_ai.linux"
	} else if projectName == "center_api_test" {
		filePath = "/www/wwwroot/center.cyuai.com/api_test/center_ai.linux"
	} else if projectName == "center_api_online" {
		filePath = "/www/wwwroot/center.cyuai.com/api_online/center_ai.linux"
	} else if projectName == "center_api" {
		filePath = "/www/wwwroot/center.cyuai.com/api/center_ai.linux"
	} else if projectName == "camera_api" {
		filePath = "/www/wwwroot/camera.cyuai.com/api/camera_ai.linux"
	} else if projectName == "camera_api_online" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_online/camera_ai.linux"
	} else if projectName == "camera_api_test" {
		filePath = "/www/wwwroot/camera.cyuai.com/api_test/camera_ai.linux"
	} else if projectName == "douyin_api_test" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api_test/douyin_ai.linux"
	} else if projectName == "douyin_api" {
		filePath = "/www/wwwroot/douyin.cyuai.com/api/douyin_ai.linux"
	} else if projectName == "sd_api_online" {
		filePath = "/www/wwwroot/sd.cyuai.com/api_online/sd_ai.linux"
	} else if projectName == "sd_api" {
		filePath = "/www/wwwroot/sd.cyuai.com/api/sd_ai.linux"
	} else if projectName == "cpn_sched" {
		filePath = "/www/wwwroot/www.suanyun.cn/api/cpn_sched.linux"
	} else if projectName == "cpn_node_hz01" {
		filePath = "/www/wwwroot/hz01.suanyun.cn/api/cpn_node.linux"
	} else if projectName == "design_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/design.cyuai.com/h5_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/design.cyuai.com/h5"
		backPath = "/www/wwwroot/design.cyuai.com/back/h5"
	} else if projectName == "cpn_node_dist" || projectName == "suanyun_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/www.suanyun.cn/dist_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/www.suanyun.cn/dist"
		backPath = "/www/wwwroot/www.suanyun.cn/back/dist"
	} else if projectName == "suanyun_dist_online" {
		filePath = fmt.Sprintf("/www/wwwroot/online.suanyun.cn/dist_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/online.suanyun.cn/dist"
		backPath = "/www/wwwroot/online.suanyun.cn/back/dist"
	} else if projectName == "docs_suanyun" {
		filePath = fmt.Sprintf("/www/wwwroot/docs.suanyun.cn/docsify_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/docs.suanyun.cn/docsify"
		backPath = "/www/wwwroot/docs.suanyun.cn/back/docsify"
	} else if projectName == "admin_dist" {
		filePath = fmt.Sprintf("/www/wwwroot/center.cyuai.com/admin1_%s.zip", time.Now().Format("20060102150405"))
		dirPath = "/www/wwwroot/center.cyuai.com/admin1"
		backPath = "/www/wwwroot/center.cyuai.com/back/admin1"
	}
	result["project_name"] = projectName

	if !strings.Contains(backPath, "back") {
		msg = "未获取到备份路径"
		return
	}

	if filePath != "" && dirPath != "" {

	}
	tmpAry := strings.Split(backPath, "back")
	historyPath := tmpAry[0] + "back"
	rollbackPath := historyPath + "/" + backName
	if !strings.Contains(rollbackPath, "/back/") {
		msg = "回滚备份路径不正确"
		return
	}

	copybackPath := strings.Replace(rollbackPath, "/back/", "/", -1)

	result["history_path"] = historyPath
	result["rollback_path"] = rollbackPath
	result["copyback_path"] = copybackPath

	if !strings.Contains(copybackPath, "/www/wwwroot/") {
		msg = "拷贝路径不正确"
		return
	}

	if tools.FileExists(rollbackPath) == false {
		msg = "备份文件不存在"
		return
	}

	cmd := exec.Command("cp", "-a", rollbackPath+"/.", copybackPath)
	if output, err := cmd.Output(); err != nil {
		msg = "拷贝失败"
		logger.Error(msg, err)
		return
	} else {
		result["copy_output"] = string(output)
	}

	code = 0
	msg = ""
}

type DirInfo struct {
	Path         string
	Name         string
	LastModified time.Time
}

func getSubDirs(rootPath string) ([]DirInfo, error) {
	dirInfos := make([]DirInfo, 0)

	// 读取指定目录下的所有内容（文件和文件夹）
	entries, err := os.ReadDir(rootPath)
	if err != nil {
		return nil, err
	}

	// 遍历所有内容，筛选出目录并获取修改时间
	for _, entry := range entries {
		// 如果是目录，则获取其最后修改时间
		if entry.IsDir() {
			// 获取目录的元数据
			info, err := entry.Info()
			if err != nil {
				return nil, err
			}
			// 将目录信息添加到切片
			dirInfos = append(dirInfos, DirInfo{
				Path:         rootPath,
				Name:         entry.Name(),
				LastModified: info.ModTime(),
			})
		}
	}

	return dirInfos, nil
}

// 遍历目录并返回包含文件夹名称和修改时间的切片
func visitDir(path string, info os.FileInfo, err error) ([]DirInfo, error) {
	var dirInfos []DirInfo

	// 如果发生错误（比如权限问题），直接返回
	if err != nil {
		return nil, err
	}

	// 只处理文件夹（目录）
	if info.IsDir() {
		// 将文件夹信息添加到切片
		dirInfos = append(dirInfos, DirInfo{
			Path:         path,
			Name:         info.Name(),
			LastModified: info.ModTime(),
		})
	}
	return dirInfos, nil
}

var ProjectApi _projectApi
