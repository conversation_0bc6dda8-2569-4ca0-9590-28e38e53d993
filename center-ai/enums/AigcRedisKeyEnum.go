package enums

import "reflect"

type aigcRedisKeyEnum_ struct {
	SdApiModels,
	SdApiPop, SdApiProgress, SdApiPush, SdApiPushTarget,
	SegmentPush, SegmentPop,
	UpScalingIn, UpScalingOut, UpScalingState string
}

func (c aigcRedisKeyEnum_) Get(id string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if typeVo.Field(i).Name == id {
			return vo.Field(i).Interface().(string)
		}
	}
	return ""
}

var AigcRedisKeyEnum = aigcRedisKeyEnum_{
	SdApiModels:     "aigc-worker:sd:models", //sd模型缓存字符串
	SdApiPop:        "aigc-worker:sd:task:out:shopshow",
	SdApiProgress:   "aigc-worker:sd:task:progress:shopshow",
	SdApiPush:       "aigc-worker:sd:task:queue",
	SdApiPushTarget: "aigc-worker:sd:task:queue_target", //绘图指定队列
	SegmentPush:     "segment-anything-v1:list:mask-in",
	SegmentPop:      "segment-anything-v1:list:mask-out",
}
