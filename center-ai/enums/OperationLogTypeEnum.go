package enums

import "reflect"

type operationLogTypeEnum_ struct {
	Other, ModifySdParm, Login int
}

var OperationLogTypeEnum = operationLogTypeEnum_{
	Other:        0, //其他
	ModifySdParm: 1, //修改sd参数
	Login:        2, //登录日志
}

func (c operationLogTypeEnum_) GetKey(value int) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if vo.Field(i).Interface().(int) == value {
			return typeVo.Field(i).Name
		}
	}
	return ""
}
