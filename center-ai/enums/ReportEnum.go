package enums

type reportTypeEnum_ struct {
	SysIntercept, UserReport int
}

var ReportTypeEnum = reportTypeEnum_{
	SysIntercept: 1, //系统拦截
	UserReport:   2, //用户举报
}

var ReportTypeNameEnum = map[int]string{
	1: "系统拦截",
	2: "用户举报",
}

var ReportStateNameEnum = map[int]string{
	0: "等待审核",
	1: "审核中",
	2: "审核完成(未通过)",
	3: "审核完成(通过)",
	4: "处理完成",
}

func ReportSecureTxt(label int) string {
	switch label {
	case 100:
		return "正常"
	case 10001:
		return "广告"
	case 20001:
		return "时政"
	case 20002:
		return "色情"
	case 20003:
		return "辱骂"
	case 20006:
		return "违法犯罪"
	case 20008:
		return "欺诈"
	case 20012:
		return "低俗"
	case 20013:
		return "版权"
	case 21000:
		return "其他"
	}
	return "其它"
}
