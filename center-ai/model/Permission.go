package model

import (
	"center-ai/utils/logger"
	"gorm.io/gorm"
)

type Permission struct {
	gorm.Model
	ProjectId    uint   `json:"project_id" gorm:"type:bigint;not null;default:0;comment:项目ID"`
	Title        string `json:"title" gorm:"type:varchar(50);comment:权限标题"`
	GroupType    int    `json:"group_type" gorm:"type:tinyint;not null;default:0;comment: 1作为菜单使用"`
	GroupRouter  string `json:"group_router" gorm:"type:varchar(50);comment:菜单路由"`
	GroupIndex   int    `json:"group_index" gorm:"type:int;not null;default:0;comment: 排序越大越前面"`
	GroupName1   string `json:"group_name1" gorm:"type:varchar(50);comment:组名1,方便管理"`
	GroupName2   string `json:"group_name2" gorm:"type:varchar(50);comment:组名2,方便管理"`
	PermOperates string `json:"perm_operates" gorm:"type:json;comment:权限操作数组"`
	RouterPaths  string `json:"router_paths" gorm:"type:json;comment:相关Path"`
	Remark       string `json:"remark" gorm:"type:varchar(150);comment:备注"`
	State        int    `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 1使用中 2暂时禁用"`
}

func (Permission) TableName() string {
	return "T_Permission"
}

func (o *Permission) GetById(id uint) error {
	return db.First(o, id).Error
}
func (o *Permission) GetList(state ...int) []Permission {
	ary := make([]Permission, 0)
	tx := db.Debug().Model(o)
	if len(state) == 1 {
		tx.Where("state=?", state[0])
	}
	tx.Order("id desc")
	if err := tx.Scan(&ary).Error; err != nil {
		logger.Error(err)
	}
	return ary
}

func (o *Permission) GetListByProject(dest interface{}, projectId uint, page int, pageSize int) (int64, error) {

	var total int64
	tx := db.Debug().Model(o)
	if projectId > 0 {
		tx.Where("project_id=?", projectId)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("group_name1 asc, group_index desc ").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Permission) GetListForMenu(dest interface{}, page int, pageSize int) (int64, error) {

	var total int64
	tx := db.Debug().Model(o)
	tx.Where("group_type=?", 1)
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("group_name1 asc, group_index desc ").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Permission) GetGroup1ForMenuIndex(dest interface{}) error {
	tx := db.Debug().Model(o).Select("group_name1,max(group_index) as max_index")
	tx.Where("group_type=?", 1).Group("group_name1")
	tx.Order("max(group_index) desc ").Scan(dest)
	return tx.Error
}

func (o *Permission) Save() error {
	return db.Save(o).Error
}
