package model

import (
	"gorm.io/gorm"
)

type Report struct {
	gorm.Model
	Uuid           string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串Id"`
	ReportType     int    `json:"report_type" gorm:"type:tinyint;not null;default:0;comment:类型"`
	SecureLabel    int    `json:"secure_label" gorm:"type:int;not null;default:0;comment:安全标签"`
	SecureTxt      string `json:"secure_txt" gorm:"type:varchar(50);comment:安全标签文本"`
	ProjectId      uint   `json:"project_id" gorm:"type:bigint;not null;default:0;comment:项目ID"`
	ProjectTitle   string `json:"project_title" gorm:"type:varchar(50);comment:项目名称"`
	ReportUserId   uint   `json:"report_user_id" gorm:"type:bigint;not null;default:0;comment:举报用户ID"`
	Content        string `json:"content" gorm:"type:json;comment:举报内容"`
	Audit          string `json:"audit" gorm:"type:json;comment:审核记录"`
	OperatorUserId uint   `json:"operator_user_id" gorm:"type:bigint;not null;default:0;comment:操作员用户ID"`
	Remark         string `json:"remark" gorm:"type:varchar(150);not null;default:'';comment:备注" `
	State          int    `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0等待审核 1审核中 2审核完成(未通过) 3审核完成(通过) "`
}

func (Report) TableName() string {
	return "T_Report"
}

func (o *Report) Save() error {
	return db.Save(o).Error
}

func (o *Report) GetById(id uint) error {
	return db.First(o, id).Error
}

func (o *Report) List(dest interface{}, reportId uint, reportType int, projectId uint, kw string, state int, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o).Order("id desc")
	if reportId > 0 {
		tx.Where("id=?", reportId)
	} else {
		if reportType > 0 {
			tx.Where("report_type=?", reportType)
		}
		if projectId > 0 {
			tx.Where("project_id=?", projectId)
		}
		if kw != "" {
			tx.Where("content like ?", "%"+kw+"%")
		}
		if state >= 0 {
			tx.Where("state=?", state)
		}
	}
	if err := tx.Count(&total).Error; err != nil {
		return 0, err
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Report) SetState(state int) error {
	return db.Model(o).Updates(Report{State: state}).Error
}

func (o *Report) AppendAudit(jsonObject map[string]interface{}) error {

	var fields []interface{}
	for key, value := range jsonObject {
		fields = append(fields, key, value)
	}

	// Prepare SQL query with placeholder for JSON string
	sql := `UPDATE T_Report SET audit = JSON_ARRAY_APPEND(audit, '$', JSON_OBJECT(?)) WHERE id = ?`

	// Execute the SQL query
	result := db.Exec(sql, fields, o.ID)

	// Check for errors
	if result.Error != nil {
		// Handle the error
		return result.Error
	}

	return nil
}
