package model

import (
	"center-ai/utils/logger"
	"gorm.io/gorm"
)

type Role struct {
	gorm.Model
	//RoleCode   string `json:"role_code" gorm:"type:varchar(20);not null"`
	RoleName    string `json:"role_name" gorm:"type:varchar(20);not null"`
	Permissions string `json:"permissions" gorm:"type:json;comment:权限操作数组"`
	Remark      string `json:"remark" gorm:"type:varchar(150);not null;default:'';comment:备注" `
}

func (Role) TableName() string {
	return "T_Role"
}

func (o *Role) GetById(id uint) error {
	return db.First(o, id).Error
}

func (o *Role) GetList(dest interface{}, projectId uint, roleName string, page int, pageSize int) (int64, error) {

	var total int64
	tx := db.Debug().Model(o)
	if projectId > 0 {
		tx.Where("project_id=?", projectId)
	}
	if roleName != "" {
		tx.Where("role_name like ?", roleName)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Role) GetListForLoad(state ...int) []Role {
	ary := make([]Role, 0)
	tx := db.Debug().Model(o)
	if len(state) == 1 {
		tx.Where("state=?", state[0])
	}
	tx.Order("id desc")
	if err := tx.Scan(&ary).Error; err != nil {
		logger.Error(err)
	}
	return ary
}

func (o *Role) Save() error {
	return db.Save(o).Error
}
