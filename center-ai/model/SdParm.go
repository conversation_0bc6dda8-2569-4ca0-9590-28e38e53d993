package model

import (
	"gorm.io/gorm"
)

type SdParm struct {
	gorm.Model
	UserId          uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	BusinessType    int    `json:"business_type" gorm:"type:tinyint;not null;default:0;comment:业务类型"`
	Title           string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	OuterNo         string `json:"outer_no" gorm:"type:varchar(50);not null;default:'';comment:外部编号"`
	Sex             int    `json:"sex" gorm:"type:tinyint;not null;default:0;comment:1男 2女"`
	Nationality     string `json:"nationality" gorm:"type:varchar(50);not null;default:'';comment:国模 外模"`
	AgeSect         string `json:"age_sect" gorm:"type:varchar(50);not null;default:'';comment:年龄段"`
	SdModelId       uint   `json:"sd_model_id" gorm:"type:bigint;not null;default:0;comment:模型ID"`
	Prompt          string `json:"prompt" gorm:"type:varchar(1500);not null;default:'';comment:风格描述"`
	NegativePrompt  string `json:"negative_prompt" gorm:"type:varchar(1500);not null;default:'';comment:我不想要什么样的风格画面描述"`
	Parameters      string `json:"parameters" gorm:"type:json;comment:sd出图参数"`
	ControlnetUnits string `json:"controlnet_units" gorm:"type:json;comment:controlnet单元数组"`
	Cover           string `json:"cover" gorm:"type:varchar(100);not null;default:'';comment:封面图像"`
	Remark          string `json:"remark" gorm:"type:varchar(500);not null;default:'';comment:后台备注"`
	OrderIndex      int    `json:"order_index" gorm:"type:int;not null;default:0;comment:序号(越大越前面)"`
	PriceCoin       int    `json:"price_coin" gorm:"type:int;not null;default:0;comment:所需Coin"`
	State           int    `json:"state" gorm:"type:int;not null;default:0;comment:状态(1可用)"`
}

func (SdParm) TableName() string {
	return "T_SdParm"
}

func (o *SdParm) GetById(id uint) error {
	return db.First(o, id).Error
}

func (o *SdParm) GetList(dest interface{}, id uint, outerNo string, businessType int, sex int, nationality string, ageSect string, kw string, state int, page int, pageSize int) (int64, error) {

	var total int64
	tx := db.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	}
	if outerNo != "" {
		tx.Where("outer_no=?", outerNo)
	}
	if businessType > 0 {
		tx.Where("business_type=?", businessType)
	}
	if sex > 0 {
		tx.Where("sex=?", sex)
	}
	if nationality != "" {
		tx.Where("nationality=?", nationality)
	}
	if ageSect != "" {
		tx.Where("age_sect=?", ageSect)
	}
	if kw != "" {
		tx.Where("parameters like ?", "%"+kw+"%")
	}
	if state >= 0 {
		tx.Where("state=?", state)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *SdParm) Save() error {
	return db.Save(o).Error
}

func (o *SdParm) SetCover(path string) error {
	return db.Model(o).Updates(SdParm{Cover: path}).Error
}

//type ControlnetUnit struct {
//	InputImage    string `json:"input_image"`
//	Mask          string `json:"mask"`
//	Module        string `json:"module"`
//	Model         string `json:"model"`
//	Weight        int    `json:"weight"`
//	ResizeMode    int    `json:"resize_mode"`
//	Lowvram       string `json:"lowvram"`
//	ProcessorRes  int    `json:"processor_res"`
//	ThresholdA    int    `json:"threshold_a"`
//	ThresholdB    int    `json:"threshold_b"`
//	Guidance      int    `json:"guidance"`
//	GuidanceStart int    `json:"guidance_start"`
//	GuidanceEnd   int    `json:"guidance_end"`
//	Guessmode     string `json:"guessmode"`
//	PixelPerfect  string `json:"pixel_perfect"`
//	ControlMode   int    `json:"control_mode"`
//}
