package service

import (
	"center-ai/enums"
	"center-ai/model"
	wechatofficial "center-ai/service/wechat/official"
	"center-ai/utils/logger"
	"center-ai/utils/myredis"
	"center-ai/utils/tools"
	"errors"
	"fmt"
	officialaccountmessage "github.com/silenceper/wechat/v2/officialaccount/message"
	"runtime"
	"time"
)

type _message struct {
	WxMessageCode   map[string]string
	WxSubscribeCode map[string]string
	SmsMessageCode  map[string]string
}

func init() {
	MessageService.WxMessageCode = map[string]string{
		"StorageWarn":           "_s_YLXaSJ6J9UjF61ml4uD3lk_edFzeYgfQRX0Tel50", //存储预警通知
		"InstanceRunning":       "-HL41y6ciDKfkE3M4C1puvqLwN3YyMDUxcCzYf7DKFk", //实例运行提醒
		"RechargeSuccess":       "dlBJ_vMbl5b4Pj40t1j_VIntvDyorprf65SrV3rzV8Q", //充值成功提醒
		"LowAmount":             "AkC9_Grojcrs1ZbJgRgcjhVlWZEXA2vxJHNjoB4eY8E", //余额不足提醒 amount time
		"Debt":                  "AkC9_Grojcrs1ZbJgRgcjo7lUC4L1qHTDgQxH05V7cI", //余额不足提醒 amount time
		"DebtAndDestroyStorage": "AkC9_Grojcrs1ZbJgRgcjhVlWZEXA2vxJHNjoB4eY8E", //欠费储存销毁
		"DebtAndLowAmount":      "AkC9_Grojcrs1ZbJgRgcjhVlWZEXA2vxJHNjoB4eY8E", //欠费余额即将不足
	}
	MessageService.WxSubscribeCode = map[string]string{
		"LowAmount": "RtjF_7XWKPeB2jNGJBG39xDRofM6mkmYSxlYeqUybN8", //余额不足提醒amount1  thing2
	}
	MessageService.SmsMessageCode = map[string]string{
		"DebtAndDestroyStorage": "SMS_475770543", //欠费储存销毁
	}
}

var MessageService _message

func (obj *_message) Handle() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	logger.Info("MessageRun 开始循环")

	var message model.Message
	lastAutoId := uint(0)
	pageSize := 20

	for {
		var ary = make([]model.Message, 0)
		if err := message.ListForSend(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info("MessageRun 获取到", len(ary), "条需要处理的数据")
		for i := 0; i < len(ary); i++ {
			inst := ary[i]
			lastAutoId = inst.ID
			if inst.RegularTime.After(tools.NationalTime()) {
				if inst.RegularTime.After(time.Now()) {
					continue
				}
			}
			if _, err := obj.PushJson(inst.ID); err != nil {
				msg := "添加到发送队列失败"
				logger.Error(msg, err)
			} else {
				//msg = "已加入到发送队列"
			}
			//if err := obj.Send(inst.ID); err != nil {
			//	logger.Error("send messageId：", inst.ID, " err:", err)
			//} else {
			//	logger.Info("send messageId：", inst.ID, " success")
			//}
		}
		if len(ary) < pageSize {
			break
		}
	}
	return nil
}

func (obj *_message) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("RunRedis奔溃:", e)
		}
	}()
	logger.Info("MessageService.Run 开始")
	testCount := 0
	for {
		value, err := obj.PopJson()
		//logger.Info("PopJson: val:", value, " err:", err)
		if err != nil {
			logger.Error(value, err)
			continue
		}
		if value == "" {
			testCount++
			//logger.Info("PopJson: testCount:", testCount)
			if testCount > 60 {
				testCount = 0
				if err := obj.Handle(); err != nil {
					logger.Error("PopJson: Handle err:", err)
				} else {
					logger.Info("PopJson: Handle success")
				}
			}
			//logger.Info("GetLocationByIp 为空")
			continue
		}
		if messageId := tools.String2Uint(value); messageId > 0 {
			logger.Info("MessageService messageId:", value)
			if err := obj.Send(messageId); err != nil {
				logger.Error(err)
			}
		} else {
			logger.Error("MessageService messageId:0 value:", value)
		}
	}
}

func (obj *_message) GetTemplateCode(method model.MessageMethodEnum, messageCode string) string {
	if method == model.MessageMethodSms {
		return obj.SmsMessageCode[messageCode]
	} else if method == model.MessageMethodWechat {
		return obj.WxMessageCode[messageCode]
	} else if method == model.MessageMethodWechatSub {
		return obj.WxSubscribeCode[messageCode]
	}
	return ""
}

func (obj *_message) Send(messageId uint) error {
	lockKey := fmt.Sprintf(enums.RedisKeyEnum.Kv+"Lock_MessageId%d", messageId)
	if myredis.RedisLock(lockKey, 1000) {
		defer myredis.RedisUnLock(lockKey)

		var message model.Message
		if err := message.GetById(messageId); err != nil {
			logger.Error("查询数据失败", err)
			return err
		}

		if message.Status != model.MessageStatusReady {
			err := errors.New("不是等待发送状态")
			logger.Error(err)
			return err
		}

		if message.RegularTime.After(tools.NationalTime()) {
			if message.RegularTime.After(time.Now()) {
				err := errors.New("定时时间未到，不发送")
				logger.Error(err)
				return err
			}
		}

		if err := message.SetStatusSending(); err != nil {
			logger.Error(err)
			return err
		}

		if message.Method == model.MessageMethodSms {
			sendParm := tools.GetMapFromJson(message.SendParm)
			content, err1 := AliSms.SendSms(message.SendTarget, message.TemplateCode, sendParm)
			if err := message.SetSendResult(content, err1, ""); err != nil {
				logger.Error("保存发送结果失败", err)
				return err
			}
		} else if message.Method == model.MessageMethodWechat {

			template := wechatofficial.WechatOfficialAccount.OfficialAccount.GetTemplate()

			if message.SendParm == "" {
				message.SendParm = "{}"
			}

			parm := tools.GetMapFromJson(message.SendParm)

			var data = map[string]*officialaccountmessage.TemplateDataItem{}
			if message.TemplateCode == "AkC9_Grojcrs1ZbJgRgcjo7lUC4L1qHTDgQxH05V7cI" {
				if parm["amount"] == "" || parm["time"] == "" {
					err := errors.New("缺少发送参数amount time")
					logger.Error(err)
					if err := message.SetSendResult("", err, ""); err != nil {
						logger.Error("保存发送结果失败", err)
						return err
					}
					return err
				}
				data = map[string]*officialaccountmessage.TemplateDataItem{
					"amount5": &officialaccountmessage.TemplateDataItem{Value: parm["amount"].(string)},
					"time7":   &officialaccountmessage.TemplateDataItem{Value: parm["time"].(string)},
				}
			} else if message.TemplateCode == "AkC9_Grojcrs1ZbJgRgcjhVlWZEXA2vxJHNjoB4eY8E" {
				if parm["amount"] == "" || parm["time"] == "" {
					err := errors.New("缺少发送参数amount time")
					logger.Error(err)
					if err := message.SetSendResult("", err, ""); err != nil {
						logger.Error("保存发送结果失败", err)
						return err
					}
					return err
				}
				data = map[string]*officialaccountmessage.TemplateDataItem{
					"amount5": &officialaccountmessage.TemplateDataItem{Value: parm["amount"].(string)},
					"time7":   &officialaccountmessage.TemplateDataItem{Value: parm["time"].(string)},
					"const3":  &officialaccountmessage.TemplateDataItem{Value: parm["const"].(string)},
				}
			} else if message.TemplateCode == "dlBJ_vMbl5b4Pj40t1j_VIntvDyorprf65SrV3rzV8Q" {
				//账号
				//{{character_string27.DATA}}
				//充值金额
				//{{amount5.DATA}}
				//账户余额
				//{{amount6.DATA}}
				//充值时间
				//{{time11.DATA}}
				//充值项目
				//{{thing16.DATA}}
				if parm["amount"] == "" || parm["time"] == "" {
					err := errors.New("缺少发送参数amount time")
					logger.Error(err)
					if err := message.SetSendResult("", err, ""); err != nil {
						logger.Error("保存发送结果失败", err)
						return err
					}
					return err
				}
				data = map[string]*officialaccountmessage.TemplateDataItem{
					"character_string27": &officialaccountmessage.TemplateDataItem{Value: parm["character_string"].(string)},
					"amount5":            &officialaccountmessage.TemplateDataItem{Value: parm["recharge_amount"].(string)},
					"amount6":            &officialaccountmessage.TemplateDataItem{Value: parm["amount"].(string)},
					"time11":             &officialaccountmessage.TemplateDataItem{Value: parm["time"].(string)},
					"thing16":            &officialaccountmessage.TemplateDataItem{Value: parm["thing"].(string)},
				}
			} else if message.TemplateCode == "-HL41y6ciDKfkE3M4C1puvqLwN3YyMDUxcCzYf7DKFk" {
				data = map[string]*officialaccountmessage.TemplateDataItem{
					"thing2":            &officialaccountmessage.TemplateDataItem{Value: parm["name"].(string)},
					"character_string3": &officialaccountmessage.TemplateDataItem{Value: parm["id"].(string)},
					"time4":             &officialaccountmessage.TemplateDataItem{Value: parm["time"].(string)},
					"thing5":            &officialaccountmessage.TemplateDataItem{Value: parm["reason"].(string)},
				}
			} else if message.TemplateCode == "_s_YLXaSJ6J9UjF61ml4uD3lk_edFzeYgfQRX0Tel50" {
				data = map[string]*officialaccountmessage.TemplateDataItem{
					"thing3":  &officialaccountmessage.TemplateDataItem{Value: parm["name"].(string)},
					"time2":   &officialaccountmessage.TemplateDataItem{Value: parm["time"].(string)},
					"thing19": &officialaccountmessage.TemplateDataItem{Value: parm["reason"].(string)},
				}
			}

			if len(data) == 0 {
				err := errors.New("未设置发送参数")
				logger.Error(err)
				if err := message.SetSendResult("", err, ""); err != nil {
					logger.Error("保存发送结果失败", err)
					return err
				}
				return err
			}

			templateMsg := officialaccountmessage.TemplateMessage{
				ToUser:     message.SendTarget,
				TemplateID: message.TemplateCode,
				Data:       data,
			}

			if msgId, err := template.Send(&templateMsg); err != nil {
				logger.Error(err)
				if err := message.SetSendResult("", err, fmt.Sprintf("%d", msgId)); err != nil {
					logger.Error("保存发送结果失败", err)
				}
				return err
			} else {
				logger.Info("发送成功 msgId:", msgId)
				if err := message.SetSendResult("", nil, fmt.Sprintf("%d", msgId)); err != nil {
					logger.Error("保存发送结果失败", err)
					return err
				}
			}
		} else if message.Method == model.MessageMethodWechatSub {

			subscribe := wechatofficial.WechatOfficialAccount.OfficialAccount.GetSubscribe()

			if message.SendParm == "" {
				message.SendParm = "{}"
			}

			parm := tools.GetMapFromJson(message.SendParm)

			var data = map[string]*officialaccountmessage.SubscribeDataItem{}
			if message.TemplateCode == "RtjF_7XWKPeB2jNGJBG39xDRofM6mkmYSxlYeqUybN8" {
				if parm["amount"] == "" || parm["thing"] == "" {
					err := errors.New("缺少发送参数amount time")
					logger.Error(err)
					if err := message.SetSendResult("", err, ""); err != nil {
						logger.Error("保存发送结果失败", err)
						return err
					}
					return err
				}
				data = map[string]*officialaccountmessage.SubscribeDataItem{
					"amount1": &officialaccountmessage.SubscribeDataItem{Value: parm["amount"].(string)},
					"thing2":  &officialaccountmessage.SubscribeDataItem{Value: parm["thing"].(string)},
				}
			}
			if len(data) == 0 {
				err := errors.New("未设置发送参数")
				logger.Error(err)
				if err := message.SetSendResult("", err, ""); err != nil {
					logger.Error("保存发送结果失败", err)
					return err
				}
				return err
			}

			subscribeMessage := officialaccountmessage.SubscribeMessage{
				ToUser:     message.SendTarget,
				TemplateID: message.TemplateCode,
				Data:       data,
			}

			if err := subscribe.Send(&subscribeMessage); err != nil {
				logger.Error("请求发送失败", err)
				if err := message.SetSendResult("", err, ""); err != nil {
					logger.Error("保存发送结果失败", err)
				}
				return err
			} else {
				logger.Info("请求发送成功 ")
				if err := message.SetSendResult("", nil, ""); err != nil {
					logger.Error("保存发送结果失败", err)
					return err
				}
			}
		} else {
			err := errors.New("未设置的提醒方式")
			return err
		}
		return nil
	} else {
		err := errors.New("已经在发送中")
		logger.Error(lockKey, err)
		return err
	}

}

func (ls *_message) PopJson() (string, error) {

	value, err := myredis.BRPop(enums.RedisKeyEnum.NeedSendMessage)
	return value, err
}

func (ls *_message) PushJson(messageId uint) (int64, error) {

	size, err := myredis.LPush(enums.RedisKeyEnum.NeedSendMessage, fmt.Sprintf("%d", messageId))
	if err != nil {
		logger.Error(err)
	}
	return size, err
}
