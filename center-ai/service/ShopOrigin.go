package service

import (
	"center-ai/enums"
	"center-ai/model"
	"center-ai/utils/config"
	"center-ai/utils/logger"
	"center-ai/utils/myimg"
	"center-ai/utils/tools"
	"errors"
	"fmt"
	"image"
	"image/color"
	"strings"
	"time"
)

type shoporigin_ struct {
}

var ShopOrigin shoporigin_

func (obj shoporigin_) GetSdSize(w int, h int) (int, int) {
	maxWidth := 768
	maxHeight := 1024
	// 计算缩放比例
	widthScale := float64(maxWidth) / float64(w)
	heightScale := float64(maxHeight) / float64(h)

	// 选择更小的缩放比例，以确保宽度和高度都不大于最大值
	scale := widthScale
	if heightScale < scale {
		scale = heightScale
	}

	// 缩放宽度和高度
	newW := int(float64(w) * scale)
	newH := int(float64(h) * scale)
	return newW, newH
}

func (obj shoporigin_) ReadyImg(originId uint) error {
	if err := obj.GenBodyImg(originId); err != nil {
		logger.Error(err)
	}
	if err := obj.GenBodyBgImg(originId); err != nil {
		return err

	}
	return nil
}

func (obj shoporigin_) GenBodyImg(originId uint) error {
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(originId); err != nil {
		logger.Error(err)
		return err
	}
	if shopOrigin.CropImg == "" {
		err := errors.New("裁剪图不存在")
		logger.Error(err)
	}
	if bodyImgUrl, err := AliMask.SegmentHDBody(config.DiffusionFilePath + shopOrigin.CropImg); err != nil {
		logger.Error(err)
		return err
	} else {
		bodyPath := ImgPath.GetBodyPath(shopOrigin.OriginImg)
		absoluteBodyPath := config.DiffusionFilePath + bodyPath

		if er := tools.SaveImgFromUrl(bodyImgUrl, absoluteBodyPath); er != nil {
			logger.Error(er)
			return er
		}
		if er := shopOrigin.SetBodyImg(bodyPath); er != nil {
			logger.Error(er)
			return er
		}
	}

	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBigHeadAndBackColor {
		if bodyImgUrl, err := AliMask.SegmentHDBody(config.DiffusionFilePath + shopOrigin.OriginImg); err != nil {
			logger.Error(err)
			return err
		} else {
			bodyPath := ImgPath.GetOriginBodyPath(shopOrigin.OriginImg)
			absoluteBodyPath := config.DiffusionFilePath + bodyPath

			if er := tools.SaveImgFromUrl(bodyImgUrl, absoluteBodyPath); er != nil {
				logger.Error(er)
				return er
			}
		}
	}
	return nil
}

func (obj shoporigin_) GenBodyBgImg(originId uint) error {
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(originId); err != nil {
		logger.Error(err)
		return err
	}
	if shopOrigin.CropImg == "" {
		err := errors.New("裁剪图不存在")
		logger.Error(err)
	}
	absoluteBodyPath := config.DiffusionFilePath + shopOrigin.BodyImg
	img, _, err := myimg.FileToImg(absoluteBodyPath)
	if err != nil {
		logger.Error(err)
		return err
	}

	logger.Info("开始换背景", shopOrigin.ID, "   ", shopOrigin.BodyBgColor)
	bodyBgImg := myimg.TurnToBackgroundColor(img, shopOrigin.BodyBgColor)

	bodyBgPath := ImgPath.GetBodyBgPath(shopOrigin.OriginImg)
	absoluteBodyBgPath := config.DiffusionFilePath + bodyBgPath

	if err := myimg.ImgToPngFile(bodyBgImg, absoluteBodyBgPath); err != nil {
		logger.Error(err)
		return err
	}
	if err := shopOrigin.SetBodyBgImg(bodyBgPath); err != nil {
		logger.Error(err)
		return err
	}

	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBigHeadAndBackColor {
		originBodyPath := ImgPath.GetOriginBodyPath(shopOrigin.OriginImg)
		absoluteOriginBodyPath := config.DiffusionFilePath + originBodyPath

		originBodyImg, _, err := myimg.FileToImg(absoluteOriginBodyPath)
		if err != nil {
			logger.Error(err)
			return err
		}

		originBodyBgImg := myimg.TurnToBackgroundColor(originBodyImg, "")

		if err := myimg.ImgToPngFile(originBodyBgImg, absoluteOriginBodyPath); err != nil {
			logger.Error(err)
			return err
		}
	}
	return nil
}

func (obj shoporigin_) MergeOutputImg12(originId uint, outImgMd5 string) error { //根据主体图去除带颜色的背景，效果不是很好，暂时放弃

	var outImg model.ShopOut
	if err := outImg.GetByMd5(outImgMd5); err != nil {
		logger.Error(err)
		return err
	}
	outImgPath := outImg.Path
	if outImgPath == "" {
		err := errors.New("图片未生成")
		logger.Error(err)
		return err
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(originId); err != nil {
		logger.Error(err)
		return err
	}

	bodyImg, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.BodyImg) //透明主体图
	if err != nil {
		logger.Error(err)
		return err
	}

	//mask, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.MaskImg) //蒙住头的蒙版图
	//if err != nil {
	//	logger.Error(err)
	//	return err
	//}

	outputImg, _, err := myimg.FileToImg(config.DiffusionFilePath + outImg.Path) //Sd出的图
	if err != nil {
		logger.Error(err)
		return err
	}

	//重合图片，在outputImg上取出在bodyImg上不透明的点
	// 创建一个新的图片，用于存储提取的内容
	outputBodyImg := image.NewRGBA(bodyImg.Bounds())
	// 遍历原图的每个像素
	for x := 0; x < bodyImg.Bounds().Dx(); x++ {
		for y := 0; y < bodyImg.Bounds().Dy(); y++ {

			// 获取透明主体图的像素颜色
			_, _, _, oa := bodyImg.At(x, y).RGBA()

			r, g, b, a := outputImg.At(x, y).RGBA() //获取SD出图的颜色
			if oa == 0 {
				outputBodyImg.Set(x, y, color.RGBA{0, 0, 0, 0})
			} else {
				outputBodyImg.Set(x, y, color.RGBA{uint8(r), uint8(g), uint8(b), uint8(a)})
			}
		}
	}

	outputBodyPath := config.DiffusionFilePath + ImgPath.GetOutputBodyPath(outImg.Path)
	if err := myimg.ImgToPngFile(outputBodyImg, outputBodyPath); err != nil {
		logger.Error(err)
		return err
	}

	resizeImg := myimg.ResizeImg(shopOrigin.CropWidth, shopOrigin.CropHeight, outputBodyImg, false)
	originBodyImg, _, err := myimg.FileToImg(config.DiffusionFilePath + ImgPath.GetOriginBodyPath(shopOrigin.OriginImg))
	if err != nil {
		logger.Error(err)
		return err
	}
	mergeImg := myimg.MergeImg(originBodyImg, resizeImg, shopOrigin.CropX, shopOrigin.CropY)
	bgMergeImg := myimg.TurnToBackgroundColor(mergeImg, "255,255,255,255") //将透明背景转换为白底

	mergePath := ImgPath.GetSdMergePath(outImgPath)
	mergeAbsolutePath := config.DiffusionFilePath + mergePath
	if err := myimg.ImgToPngFile(bgMergeImg, mergeAbsolutePath); err != nil {
		logger.Error(err)
	} else {
		if err1 := outImg.SetMergePath(mergePath); err1 != nil {
			logger.Error(err1, mergePath)
		}
	}
	return nil
}

func (obj shoporigin_) MergeOutputImg(originId uint, outImgMd5 string) error {

	var outImg model.ShopOut
	if err := outImg.GetByMd5(outImgMd5); err != nil {
		logger.Error(err)
		return err
	}
	outImgPath := outImg.Path
	if outImgPath == "" {
		err := errors.New("图片未生成")
		logger.Error(err)
		return err
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(originId); err != nil {
		logger.Error(err)
		return err
	}
	bodyImgUrl, er := AliMask.SegmentHDBody(config.DiffusionFilePath + outImgPath)
	if er != nil {
		logger.Error(er)
		time.Sleep(time.Second * 1)
		bodyImgUrl, er = AliMask.SegmentHDBody(config.DiffusionFilePath + outImgPath)
	}
	if er != nil {
		logger.Error("第二次尝试 ", er)
		return er
	}

	bodyPath := ImgPath.GetOutputBodyPath(outImgPath) //获取SD图的透明主体图路径
	absoluteBodyPath := config.DiffusionFilePath + bodyPath

	if err := tools.SaveImgFromUrl(bodyImgUrl, absoluteBodyPath); err != nil {
		logger.Error(err)
		return err
	}

	outImgBody, _, err := myimg.FileToImg(absoluteBodyPath)
	if err != nil {
		logger.Error(err)
		return err
	}
	resizeImg := myimg.ResizeImg(shopOrigin.CropWidth, shopOrigin.CropHeight, outImgBody, false)

	originAbsolutePath := config.DiffusionFilePath + ImgPath.GetOriginBodyPath1(shopOrigin.OriginImg) //获取原始图的透明主体图路径
	if b, _ := tools.PathFileExists(originAbsolutePath); b == false {
		originAbsolutePath = config.DiffusionFilePath + ImgPath.GetOriginBodyPath(shopOrigin.OriginImg)
	}
	originImg, _, err := myimg.FileToImg(originAbsolutePath)
	if err != nil {
		logger.Error(err, originAbsolutePath)
		return err
	}

	c := false
	if b, _ := tools.PathFileExists(config.DiffusionFilePath + ImgPath.GetOriginBodyMapInvertPath(shopOrigin.OriginImg)); b {
		c = true
		mapImg, _, err := myimg.FileToImg(config.DiffusionFilePath + ImgPath.GetOriginBodyMapInvertPath(shopOrigin.OriginImg))
		if err != nil {
			logger.Error(err, ImgPath.GetOriginBodyMapInvertPath(shopOrigin.OriginImg))
			return err
		} else {
			originImg = mapImg
		}
	}

	mergeImg := myimg.MergeImg(originImg, resizeImg, shopOrigin.CropX, shopOrigin.CropY)
	if c {
		mergeMask := myimg.MaskAllFromBody(mergeImg, 58981)
		mergeMask = myimg.MaskResize(mergeMask, -2)
		if err := myimg.ImgToPngFile(mergeMask, config.DiffusionFilePath+ImgPath.GetSdMergeMaskPath(outImgPath)); err != nil {
			logger.Error(err)
		}

		mergeMap := myimg.MapFromMask(mergeImg, mergeMask, false, "255,255,255,255")
		if err := myimg.ImgToPngFile(mergeMap, config.DiffusionFilePath+ImgPath.GetSdMergeMapPath(outImgPath)); err != nil {
			logger.Error(err)
		}

		mergeBg := myimg.TurnToBackgroundColor(mergeImg, shopOrigin.BodyBgColor)
		if err := myimg.ImgToPngFile(mergeBg, config.DiffusionFilePath+ImgPath.GetSdMergeBgPath(outImgPath)); err != nil {
			logger.Error(err)
		}
	}

	mergeImg = myimg.TurnToBackgroundColor(mergeImg, "255,255,255,255")
	mergePath := ImgPath.GetSdMergePath(outImgPath) //获取SD图的合并图路径
	mergeAbsolutePath := config.DiffusionFilePath + mergePath

	chunk := myimg.TEXtChunk{
		Key:   "parameters",
		Value: fmt.Sprintf("MadeIn: cyuai, SN: %s", outImg.Md5),
	}
	if err = myimg.WriteTextChunkToPng1(mergeImg, mergeAbsolutePath, chunk); err != nil {
		logger.Error(err)
		return err
	} else {
		if err1 := outImg.SetMergePath(mergePath); err1 != nil {
			logger.Error(err1, mergePath)
			return err1
		}
	}

	if c {
		if err := ShopOrigin.PushChangeBackground(outImgMd5); err != nil {
			logger.Error(err)
		}
	}
	//if err := myimg.ImgToPngFile(mergeImg, mergeAbsolutePath); err != nil {
	//	logger.Error(err)
	//	return err
	//} else {
	//	if err1 := outImg.SetMergePath(mergePath); err1 != nil {
	//		logger.Error(err1, mergePath)
	//		return err
	//	}
	//}

	//if chunks, err := myimg.ReadTextChunksFromPng(config.DiffusionFilePath + outImgPath); err == nil {
	//	if len(chunks) > 0 && chunks[0].Key == "parameters" {
	//		chunks[0].Value = chunks[0].Value + fmt.Sprintf("\nMadeType: %d, ShopFaceId: %d, UserId: %d, OutMd5: %s", outImg.MadeType, outImg.ShopFaceId, shopOrigin.UserId, outImg.Md5)
	//		if er := myimg.WriteTextChunkToPng1(mergeImg, mergeAbsolutePath, chunks[0]); er != nil {
	//			logger.Error(err)
	//		}
	//	}
	//} else {
	//	logger.Error(err)
	//}

	return nil
}

func (obj shoporigin_) PushChangeBackground(outImgMd5 string) error {
	//图生图接口 换纯色背景图 需要带颜色的主题图 主体黑白蒙版图
	msg := ""
	parameters := make(map[string]interface{})
	imagesPath := make(map[string]string)

	fields := make(map[string]interface{})
	fields["sd_server"] = ""
	fields["sdapi"] = "Img2img"

	logger.Info("更换纯色背景逻辑开始")
	var sdParam model.SdParm
	if err := sdParam.GetById(uint(5)); err != nil {
		logger.Error(err)
		msg = "获取模特数据失败"
		return err
	}

	var outImg model.ShopOut
	if err := outImg.GetByMd5(outImgMd5); err != nil {
		logger.Error(err)
		return err
	}

	useControlnetUnits := make([]map[string]interface{}, 0)
	if controlnetUnit, err := SdClient.GetControlnetUnit("lineart_realistic"); err != nil {
		logger.Error(err)
		return err
	} else {
		controlnetUnit["pixel_perfect"] = true
		useControlnetUnits = append(useControlnetUnits, controlnetUnit)
	}
	if controlnetUnit, err := SdClient.GetControlnetUnit("openpose"); err != nil {
		logger.Error(err)
		return err
	} else {
		useControlnetUnits = append(useControlnetUnits, controlnetUnit)
	}

	if tmpParameters, err := SdClient.GenParameters1(sdParam.Parameters, useControlnetUnits); err != nil {
		logger.Error("sdParamId:", sdParam.ID, err)
		return err
	} else {
		parameters = tmpParameters
	}

	parameters["sampler_name"] = "DDIM" //纯色背景用这个
	parametersStr := tools.GetJsonFromMap(parameters)

	if strings.Contains(parametersStr, "{==={InitImg}===}") {
		imagesPath["InitImg"] = ImgPath.GetSdMergeBgPath(outImg.Path)
	}
	if strings.Contains(parametersStr, "{==={MaskImg}===}") {
		imagesPath["MaskImg"] = ImgPath.GetSdMergeMaskPath(outImg.Path)
	}
	imagesPath["lineart_realistic_input_image"] = ImgPath.GetSdMergeMapPath(outImg.Path)
	imagesPath["openpose_input_image"] = ImgPath.GetSdMergeBgPath(outImg.Path)

	fields["custom_app"] = "shopshow"
	fields["custom_data"] = ""
	fields["custom_md5"] = outImg.Md5                               //"{==={CustomMd5}===}"
	fields["custom_path"] = ImgPath.GetSdMergeSdBgPath(outImg.Path) // ImgPath.GetOutputDirectory(shopOrigin.OriginImg) + "{==={CustomMd5}===}.png"
	fields["parameters"] = tools.GetJsonFromMap(parameters)
	fields["images_path"] = imagesPath

	//options := make(map[string]interface{})
	//options["need_info"] = true
	//fields["options"] = options

	pushJson := tools.GetJsonFromMap(fields)

	logger.Info("pushjson:", pushJson)
	if str, err := SdClient.Sd2img(pushJson); err != nil {
		logger.Error(err, str)
		return err
	} else {
		logger.Info("Post Res:", str)
	}
	if msg != "" {

	}
	return nil
}

func (obj shoporigin_) CheckSdGen(outImg model.ShopOut) (map[string]interface{}, error) {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("CheckSdGen奔溃:", e, outImg.ID, "  "+outImg.Md5)
		}
	}()
	mWarn := make(map[string]interface{})
	parametersStr, err := SdClient.GetPngInfo(config.DiffusionFilePath + outImg.Path)
	if err != nil {
		return mWarn, err
	}
	mParm, err := SdClient.AnalyzePngInfo(parametersStr)
	if err != nil {
		return mWarn, err
	}

	pushJson := outImg.PushJson
	if pushJson != "" {
		mPush := tools.GetMapFromJson(pushJson)

		if _, ok := mPush["parameters"]; !ok {
			return mWarn, fmt.Errorf("parameters 字段不存在")
		}
		parameters := tools.GetMapFromJson(mPush["parameters"].(string))
		prompt := ""
		if _, ok := parameters["prompt"]; ok {
			prompt = parameters["prompt"].(string)
		}

		modelName := ""
		if _, ok := parameters["override_settings"]; ok {
			overrideSettings := parameters["override_settings"].(map[string]interface{})
			if _, ok1 := overrideSettings["sd_model_checkpoint"]; ok1 {
				modelName = overrideSettings["sd_model_checkpoint"].(string)
			}
		}
		modelName1 := ""
		if _, ok := mParm["Model"]; ok {
			modelName1 = mParm["Model"].(string)
		}
		if modelName == "" || modelName != modelName1 {
			mWarn["model"] = fmt.Sprintf("主模型不匹配 输入模型%s 输出模型%s", modelName, modelName1)
		}

		loraHashes := ""
		if _, ok := mParm["Lora hashes"]; ok {
			loraHashes = mParm["Lora hashes"].(string)
		}

		aryLora := SdClient.GetLoras(prompt)
		for idx, lora := range aryLora {
			if !strings.Contains(loraHashes, lora) {
				mWarn[fmt.Sprintf("lora%d", idx)] = fmt.Sprintf("Lora:%s 未生效[%s]", lora, loraHashes)
			}
		}
		if loraHashes != "" && len(aryLora) == 0 {
			mWarn[fmt.Sprintf("lora")] = fmt.Sprintf("输入输出Lora不匹配:输入Lora为空 输出Lora hashes:%s", loraHashes)
		}

		controlNets := make([]map[string]interface{}, 0)
		if _, ok := mParm["ControlNets"]; ok {
			controlNets = mParm["ControlNets"].([]map[string]interface{})
		}

		args := make([]interface{}, 0)
		if _, ok := parameters["alwayson_scripts"]; ok {
			alwayson_scripts := parameters["alwayson_scripts"].(map[string]interface{})
			if _, ok := alwayson_scripts["controlnet"]; ok {
				controlnet := alwayson_scripts["controlnet"].(map[string]interface{})
				if _, ok := controlnet["args"]; ok {
					args = controlnet["args"].([]interface{})
				}
			}
		}

		if len(controlNets) != len(args) {
			mWarn["ControlNet"] = fmt.Sprintf("ControlNet输入输出数量不一致 输入%d 输出%d", len(args), len(controlNets))
		} else {
			for idx, arg1 := range args {
				arg := arg1.(map[string]interface{})
				if _, ok := arg["module"]; ok {
					module := arg["module"].(string)
					exists := false
					for idy, control := range controlNets {
						if _, ok := control["Module"]; ok {
							if module == control["Module"].(string) {
								exists = true
								break
							}
						} else {
							mWarn[fmt.Sprintf("ControlNet_Module %d", idy)] = fmt.Sprintf("ControlNet参数错误 输出Module不存在")
						}
					}
					if exists == false {
						mWarn[fmt.Sprintf("ControlNet_Exist%d", idx)] = fmt.Sprintf("ControlNet输出未检测到 module:%s", module)
					}
				} else {
					mWarn[fmt.Sprintf("ControlNet_modle%d", idx)] = fmt.Sprintf("ControlNet参数错误 输入module不存在")
				}
			}
		}
	}
	return mWarn, nil

}
