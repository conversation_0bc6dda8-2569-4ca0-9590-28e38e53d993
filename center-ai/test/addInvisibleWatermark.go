package main

import (
	"image"
	"image/color"
	"image/jpeg"
	"os"
)

func loadImage(filename string) (image.Image, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	img, _, err := image.Decode(file)
	if err != nil {
		return nil, err
	}

	return img, nil
}

func saveImage(filename string, img image.Image) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	return jpeg.Encode(file, img, nil)
}

func embedWatermark(img image.Image, text string) image.Image {
	// 将文本转换为二进制
	binaryText := textToBinary(text)

	// 获取图像的像素
	pixels := getPixels(img)

	// 将水印嵌入到图像的 LSB（最低有效位）
	for i, bit := range binaryText {
		if bit == '1' {
			pixels[i] |= 1
		} else {
			pixels[i] &= 0xFE
		}
	}

	// 生成新的图像
	newImg := setPixels(img, pixels)

	return newImg
}

func textToBinary(text string) []byte {
	var result []byte
	for _, char := range text {
		for i := 7; i >= 0; i-- {
			bit := (char >> uint(i)) & 1
			result = append(result, byte('0'+bit))
		}
	}
	return result
}

func getPixels(img image.Image) []byte {
	bounds := img.Bounds()
	width, height := bounds.Dx(), bounds.Dy()
	var pixels []byte

	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			_, _, _, a := img.At(x, y).RGBA()
			// 取 alpha 通道的最低有效位
			pixels = append(pixels, byte(a&1))
		}
	}

	return pixels
}

func setPixels(img image.Image, pixels []byte) image.Image {
	bounds := img.Bounds()
	width, height := bounds.Dx(), bounds.Dy()
	newImg := image.NewRGBA(bounds)
	index := 0

	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			r, g, b, a := img.At(x, y).RGBA()
			// 用新的 alpha 通道值替换
			alpha := uint8((a >> 8) | (uint32(pixels[index]) << 7))
			newImg.Set(x, y, color.RGBA{uint8(r >> 8), uint8(g >> 8), uint8(b >> 8), alpha})
			index++
		}
	}
	return newImg
}
