onUiLoaded(() => {
  document.title = '��������AIѵ��������ͼ';

  let n = 0;
  const f = () => {
    const tabs = document.getElementById('txt2img_extra_tabs');
    if (!tabs) {
      if (n < 10) {
        n++;
        setTimeout(f, 1000);
      }
      return;
    }

    Array.from(tabs.querySelectorAll(':scope > .tab-nav:first-child > button')).forEach(el => {
      if (el.textContent.trim() === 'LoRA') {
        el.classList.add('selected');
        el.click();
      } else {
        el.classList.remove('selected');
      }
    });
    Array.from(tabs.querySelectorAll(':scope > .tabitem')).forEach(el => el.style.display = el.id === 'txt2img_lora' ? 'block' : 'none');
    Array.from(tabs.querySelectorAll(':scope > .tab-nav:first-child > .extra-networks-controls-div > .extra-network-control')).forEach(el => el.style.display = el.id === 'txt2img_lora_controls' ? 'block' : 'none');

    setTimeout(() => {
      document.getElementById('txt2img_lora_extra_refresh').click();
      let n = 0;
      const f = () => {
        const a = Array.from(document.getElementById('txt2img_lora_cards').querySelectorAll('.card'));
        if (a.length > 0 && a[a.length - 1].dataset.name) {
          cardClicked('txt2img', '<lora:' + a[a.length - 1].dataset.name + ':' + opts.extra_networks_default_multiplier + '>', '', false);
        } else if (n < 5) {
          n++;
          setTimeout(f, 1000);
        }
      }
      setTimeout(f, 1000);
    });
  }

  setTimeout(f, 1000);
});

window.addEventListener('beforeunload', e => {
  fetch('https://www.chenyu.cn/api/v1/train/run/' + location.hostname.split('.')[0] + '?action=cancel%20generate', { method: 'POST' });
  e.preventDefault();
});
