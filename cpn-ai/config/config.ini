[server]
Bundle1 = suanyun.cyuai.aigc
Bundle = cn.suanyun.www
AppMode = debug
Env = dev
RunTimer = true
HttpPort  = :6002
#AesKey = myverystrongpass
AesKey = cyuaisuanyun0712
JwtKey = cbD88c01wb12

PrivateStorage = /Users/<USER>/mnt/smb/suanyun-user/

DiffusionFilePath = /Users/<USER>/stable-diffusion-webui/aigc-output/
DiffusionDomain = https://img.cyuai.com/
Domain = https://aigc.cyuai.com/
CenterServer1 = http://localhost:5101/api/
CenterServer2 = http://127.0.0.1:5101/api/
CenterServer3 = https://preview.cyuai.com/api/
CenterServer = http://*************:5101/api/

AccessKeyId = LTAI5tGmiupcZDwcgABKLCBW
AccessKeySecret = ******************************

FileStorageInterfaceUrl = http://************:6001

DataRegion = 1,2
#[生产]
[database生产]
SQL_DSN = cpnuser:A6fDXsTqNJqai@tcp(*************:3306)/cpn-ai?charset=utf8mb4&parseTime=True&loc=Local
REDIS_CONN_STRING = redis://:Dm2mrAHN@*************:6379/0

#测试
[database]
SQL_DSN = cpnuser:CmCaGP2PTCi4cnhp@tcp(*************:3306)/cpn-ai?charset=utf8mb4&parseTime=True&loc=Local
REDIS_CONN_STRING = redis://:Dm2mrAHN@*************:6379/0


