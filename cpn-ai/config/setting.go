package config

import (
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"errors"
	"gopkg.in/ini.v1"
	"path/filepath"
	"runtime"
)

var (
	Bundle       string
	CenterServer string
	AppMode      string
	Env          string
	RunTimer     bool
	HttpPort     string
	JwtKey       string
	AesBlock     cipher.Block

	PrivateStorage string

	DiffusionApi    string
	Txt2ImgFilePath string
	TempImgFilePath string

	StaticFilePath    string
	DiffusionFilePath string
	DiffusionDomain   string
	Domain            string

	TmpFilePath  string
	SaveFilePath string

	AccessKeyId     string
	AccessKeySecret string

	WeixinAppId  string
	WeixinSecret string

	SQL_DSN                 string
	REDIS_CONN_STRING       string
	FileStorageInterfaceUrl string
)

func init() {
	//execPath, err := os.Executable()
	//if err != nil {
	//	log.Fatalf("获取程序路径错误: %v", err)
	//}
	//execDir := filepath.Dir(execPath)
	//fmt.Printf("程序运行路径: %s\n", execDir)

	//configPath := filepath.Join(execDir, "config", "config.ini")
	//file, err := ini.Load(configPath)
	//if err != nil {
	//	log.Fatalf("配置文件读取错误: %v", err)
	//}
	file, err := ini.Load("./config/config.ini")
	if err != nil {
		// 获取当前源文件的目录
		if _, filename, _, ok := runtime.Caller(0); ok {
			sourceDir := filepath.Dir(filename)
			configPath := filepath.Join(sourceDir, "config.ini")
			if file, err = ini.Load(configPath); err != nil {
				logger.Error("配置文件读取错误", err)
			}
		}
	}
	if file != nil {
		LoadServer(file)
	}

}

func LoadServer(file *ini.File) {
	Bundle = file.Section("server").Key("Bundle").MustString("")
	CenterServer = file.Section("server").Key("CenterServer").MustString("")
	AppMode = file.Section("server").Key("AppMode").MustString("")
	Env = file.Section("server").Key("Env").MustString("")
	RunTimer = file.Section("server").Key("RunTimer").MustBool(false)
	HttpPort = file.Section("server").Key("HttpPort").MustString("")
	JwtKey = file.Section("server").Key("JwtKey").MustString("")
	aesKey := file.Section("server").Key("AesKey").MustString("")
	if len(aesKey) != 16 {
		err := errors.New("aesKey位数不正确")
		logger.Fatal(err, "len:", len(aesKey))
		panic(err)
	}
	if tmp, err := aes.NewCipher([]byte(aesKey)); err != nil {
		logger.Error(err)
		panic(err)
	} else {
		AesBlock = tmp
	}

	//PrivateStorage = file.Section("server").Key("PrivateStorage").MustString("")
	PrivateStorage = "/mnt/user-data/store0/"
	if Env == enums.EnvEnum.DEV {
		PrivateStorage = file.Section("server").Key("PrivateStorage").MustString("")
	}
	AccessKeyId = file.Section("server").Key("AccessKeyId").MustString("")
	AccessKeySecret = file.Section("server").Key("AccessKeySecret").MustString("")

	StaticFilePath = file.Section("server").Key("StaticFilePath").MustString("")
	DiffusionFilePath = file.Section("server").Key("DiffusionFilePath").MustString("")
	DiffusionDomain = file.Section("server").Key("DiffusionDomain").MustString("")
	Domain = file.Section("server").Key("Domain").MustString("")
	FileStorageInterfaceUrl = file.Section("server").Key("FileStorageInterfaceUrl").MustString("")

	SQL_DSN = file.Section("database").Key("SQL_DSN").MustString("")
	REDIS_CONN_STRING = file.Section("database").Key("REDIS_CONN_STRING").MustString("")
}

func EncryptAes(text string) string {
	plaintext := []byte(text)
	ciphertext := make([]byte, len(plaintext))
	stream := cipher.NewCTR(AesBlock, make([]byte, AesBlock.BlockSize()))
	stream.XORKeyStream(ciphertext, plaintext)
	return base64.StdEncoding.EncodeToString(ciphertext)
}

func DecryptAes(text string) string {
	ciphertext, err := base64.StdEncoding.DecodeString(text)
	if err != nil {
		return ""
	}
	decrypted := make([]byte, len(ciphertext))
	stream := cipher.NewCTR(AesBlock, make([]byte, AesBlock.BlockSize()))
	stream.XORKeyStream(decrypted, ciphertext)
	return string(decrypted)
}
