package controller

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

type amountApi_ struct {
}

type addAmountReq struct {
	UserId   uint            `json:"user_id"`
	Amount   decimal.Decimal `json:"amount"`
	Operator string          `json:"operator"`
	Remark   string          `json:"remark"`
}

var AmountApi amountApi_

type balanceListReq struct {
	KW       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type balanceItemResp struct {
	ID             uint              `json:"-"`
	UserId         uint              `json:"-"`
	CreatedAt      jsontime.JsonTime `json:"created_at"'`
	OrderNo        string            `json:"order_no"`
	OccurredAmount decimal.Decimal   `json:"occurred_amount"`
	CardId         uint              `json:"-"`
	CardTxt        string            `json:"card_txt"`
	Show           string            `json:"show"`
}

func (obj amountApi_) GetBalance(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq balanceListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.PageSize < 1 {
		oReq.PageSize = 1
	}
	if oReq.PageSize > 50 {
		oReq.PageSize = 50
	}
	if oReq.Page < 1 {
		oReq.Page = 1
	}

	var amountBalance model.AmountBalance
	ary := make([]balanceItemResp, 0)
	if total, err := amountBalance.List(&ary, claims.UserId, oReq.KW, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询出错"
		logger.Error(msg, err)
		return
	} else {

		for i := 0; i < len(ary); i++ {
			if ary[i].CardId > 0 {
				ary[i].CardTxt = "[算力卡]"
			}
		}

		result["items"] = ary
		result["total"] = total
	}
	code = 0
}

func (obj amountApi_) BalanceCheck(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims == nil {
	//	msg = "请先登录"
	//	code = 2
	//	return
	//}

	var oReq addAmountReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.UserId <= 0 {
		msg = "用户ID错误"
		logger.Error(msg)
		return
	}

	if oReq.UserId == 10001000 {
		if err := service.ChargingService.BalanceCheckAllUser(); err != nil {
			msg = "用户流水验证不通过"
			result["err"] = err.Error()
			logger.Error(msg, oReq.UserId, err)
			return
		}
		msg = "全部用户流水验证通过"
	} else {
		if err := service.ChargingService.BalanceCheck(oReq.UserId); err != nil {
			msg = "用户流水验证不通过"
			result["err"] = err.Error()
			logger.Error(msg, oReq.UserId, err)
			return
		}
		msg = "用户流水验证通过"
	}

	code = 0
}

func (obj amountApi_) AddQuick(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq addAmountReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var user model.User
	str := strconv.FormatUint(uint64(oReq.UserId), 10)
	if utils.IsMobile(str) {
		if err := user.GetByMobile(str); err != nil {
			logger.Error(err)
			msg = "手机号码不正确"
			return
		}
	} else {
		if err := user.GetById(oReq.UserId); err != nil {
			logger.Error(err)
			msg = "用户ID不正确"
			return
		}
	}

	if oReq.Amount.Cmp(decimal.NewFromInt(0)) <= 0 {
		msg = "请输入金额"
		return
	}

	if len(oReq.Operator) == 0 {
		msg = "请输入操作员"
		return
	}
	if len(oReq.Remark) > 200 {
		msg = "备注内容不能大于200个字符"
		return
	}

	balance := model.AmountBalance{
		UserId:         user.ID,
		OperatorId:     claims.UserId,
		OccurredAmount: oReq.Amount,
	}

	orderNo, err := model.OrderNo.NewByOrderType(enums.OrderTypeEnum.ManagerAdd, 0)
	if err != nil {
		logger.Error(err)
		msg = "生成单号错误"
		return
	}

	remark := fmt.Sprintf("管理员(%d)添加,%s", claims.UserId, oReq.Remark)

	if err = balance.GetBalanceObject(orderNo, 0, user.ID, enums.OrderTypeEnum.ManagerAdd, oReq.Amount, "内测赠送", remark, claims.UserId, oReq.Operator); err != nil {
		logger.Error(err)
		msg = "生成流水数据错误"
		return
	}
	if err = model.Transactions.ManageAddAmount(&balance); err != nil {
		logger.Error(err)
		msg = "金额添加出错"
		return
	}
	msg = "添加成功"
	code = 0
}

func (obj amountApi_) BatchQuick(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	operator := c.PostForm("operator")
	remark := c.PostForm("remark")
	if len(operator) == 0 {
		msg = "请输入操作员"
		return
	}
	if len(remark) > 200 {
		msg = "备注内容不能大于200个字符"
		return
	}

	fh, err := c.FormFile("excel")
	if err != nil {
		msg = "获取excel文件失败"
		logger.Error(msg, err)
		return
	}
	if fh.Size > 10*1024*1024 {
		msg = "excel文件太大"
		return
	}
	mf, err := fh.Open()
	if err != nil {
		msg = "获取excel文件失败"
		logger.Error(msg, err)
		return
	}
	defer mf.Close()

	ef, err := excelize.OpenReader(mf)
	if err != nil {
		msg = "打开excel文件失败"
		logger.Error(msg, err)
		return
	}
	a := ef.GetSheetList()
	if len(a) == 0 {
		msg = "空excel文件"
		return
	}
	rows, err := ef.GetRows(a[0])
	if err != nil {
		msg = "读取excel文件失败"
		logger.Error(msg, err)
		return
	}
	if len(rows) == 0 {
		msg = "excel文件没有行"
		return
	}

	type Row struct {
		UserID uint
		Mobile string
		Amount decimal.Decimal
	}
	var validRows []Row // 用于存储校验通过的行数据

	// 定义一个结构体来存储行错误信息
	type RowError struct {
		LineNumber int    `json:"line_number"`
		Mobile     string `json:"mobile"`
		AmountStr  string `json:"amount_str"`
		ErrorMsg   string `json:"error_msg"`
	}
	var errorRows []RowError // 用于存储校验失败的行信息

	for i, rowData := range rows {
		lineNumber := i + 1 // 行号从1开始
		logger.Info("处理第", lineNumber, "行,数据:", rowData)

		if len(rowData) != 2 {
			errorRows = append(errorRows, RowError{
				LineNumber: lineNumber,
				Mobile:     "", // 可能没有手机号列
				AmountStr:  "", // 可能没有金额列
				ErrorMsg:   "行必须包含2列：手机号和充值金额",
			})
			continue // 继续检查下一行
		}

		mobile := rowData[0]
		amountStr := rowData[1]

		// 跳过表头
		if i == 0 && ((mobile == "手机号" && amountStr == "充值金额") || (mobile == "Mobile" && amountStr == "Amount")) {
			continue
		}

		// 校验手机号
		if !utils.IsMobile(mobile) {
			errorRows = append(errorRows, RowError{
				LineNumber: lineNumber,
				Mobile:     mobile,
				AmountStr:  amountStr,
				ErrorMsg:   "第1列不是有效的手机号",
			})
			continue // 继续检查下一行
		}

		// 校验金额
		amount, err := decimal.NewFromString(amountStr)
		if err != nil || amount.Cmp(decimal.NewFromInt(10000)) > 0 ||
			amount.Cmp(decimal.NewFromInt(-10000)) < 0 ||
			amount.Cmp(decimal.Zero) == 0 {
			errorRows = append(errorRows, RowError{
				LineNumber: lineNumber,
				Mobile:     mobile,
				AmountStr:  amountStr,
				ErrorMsg:   "第2列充值金额不正确 (必须在 -10000 到 10000 之间，且不能为 0)",
			})
			continue // 继续检查下一行
		}

		// 校验用户是否存在
		var user model.User
		if err := user.GetByMobile(mobile); err != nil {
			errorMsg := "查询手机号失败"
			if err == gorm.ErrRecordNotFound {
				errorMsg = "用户不存在"
			} else {
				logger.Error("查询手机号失败", err) // 记录数据库或其他查询错误
			}
			errorRows = append(errorRows, RowError{
				LineNumber: lineNumber,
				Mobile:     mobile,
				AmountStr:  amountStr,
				ErrorMsg:   errorMsg,
			})
			continue // 继续检查下一行
		}
		// 如果所有校验都通过，则将行数据添加到 validRows 中
		validRows = append(validRows, Row{
			UserID: user.ID,
			Mobile: mobile,
			Amount: amount,
		})
	}
	logger.Info("校验通过的行数据:", validRows)
	logger.Info("校验失败的行信息:", errorRows)

	// 检查是否有校验失败的行
	if len(errorRows) > 0 {
		// 将错误信息列表转换为字符串
		var errorStrings []string
		for _, errInfo := range errorRows {
			errorStrings = append(errorStrings, fmt.Sprintf("第%d行 (手机号: %s, 金额: %s): %s", errInfo.LineNumber, errInfo.Mobile, errInfo.AmountStr, errInfo.ErrorMsg))
		}
		// 使用换行符连接所有错误信息
		// 需要在文件开头添加 import "strings"
		msg = "Excel 文件包含无效数据:<br>" + strings.Join(errorStrings, "<br>")
		// result["errors"] = errorRows // 不再将原始错误列表放入 result
		code = 1 // 使用适当的错误码表示校验失败
		return
	}

	// 如果没有错误，则执行数据库事务
	err = model.DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		for _, line := range validRows { // 使用校验通过的行数据
			logger.Info("处理用户:", line.Mobile, "金额:", line.Amount)
			balance := model.AmountBalance{
				UserId:         line.UserID,
				OperatorId:     claims.UserId,
				OccurredAmount: line.Amount,
			}

			var orderType int
			var s, r string
			if line.Amount.Cmp(decimal.Zero) < 0 {
				orderType = enums.OrderTypeEnum.ManagerCost
				s = "手动扣减"
				r = fmt.Sprintf("管理员(%d)导入扣减,%s", claims.UserId, remark)
				logger.Info(r)
			} else {
				orderType = enums.OrderTypeEnum.ManagerAdd
				s = "内测赠送"
				r = fmt.Sprintf("管理员(%d)导入添加,%s", claims.UserId, remark)
				logger.Info(r)
			}

			orderNo, err := model.OrderNo.NewByOrderType(enums.OrderTypeEnum.ManagerAdd, 0)
			if err != nil {
				logger.Error("生成单号错误", err)
				return fmt.Errorf("生成单号错误：%s", err)
			}

			if err = balance.GetBalanceObject(orderNo, 0, line.UserID, orderType, line.Amount, s, r, claims.UserId, operator); err != nil {
				logger.Error("生成流水数据错误", err)
				return fmt.Errorf("生成流水数据错误：%s", err)
			}

			// 使用事务 tx 进行数据库操作
			if err = balance.New(tx); err != nil {
				logger.Error("为用户 %s 处理金额 %s 时出错", line.Mobile, line.Amount.String())
				// 注意：这里返回错误会触发事务回滚
				return fmt.Errorf("为用户 %s 处理金额 %s 时出错: %w", line.Mobile, line.Amount.String(), err)
			}
		}
		return nil // 事务成功提交
	})
	if err != nil {
		logger.Error(err)
		msg = err.Error()
		return
	}

	msg = "添加成功"
	code = 0
}

func (obj amountApi_) CostQuick(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq addAmountReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var user model.User
	str := strconv.FormatUint(uint64(oReq.UserId), 10)
	if utils.IsMobile(str) {
		if err := user.GetByMobile(str); err != nil {
			logger.Error(err)
			msg = "手机号码不正确"
			return
		}
	} else {
		if err := user.GetById(oReq.UserId); err != nil {
			logger.Error(err)
			msg = "用户ID不正确"
			return
		}
	}

	if oReq.Amount.GreaterThanOrEqual(decimal.Zero) {
		msg = "输入金额必须小于0"
		return
	}

	if len(oReq.Operator) == 0 {
		msg = "请输入操作员"
		return
	}
	if len(oReq.Remark) > 200 {
		msg = "备注内容不能大于200个字符"
		return
	}

	balance := model.AmountBalance{
		UserId:         user.ID,
		OperatorId:     claims.UserId,
		OccurredAmount: oReq.Amount,
	}

	orderNo, err := model.OrderNo.NewByOrderType(enums.OrderTypeEnum.ManagerCost, 0)
	if err != nil {
		logger.Error(err)
		msg = "生成单号错误"
		return
	}

	remark := fmt.Sprintf("管理员(%d)扣减,%s", claims.UserId, oReq.Remark)

	if err = balance.GetBalanceObject(orderNo, 0, user.ID, enums.OrderTypeEnum.ManagerCost, oReq.Amount, "手动扣减", remark, claims.UserId, oReq.Operator); err != nil {
		logger.Error(err)
		msg = "生成流水数据错误"
		return
	}
	if err = model.Transactions.ManageAddAmount(&balance); err != nil {
		logger.Error(err)
		msg = "金额扣减出错"
		return
	}
	msg = "扣减成功"
	code = 0
}
