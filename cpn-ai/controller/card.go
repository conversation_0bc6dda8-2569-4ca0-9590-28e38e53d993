package controller

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"net/http"
	"runtime"
	"strings"
)

type cardApi_ struct {
	BindTest map[string]int `json:"bind_test"`
}

var CardApi cardApi_

type cardBuyReq struct {
	CouponCode string `json:"coupon_code"`
	Quantity   int    `json:"quantity"`
	AutoBind   int    `json:"auto_bind"`
}

type cardBindReq struct {
	CardNo string `json:"card_no"`
}

type cardListReq struct {
	Action   string `json:"action"`
	CardUuid string `json:"card_uuid"`
	CardNo   string `json:"card_no"`
	Status   int    `json:"status"`
	Kw       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type cardResp struct {
	Uuid        string                   `json:"uuid"`
	CouponId    uint                     `json:"-"`
	Title       string                   `json:"title"`
	CardNo      string                   `json:"card_no"`
	ValidDays   int                      `json:"valid_days"`
	BindTime    jsontime.JsonTime        `json:"bind_time"`
	ExpireDate  jsontime.JsonTime        `json:"expire_date"`
	BuyUserId   uint                     `json:"-"`
	BindUserId  uint                     `json:"-"`
	BindTxt     string                   `json:"bind_txt"`
	SalePrice   decimal.Decimal          `json:"sale_price"`
	FacePrice   decimal.Decimal          `json:"face_price"`
	LeaveAmount decimal.Decimal          `json:"leave_amount"`
	PodIds      string                   `json:"-"`
	Pods        string                   `json:"-"`
	PodsObj     []map[string]interface{} `json:"pods"`
	Remark      string                   `json:"remark"`
	Status      int                      `json:"status"`
	StatusTxt   string                   `json:"status_txt"`
}

func (obj cardApi_) Buy(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	} else {
		msg = "请支付购买"
		return
	}

	var oReq cardBuyReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	oReq.CouponCode = strings.Trim(oReq.CouponCode, " ")
	if oReq.CouponCode == "" {
		msg = "请输入优惠卷"
		return
	}

	var coupon model.Coupon
	if err := coupon.GetByCouponCode(oReq.CouponCode); err != nil {
		msg = "优惠卷查询失败"
		logger.Error(msg, err, oReq.CouponCode)
		return
	}
	if coupon.Status == 0 {
		msg = "该优惠卷暂时不可用"
		logger.Error(msg, oReq.CouponCode)
		return
	}

	if coupon.Quantity > 0 {
		leaveQuantity := coupon.Quantity - coupon.SoldCount
		if leaveQuantity <= 0 {
			msg = "该优惠卷已售完"
			return
		}
		if leaveQuantity < oReq.Quantity {
			msg = fmt.Sprintf("当前只有%d张可售优惠卷", leaveQuantity)
			return
		}
	}

	if coupon.SingleMax > 0 {

		queryParm := make(map[string]interface{})
		queryParm["buy_user_id"] = claims.UserId
		queryParm["coupon_id"] = coupon.ID

		var card model.Card
		if total, err := card.List(nil, queryParm, 1, 10000); err != nil {
			msg = "查询用户购买情况失败"
			logger.Error(msg, err)
			return
		} else {
			if oReq.Quantity+int(total) > coupon.SingleMax {
				msg = fmt.Sprintf("本优惠卷每人最多只能购买%d张", coupon.SingleMax)
				if total > 0 {
					msg = fmt.Sprintf("本优惠卷每人最多只能购买%d张，您前面已成功购买了%d张", coupon.SingleMax, total)
				}
				return
			}
		}
	}

	cardPrefix := coupon.CardPrefix
	digits := 16 - len(cardPrefix)

	cards := make([]model.Card, 0)
	for i := 0; i < oReq.Quantity; i++ {
		cardNoLast := utils.GenerateRandomNumberStr(digits)
		cardNo := cardPrefix + cardNoLast
		if len(cardNo) != 16 {
			msg = "生成卡号失败"
			logger.Error(msg, "couponId:", coupon.ID)
			return
		}
		card := model.Card{
			Uuid:        utils.GetUUID(),
			CouponId:    coupon.ID,
			CardNo:      cardNo,
			Title:       coupon.Title,
			ValidDays:   coupon.ValidDays,
			ExpireDate:  coupon.ExpireDate,
			BuyUserId:   claims.UserId,
			SalePrice:   coupon.SalePrice,
			FacePrice:   coupon.FacePrice,
			LeaveAmount: coupon.FacePrice,
			PodIds:      coupon.PodIds,
			Pods:        coupon.Pods,
			Status:      1,
		}
		cards = append(cards, card)
	}

	if err := model.Transactions.BuyCard(cards); err != nil {
		msg = "购买失败"
		logger.Error(err, utils.GetJsonFromStruct(oReq))
		return
	}

	//resps := make([]cardResp, 0)
	//for _, card := range cards {
	//	var resp cardResp
	//	if err := utils.Scan(card, &resp); err != nil {
	//		msg = "数据转换失败"
	//		logger.Error(msg, err, " uuid:", coupon.Uuid)
	//		return
	//	} else {
	//		resps = append(resps, resp)
	//	}
	//}

	//result["cards"] = resps
	result["count"] = len(cards)
	msg = "购买成功"
	code = 0

}

func (obj cardApi_) Bind(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	ip := utils.GetClientIp(c.Request.Header)
	//ip = "**************"
	if ip != "" {
		if val, ok := CardApi.BindTest[ip]; ok {
			CardApi.BindTest[ip] = val + 1
			if val > 100 {
				msg = "绑定尝试超限"
				return
			}
		} else {
			CardApi.BindTest[ip] = 1
		}
	}
	result["bind_ip"] = ip
	result["bind_test"] = CardApi.BindTest

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq cardBindReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	oReq.CardNo = strings.Replace(oReq.CardNo, "-", "", -1)
	if len(oReq.CardNo) != 16 {
		msg = "卡号位数不正确"
		return
	}

	var card model.Card
	if err := card.GetByCardNo(oReq.CardNo); err != nil {
		if err == gorm.ErrRecordNotFound {
			msg = "卡号不存在"
		} else {
			msg = "查询失败"
		}
		logger.Error(err)
		return
	}

	if card.Status == 0 {
		msg = "该卡暂时不可用"
		return
	}

	if card.BindUserId > 0 {
		msg = "该卡已经被绑定"
		return
	}

	//expireDate := jsontime.JsonTime(card.ExpireDate).Date().Time()
	//tomorrow := jsontime.Today().Time().AddDate(0, 0, 1)
	//if expireDate.Before(tomorrow) {
	//	msg = "该卡已过最后绑定日期"
	//	return
	//}

	today := jsontime.Today().Time()
	if card.ExpireDate.Before(today) {
		msg = "该卡已过最后绑定日期"
		return
	}

	if err := card.Bind(claims.UserId); err != nil {
		msg = "绑定失败"
		logger.Error(msg, err, " cardNo:", oReq.CardNo)
		return
	}

	if card.BuyUserId > 0 {
		msg = "绑定成功"
		code = 0
		return
	} else {
		msg = "绑定失败"
	}

}

func (obj cardApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq cardListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var card model.Card
	var ary = make([]cardResp, 0)

	queryParm := make(map[string]interface{})

	if oReq.CardUuid != "" {
		if err := card.GetByUuid(oReq.CardUuid); err != nil {
			msg = "记录不存在"
			logger.Error(err)
			return
		}
		queryParm["id"] = card.ID
	}

	if oReq.CardNo != "" {
		oReq.CardNo = strings.Replace(oReq.CardNo, "-", "", -1)
		if len(oReq.CardNo) != 16 {
			msg = "卡号位数不正确"
			return
		}
		if err := card.GetByCardNo(oReq.CardNo); err != nil {
			msg = "记录不存在"
			logger.Error(err)
			return
		}
		queryParm["id"] = card.ID
	}

	if oReq.Status >= 0 {
		queryParm["status"] = oReq.Status
	}

	if oReq.Action == "buy" {
		queryParm["buy_user_id"] = claims.UserId
	} else if oReq.Action == "bind" {
		queryParm["bind_user_id"] = claims.UserId
	} else {
		queryParm["user_id"] = claims.UserId
	}

	if total, err := card.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		today := jsontime.Today().Time()
		for i := 0; i < len(ary); i++ {
			ary[i].ExpireDate = ary[i].ExpireDate.Date()
			ary[i].CardNo = service.ShowCardNo(ary[i].CardNo)
			ary[i].PodsObj = utils.GetMapAryFromJson(ary[i].Pods)

			if ary[i].Status == 2 {
				ary[i].StatusTxt = "暂停使用"
			}
			if ary[i].Status == 1 {
				ary[i].StatusTxt = "有效"
			} else {
				ary[i].StatusTxt = "无效"
			}

			if ary[i].LeaveAmount.Equal(decimal.Zero) {
				ary[i].StatusTxt = "已用完"
			} else {
				if ary[i].ExpireDate.Time().Before(today) {
					ary[i].StatusTxt = "已过期"
				}
			}

			if ary[i].Status == 4 {
				ary[i].StatusTxt = "已作废"
			}

			if ary[i].BindUserId > 0 {
				ary[i].BindTxt = "已绑定"
				if ary[i].BindUserId != claims.UserId {
					ary[i].BindTxt = "已被别人绑定"
				}
			} else {
				if ary[i].ExpireDate.Time().Before(today) {
					ary[i].BindTxt = "已过期"
				} else {
					ary[i].BindTxt = "未绑定"
				}
			}
		}
		result["cards"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj cardApi_) ValidCount(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var card model.Card
	if validCount, err := card.CardValidCount(claims.UserId); err != nil {
		logger.Error(err)
		msg = "查询失败"
	} else {
		result["valid_count"] = validCount
		code = 0
	}
}
