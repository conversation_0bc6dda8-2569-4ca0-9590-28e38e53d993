package controller

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/service/relay"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

type conversationApi_ struct {
}

var ConversationApi conversationApi_

type newConversationReq struct {
}

type saveQuestionConversationReq struct {
	ConvUuid  string `json:"conv_uuid"`
	Content   string `json:"content"`
	Model     string `json:"model"`
	ModelUuid string `json:"model_uuid"`
}

type conversationListReq struct {
	ConvUuid string `json:"conv_uuid"`
	KW       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type conversationAnswerStream struct {
	ConvUuid     string `json:"conv_uuid"`
	QuestionUuid string `json:"question_uuid"`
}

type conversationResp struct {
	Uuid      string                   `json:"uuid"`
	Title     string                   `json:"title"`
	Mapping   string                   `json:"-"`
	Mapp      []map[string]interface{} `json:"mapp"`
	CreatedAt jsontime.JsonTime        `json:"created_at"`
	UpdatedAt jsontime.JsonTime        `json:"updated_at"`
}

func (obj conversationApi_) New(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("mytoken").(*middleware.MyToken)
	if claims.UserId <= 0 {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq newConversationReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	conversation := model.Conversation{
		Uuid:    utils.GetUUID(),
		UserId:  claims.UserId,
		TokenId: claims.TokenId,
		Mapping: "[]",
	}

	if err := conversation.Save(); err != nil {
		logger.Error(err)
		msg = "对话创建失败"
		return
	} else {
		result["conversation"] = conversationResp{
			Uuid:      conversation.Uuid,
			Title:     conversation.Title,
			Mapp:      utils.GetMapAryFromJson(conversation.Mapping),
			UpdatedAt: jsontime.JsonTime(conversation.CreatedAt),
			CreatedAt: jsontime.JsonTime(conversation.CreatedAt),
		}
	}

	msg = "对话创建成功"
	code = 0
}

func (obj conversationApi_) Del(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("mytoken").(*middleware.MyToken)
	if claims.UserId <= 0 {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq saveQuestionConversationReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg)
		return
	}
	if oReq.ConvUuid == "" {
		msg = "参数错误"
		logger.Error(msg)
		return
	}

	var conversation model.Conversation
	if err := conversation.GetByUuid(oReq.ConvUuid); err != nil {
		msg = "对话不存在"
		logger.Error(msg, err, oReq.ConvUuid)
		return
	}
	if conversation.TokenId != claims.TokenId {
		msg = "无权限"
		logger.Error(msg)
		return
	}
	if err := conversation.Delete(); err != nil {
		msg = "会话删除失败"
		logger.Error(msg, err)
		return
	}

	msg = "会话删除成功"
	code = 0
}

func (obj conversationApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	var oReq conversationListReq

	claims := c.Value("mytoken").(*middleware.MyToken)
	if claims.UserId <= 0 {
		msg = "请先登录"
		code = 2
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.PageSize < 1 {
		oReq.PageSize = 10
	}
	if oReq.Page < 1 {
		oReq.Page = 1
	}

	var conversation model.Conversation
	ary := make([]conversationResp, 0)

	if total, err := conversation.List(&ary, oReq.ConvUuid, claims.UserId, claims.TokenId, oReq.KW, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询出错"
		logger.Error(msg, err)
		return
	} else {
		if oReq.ConvUuid != "" {
			for i := 0; i < len(ary); i++ {
				ary[i].Mapp = utils.GetMapAryFromJson(ary[i].Mapping)
			}
		}
		result["items"] = ary
		result["total"] = total
	}
	code = 0
}

func (obj conversationApi_) AskQuestion(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("mytoken").(*middleware.MyToken)
	if claims.UserId <= 0 {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq saveQuestionConversationReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ConvUuid == "" {
		msg = "参数错误"
		logger.Error(msg)
		return
	}
	if oReq.Content == "" {
		msg = "请输入问题"
		logger.Error(msg)
		return
	}

	var conversation model.Conversation
	if err := conversation.GetByUuid(oReq.ConvUuid); err != nil {
		msg = "对话不存在"
		logger.Error(msg, err, oReq.ConvUuid)
		return
	}

	messages := make([]relay.Message, 0)
	messages = append(messages, relay.Message{
		Role:    "user",
		Content: oReq.Content,
	})
	promptTokens := relay.CountTokenMessages(messages, oReq.Model)
	questionUuid := utils.GetUUID()
	chat := service.ChatItem{
		Uuid:         questionUuid,
		Model:        oReq.Model,
		Role:         "user",
		Content:      oReq.Content,
		CreatedAt:    time.Now(),
		PromptTokens: promptTokens,
	}

	//aa := struct {
	//	Name string `json:"name"`
	//	Id   int    `json:"id"`
	//}{
	//	Name: "ssss",
	//	Id:   10,
	//}
	json := utils.GetJsonFromStruct(chat)
	m := utils.GetMapFromJson(json)

	if err := conversation.AppendMapping(m); err != nil {
		logger.Error(err)
	}

	if conversation.Title == "" {
		title := oReq.Content
		if len(title) > 50 {
			title = utils.Substring(oReq.Content, 0, 47) + "..."
		}
		if err := conversation.SetTitle(title); err != nil {
			msg = "保存标题出错"
			logger.Error(msg, err)
		}
	}
	result["conv_uuid"] = oReq.ConvUuid
	result["question_uuid"] = questionUuid
	msg = "问题保存成功"
	code = 0
}

func (obj conversationApi_) AnswerStream(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"

		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	//claims := c.Value("mytoken").(*middleware.MyToken)
	//if claims.UserId <= 0 {
	//	msg = "请先登录"
	//	code = 2
	//	return
	//}

	oReq := conversationAnswerStream{
		ConvUuid:     c.Query("conv_uuid"),
		QuestionUuid: c.Query("question_uuid"),
	}

	if oReq.ConvUuid == "" {
		msg = "参数错误"
		logger.Error(msg)
		return
	}
	if oReq.QuestionUuid == "" {
		msg = "请输入问题"
		logger.Error(msg)
		return
	}

	var conversation model.Conversation
	if err := conversation.GetByUuid(oReq.ConvUuid); err != nil {
		msg = "对话不存在"
		logger.Error(msg, err, oReq.ConvUuid)
		return
	}

	ary := utils.GetMapAryFromJson(conversation.Mapping)

	fastchat := service.Fastchat{
		//Model:    oReq.Model,
		//HostPort: "https://suanyun.cyuai.com/fastchat",
		//ApiBase:  "http://**************:8000/v1",
	}

	//if common.Config.Env == enums.EnvEnum.PRODUCTION {
	//	fastchat.ApiBase = "http://***************:10800/v1"
	//	fastchat.ApiBase = "http://**************:8000/v1"
	//	fastchat.ApiBase = "http://***************:11800/v1"
	//} else {
	//	fastchat.ApiBase = "https://suanyun.cyuai.com/fastchat/v1" // https://suanyun.cyuai.com/fastchat/  ===>  http://**************:8000/
	//}

	messages := make([]relay.Message, 0)
	model := ""
	tokens := float64(0)
	for i := len(ary) - 1; i >= 0; i-- {

		if tokens > 0 {
			message := relay.Message{
				Role:    ary[i]["role"].(string),
				Content: ary[i]["content"].(string),
			}

			if message.Role == "user" {
				tokens += ary[i]["prompt_tokens"].(float64)
			} else {
				tokens += ary[i]["completion_tokens"].(float64)
			}
			messages = append([]relay.Message{message}, messages...)
			logger.Info("messages:", messages)
			if tokens > 1024 {
				break
			}
		}

		if oReq.QuestionUuid == ary[i]["uuid"].(string) {
			model = ary[i]["model"].(string)
			message := relay.Message{
				Role:    ary[i]["role"].(string),
				Content: ary[i]["content"].(string),
			}
			messages = append(messages, message)
			tokens += ary[i]["prompt_tokens"].(float64)
		}

	}
	logger.Info("last messages:", messages)
	if len(messages) == 0 {
		msg = "问题没找到"
		logger.Error(msg, oReq)
		return
	}

	myToken := middleware.MyToken{
		UserId:  conversation.UserId,
		TokenId: conversation.TokenId,
	}
	c.Set("mytoken", &myToken)

	generalOpenAIRequest := service.GeneralOpenAIRequest{
		GeneralOpenAIRequest: relay.GeneralOpenAIRequest{
			Stream:   true,
			Model:    model,
			Messages: messages,
		},
		ApiBase: fastchat.ApiBase,
	}
	fastchat.Model = model

	if chatResult, err := fastchat.ChatCompletions11(c, generalOpenAIRequest); err != nil {
		logger.Error(err)
		result["fastchat_chatcompletions_err"] = err
		msg = err.Error()
		return
	} else {

		content := ""
		if len(chatResult.Choices) > 0 {
			content = chatResult.Choices[0].Message.Content
		}

		chat := service.ChatItem{
			Uuid:      utils.GetUUID(),
			Model:     chatResult.Model,
			Role:      "assistant",
			Content:   content,
			CreatedAt: time.Now(),

			CompletionTokens: chatResult.CompletionTokens,
			PromptTokens:     chatResult.PromptTokens,
			Created:          chatResult.Created,
			ID:               chatResult.Id,
			CompletedAt:      chatResult.CompletedAt,
			BlockKey:         chatResult.BlockKey,
		}
		json := utils.GetJsonFromStruct(chat)
		m := utils.GetMapFromJson(json)
		if err := conversation.AppendMapping(m); err != nil {
			logger.Error(err)
		} else {
			logger.Info("已将答案保存到记录中")
		}

	}
	msg = "请求中..."
	code = 0
}

func (obj conversationApi_) SaveAnswer(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("mytoken").(*middleware.MyToken)
	if claims.UserId <= 0 {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq saveQuestionConversationReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ConvUuid == "" {
		msg = "参数错误"
		logger.Error(msg)
		return
	}
	if oReq.Content == "" {
		msg = "请输入问题"
		logger.Error(msg)
		return
	}

	var conversation model.Conversation
	if err := conversation.GetByUuid(oReq.ConvUuid); err != nil {
		msg = "对话不存在"
		logger.Error(msg, err, oReq.ConvUuid)
		return
	}

	chat := service.ChatItem{
		Uuid:      utils.GetUUID(),
		Model:     oReq.Model,
		Role:      "assistant",
		Content:   oReq.Content,
		CreatedAt: time.Now(),
	}

	//aa := struct {
	//	Name string `json:"name"`
	//	Id   int    `json:"id"`
	//}{
	//	Name: "ssss",
	//	Id:   10,
	//}
	json := utils.GetJsonFromStruct(chat)
	m := utils.GetMapFromJson(json)

	if err := conversation.AppendMapping(m); err != nil {
		logger.Error(err)
	}

	msg = "回答保存成功"
	code = 0
}
