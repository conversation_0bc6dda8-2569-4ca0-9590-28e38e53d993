package controller

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/structs"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

type gpuApi_ struct {
}

var GpuApi gpuApi_

type gpuModelsReq struct {
	PodUuid      string `json:"pod_uuid"`
	InstanceUuid string `json:"instance_uuid"`
}

type gpuModelResp struct {
	ID        uint              `json:"-"`
	Uuid      string            `json:"uuid"`
	Title     string            `json:"title"`
	GpuName   string            `json:"-"`
	Desc      string            `json:"desc"`
	MemoryG   int               `json:"-"`
	MemoryM   int               `json:"-"`
	Price     string            `json:"price"`
	Remark    string            `json:"remark"`
	Status    int               `json:"status"`
	StatusTxt string            `json:"status_txt"`
	CreatedAt jsontime.JsonTime `json:"created_at"`
	TotalGpus int               `json:"-"`
	FreeGpus  int               `json:"-"`
	FreeTxt   string            `json:"free_txt"`

	//CPU和内存
	Cpu    int `json:"cpu"`
	Memory int `json:"memory"`
}

type gpusReq struct {
	AutoID    uint `json:"auto_id"`
	VirtualId uint `json:"virtual_id"`
	Status    int  `json:"status"`
	Page      int  `json:"page"`
	PageSize  int  `json:"page_size"`
}

type gpusResp struct {
	ID            uint              `json:"id"`
	Index         int               `json:"index"`
	Uuid          string            `json:"uuid"`
	GpuModelId    uint              `json:"-"`
	VirtualId     uint              `json:"virtual_id"`
	HostPort      string            `json:"host_port"`
	GpuName       string            `json:"gpu_name"`
	MemoryG       int               `json:"memory_g"`
	MemoryM       int               `json:"memory_m"`
	Remark        string            `json:"remark"`
	Status        int               `json:"status"`
	StatusTxt     string            `json:"status_txt"`
	CreatedAt     jsontime.JsonTime `json:"created_at"`
	LastCheckTime jsontime.JsonTime `json:"last_check_time"`
}

func (obj gpuApi_) Models(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims == nil {
	//	msg = "请先登录"
	//	code = 2
	//	return
	//}

	var oReq gpuModelsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var pod model.Pod
	if oReq.PodUuid != "" {
		if err := pod.GetByUuid(oReq.PodUuid); err != nil {
			logger.Error(err)
		}
	} else if oReq.InstanceUuid != "" {
		var instance model.Instance
		if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
			logger.Error(err)
		} else {
			if err := pod.GetById(instance.PodId); err != nil {
				logger.Error(err)
			}
		}
	}

	//var aryStats = make([]structs.GpuStats, 0)
	mStats := make(map[uint]structs.GpuStats)

	virtualIds := make([]uint, 0)
	if pod.VirtualIds != "" {
		ary := strings.Split(pod.VirtualIds, "|")
		for _, val := range ary {
			if virtualId := utils.String2Uint(val); virtualId > 0 {
				virtualIds = append(virtualIds, virtualId)
			}
		}
	}

	//if err := virtual.Stats(&aryStats, virtualIds); err != nil {
	//	logger.Error(err)
	//} else {
	//
	//}

	isValidVirtual := func(podIds string, podId uint) bool {
		if podIds == "" {
			return true
		}
		tmp := fmt.Sprintf("|%d|", podId)
		return strings.Contains(podIds, tmp)
	}

	var virtual model.Virtual
	aryVirtual := make([]model.Virtual, 0)
	if _, err := virtual.ListStats(&aryVirtual, virtualIds, 1, 1000); err != nil {
		logger.Error(err)
	} else {
		for _, tmpVirtual := range aryVirtual {
			if !isValidVirtual(tmpVirtual.PodIds, pod.ID) {
				continue
			}
			if config.Env == enums.EnvEnum.ONLINE {
				if tmpVirtual.NodeId != 27 {
					continue
				}
			}
			if config.Env == enums.EnvEnum.PRODUCTION {
				if tmpVirtual.NodeId == 27 {
					continue
				}
			}

			if tmpStat, ok := mStats[tmpVirtual.GpuModelId]; ok {
				tmpStat.FreeGpus += tmpVirtual.FreeGpus
				tmpStat.TotalGpus += tmpVirtual.TotalGpus
				mStats[tmpVirtual.GpuModelId] = tmpStat
			} else {
				mStats[tmpVirtual.GpuModelId] = structs.GpuStats{GpuModelId: tmpVirtual.GpuModelId, FreeGpus: tmpVirtual.FreeGpus, TotalGpus: tmpVirtual.TotalGpus}
			}
		}
	}

	var gpuModel model.GpuModel

	var ary = make([]gpuModelResp, 0)
	if total, err := gpuModel.List(&ary, uint(0), "", 1, 1, 100); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {
			ary[i].FreeTxt = "已售罄"
			if tmpStat, ok := mStats[ary[i].ID]; ok {
				ary[i].FreeGpus = tmpStat.FreeGpus
				ary[i].TotalGpus = tmpStat.TotalGpus
				//充足 少量 紧张 无
				if ary[i].FreeGpus <= 0 {
					ary[i].FreeTxt = "已售罄"
				} else if ary[i].FreeGpus < 5 {
					ary[i].FreeTxt = "紧张"
				} else if ary[i].FreeGpus < 10 {
					ary[i].FreeTxt = "少量"
				} else {
					ary[i].FreeTxt = "充足"
				}
			}
		}
		result["items"] = ary
		result["no_card_price"] = common.NoCardPrice
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj gpuApi_) ListForManage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims == nil {
	//	msg = "请先登录"
	//	code = 2
	//	return
	//}

	var oReq gpusReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var gpus model.Gpus

	var ary = make([]gpusResp, 0)
	if total, err := gpus.List(&ary, oReq.AutoID, oReq.VirtualId, oReq.Status, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		return
	} else {
		for i := 0; i < len(ary); i++ {
			if ary[i].Status == 1 {
				ary[i].StatusTxt = "使用中"
			} else if ary[i].Status == 2 {
				ary[i].StatusTxt = "空闲中"
			}
		}
		result["gpus"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}
