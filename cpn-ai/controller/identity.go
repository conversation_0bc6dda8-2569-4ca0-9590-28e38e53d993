package controller

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/myimg"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/service/pay"
	"encoding/base64"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/skip2/go-qrcode"
	"gorm.io/gorm"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"
	"time"
)

type identityApi_ struct {
}

var IdentityApi identityApi_

type identityReq struct {
	CertChannel string                    `json:"cert_channel"`
	CertType    model.CertifyCertTypeEnum `json:"cert_type"`
	CertName    string                    `json:"cert_name"`
	CertNo      string                    `json:"cert_no"`
	OrderNo     string                    `json:"order_no"`
}
type sendEduEmailReq struct {
	Email      string `json:"email"`
	SchoolName string `json:"school_name"`
	VerifyCode string `json:"verify_code"`
}

type studentCertifyReq struct {
	SchoolName      string `json:"school_name" `
	TrueName        string `json:"true_name"`
	IdCardNo        string `json:"id_card_no" `
	IdCardImgBase64 string `json:"id_card_img_base64"`
	FrontImgBase64  string `json:"front_img_base64"`
	BackImgBase64   string `json:"back_img_base64" `
}

type companyCertifyReq struct {
	CompanyName      string `json:"company_name" `
	CompanyNo        string `json:"company_no" `
	LegalName        string `json:"legal_name"`
	LegalNo          string `json:"legan_no" `
	CompanyImgBase64 string `json:"company_img_base64"`
	FrontImgBase64   string `json:"front_img_base64"`
	BackImgBase64    string `json:"back_img_base64" `
}

type studentCertifyInfoResp struct {
	Nanoid     string `json:"nanoid"`
	SchoolName string `json:"school_name" `
	TrueName   string `json:"true_name"`
	IdCardNo   string `json:"id_card_no" `
	IdCardImg  string `json:"id_card_img"`
	FrontImg   string `json:"front_img"`
	BackImg    string `json:"back_img" `
	Status     int    `json:"status"`
	StatusTxt  string `json:"status_txt"`
	Reason     string `json:"reason"`
}

type companyCertifyInfoResp struct {
	Nanoid      string `json:"nanoid"`
	CompanyName string `json:"company_name" `
	CompanyNo   string `json:"company_no" `
	LegalName   string `json:"legal_name"`
	LegalNo     string `json:"legan_no" `
	CompanyImg  string `json:"company_img"`
	FrontImg    string `json:"front_img"`
	BackImg     string `json:"back_img" `
	Status      int    `json:"status"`
	StatusTxt   string `json:"status_txt"`
	Reason      string `json:"reason"`
}

// 不是阿里云 实人认证  实人认证服务
// 是支付宝身份验证 https://opendocs.alipay.com/open/20181012100420932508

// identity_type 1.若本人验证，使用CERT_INFO； 2.若代他人验证，使用AGENT_CERT_INFO；
func (obj identityApi_) QrCode(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq identityReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	claims := c.Value("claims").(*middleware.MyClaims)
	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "获取用户信息失败"
		logger.Error(err)
	}

	logger.Info("oReq:", utils.GetJsonFromStruct(oReq), " userID:", claims.UserId)

	oReq.CertName = strings.TrimSpace(oReq.CertName)
	oReq.CertNo = strings.TrimSpace(oReq.CertNo)
	if len(oReq.CertName) <= 1 {
		msg = "请输入真实姓名"
		return
	}
	if len(oReq.CertNo) < 6 {
		msg = "请输入证件号码"
		return
	}

	if oReq.CertType == "" {
		oReq.CertType = model.CertifyCertType_IDENTITY_CARD
	}

	if name := model.CertifyCertTypeEnumName(oReq.CertType); name == "" {
		msg = "证件类型参数错误"
		logger.Error(msg, oReq)
		return
	}

	returnUrl := ""
	encodeCertName := config.EncryptAes(oReq.CertName)
	encodeCertNo := config.EncryptAes(oReq.CertNo)

	if config.DecryptAes(encodeCertName) != oReq.CertName || config.DecryptAes(encodeCertNo) != oReq.CertNo {
		msg = "加密失败"
		return
	}

	if oReq.CertChannel == enums.CertifyChannelEnum.AliPay {
		var certify model.Certify
		if err := certify.GetByCertNameAndNo(claims.UserId, oReq.CertChannel, oReq.CertType, encodeCertName, encodeCertNo); err != nil {
			if err == gorm.ErrRecordNotFound {
				orderNo := utils.GetUUID()
				certify = model.Certify{
					CertChannel: oReq.CertChannel,
					CertType:    oReq.CertType,
					UserId:      claims.UserId,
					CertName:    encodeCertName,
					CertNo:      encodeCertNo,
					OrderNo:     orderNo,
					Response:    "{}",
				}
				if err := certify.Save(); err != nil {
					msg = "生成验证记录失败"
					logger.Error(err)
					return
				}
			} else {
				msg = "查询失败"
				logger.Error(msg, err)
				return
			}
		}
		if certify.CertifyId == "" {
			certifyId, err := pay.AlipayService.UserCertifyOpenInit(returnUrl, certify.OrderNo, string(oReq.CertType), oReq.CertName, oReq.CertNo)
			if err != nil || certifyId == "" {
				msg = "查询初始化失败"
				logger.Error(msg, err, "certifyId:", certifyId)
				return
			}
			if err := certify.SetCertifyId(certifyId); err != nil {
				msg = "存储验证ID失败"
				logger.Error(err)
				return
			}
		}

		if certify.CertifyUrl == "" {
			certifyUrl, err := pay.AlipayService.UserCertifyOpenCertify(returnUrl, certify.CertifyId)
			if err != nil || certifyUrl == "" {
				msg = "获取验证Url失败"
				logger.Error(msg, err)
				return
			}
			if err := certify.SetCertifyUrl(certifyUrl); err != nil {
				msg = "存储验证Url失败"
				logger.Error(err)
				return
			}
		}
		if certify.CertifyUrl == "" {
			msg = "验证Url生成失败"
			return
		}

		qrCode, err := qrcode.New(certify.CertifyUrl, qrcode.Medium)
		if err != nil {
			msg = "生成二维码失败"
			logger.Error(msg, err)
			return
		}
		pngBytes, err := qrCode.PNG(256)
		if err != nil {
			msg = "生成二维码失败"
			logger.Error(msg, err)
			return
		}
		result["certify_code"] = "data:image/png;base64," + base64.StdEncoding.EncodeToString(pngBytes)
		result["order_no"] = certify.OrderNo
		//result["certify_id"] = certifyId
		//result["certify_url"] = certifyUrl
		code = 0
		return
	}
	msg = "暂不支持"

}

func (obj identityApi_) Query(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq identityReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	oReq.OrderNo = strings.TrimSpace(oReq.OrderNo)
	if len(oReq.OrderNo) < 15 {
		msg = "订单号不正确"
		return
	}

	claims := c.Value("claims").(*middleware.MyClaims)
	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "获取用户信息失败"
		logger.Error(msg, err)
	}

	if user.CertNo != "" && user.CertName != "" {
		msg = "身份验证已通过"
		result["passed"] = true
		code = 0
		return
	}

	var certify model.Certify
	if err := certify.GetByOrderNo(oReq.OrderNo); err != nil {
		msg = "查询订单失败"
		logger.Error(msg, err)
	}

	if certify.CertChannel == enums.CertifyChannelEnum.AliPay {

		if str, err := pay.AlipayService.UserCertifyOpenQuery(certify.CertifyId); err != nil {
			msg = "验证未通过，请检查输入是否正确"
			logger.Error(msg, "oReq.OrderNo:", oReq.OrderNo, " err:", err)
			if err := certify.SetStatus(2, str); err != nil {
				logger.Error(msg, err)
			}
			if err.Error() == "验证未通过" {
				msg = err.Error()
				result["passed"] = false
				code = 0
			}
			return
		} else {
			if err := certify.SetStatus(1, str); err != nil {
				msg = "设置状态失败"
				logger.Error(msg, err)
				return
			}
			if err := user.SetUserCert(certify.CertName, certify.CertNo, ""); err != nil {
				msg = "设置用户实名信息失败"
				logger.Error(msg, err)
				return
			}
			msg = "身份验证通过"
			result["passed"] = true
			code = 0
			return
		}

	}
	msg = "暂不支持"

}

func (obj identityApi_) SendEduEmail(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq sendEduEmailReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	claims := c.Value("claims").(*middleware.MyClaims)

	redisKey := enums.RedisKeyEnum.SmsLogin + fmt.Sprintf("verifyeduemail:%d", claims.UserId)

	testCount, _ := common.RedisGet(redisKey)
	if testCount == "" {
		testCount = "0"
	}
	iTestCount, _ := strconv.Atoi(testCount)
	if iTestCount > 20 {
		msg = "验证码尝试次数过多，请10分钟后重试"
		logger.Error(msg, claims.UserId)
		return
	}
	iTestCount = iTestCount + 1
	if err := common.RedisSet(redisKey, strconv.Itoa(iTestCount), time.Minute*10); err != nil {
		msg = "设置验证码尝试次数失败"
		logger.Error(msg, claims.UserId)
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "获取用户信息失败"
		logger.Error(err)
	}
	if user.CertName == "" || user.CertNo == "" {
		msg = "请先进行实名认证"
		return
	}

	if user.StudentCerifyId > 0 {
		msg = "学生认证已通过"
		return
	}

	logger.Info("oReq:", utils.GetJsonFromStruct(oReq), " userID:", claims.UserId)
	if utils.IsEmail(oReq.Email) == false { //.edu.cn
		msg = "邮箱不正确"
		return
	}
	if user.Insider == 0 {
		if utils.IsEduEmail(oReq.Email) == false {
			msg = "不是教育邮箱"
			return
		}
	}
	var certifyStudent model.CertifyStudent
	if err := certifyStudent.GetByUserId(claims.UserId); err != nil {
		if err != gorm.ErrRecordNotFound {
			msg = "查询失败"
			logger.Error(msg, err)
		}
	}

	if certifyStudent.Status == 1 {
		msg = "该账号学生认证已通过"
		return
	}
	//if len(oReq.SchoolName) < 5 {
	//	msg = "请输入学校全称"
	//	return
	//}

	if certifyStudent.UserId == 0 {
		certifyStudent.UserId = claims.UserId
	}
	certifyStudent.SchoolName = oReq.SchoolName
	certifyStudent.VerifyCode = utils.CreateCaptcha(6)
	if err := certifyStudent.Save(); err != nil {
		msg = "保存失败"
		logger.Error(msg, err)
		return
	}

	html := `
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>晨羽智云</title>
</head>

<body style="margin: 0;">
    <div style="
            height: 99vh;
            overflow-y: auto;
            display: flex; 
            justify-content: center; 
    ">
        <div style="max-width: 600px;font-family: PingFangSC, PingFang SC;margin:50px 0">
            <img style="width: 150px;" src="https://img.cyuai.com/cpn/res/logo3.png" alt="">

            <div style="background: #fa9851;color: white;padding: 50px 20px 20px;font-size: 20px;font-weight: 600;">
                晨羽智云 验证码
            </div>
            <div style="background:rgb(242, 245, 249);padding: 20px;">
                <div style="margin: 10px;font-size: 16px;font-weight: 550;">尊敬的 晨羽智云 用户:</div>
                <div style="margin: 10px;font-size: 15px;color: #444;">您正在进行学生认证，您的验证码为</div>
                <div style="text-align: center; margin: 30px;font-size: 30px;font-weight: 600;">{VerifyCode}</div>
                <div style="margin: 10px;line-height: 1.5;color: #444;font-size: 15px;">
                    如果您并未请求此验证码，则可能是他人正在使用您的邮箱进行认证。
                    <span style="font-weight: 600;color: #000;">请勿将此验证码转发给或提供给任何人。</span>
                </div>
                <div style="margin: 10px;">此致</div>
                <div style="margin: 10px;">晨羽智云 团队敬上</div>
            </div>
            <div style="margin: 20px 0 100px;">此电子邮件地址无法接收回复。如需更多信息，请联系客服: 195 2153 5956</div>
        </div>
    </div>
</body>

</html>`
	html = strings.Replace(html, "{VerifyCode}", certifyStudent.VerifyCode, -1)

	emailReq := service.EmailReq{
		To:      oReq.Email,
		Subject: "晨羽智云学生认证",
		Content: html,
	}

	if err := service.EmailService.SendFromService(emailReq); err != nil {
		msg = "邮件发送失败，请稍后重试"
		logger.Error(msg, err)
		return
	} else {
		msg = "认证邮件发送成功"
		code = 0
		return
	}
}

func (obj identityApi_) VerifyEduEmail(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq sendEduEmailReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	claims := c.Value("claims").(*middleware.MyClaims)

	redisKey := enums.RedisKeyEnum.SmsLogin + fmt.Sprintf("verifyeduemail:%d", claims.UserId)
	testCount, _ := common.RedisGet(redisKey)
	if testCount == "" {
		testCount = "0"
	}
	iTestCount, _ := strconv.Atoi(testCount)
	if iTestCount > 20 {
		msg = "验证码尝试次数过多，请10分钟后重试"
		logger.Error(msg, claims.UserId)
		return
	}
	iTestCount = iTestCount + 1
	if err := common.RedisSet(redisKey, strconv.Itoa(iTestCount), time.Minute*10); err != nil {
		msg = "设置验证码尝试次数失败"
		logger.Error(msg, claims.UserId)
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "获取用户信息失败"
		logger.Error(err)
	}
	if user.StudentCerifyId > 0 {
		msg = "学生认证已通过"
		return
	}

	logger.Info("oReq:", utils.GetJsonFromStruct(oReq), " userID:", claims.UserId)

	var certifyStudent model.CertifyStudent
	if err := certifyStudent.GetByUserId(claims.UserId); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
	}

	if certifyStudent.Status == 1 {
		msg = "该账号学生认证已通过"
		return
	}

	if certifyStudent.VerifyCode != oReq.VerifyCode || oReq.VerifyCode == "" {
		msg = "验证码不正确"
		return
	}

	if certifyStudent.VerifyCode == oReq.VerifyCode {
		if err := certifyStudent.SetStatusPass(); err != nil {
			msg = "更新认证信息失败"
			logger.Error(msg, err)
			return
		} else {
			msg = "认证成功"
			code = 0
			return
		}
	}
	msg = "认证失败"
	return
}

func (obj identityApi_) StudentCertify(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq studentCertifyReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	claims := c.Value("claims").(*middleware.MyClaims)
	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "获取用户信息失败"
		logger.Error(err)
	}
	if user.CertName == "" || user.CertNo == "" {
		msg = "请先进行实名认证"
		return
	}

	if user.StudentCerifyId > 0 {
		msg = "学生认证已通过"
		return
	}

	//logger.Info("oReq:", utils.GetJsonFromStruct(oReq), " userID:", claims.UserId)
	if oReq.SchoolName == "" {
		msg = "请输入学校全名"
		return
	}
	if oReq.TrueName == "" {
		msg = "请输入真实姓名"
		return
	}

	certName := config.DecryptAes(user.CertName)
	certNo := config.DecryptAes(user.CertNo)
	if certName != oReq.TrueName {
		msg = "真实姓名与认证姓名不一致"
		return
	}
	//var checkCertNo model.CertifyStudent
	//if err := checkCertNo.GetByCertNo(certNo); err != nil {
	//	if err != gorm.ErrRecordNotFound {
	//		msg = "验证身份信息失败"
	//		logger.Error(msg, err)
	//		return
	//	}
	//} else {
	//	if checkCertNo.UserId != claims.UserId {
	//		msg = "学生认证不能认证多个账号"
	//		return
	//	}
	//}

	if oReq.IdCardNo == "" {
		msg = "请输入学生证号"
		return
	}

	var checkIdCardNo model.CertifyStudent
	if err := checkIdCardNo.GetByIdCardNo(oReq.IdCardNo); err != nil {
		if err != gorm.ErrRecordNotFound {
			msg = "查询学生证号信息失败"
			logger.Error(msg, err)
			return
		}
	} else {
		if checkIdCardNo.UserId != claims.UserId {
			msg = "该学生证号已被使用"
			return
		}
	}

	var certifyStudent model.CertifyStudent
	if err := certifyStudent.GetByUserId(claims.UserId); err != nil {
		if err != gorm.ErrRecordNotFound {
			msg = "查询失败"
			logger.Error(msg, err)
		}
	}

	if certifyStudent.Status == 1 {
		msg = "该账号学生认证已通过"
		return
	}

	if certifyStudent.Nanoid == "" {
		certifyStudent.Nanoid = utils.Generate12NanoId()
	}

	if certifyStudent.UserId == 0 {
		certifyStudent.UserId = claims.UserId
	}
	if certifyStudent.CertChannel == "" {
		certifyStudent.CertChannel = enums.CertifyChannelEnum.Manual
	}
	if certifyStudent.CertType == "" {
		certifyStudent.CertType = model.CertifyStudentCertType_ID_CAED
	}
	certifyStudent.SchoolName = oReq.SchoolName
	certifyStudent.TrueName = oReq.TrueName
	certifyStudent.CertNo = certNo
	certifyStudent.IdCardNo = oReq.IdCardNo
	//certifyCompany.LegalName = oReq.LegalName
	//certifyCompany.LegalNo = oReq.LegalNo

	idCardImgDeletePath := ""
	frontImgDeletePath := ""
	backImgDeletePath := ""
	if oReq.IdCardImgBase64 != "" {
		logger.Info(user.ID, "开始更新学生证图")
		//oMd5Str := fmt.Sprintf("%d,%s", pod.ID, pod.CreatedAt.Format("2006-01-02 15:04:05.000"))

		imageUuid := utils.GetUUID()
		pathImage := "cpn/certify/" + imageUuid + ".jpg"

		absolutePath := path.Join(config.DiffusionFilePath, pathImage)
		if img, err := myimg.Base64ToImg(oReq.IdCardImgBase64); err != nil {
			//logger.Error(err, oReq.CoverBase64)
			msg = "base64转图片失败"
			logger.Error(msg, err)
			return
		} else {
			if err := myimg.ImgToFile(img, absolutePath); err != nil {
				msg = "图片保存失败"
				logger.Error(msg, err)
			} else {
				logger.Info(user.ID, "  图片保存成功")
				idCardImgDeletePath = certifyStudent.IdCardImg
				certifyStudent.IdCardImg = pathImage
			}
		}
	}

	if oReq.FrontImgBase64 != "" {
		logger.Info(user.ID, "开始更新正面图片")
		//oMd5Str := fmt.Sprintf("%d,%s", pod.ID, pod.CreatedAt.Format("2006-01-02 15:04:05.000"))

		imageUuid := utils.GetUUID()
		pathImage := "cpn/certify/" + imageUuid + ".jpg"

		absolutePath := path.Join(config.DiffusionFilePath, pathImage)
		if img, err := myimg.Base64ToImg(oReq.FrontImgBase64); err != nil {
			//logger.Error(err, oReq.CoverBase64)
			msg = "base64转图片失败"
			logger.Error(msg, err)
			return
		} else {
			if err := myimg.ImgToFile(img, absolutePath); err != nil {
				msg = "图片保存失败"
				logger.Error(msg, err)
			} else {
				logger.Info(user.ID, "  图片保存成功")
				frontImgDeletePath = certifyStudent.FrontImg
				certifyStudent.FrontImg = pathImage
			}
		}
	}
	if oReq.BackImgBase64 != "" {
		logger.Info(user.ID, "开始更新反面图片")
		//oMd5Str := fmt.Sprintf("%d,%s", pod.ID, pod.CreatedAt.Format("2006-01-02 15:04:05.000"))

		imageUuid := utils.GetUUID()
		pathImage := "cpn/certify/" + imageUuid + ".jpg"

		absolutePath := path.Join(config.DiffusionFilePath, pathImage)
		if img, err := myimg.Base64ToImg(oReq.BackImgBase64); err != nil {
			//logger.Error(err, oReq.CoverBase64)
			msg = "base64转图片失败"
			logger.Error(msg, err)
			return
		} else {
			if err := myimg.ImgToFile(img, absolutePath); err != nil {
				msg = "图片保存失败"
				logger.Error(msg, err)
			} else {
				logger.Info(user.ID, "  图片保存成功")
				backImgDeletePath = certifyStudent.BackImg
				certifyStudent.BackImg = pathImage
			}
		}
	}

	if err := certifyStudent.Save(); err != nil {
		msg = "保存失败"
		logger.Error(msg, err)
		return
	} else {
		if idCardImgDeletePath != "" && idCardImgDeletePath != certifyStudent.IdCardImg {
			absolutePath := path.Join(config.DiffusionFilePath, idCardImgDeletePath)
			if err := os.Remove(absolutePath); err != nil {
				logger.Error(err, absolutePath)
			}
		}

		if frontImgDeletePath != "" && frontImgDeletePath != certifyStudent.FrontImg {
			absolutePath := path.Join(config.DiffusionFilePath, frontImgDeletePath)
			if err := os.Remove(absolutePath); err != nil {
				logger.Error(err, absolutePath)
			}
		}

		if backImgDeletePath != "" && backImgDeletePath != certifyStudent.BackImg {
			absolutePath := path.Join(config.DiffusionFilePath, backImgDeletePath)
			if err := os.Remove(absolutePath); err != nil {
				logger.Error(err, absolutePath)
			}
		}

		if resp, err := obj.CertifyStudent2Resp(certifyStudent); err != nil {
			msg = "数据转换失败"
			logger.Error(err)
			return
		} else {
			result["student"] = resp
			msg = "数据上传成功，请等待审核"
			code = 0
			return
		}
	}
}

func (obj identityApi_) CompanyCertify(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq companyCertifyReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	claims := c.Value("claims").(*middleware.MyClaims)
	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "获取用户信息失败"
		logger.Error(err)
	}
	if user.CertName == "" || user.CertNo == "" {
		msg = "请先进行实名认证"
		return
	}

	if user.CompanyCerifyId > 0 {
		msg = "公司认证已通过"
		return
	}

	//logger.Info("oReq:", utils.GetJsonFromStruct(oReq), " userID:", claims.UserId)
	if oReq.CompanyNo == "" || oReq.CompanyName == "" {
		msg = "请输入公司信息"
		return
	}
	if oReq.LegalNo == "" || oReq.LegalName == "" {
		msg = "请输入法人信息"
		return
	}

	var certifyCompany model.CertifyCompany
	if err := certifyCompany.GetByUserId(claims.UserId); err != nil {
		if err != gorm.ErrRecordNotFound {
			msg = "查询失败"
			logger.Error(msg, err)
		}
	}

	if certifyCompany.Status == 1 {
		msg = "该账号公司认证已通过"
		return
	}

	if certifyCompany.Nanoid == "" {
		certifyCompany.Nanoid = utils.Generate12NanoId()
	}

	if certifyCompany.UserId == 0 {
		certifyCompany.UserId = claims.UserId
	}
	if certifyCompany.CertChannel == "" {
		certifyCompany.CertChannel = enums.CertifyChannelEnum.Manual
	}
	if certifyCompany.CertType == "" {
		certifyCompany.CertType = model.CertifyCompanyCertType_BUSINESS_LICENSE
	}
	certifyCompany.CompanyName = oReq.CompanyName
	certifyCompany.CompanyNo = oReq.CompanyNo
	certifyCompany.LegalName = oReq.LegalName
	certifyCompany.LegalNo = oReq.LegalNo

	companyImgDeletePath := ""
	frontImgDeletePath := ""
	backImgDeletePath := ""
	if oReq.CompanyImgBase64 != "" {
		logger.Info(user.ID, "开始更新Company图")
		//oMd5Str := fmt.Sprintf("%d,%s", pod.ID, pod.CreatedAt.Format("2006-01-02 15:04:05.000"))

		imageUuid := utils.GetUUID()
		pathImage := "cpn/certify/" + imageUuid + ".jpg"

		absolutePath := path.Join(config.DiffusionFilePath, pathImage)
		if img, err := myimg.Base64ToImg(oReq.CompanyImgBase64); err != nil {
			//logger.Error(err, oReq.CoverBase64)
			msg = "base64转图片失败"
			logger.Error(msg, err)
			return
		} else {
			if err := myimg.ImgToFile(img, absolutePath); err != nil {
				msg = "图片保存失败"
				logger.Error(msg, err)
			} else {
				logger.Info(user.ID, "  图片保存成功")
				companyImgDeletePath = certifyCompany.CompanyImg
				certifyCompany.CompanyImg = pathImage
			}
		}
	}
	if oReq.FrontImgBase64 != "" {
		logger.Info(user.ID, "开始更新正面图片")
		//oMd5Str := fmt.Sprintf("%d,%s", pod.ID, pod.CreatedAt.Format("2006-01-02 15:04:05.000"))

		imageUuid := utils.GetUUID()
		pathImage := "cpn/certify/" + imageUuid + ".jpg"

		absolutePath := path.Join(config.DiffusionFilePath, pathImage)
		if img, err := myimg.Base64ToImg(oReq.FrontImgBase64); err != nil {
			//logger.Error(err, oReq.CoverBase64)
			msg = "base64转图片失败"
			logger.Error(msg, err)
			return
		} else {
			if err := myimg.ImgToFile(img, absolutePath); err != nil {
				msg = "图片保存失败"
				logger.Error(msg, err)
			} else {
				logger.Info(user.ID, "  图片保存成功")
				frontImgDeletePath = certifyCompany.FrontImg
				certifyCompany.FrontImg = pathImage
			}
		}
	}
	if oReq.BackImgBase64 != "" {
		logger.Info(user.ID, "开始更新反面图片")
		//oMd5Str := fmt.Sprintf("%d,%s", pod.ID, pod.CreatedAt.Format("2006-01-02 15:04:05.000"))

		imageUuid := utils.GetUUID()
		pathImage := "cpn/certify/" + imageUuid + ".jpg"

		absolutePath := path.Join(config.DiffusionFilePath, pathImage)
		if img, err := myimg.Base64ToImg(oReq.BackImgBase64); err != nil {
			//logger.Error(err, oReq.CoverBase64)
			msg = "base64转图片失败"
			logger.Error(msg, err)
			return
		} else {
			if err := myimg.ImgToFile(img, absolutePath); err != nil {
				msg = "图片保存失败"
				logger.Error(msg, err)
			} else {
				logger.Info(user.ID, "  图片保存成功")
				backImgDeletePath = certifyCompany.BackImg
				certifyCompany.BackImg = pathImage
			}
		}
	}

	if err := certifyCompany.Save(); err != nil {
		msg = "保存失败"
		logger.Error(msg, err)
		return
	} else {
		if companyImgDeletePath != "" && companyImgDeletePath != certifyCompany.CompanyImg {
			absolutePath := path.Join(config.DiffusionFilePath, companyImgDeletePath)
			if err := os.Remove(absolutePath); err != nil {
				logger.Error(err, absolutePath)
			}
		}

		if frontImgDeletePath != "" && frontImgDeletePath != certifyCompany.FrontImg {
			absolutePath := path.Join(config.DiffusionFilePath, frontImgDeletePath)
			if err := os.Remove(absolutePath); err != nil {
				logger.Error(err, absolutePath)
			}
		}

		if backImgDeletePath != "" && backImgDeletePath != certifyCompany.BackImg {
			absolutePath := path.Join(config.DiffusionFilePath, backImgDeletePath)
			if err := os.Remove(absolutePath); err != nil {
				logger.Error(err, absolutePath)
			}
		}

		if resp, err := obj.CertifyCompany2Resp(certifyCompany); err != nil {
			msg = "数据转换失败"
			logger.Error(err)
			return
		} else {
			result["company"] = resp
			msg = "数据上传成功，请等待审核"
			code = 0
			return
		}
	}
}

func (obj identityApi_) CertifyCompany2Resp(certifyCompany model.CertifyCompany) (companyCertifyInfoResp, error) {
	var resp companyCertifyInfoResp
	if err := utils.Scan(certifyCompany, &resp); err != nil {
		logger.Error(err)
		return resp, err
	}
	if resp.CompanyImg != "" {
		resp.CompanyImg = fmt.Sprintf("%s%s", config.DiffusionDomain, resp.CompanyImg)
	}
	if resp.FrontImg != "" {
		resp.FrontImg = fmt.Sprintf("%s%s", config.DiffusionDomain, resp.FrontImg)
	}
	if resp.BackImg != "" {
		resp.BackImg = fmt.Sprintf("%s%s", config.DiffusionDomain, resp.BackImg)
	}
	resp.StatusTxt = model.CertifyCompanyStatusTxt(resp.Status)
	return resp, nil
}

func (obj identityApi_) CertifyStudent2Resp(certifyStudent model.CertifyStudent) (studentCertifyInfoResp, error) {
	var resp studentCertifyInfoResp
	if err := utils.Scan(certifyStudent, &resp); err != nil {
		logger.Error(err)
		return resp, err
	}
	if resp.IdCardImg != "" {
		resp.IdCardImg = fmt.Sprintf("%s%s", config.DiffusionDomain, certifyStudent.IdCardImg)
	}
	if resp.FrontImg != "" {
		resp.FrontImg = fmt.Sprintf("%s%s", config.DiffusionDomain, resp.FrontImg)
	}
	if resp.BackImg != "" {
		resp.BackImg = fmt.Sprintf("%s%s", config.DiffusionDomain, resp.BackImg)
	}
	resp.StatusTxt = model.CertifyCompanyStatusTxt(resp.Status)
	return resp, nil
}

func (obj identityApi_) CompanyInfo(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		if err == gorm.ErrRecordNotFound {
			result["company"] = companyCertifyInfoResp{}
			code = 0
			return
		}
		msg = "获取用户信息失败"
		logger.Error(err)
	}

	var certifyCompany model.CertifyCompany
	if err := certifyCompany.GetByUserId(claims.UserId); err != nil {
		if err == gorm.ErrRecordNotFound {
			result["company"] = companyCertifyInfoResp{}
			code = 0
			return
		}
		msg = "数据查询失败"
		logger.Error(msg, err)
		return
	}

	if resp, err := obj.CertifyCompany2Resp(certifyCompany); err != nil {
		msg = "数据转换失败"
		logger.Error(err)
		return
	} else {
		result["company"] = resp
		code = 0
	}
}

func (obj identityApi_) StudentInfo(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		if err == gorm.ErrRecordNotFound {
			result["company"] = companyCertifyInfoResp{}
			code = 0
			return
		}
		msg = "获取用户信息失败"
		logger.Error(err)
	}

	var certifyStudent model.CertifyStudent
	if err := certifyStudent.GetByUserId(claims.UserId); err != nil {
		if err == gorm.ErrRecordNotFound {
			result["student"] = studentCertifyInfoResp{}
			code = 0
			return
		}
		msg = "数据查询失败"
		logger.Error(msg, err)
		return
	}

	if resp, err := obj.CertifyStudent2Resp(certifyStudent); err != nil {
		msg = "数据转换失败"
		logger.Error(err)
		return
	} else {
		result["student"] = resp
		code = 0
	}
}
