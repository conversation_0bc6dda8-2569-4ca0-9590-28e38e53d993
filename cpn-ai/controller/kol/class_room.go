package kol

import (
	"bytes"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/myimg"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/structs"
	"fmt"
	"io"
	"net/http"
	"os"
	"path"
	"runtime"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

type classRoomApi_ struct {
}

var ClassRoomApi classRoomApi_

type classRoomReq struct {
	ClassRoomUuid string `json:"class_room_uuid"`
	Status        int    `json:"status"`
	Kw            string `json:"kw"`
	Page          int    `json:"page"`
	PageSize      int    `json:"page_size"`
}

type setClassRoomReq struct {
	Action        string `json:"action"`
	ClassRoomUuid string `json:"class_room_uuid"`
	Status        int    `json:"status"`
	Remark        string `json:"remark"`
}

type listPodReq struct {
	ClassRoomUuid string `json:"class_room_uuid"`
	Status        int    `json:"status"`
	Kw            string `json:"kw"`
	Page          int    `json:"page"`
	PageSize      int    `json:"page_size"`
}

type classUserReq struct {
	ClassUserUuid string `json:"class_user_uuid"`
	ClassRoomUuid string `json:"class_room_uuid"`
	Status        int    `json:"status"`
	Kw            string `json:"kw"`
	Page          int    `json:"page"`
	PageSize      int    `json:"page_size"`
}

type applyClassRoomReq struct {
	ClassRoomUuid string `json:"class_room_uuid"`
	Title         string `json:"title"`
	Des           string `json:"des"`
	Markdown      string `json:"markdown"`
	PodUuids      string `json:"pod_uuids"`
	CoverBase64   string `json:"cover_base64"`
	LogoBase64    string `json:"logo_base64"`
	Status        int    `json:"status"`
}

type auditClassRoomReq struct {
	ClassRoomUuid string `json:"class_room_uuid"`
	RejectReason  string `json:"reject_reason"`
}

type bindStuReq struct {
	Mobile         string `json:"mobile"`
	ClassRoomUuids string `json:"class_room_uuids"`
}

type removeStuReq struct {
	ClassUserUuid string `json:"class_user_uuid"`
}

type setStuReq struct {
	Action         string `json:"action"`
	ClassUserUuid  string `json:"class_user_uuid"`
	Status         int    `json:"status"`
	Remark         string `json:"remark"`
	ClassRoomUuids string `json:"class_room_uuids"`
}

type simpleClassRoom struct {
	ID    uint   `json:"-"`
	Uuid  string `json:"uuid"`
	Title string `json:"title"`
}

type simplePod struct {
	ID        uint      `json:"-"`
	Uuid      string    `json:"uuid"`
	Title     string    `json:"title"`
	Desc      string    `json:"-"`
	Markdown  string    `json:"-"`
	Logo      string    `json:"logo"`
	Cover     string    `json:"cover"`
	UserId    uint      `json:"-"`
	Status    int       `json:"status"`
	StatusTxt string    `json:"status_txt"`
	UpdatedAt time.Time `json:"-"`
}

type classRoomResp struct {
	ID              uint                   `json:"-"`
	Uuid            string                 `json:"uuid"`
	Title           string                 `json:"title"`
	Des             string                 `json:"des"`
	Markdown        string                 `json:"markdown"`
	Logo            string                 `json:"logo"`
	Cover           string                 `json:"cover"`
	PodIds          string                 `json:"-"`
	Pods            []simplePod            `json:"pods" gorm:"-"`
	Status          int                    `json:"status"`
	StatusTxt       string                 `json:"status_txt"`
	AuditStatus     int                    `json:"audit_status"`
	AuditStatusTxt  string                 `json:"audit_status_txt"`
	AuditContent    string                 `json:"-"`
	AuditContentMap map[string]interface{} `json:"audit_content"`
	CreatedAt       jsontime.JsonTime      `json:"created_at"`
	UpdatedAt       jsontime.JsonTime      `json:"updated_at"`
}

type classUserResp struct {
	ID            uint              `json:"-"`
	Uuid          string            `json:"uuid"`
	UserId        uint              `json:"-"`
	StuUserId     uint              `json:"-"`
	FirstMobile   string            `json:"first_mobile"`
	ClassRoomIds  string            `json:"-"`
	ClassRooms    []simpleClassRoom `json:"class_rooms" gorm:"-"`
	Remark        string            `json:"remark"`
	Status        int               `json:"status"`
	StatusTxt     string            `json:"status_txt"`
	StatusExplain string            `json:"status_explain"`
}

func (obj classRoomApi_) Apply(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "获取用户信息失败"
		logger.Error(err)
		return
	}
	if user.UserType != enums.UserTypeEnum.Kol {
		msg = "不是KOL用户，请联系申请成为KOL"
		return
	}

	var oReq applyClassRoomReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ClassRoomUuid == "" && oReq.Title == "" {
		msg = "请输入标题"
		return
	}
	if oReq.ClassRoomUuid == "" && oReq.Des == "" {
		msg = "请输入简介"
		return
	}

	//podAudit := structs.PodAudit{Title: oReq.Title, Desc: oReq.Desc}
	var auditContent structs.AuditContent
	var classRoom model.ClassRoom

	if oReq.ClassRoomUuid != "" {
		if err := classRoom.GetByUuid(oReq.ClassRoomUuid); err != nil {
			msg = "课堂信息获取失败"
			logger.Error(msg, err)
			return
		}
		if classRoom.AuditContent != "" {
			if err := utils.GetStructFromJson(&auditContent, classRoom.AuditContent); err != nil {
				logger.Error(err)
			}
		}
	}

	if oReq.Title != "" && oReq.Title != classRoom.Title {
		auditContent.Title = oReq.Title
	}
	if oReq.Des != "" && oReq.Des != classRoom.Des {
		auditContent.Des = oReq.Des
	}

	if oReq.Markdown != "" && oReq.Markdown != classRoom.Markdown {
		auditContent.Markdown = oReq.Markdown
	}

	logoDeletePath := ""
	coverDeletePath := ""
	if oReq.LogoBase64 != "" {
		logger.Info(classRoom.ID, "开始更新logo图")
		//oMd5Str := fmt.Sprintf("%d,%s", pod.ID, pod.CreatedAt.Format("2006-01-02 15:04:05.000"))

		logoUuid := utils.GetUUID()
		pathImage := "cpn/pod_logo/" + logoUuid + ".jpg"

		absolutePath := path.Join(config.DiffusionFilePath, pathImage)

		if img, err := myimg.Base64ToImg(oReq.LogoBase64); err != nil {
			//logger.Error(err, oReq.CoverBase64)
			msg = "base64转图片失败"
			logger.Error(msg, err)
			return
		} else {
			logger.Info(classRoom.ID, "   x:", img.Bounds().Size().X, "   y:", img.Bounds().Size().Y)
			x := img.Bounds().Size().X
			y := img.Bounds().Size().Y
			if x != y {
				msg = "Logo图片尺寸为1:1"
			} else {
				small := myimg.ResizeImg(180, 180, img, true)
				if err := myimg.ImgToFile(small, absolutePath); err != nil {
					msg = "缩略图保存失败"
					logger.Error(msg, err)
				} else {
					logger.Info(classRoom.ID, "  缩略图生成成功")
					logoDeletePath = auditContent.Logo
					auditContent.Logo = pathImage
				}
			}
		}

	}

	if oReq.CoverBase64 != "" {
		logger.Info(classRoom.ID, "开始更新封面图")
		converUuid := utils.GetUUID()
		pathImage := "cpn/pod_logo/" + converUuid + "_cover.jpg"

		absolutePath := path.Join(config.DiffusionFilePath, pathImage)

		if img, err := myimg.Base64ToImg(oReq.CoverBase64); err != nil {
			//logger.Error(err, oReq.CoverBase64)
			msg = "base64转图片失败"
			logger.Error(msg, err)
			return
		} else {

			x := float64(img.Bounds().Size().X)
			y := float64(img.Bounds().Size().Y)
			if x/y < 1.51 {
				msg = "封面图片需要是长方形的"
				logger.Error(msg, "  x:", x, "   y:", y, "   x/y:", x/y)
			} else {
				if err := myimg.ImgToFile(img, absolutePath); err != nil {
					msg = "封面图保存失败"
					logger.Error(msg, err)
				} else {
					logger.Info(classRoom.ID, "  封面图生成成功")
					coverDeletePath = auditContent.Cover
					auditContent.Cover = pathImage
				}
			}
		}

	}

	auditContentStr := utils.GetJsonFromStruct(auditContent)
	if auditContentStr == "" {
		msg = "保存审核数据失败"
		logger.Error(msg, auditContent)
		return
	}

	if classRoom.ID == 0 {
		classRoom.Uuid = utils.GetUUID()
		classRoom.UserId = claims.UserId
		classRoom.AuditContent = auditContentStr
		classRoom.AuditStatus = enums.PodAuditStatusEnum.Makeing
		if err := classRoom.Save(); err != nil {
			msg = "保存失败"
			logger.Error(msg, err, oReq)
			return
		}
	} else {
		if classRoom.UserId != claims.UserId {
			msg = "无权限"
			return
		}
		mm := make(map[string]interface{})

		if classRoom.AuditStatus != enums.PodAuditStatusEnum.Makeing {
			mm["audit_status"] = enums.PodAuditStatusEnum.Makeing
		}

		if classRoom.AuditContent != auditContentStr {
			mm["audit_content"] = auditContentStr
		}
		//oldPodIds := classRoom.PodIds
		//if oldPodIds != "" {
		//	ary := utils.Ids2UintAry(oldPodIds)
		//	for _, podId := range ary {
		//		if strings.Contains(podIds, fmt.Sprintf("|%d|", podId)) {
		//			continue
		//		}
		//		var pod model.Pod
		//		if err := pod.GetById(podId); err != nil {
		//			msg = "获取Pod信息失败"
		//			logger.Error(msg, err)
		//			return
		//		} else {
		//			if err := pod.RemoveClassRoomId(classRoom.ID); err != nil {
		//				msg = "移除失败"
		//				logger.Error(msg, err)
		//				return
		//			}
		//		}
		//	}
		//}
		//mm["pod_ids"] = podIds

		if err := classRoom.Updates(mm); err != nil {
			msg = "保存失败"
			logger.Error(msg, err, oReq)
			return
		}
	}

	if logoDeletePath != "" && logoDeletePath != classRoom.Logo {
		absolutePath := path.Join(config.DiffusionFilePath, logoDeletePath)
		if err := os.Remove(absolutePath); err != nil {
			logger.Error(err, absolutePath)
		}
	}

	if coverDeletePath != "" && coverDeletePath != classRoom.Cover {
		absolutePath := path.Join(config.DiffusionFilePath, coverDeletePath)
		if err := os.Remove(absolutePath); err != nil {
			logger.Error(err, absolutePath)
		}
	}

	podIds := ""
	if oReq.PodUuids != "" {
		ary := strings.Split(oReq.PodUuids, "|")
		for _, val := range ary {
			if val == "" {
				continue
			}
			var pod model.Pod
			if err := pod.GetByUuidFromCache(val); err != nil {
				msg = "获取Pod信息失败"
				logger.Error(msg, err)
				return
			}
			podIds += fmt.Sprintf("%d|", pod.ID)
		}
		if podIds != "" {
			podIds = "|" + podIds
		}
		if err := classRoom.SetPodIds(podIds); err != nil {
			msg = "设置Pod失败"
			logger.Error(msg, err)
			return
		}
	}

	result["class_room_uuid"] = classRoom.Uuid
	msg = "保存成功，如无其他修改请提交审核 " + msg
	code = 0
}

func (obj classRoomApi_) Auditing(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq auditClassRoomReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ClassRoomUuid == "" {
		msg = "参数错误"
		return
	}

	var classRoom model.ClassRoom
	if err := classRoom.GetByUuid(oReq.ClassRoomUuid); err != nil {
		msg = "课堂信息获取失败"
		logger.Error(msg, err, oReq)
		return
	}

	if classRoom.AuditStatus == enums.PodAuditStatusEnum.AuditPass {
		msg = "审核已通过，请勿重复操作"
		return
	}

	if classRoom.AuditStatus == enums.PodAuditStatusEnum.Auditing {
		msg = "审核申请已提交，请勿重复操作"
		//go func() {
		//	//jsonStr := `{"name":"John", "class":101}`
		//	//c.Request.Body = ioutil.NopCloser(bytes.NewBufferString(jsonStr))
		//	nReq := auditClassRoomReq{
		//		ClassRoomUuid: classRoom.Uuid,
		//		RejectReason:  "审核通过",
		//	}
		//	jsonStr := utils.GetJsonFromStruct(nReq)
		//	c.Request.Body = io.NopCloser(bytes.NewBufferString(jsonStr))
		//	obj.Audited(c)
		//
		//}()
		return
	}

	if classRoom.AuditStatus != enums.PodAuditStatusEnum.Makeing {
		msg = "审核状态不正确"
		logger.Error(msg, oReq, "  pod.AuditStatus：", classRoom.AuditStatus)
		return
	}

	if err := classRoom.SetAuditStatus(enums.PodAuditStatusEnum.Auditing); err != nil {
		msg = "设置审核状态失败"
		logger.Error(msg, err)
		return
	} else {
		go func() {
			//jsonStr := `{"name":"John", "class":101}`
			//c.Request.Body = ioutil.NopCloser(bytes.NewBufferString(jsonStr))
			nReq := auditClassRoomReq{
				ClassRoomUuid: classRoom.Uuid,
				RejectReason:  "审核通过",
			}
			jsonStr := utils.GetJsonFromStruct(nReq)
			c.Request.Body = io.NopCloser(bytes.NewBufferString(jsonStr))
			obj.Audited(c)

		}()
		code = 0
		msg = "正在审核中，请稍后"
		return
	}
}

func (obj classRoomApi_) Audited(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	adminUserId := uint(0)
	userId := uint(0)
	if c.Value("center_claims") != nil {
		claims := c.Value("center_claims").(*middleware.CenterClaims)
		if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
			msg = "权限不足"
			return
		}
		adminUserId = claims.UserId
	} else {
		claims := c.Value("claims").(*middleware.MyClaims)
		if claims == nil {
			msg = "请先登录"
			code = 2
			return
		}
		userId = claims.UserId
	}

	var oReq auditClassRoomReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ClassRoomUuid == "" {
		msg = "参数错误"
		return
	}
	if oReq.RejectReason == "" {
		msg = "请输入审核内容"
		return
	}

	var classRoom model.ClassRoom
	if err := classRoom.GetByUuid(oReq.ClassRoomUuid); err != nil {
		msg = "pod信息获取失败"
		logger.Error(msg, err, oReq)
		return
	}

	if userId > 0 && classRoom.UserId != userId {
		msg = "无权限"
		return
	}

	if classRoom.AuditStatus != enums.PodAuditStatusEnum.Auditing {
		msg = "未提交审核申请"
		logger.Error(msg, oReq, "  pod.AuditStatus：", classRoom.AuditStatus)
		return
	}

	var auditContent structs.AuditContent
	if err := utils.GetStructFromJson(&auditContent, classRoom.AuditContent); err != nil {
		msg = "获取修改信息失败"
		logger.Error(msg, err, oReq)
		return
	}

	if oReq.RejectReason != "审核通过" {
		auditContent.RejectReason = oReq.RejectReason
		str := utils.GetJsonFromStruct(auditContent)
		if str == "" {
			msg = "序列化审核数据失败"
			logger.Error(msg, str)
			return
		}
		if err := classRoom.SetAuditContent(str); err != nil {
			msg = "保存审核结果失败"
			logger.Error(msg, err)
			return
		}

		if err := classRoom.SetAuditStatus(enums.PodAuditStatusEnum.AuditReject); err != nil {
			msg = "设置审核状态失败"
			logger.Error(msg, err)
			return
		} else {
			code = 0
			msg = "审核驳回完成"
			return
		}
	}

	classRoom.AuditContent = "{}"

	if adminUserId > 0 {
		logJson := utils.GetJsonFromStruct(classRoom)
		operationLog := model.OperationLog{
			OperatorUserId: adminUserId,
			LogType:        enums.OperationLogTypeEnum.ClassRoom,
			OrigWhere:      enums.OperationOrigWhereEnum.ClassRoom,
			OrigId:         classRoom.ID,
			Ip:             utils.GetClientIp(c.Request.Header),
			LogJson:        logJson,
			UserEnv:        "{}",
		}
		if err := operationLog.Save(); err != nil {
			msg = "备份信息失败"
			logger.Error(msg, err)
			return
		}
	}

	//pod.AuditStatus = enums.PodAuditStatusEnum.AuditPass

	mm := make(map[string]interface{})
	if auditContent.Title != "" && auditContent.Title != classRoom.Title {
		mm["title"] = auditContent.Title
	}

	if auditContent.Des != "" && auditContent.Des != classRoom.Des {
		mm["des"] = auditContent.Des
	}

	//logger.Info("markdown:", podAuditContent.Markdown, "    ", utils.GetJsonFromStruct(podAuditContent))
	//logger.Info("pod.Markdown:", pod.Markdown, "    ", utils.GetJsonFromStruct(podAuditContent))
	if auditContent.Markdown != "" && auditContent.Markdown != classRoom.Markdown {
		mm["markdown"] = auditContent.Markdown
	}

	if auditContent.Cover != "" && auditContent.Cover != classRoom.Cover {
		mm["cover"] = auditContent.Cover
	}

	if auditContent.Logo != "" && auditContent.Logo != classRoom.Logo {
		mm["logo"] = auditContent.Logo
	}

	mm["audit_content"] = "{}"
	mm["audit_status"] = enums.PodAuditStatusEnum.AuditPass

	logoDeletePath := ""
	coverDeletePath := ""
	if auditContent.Logo != "" && auditContent.Logo != classRoom.Logo {
		logoDeletePath = classRoom.Logo
	}
	if auditContent.Cover != "" && auditContent.Cover != classRoom.Cover {
		coverDeletePath = classRoom.Cover
	}
	if err := classRoom.Updates(mm); err != nil {
		msg = "保存失败"
		logger.Error(msg, err, oReq, "  ", utils.GetJsonFromStruct(mm))
		return
	}

	if logoDeletePath != "" {
		absolutePath := path.Join(config.DiffusionFilePath, logoDeletePath)
		if err := os.Remove(absolutePath); err != nil {
			logger.Error(err, absolutePath)
		}
	}

	if coverDeletePath != "" {
		absolutePath := path.Join(config.DiffusionFilePath, coverDeletePath)
		if err := os.Remove(absolutePath); err != nil {
			logger.Error(err, absolutePath)
		}
	}
	msg = "审核通过"
	code = 0
}

func (obj classRoomApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq classRoomReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page < 1 {
		oReq.Page = 1
	}
	if oReq.PageSize < 1 || oReq.PageSize > 50 {
		oReq.PageSize = 50
	}

	queryParm := utils.MakeInterfaceMap()
	queryParm["user_id"] = claims.UserId
	var classRoom model.ClassRoom
	if oReq.ClassRoomUuid != "" {
		if err := classRoom.GetByUuid(oReq.ClassRoomUuid); err != nil {
			msg = "记录不存在"
			logger.Error(msg, err, oReq)
			return
		}
		queryParm["id"] = classRoom.ID
	}

	var ary = make([]classRoomResp, 0)
	if total, err := classRoom.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {

			pods := make([]simplePod, 0)
			podIds := utils.Ids2UintAry(ary[i].PodIds)
			for _, podId := range podIds {
				var pod model.Pod
				if err := pod.GetByIdFromCache(podId); err != nil {
					logger.Error(err, " ", podId)
				} else {
					pods = append(pods, simplePod{ID: pod.ID, Uuid: pod.Uuid, Title: pod.Title})
				}
			}
			ary[i].Pods = pods

			if ary[i].AuditContent != "" {
				mm := utils.GetMapFromJson(ary[i].AuditContent)
				if mm != nil {
					if _, ok := mm["logo"]; ok {
						tmp := mm["logo"].(string)
						if tmp != "" {
							mm["logo"] = config.DiffusionDomain + tmp
							if ary[i].Logo == "" {
								ary[i].Logo = tmp
							}
						}
					}
					if _, ok := mm["cover"]; ok {
						tmp := mm["cover"].(string)
						if tmp != "" {
							mm["cover"] = config.DiffusionDomain + tmp
							if ary[i].Cover == "" {
								ary[i].Cover = tmp
							}
						}
					}
					if _, ok := mm["title"]; ok {
						tmp := mm["title"].(string)
						if tmp != "" && ary[i].Title == "" {
							ary[i].Title = tmp
						}
					}
					if _, ok := mm["des"]; ok {
						tmp := mm["des"].(string)
						if tmp != "" && ary[i].Des == "" {
							ary[i].Des = tmp
						}
					}
				}
				ary[i].AuditContentMap = mm
			}

			ary[i].AuditStatusTxt = enums.PodAuditStatusEnum.Name(ary[i].AuditStatus)

			if ary[i].Status == 1 {
				ary[i].StatusTxt = "已上架"
			} else {
				ary[i].StatusTxt = "未上架"
			}

			if ary[i].Logo != "" {
				ary[i].Logo = fmt.Sprintf("%s%s", config.DiffusionDomain, ary[i].Logo)
			}
			if ary[i].Cover != "" {
				ary[i].Cover = fmt.Sprintf("%s%s", config.DiffusionDomain, ary[i].Cover)
			}
		}
		result["class_rooms"] = ary
		result["total"] = total
	}

	msg = ""
	code = 0
}

func (obj classRoomApi_) Set(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq setClassRoomReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Action == "" {
		msg = "参数错误"
		return
	}
	if oReq.ClassRoomUuid == "" {
		msg = "参数错误"
		return
	}

	var classRoom model.ClassRoom
	if err := classRoom.GetByUuid(oReq.ClassRoomUuid); err != nil {
		msg = "查询记录失败"
		logger.Error(msg, err)
		return
	} else {
		if classRoom.UserId != claims.UserId {
			msg = "无权限"
			return
		}
		if oReq.Action == "status" {
			if oReq.Status < 0 || oReq.Status > 2 {
				msg = "状态参数错误"
				return
			}
			if err := classRoom.SetStatus(oReq.Status); err != nil {
				msg = "设置失败"
				logger.Error(msg, err)
				return
			} else {
				msg = "设置成功"
				code = 0
				return
			}
		} else {
			msg = "设置参数错误"
			return
		}
	}
}

func (obj classRoomApi_) ListPod(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq listPodReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page < 1 {
		oReq.Page = 1
	}
	if oReq.PageSize < 1 || oReq.PageSize > 50 {
		oReq.PageSize = 50
	}

	queryParm := utils.MakeInterfaceMap()
	queryParm["user_id"] = claims.UserId
	//queryParm["status"] = 1
	if oReq.Kw != "" {
		queryParm["kw"] = oReq.Kw
	}

	if oReq.ClassRoomUuid != "" {
		var classRoom model.ClassRoom
		if err := classRoom.GetByUuid(oReq.ClassRoomUuid); err != nil {
			msg = "查询课堂信息失败"
			logger.Error(msg, err)
			return
		}
		podIds := make([]uint, 0)
		if classRoom.PodIds != "" {
			ary := strings.Split(classRoom.PodIds, "|")
			for _, val := range ary {
				if val == "" {
					continue
				}
				if podId := utils.String2Uint(val); podId > 0 {
					podIds = append(podIds, podId)
				}
			}
		}
		queryParm["pod_ids"] = podIds
	}

	var ary = make([]simplePod, 0)
	var pod model.Pod
	if total, err := pod.ListPro(&ary, queryParm, oReq.Page, oReq.PageSize, ""); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {

			if ary[i].Logo != "" {
				ary[i].Logo = fmt.Sprintf("%s%s?t=%d", config.DiffusionDomain, ary[i].Logo, ary[i].UpdatedAt.Unix())
			}
			if ary[i].Cover != "" {
				ary[i].Cover = fmt.Sprintf("%s%s?t=%d", config.DiffusionDomain, ary[i].Cover, ary[i].UpdatedAt.Unix())
			}

			ary[i].StatusTxt = "未知"
			if ary[i].Status == 0 {
				ary[i].StatusTxt = "未上线"
			} else {
				ary[i].StatusTxt = "已上线"
			}
		}
		result["pods"] = ary
		result["total"] = total
	}

	msg = ""
	code = 0
}

func (obj classRoomApi_) ListStu(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq classUserReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page < 1 {
		oReq.Page = 1
	}
	if oReq.PageSize < 1 || oReq.PageSize > 50 {
		oReq.PageSize = 50
	}

	queryParm := utils.MakeInterfaceMap()
	queryParm["user_id"] = claims.UserId
	var classRoom model.ClassRoom
	if oReq.ClassRoomUuid != "" {
		if err := classRoom.GetByUuid(oReq.ClassRoomUuid); err != nil {
			msg = "记录不存在"
			logger.Error(msg, err, oReq)
			return
		}
	}

	if oReq.ClassUserUuid != "" {
		var classUser model.ClassUser
		if err := classUser.GetByUuid(oReq.ClassUserUuid); err != nil {
			msg = "查询学员信息失败"
			logger.Error(msg, err)
			return
		}
		queryParm["id"] = classUser.ID
	}

	if classRoom.ID > 0 {
		queryParm["class_room_id"] = classRoom.ID
	}
	if oReq.Kw != "" {
		queryParm["kw"] = oReq.Kw
	}
	var ary = make([]classUserResp, 0)
	var classUser model.ClassUser
	if total, err := classUser.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {
			classRooms := make([]simpleClassRoom, 0)
			classRoomIds := utils.Ids2UintAry(ary[i].ClassRoomIds)
			for _, classRoomId := range classRoomIds {
				var tmpClassRoom model.ClassRoom
				if err := tmpClassRoom.GetByIdFromCache(classRoomId); err != nil {
					logger.Error(err, " ", classRoomId)
				} else {

					title := tmpClassRoom.Title
					if tmpClassRoom.Title == "" && tmpClassRoom.AuditContent != "" {
						mm := utils.GetMapFromJson(tmpClassRoom.AuditContent)
						if mm != nil {
							if _, ok := mm["title"]; ok {
								title = mm["title"].(string)
							}
						}
					}

					classRooms = append(classRooms, simpleClassRoom{ID: tmpClassRoom.ID, Uuid: tmpClassRoom.Uuid, Title: title})
				}
			}
			ary[i].ClassRooms = classRooms
			ary[i].StatusTxt = enums.ClassRoomEnum.StuUserStatusName(ary[i].Status)
			ary[i].StatusExplain = enums.ClassRoomEnum.StuUserStatusExplain(ary[i].Status)
		}
		result["class_users"] = ary
		result["total"] = total
	}

	msg = ""
	code = 0
}

func (obj classRoomApi_) BindStu(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq bindStuReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Mobile == "" {
		msg = "参数错误"
		return
	}

	var stu model.User
	if err := stu.GetByMobile(oReq.Mobile); err != nil {
		if err == gorm.ErrRecordNotFound {
			msg = "用户不存在"
			return
		}
		msg = "查询失败"
		logger.Error(msg, err)
		return
	}

	classRoomIds := ""
	aryClassRoom := make([]model.ClassRoom, 0)
	var classRoom model.ClassRoom
	if err := classRoom.GetsByUuids(&aryClassRoom, oReq.ClassRoomUuids); err != nil {
		msg = "查询课堂失败"
		logger.Error(msg, err)
	}
	for _, item := range aryClassRoom {
		if item.UserId != claims.UserId {
			msg = "课堂权限不一致"
			logger.Error(msg, utils.GetJsonFromStruct(item), "  userId:", claims.UserId)
			return
		}
		classRoomIds += fmt.Sprintf("%d|", item.ID)
	}
	if classRoomIds != "" {
		classRoomIds = "|" + classRoomIds
	}

	var classUser model.ClassUser
	if err := classUser.GetByUserAndStu(claims.UserId, stu.ID); err != nil {
		if err == gorm.ErrRecordNotFound {
			classUser = model.ClassUser{
				Uuid:         utils.GetUUID(),
				UserId:       claims.UserId,
				StuUserId:    stu.ID,
				FirstMobile:  oReq.Mobile,
				ClassRoomIds: classRoomIds,
				Status:       1,
			}
			if err := classUser.Save(); err != nil {
				msg = "添加失败"
				logger.Error(msg, err)
				return
			} else {
				msg = "添加成功"
				code = 0
				return
			}
		} else {
			msg = "查询记录失败"
			logger.Error(msg, err)
			return
		}
	} else {
		if classUser.ClassRoomIds != classRoomIds {
			if classUser.SetClassRoomIds(classRoomIds) != nil {
				msg = "更新失败"
				logger.Error(msg)
				return
			}
		}
		msg = "设置成功"
		code = 0
		return
	}
}

func (obj classRoomApi_) BatchStu(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	fh, err := c.FormFile("excel")
	if err != nil {
		msg = "获取excel文件失败"
		logger.Error(msg, err)
		return
	}
	if fh.Size > 10*1024*1024 {
		msg = "excel文件太大"
		return
	}
	mf, err := fh.Open()
	if err != nil {
		msg = "获取excel文件失败"
		logger.Error(msg, err)
		return
	}
	defer mf.Close()

	ef, err := excelize.OpenReader(mf)
	if err != nil {
		msg = "打开excel文件失败"
		logger.Error(msg, err)
		return
	}
	a := ef.GetSheetList()
	if len(a) == 0 {
		msg = "空excel文件"
		return
	}
	rows, err := ef.GetRows(a[0])
	if err != nil {
		msg = "读取excel文件失败"
		logger.Error(msg, err)
		return
	}
	if len(rows) == 0 {
		msg = "excel文件没有行"
		return
	}

	type Row struct {
		UserID uint
		Mobile string
	}
	var lines []Row

	for i, a := range rows {
		if len(a) != 1 {
			msg = fmt.Sprintf("第%d行只能1列，第1列手机号", i+1)
			return
		}

		if i == 0 && a[0] == "手机号" {
			continue
		} else if i == 0 && a[0] == "Mobile" {
			continue
		}

		if !utils.IsMobile(a[0]) {
			msg = fmt.Sprintf("第%d行第1列不是手机号", i+1)
			return
		}

		var user model.User
		if err := user.GetByMobile(a[0]); err != nil {
			if err == gorm.ErrRecordNotFound {
				msg = fmt.Sprintf("第%d行第1列用户不存在", i+1)
				return
			}
			msg = "查询手机号失败"
			logger.Error(msg, err)
			return
		}

		lines = append(lines, Row{user.ID, a[0]})
	}

	classRoomIds := ""
	aryClassRoom := make([]model.ClassRoom, 0)
	var classRoom model.ClassRoom
	if err := classRoom.GetsByUuids(&aryClassRoom, c.PostForm("class_room_uuids")); err != nil {
		msg = "查询课堂失败"
		logger.Error(msg, err)
	}
	for _, item := range aryClassRoom {
		if item.UserId != claims.UserId {
			msg = "课堂权限不一致"
			logger.Error(msg, utils.GetJsonFromStruct(item), "  userId:", claims.UserId)
			return
		}
		classRoomIds += fmt.Sprintf("%d|", item.ID)
	}
	if classRoomIds != "" {
		classRoomIds = "|" + classRoomIds
	}

	for i, line := range lines {
		var classUser model.ClassUser
		if err := classUser.GetByUserAndStu(claims.UserId, line.UserID); err != nil {
			if err == gorm.ErrRecordNotFound {
				classUser = model.ClassUser{
					Uuid:         utils.GetUUID(),
					UserId:       claims.UserId,
					StuUserId:    line.UserID,
					FirstMobile:  line.Mobile,
					ClassRoomIds: classRoomIds,
					Status:       1,
				}
				if err := classUser.Save(); err != nil {
					msg = fmt.Sprintf("第%d行添加失败", i+1)
					logger.Error(msg, err)
					return
				}
			} else {
				msg = fmt.Sprintf("第%d行查询记录失败", i+1)
				logger.Error(msg, err)
				return
			}
		} else {
			if classUser.ClassRoomIds != classRoomIds {
				if classUser.SetClassRoomIds(classRoomIds) != nil {
					msg = fmt.Sprintf("第%d行更新失败", i+1)
					logger.Error(msg)
					return
				}
			}
		}
	}

	msg = "导入成功"
	code = 0
}

func (obj classRoomApi_) RemoveStu(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq removeStuReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ClassUserUuid == "" {
		msg = "参数错误"
		return
	}

	var classUser model.ClassUser
	if err := classUser.GetByUuid(oReq.ClassUserUuid); err != nil {
		if err == gorm.ErrRecordNotFound {
			msg = "移除成功"
			code = 0
			return
		} else {
			msg = "查询记录失败"
			logger.Error(msg, err)
			return
		}
	} else {
		if classUser.UserId != claims.UserId {
			msg = "无权限"
			return
		}
		if err := classUser.Delete(); err != nil {
			msg = "移除失败"
			logger.Error(msg, err)
			return
		}
		msg = "移除成功"
		code = 0
		return
	}
}

func (obj classRoomApi_) SetStu(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq setStuReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Action == "" {
		msg = "参数错误"
		return
	}
	if oReq.ClassUserUuid == "" {
		msg = "参数错误"
		return
	}

	var classUser model.ClassUser
	if err := classUser.GetByUuid(oReq.ClassUserUuid); err != nil {
		msg = "查询记录失败"
		logger.Error(msg, err)
		return
	} else {
		if classUser.UserId != claims.UserId {
			msg = "无权限"
			return
		}
		if oReq.Action == "status" {
			if oReq.Status < 0 || oReq.Status > 2 {
				msg = "状态参数错误"
				return
			}
			if err := classUser.SetStatus(oReq.Status); err != nil {
				msg = "设置失败"
				logger.Error(msg, err)
				return
			} else {
				msg = "设置成功"
				code = 0
				return
			}
		} else if oReq.Action == "remark" {
			if err := classUser.SetRemark(oReq.Remark); err != nil {
				msg = "设置失败"
				logger.Error(msg, err)
				return
			} else {
				msg = "设置成功"
				code = 0
				return
			}
		} else if oReq.Action == "class_room" {
			classRoomIds := ""
			aryClassRoom := make([]model.ClassRoom, 0)
			var classRoom model.ClassRoom
			if err := classRoom.GetsByUuids(&aryClassRoom, oReq.ClassRoomUuids); err != nil {
				msg = "查询课堂失败"
				logger.Error(msg, err)
			}
			for _, item := range aryClassRoom {
				if item.UserId != claims.UserId {
					msg = "课堂权限不一致"
					logger.Error(msg, utils.GetJsonFromStruct(item), "  userId:", claims.UserId)
					return
				}
				classRoomIds += fmt.Sprintf("%d|", item.ID)
			}
			if classRoomIds != "" {
				classRoomIds = "|" + classRoomIds
			}
			if err := classUser.SetClassRoomIds(classRoomIds); err != nil {
				msg = "设置失败"
				logger.Error(msg, err)
				return
			} else {
				msg = "设置成功"
				code = 0
				return
			}
		} else {
			msg = "设置参数错误"
			return
		}
	}
}
