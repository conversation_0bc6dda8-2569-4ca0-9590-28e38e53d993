package manage

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"time"
)

type classRoomApi_ struct {
}

var ClassRoomApi classRoomApi_

type listClassRoomReq struct {
	ClassRoomId uint   `json:"class_room_id"`
	UserId      uint   `json:"user_id"`
	Kw          string `json:"kw"`
	Status      int    `json:"status"`
	Page        int    `json:"page"`
	PageSize    int    `json:"page_size"`
}

type classUserReq struct {
	UserId        uint   `json:"user_id"`
	Mobile        string `json:"mobile"`
	StuUserId     uint   `json:"stu_user_id"`
	StuMobile     string `json:"stu_mobile"`
	ClassUserUuid string `json:"class_user_uuid"`
	ClassRoomUuid string `json:"class_room_uuid"`
	FirstMobile   string `json:"first_mobile"`
	Status        int    `json:"status"`
	Kw            string `json:"kw"`
	Page          int    `json:"page"`
	PageSize      int    `json:"page_size"`
}

type simplePod struct {
	ID        uint      `json:"-"`
	Uuid      string    `json:"uuid"`
	Title     string    `json:"title"`
	Desc      string    `json:"-"`
	Markdown  string    `json:"-"`
	Logo      string    `json:"logo"`
	Cover     string    `json:"cover"`
	UserId    uint      `json:"-"`
	Status    int       `json:"status"`
	StatusTxt string    `json:"status_txt"`
	UpdatedAt time.Time `json:"-"`
}

type classRoomResp struct {
	ID              uint                   `json:"-"`
	Uuid            string                 `json:"uuid"`
	Title           string                 `json:"title"`
	Des             string                 `json:"des"`
	Markdown        string                 `json:"markdown"`
	Logo            string                 `json:"logo"`
	Cover           string                 `json:"cover"`
	PodIds          string                 `json:"-"`
	Pods            []simplePod            `json:"pods" gorm:"-"`
	Status          int                    `json:"status"`
	StatusTxt       string                 `json:"status_txt"`
	AuditStatus     int                    `json:"audit_status"`
	AuditStatusTxt  string                 `json:"audit_status_txt"`
	AuditContent    string                 `json:"-"`
	AuditContentMap map[string]interface{} `json:"audit_content"`
	CreatedAt       jsontime.JsonTime      `json:"created_at"`
	UpdatedAt       jsontime.JsonTime      `json:"updated_at"`
}

type simpleClassRoom struct {
	ID    uint   `json:"-"`
	Uuid  string `json:"uuid"`
	Title string `json:"title"`
}

type classUserResp struct {
	ID            uint              `json:"-"`
	Uuid          string            `json:"uuid"`
	UserId        uint              `json:"user_id"`
	StuUserId     uint              `json:"stu_user_id"`
	FirstMobile   string            `json:"first_mobile"`
	ClassRoomIds  string            `json:"class_room_ids"`
	ClassRooms    []simpleClassRoom `json:"class_rooms" gorm:"-"`
	Remark        string            `json:"remark"`
	Status        int               `json:"status"`
	StatusTxt     string            `json:"status_txt"`
	StatusExplain string            `json:"status_explain"`
}

func (obj classRoomApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq listClassRoomReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page < 1 {
		oReq.Page = 1
	}
	if oReq.PageSize < 1 || oReq.PageSize > 50 {
		oReq.PageSize = 50
	}

	queryParm := utils.MakeInterfaceMap()
	if oReq.UserId > 0 {
		queryParm["user_id"] = claims.UserId
	}

	var classRoom model.ClassRoom
	if oReq.ClassRoomId > 0 {
		if err := classRoom.GetById(oReq.ClassRoomId); err != nil {
			msg = "记录不存在"
			logger.Error(msg, err, oReq)
			return
		}
		queryParm["id"] = classRoom.ID
	}

	var ary = make([]classRoomResp, 0)
	if total, err := classRoom.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {

			pods := make([]simplePod, 0)
			podIds := utils.Ids2UintAry(ary[i].PodIds)
			for _, podId := range podIds {
				var pod model.Pod
				if err := pod.GetByIdFromCache(podId); err != nil {
					logger.Error(err, " ", podId)
				} else {
					pods = append(pods, simplePod{ID: pod.ID, Uuid: pod.Uuid, Title: pod.Title})
				}
			}
			ary[i].Pods = pods

			if ary[i].AuditContent != "" {
				mm := utils.GetMapFromJson(ary[i].AuditContent)
				if mm != nil {
					if _, ok := mm["logo"]; ok {
						tmp := mm["logo"].(string)
						if tmp != "" {
							mm["logo"] = config.DiffusionDomain + tmp
							if ary[i].Logo == "" {
								ary[i].Logo = tmp
							}
						}
					}
					if _, ok := mm["cover"]; ok {
						tmp := mm["cover"].(string)
						if tmp != "" {
							mm["cover"] = config.DiffusionDomain + tmp
							if ary[i].Cover == "" {
								ary[i].Cover = tmp
							}
						}
					}
					if _, ok := mm["title"]; ok {
						tmp := mm["title"].(string)
						if tmp != "" && ary[i].Title == "" {
							ary[i].Title = tmp
						}
					}
					if _, ok := mm["des"]; ok {
						tmp := mm["des"].(string)
						if tmp != "" && ary[i].Des == "" {
							ary[i].Des = tmp
						}
					}
				}
				ary[i].AuditContentMap = mm
			}

			ary[i].AuditStatusTxt = enums.PodAuditStatusEnum.Name(ary[i].AuditStatus)

			if ary[i].Status == 1 {
				ary[i].StatusTxt = "已上架"
			} else {
				ary[i].StatusTxt = "未上架"
			}

			if ary[i].Logo != "" {
				ary[i].Logo = fmt.Sprintf("%s%s", config.DiffusionDomain, ary[i].Logo)
			}
			if ary[i].Cover != "" {
				ary[i].Cover = fmt.Sprintf("%s%s", config.DiffusionDomain, ary[i].Cover)
			}
		}
		result["class_rooms"] = ary
		result["total"] = total
	}

	msg = ""
	code = 0
}

func (obj classRoomApi_) ListStu(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq classUserReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page < 1 {
		oReq.Page = 1
	}
	if oReq.PageSize < 1 || oReq.PageSize > 50 {
		oReq.PageSize = 50
	}

	queryParm := utils.MakeInterfaceMap()
	if oReq.UserId > 0 {
		queryParm["user_id"] = oReq.UserId
	}

	if oReq.Mobile != "" {
		if oReq.UserId > 0 {
			msg = "手机号和用户ID只能填一个"
			return
		}
		var user model.User
		if err := user.GetByMobile(oReq.Mobile); err != nil {
			msg = err.Error()
			if err == gorm.ErrRecordNotFound {
				msg = "该手机号码用户不存在"
			}
			return
		} else {
			queryParm["user_id"] = user.ID
		}
	}

	if oReq.StuUserId > 0 {
		queryParm["stu_user_id"] = oReq.StuUserId
	}

	if oReq.StuMobile != "" {
		if oReq.StuUserId > 0 {
			msg = "学员手机号和学员用户ID只能填一个"
			return
		}
		var user model.User
		if err := user.GetByMobile(oReq.Mobile); err != nil {
			msg = err.Error()
			if err == gorm.ErrRecordNotFound {
				msg = "该手机号码用户不存在"
			}
			return
		} else {

		}
	}
	if oReq.FirstMobile != "" {
		queryParm["first_mobile"] = oReq.FirstMobile
	}

	var classRoom model.ClassRoom
	if oReq.ClassRoomUuid != "" {
		if err := classRoom.GetByUuid(oReq.ClassRoomUuid); err != nil {
			msg = "记录不存在"
			logger.Error(msg, err, oReq)
			return
		}
	}

	if oReq.ClassUserUuid != "" {
		var classUser model.ClassUser
		if err := classUser.GetByUuid(oReq.ClassUserUuid); err != nil {
			msg = "查询学员信息失败"
			logger.Error(msg, err)
			return
		}
		queryParm["id"] = classUser.ID
	}

	if classRoom.ID > 0 {
		queryParm["class_room_id"] = classRoom.ID
	}
	if oReq.Kw != "" {
		queryParm["kw"] = oReq.Kw
	}
	var ary = make([]classUserResp, 0)
	var classUser model.ClassUser
	if total, err := classUser.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {
			classRooms := make([]simpleClassRoom, 0)
			classRoomIds := utils.Ids2UintAry(ary[i].ClassRoomIds)
			for _, classRoomId := range classRoomIds {
				var tmpClassRoom model.ClassRoom
				if err := tmpClassRoom.GetByIdFromCache(classRoomId); err != nil {
					logger.Error(err, " ", classRoomId)
				} else {

					title := tmpClassRoom.Title
					if tmpClassRoom.Title == "" && tmpClassRoom.AuditContent != "" {
						mm := utils.GetMapFromJson(tmpClassRoom.AuditContent)
						if mm != nil {
							if _, ok := mm["title"]; ok {
								title = mm["title"].(string)
							}
						}
					}

					classRooms = append(classRooms, simpleClassRoom{ID: tmpClassRoom.ID, Uuid: tmpClassRoom.Uuid, Title: title})
				}
			}
			ary[i].ClassRooms = classRooms
			ary[i].StatusTxt = enums.ClassRoomEnum.StuUserStatusName(ary[i].Status)
			ary[i].StatusExplain = enums.ClassRoomEnum.StuUserStatusExplain(ary[i].Status)
		}
		result["class_users"] = ary
		result["total"] = total
	}

	msg = ""
	code = 0
}
