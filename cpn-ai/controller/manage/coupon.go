package manage

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/structs"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"net/http"
	"runtime"
	"strings"
	"time"
)

type couponApi_ struct {
}

var CouponApi couponApi_

type couponReq struct {
	CouponUuid string `json:"coupon_uuid"`
}

type genCouponReq struct {
	CouponUuid string          `json:"coupon_uuid"`
	CouponCode string          `json:"coupon_code"`
	Title      string          `json:"title"`
	SalePrice  decimal.Decimal `json:"sale_price"`
	FacePrice  decimal.Decimal `json:"face_price"`
	ExpireDate time.Time       `json:"expire_date"`
	ValidDays  int             `json:"valid_days"`
	Quantity   int             `json:"quantity"`
	SingleMax  int             `json:"single_max"`
	PodIds     string          `json:"pod_ids"`
	PodUuids   string          `json:"pod_uuids"`
	Remark     string          `json:"remark"`
}

type couponListReq struct {
	CouponUuid string `json:"coupon_uuid"`
	CouponCode string `json:"coupon_code"`
	Status     int    `json:"status"`
	Kw         string `json:"kw"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type cardListReq struct {
	CouponUuid string `json:"coupon_uuid"`
	CardUuid   string `json:"card_uuid"`
	CardNo     string `json:"card_no"`
	BuyUserId  uint   `json:"buy_user_id"`
	BindUserId uint   `json:"bind_user_id"`
	Status     int    `json:"status"`
	Kw         string `json:"kw"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type discardReq struct {
	CardNo   string `json:"card_no"`
	Remark   string `json:"remark"`
	Operator string `json:"operator"`
}

type couponResp struct {
	Uuid       string                   `json:"uuid"`
	CouponCode string                   `json:"coupon_code"`
	Title      string                   `json:"title"`
	SalePrice  decimal.Decimal          `json:"sale_price"`
	FacePrice  decimal.Decimal          `json:"face_price"`
	ExpireDate jsontime.JsonTime        `json:"expire_date"`
	ValidDays  int                      `json:"valid_days"`
	Quantity   int                      `json:"quantity"`
	SingleMax  int                      `json:"single_max"`
	SoldCount  int                      `json:"sold_count"`
	PodIds     string                   `json:"-"`
	Pods       string                   `json:"-"`
	PodsObj    []map[string]interface{} `json:"pods"`
	Remark     string                   `json:"remark"`
	Status     int                      `json:"status"`
	StatusTxt  string                   `json:"status_txt"`
}

type cardResp struct {
	ID          uint                     `json:"id"`
	Uuid        string                   `json:"uuid"`
	CouponId    uint                     `json:"-"`
	Title       string                   `json:"title"`
	CardNo      string                   `json:"card_no"`
	ValidDays   int                      `json:"valid_days"`
	BindTime    jsontime.JsonTime        `json:"bind_time"`
	ExpireDate  jsontime.JsonTime        `json:"expire_date"`
	BuyUserId   uint                     `json:"-"`
	BindUserId  uint                     `json:"-"`
	BindTxt     string                   `json:"bind_txt"`
	SalePrice   decimal.Decimal          `json:"sale_price"`
	FacePrice   decimal.Decimal          `json:"face_price"`
	LeaveAmount decimal.Decimal          `json:"leave_amount"`
	PodIds      string                   `json:"-"`
	Pods        string                   `json:"-"`
	PodsObj     []map[string]interface{} `json:"pods"`
	Remark      string                   `json:"remark"`
	Status      model.CardStatusEnum     `json:"status"`
	StatusTxt   string                   `json:"status_txt"`
}

func (obj couponApi_) GenCode(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	for i := 0; i < 10; i++ {
		couponCode := utils.GenerateCouponString(6)
		if len(couponCode) != 6 {
			continue
		}
		var check model.Coupon
		if err := check.GetByCouponCode(couponCode); err != nil {
			if err == gorm.ErrRecordNotFound {
				result["coupon_code"] = couponCode
				code = 0
				return
			}
		} else {
			continue
		}
	}
	msg = "生成优惠码失败，请重试"
	return
}

func (obj couponApi_) Gen(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq genCouponReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.CouponUuid != "" {
		msg = "进入修改模式"
		var coupon model.Coupon
		if err := coupon.GetByUuid(oReq.CouponUuid); err != nil {
			msg = "查询记录失败"
			logger.Error(msg, err)
			return
		}

		mm := make(map[string]interface{})
		if oReq.Title != "" && oReq.Title != coupon.Title {
			mm["title"] = oReq.Title
		}
		if oReq.SalePrice.GreaterThan(decimal.Zero) && oReq.SalePrice.Equal(coupon.SalePrice) == false {
			if coupon.SoldCount > 0 {
				msg = "已经开始销售，不能修改销售价格"
				return
			}
			mm["sale_price"] = oReq.SalePrice
		}
		if oReq.FacePrice.GreaterThan(decimal.Zero) && oReq.FacePrice.Equal(coupon.FacePrice) == false {
			mm["face_price"] = oReq.FacePrice
		}
		if oReq.ExpireDate.After(common.NationalDay) && oReq.ExpireDate.Equal(coupon.ExpireDate) == false {
			if coupon.SoldCount > 0 {
				msg = "已经开始销售，不能修改销售价格"
				return
			}
			mm["expire_date"] = oReq.ExpireDate
		}
		if oReq.ValidDays > 0 && oReq.ValidDays != coupon.ValidDays {
			if coupon.SoldCount > 0 {
				msg = "已经开始销售，不能修改有效天数"
				return
			}
			if oReq.ValidDays > 365 {
				msg = "有效天数最大为365天"
				return
			}
			mm["valid_days"] = oReq.ValidDays
		}

		if oReq.Quantity > 0 && oReq.Quantity != coupon.Quantity {
			if oReq.Quantity < coupon.SoldCount {
				msg = "发行量不能小于已销售数量"
				return
			}
			mm["quantity"] = oReq.Quantity
		}
		if oReq.SingleMax > 0 && oReq.SingleMax != coupon.SingleMax {
			mm["single_max"] = oReq.SingleMax
		}
		if oReq.Remark != "" && oReq.Remark != coupon.Remark {
			mm["remark"] = oReq.Remark
		}

		podIds := ""
		aryPod := make([]structs.TragetPod, 0)
		if oReq.PodUuids != "" {
			aryPodId := make([]string, 0)
			ary := strings.Split(oReq.PodUuids, "|")
			i := 0
			for _, podUuid := range ary {
				i++
				var pod model.Pod
				if err := pod.GetByUuid(podUuid); err != nil {
					msg = "Pod查询失败"
					logger.Error(err, "podUuid:", podUuid)
					return
				} else {
					if pod.Title == "" {
						msg = fmt.Sprintf("第%d个Pod的标题不能为空", i)
						return
					}
					if pod.Category != enums.PodCategoryEnum.PodInstance {
						msg = fmt.Sprintf("第%d个Pod的类型不正确", i)
						return
					}
					aryPodId = append(aryPodId, utils.Uint2String(pod.ID))
					aryPod = append(aryPod, structs.TragetPod{Uuid: pod.Uuid, Title: pod.Title})
				}
			}
			if len(aryPodId) > 0 {
				podIds = "|" + strings.Join(aryPodId, "|") + "|"
			}
		}

		if podIds != "" && podIds != coupon.PodIds {
			if coupon.SoldCount > 0 {
				msg = "已经开始销售，不能修改定向Pod"
				return
			}
			tmp := utils.GetJsonFromStruct(aryPod)
			if tmp == "" {
				msg = "定向Pod解析失败"
				return
			}
			mm["pod_ids"] = podIds
			mm["pods"] = tmp
		}

		if len(mm) == 0 {
			msg = "请输入修改内容"
		} else {
			if err := coupon.Updates(mm); err != nil {
				msg = "修改失败"
				logger.Error(msg, err, oReq)
				return
			} else {
				result["coupon_uuid"] = coupon.Uuid
				msg = "修改成功"
				code = 0
			}
		}
		return
	}

	if utils.ValidCouponString(oReq.CouponCode, 6) == false {
		msg = "优惠码长度为6位，由大写字母和数字组成，并且不包含0和O"
		logger.Error(msg, oReq)
		return
	}
	if strings.Contains(oReq.CouponCode, " ") {
		msg = "优惠码不能包含空格"
		logger.Error(msg, oReq)
		return
	}

	var check model.Coupon
	if err := check.GetByCouponCode(oReq.CouponCode); err != nil {
		if err != gorm.ErrRecordNotFound {
			msg = "查询优惠码出错"
			return
		}
	} else {
		msg = "优惠码已存在"
		logger.Error(msg, oReq)
		return
	}

	if oReq.FacePrice.IsZero() {
		msg = "请输入面值"
		logger.Error(msg, oReq)
		return
	}

	if oReq.SalePrice.IsZero() {
		msg = "请输入销售价格"
		logger.Error(msg, oReq)
		return
	}

	if oReq.ExpireDate.Before(time.Now()) {
		msg = "过期日期不正确"
		logger.Error(msg, oReq)
		return
	}

	if oReq.ValidDays <= 0 {
		msg = "请输入有效天数"
		logger.Error(msg, oReq)
		return
	}
	if oReq.ValidDays > 365 {
		msg = "有效天数最大为365天"
		logger.Error(msg, oReq)
		return
	}

	if oReq.Quantity <= 0 {
		msg = "请输入发行量"
		logger.Error(msg, oReq)
		return
	}

	podIds := ""
	aryPod := make([]structs.TragetPod, 0)
	if oReq.PodUuids != "" {
		aryPodId := make([]string, 0)
		ary := strings.Split(oReq.PodUuids, "|")
		i := 0
		for _, podUuid := range ary {
			i++
			var pod model.Pod
			if err := pod.GetByUuid(podUuid); err != nil {
				logger.Error(err, "podUuid:", podUuid)
			} else {
				aryPodId = append(aryPodId, utils.Uint2String(pod.ID))
				if pod.Title == "" {
					msg = fmt.Sprintf("第%d个Pod的标题不能为空", i)
					return
				}
				if pod.Category != enums.PodCategoryEnum.PodInstance {
					msg = fmt.Sprintf("第%d个Pod的类型不正确", i)
					return
				}

				aryPod = append(aryPod, structs.TragetPod{Uuid: pod.Uuid, Title: pod.Title})
			}
		}
		if len(aryPodId) > 0 {
			podIds = "|" + strings.Join(aryPodId, "|") + "|"
		}
	}

	cardPrefix := utils.GenerateRandomNumber(6)

	coupon := model.Coupon{
		Uuid:       utils.GetUUID(),
		CouponCode: oReq.CouponCode,
		CardPrefix: fmt.Sprintf("%d", cardPrefix),
		Title:      oReq.Title,
		SalePrice:  oReq.SalePrice,
		FacePrice:  oReq.FacePrice,
		ExpireDate: oReq.ExpireDate,
		ValidDays:  oReq.ValidDays,
		Quantity:   oReq.Quantity,
		SingleMax:  oReq.SingleMax,
		PodIds:     podIds,
		Pods:       utils.GetJsonFromStruct(aryPod),
		Remark:     oReq.Remark,
	}
	if coupon.Pods == "" && podIds != "" {
		msg = "定向Pod解析失败"
		logger.Error(msg, " poduuids:", oReq.PodUuids)
		return
	}
	if err := coupon.Save(); err != nil {
		msg = "保存失败"
		logger.Error(err, utils.GetJsonFromStruct(oReq))
		return
	}

	if coupon.ID <= 0 {
		msg = "保存失败"
		logger.Error("coupon.ID <= 0", utils.GetJsonFromStruct(oReq))
		return
	}
	//var resp couponResp
	//if err := utils.Scan(coupon, &resp); err != nil {
	//	msg = "数据转换失败"
	//	logger.Error(msg, err, " uuid:", coupon.Uuid)
	//	return
	//}
	//result["coupon"] = resp
	result["coupon_uuid"] = coupon.Uuid
	msg = "保存成功"
	code = 0

}

func (obj couponApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq couponListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var coupon model.Coupon
	var ary = make([]couponResp, 0)

	queryParm := make(map[string]interface{})

	//queryParm["user_id"] = claims.UserId

	if oReq.CouponUuid != "" {
		if err := coupon.GetByUuid(oReq.CouponUuid); err != nil {
			msg = "记录不存在"
			logger.Error(err)
			return
		}
		queryParm["id"] = coupon.ID
	}
	if oReq.CouponCode != "" {
		queryParm["coupon_code"] = oReq.CouponCode
	}
	if oReq.Status >= 0 {
		queryParm["status"] = oReq.Status
	}

	if total, err := coupon.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		for i := 0; i < len(ary); i++ {
			if ary[i].Status == 1 {
				ary[i].StatusTxt = "有效"
			} else {
				ary[i].StatusTxt = "无效"
			}
			ary[i].PodsObj = utils.GetMapAryFromJson(ary[i].Pods)
		}
		result["coupons"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj couponApi_) OnSale(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq couponReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.CouponUuid == "" {
		msg = "参数错误"
		return
	}

	var coupon model.Coupon
	if err := coupon.GetByUuid(oReq.CouponUuid); err != nil {
		msg = "优惠卷信息获取失败"
		logger.Error(msg, err, oReq)
		return
	}

	if coupon.Status == 1 {
		msg = "已经是上架状态"
		return
	}

	if err := coupon.SetStatus(1); err != nil {
		msg = "上架失败"
		logger.Error(msg, err)
		return
	} else {
		code = 0
		msg = "上架成功"
		return
	}
}

func (obj couponApi_) OffSale(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq couponReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.CouponUuid == "" {
		msg = "参数错误"
		return
	}

	var coupon model.Coupon
	if err := coupon.GetByUuid(oReq.CouponUuid); err != nil {
		msg = "优惠卷信息获取失败"
		logger.Error(msg, err, oReq)
		return
	}

	if coupon.Status == 0 {
		msg = "已经是下架状态"
		return
	}

	if err := coupon.SetStatus(0); err != nil {
		msg = "下架失败"
		logger.Error(msg, err)
		return
	} else {
		code = 0
		msg = "下架成功"
		return
	}
}

func (obj couponApi_) Del(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq couponReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.CouponUuid == "" {
		msg = "参数错误"
		return
	}

	var coupon model.Coupon
	if err := coupon.GetByUuid(oReq.CouponUuid); err != nil {
		msg = "优惠卷信息获取失败"
		logger.Error(msg, err, oReq)
		return
	}

	if err := coupon.SetStatus(9); err != nil {
		msg = "删除失败"
		logger.Error(msg, err)
		return
	} else {
		code = 0
		msg = "删除成功"
		return
	}
	//if coupon.SoldCount > 0 {
	//	if err := coupon.SetStatus(9); err != nil {
	//		msg = "删除失败"
	//		logger.Error(msg, err)
	//		return
	//	} else {
	//		code = 0
	//		msg = "删除成功"
	//		return
	//	}
	//} else {
	//	if err := coupon.Delete(); err != nil {
	//		msg = "删除失败"
	//		logger.Error(msg, err)
	//		return
	//	} else {
	//		code = 0
	//		msg = "删除成功"
	//		return
	//	}
	//}

}

func (obj couponApi_) Cards(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq cardListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var card model.Card
	var ary = make([]cardResp, 0)

	queryParm := make(map[string]interface{})

	if oReq.CardUuid != "" {
		if err := card.GetByUuid(oReq.CardUuid); err != nil {
			msg = "记录不存在"
			logger.Error(err)
			return
		}
		queryParm["id"] = card.ID
	}

	if oReq.CardNo != "" {
		oReq.CardNo = strings.Replace(oReq.CardNo, "-", "", -1)
		if len(oReq.CardNo) != 16 {
			msg = "卡号位数不正确"
			return
		}
		if err := card.GetByCardNo(oReq.CardNo); err != nil {
			msg = "记录不存在"
			logger.Error(err)
			return
		}
		queryParm["id"] = card.ID
	}

	if oReq.Status >= 0 {
		queryParm["status"] = oReq.Status
	}

	if oReq.BuyUserId > 0 {
		queryParm["buy_user_id"] = oReq.BuyUserId
	}

	if oReq.BindUserId > 0 {
		queryParm["bind_user_id"] = oReq.BindUserId
	}

	if total, err := card.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		today := jsontime.Today().Time()
		for i := 0; i < len(ary); i++ {
			ary[i].ExpireDate = ary[i].ExpireDate.Date()
			ary[i].CardNo = service.ShowCardNo(ary[i].CardNo)
			ary[i].PodsObj = utils.GetMapAryFromJson(ary[i].Pods)

			ary[i].StatusTxt = model.CardStatusEnumName(ary[i].Status)

			if ary[i].LeaveAmount.Equal(decimal.Zero) {
				ary[i].StatusTxt = "已用完"
			} else {
				if ary[i].ExpireDate.Time().Before(today) {
					ary[i].StatusTxt = "已过期"
				}
			}

			if ary[i].Status == 4 {
				ary[i].StatusTxt = "已作废"
			}

			if ary[i].BindUserId > 0 {
				ary[i].BindTxt = "已绑定"
				if ary[i].BindUserId != claims.UserId {
					ary[i].BindTxt = "已被别人绑定"
				}
			} else {
				if ary[i].ExpireDate.Time().Before(today) {
					ary[i].BindTxt = "已过期"
				} else {
					ary[i].BindTxt = "未绑定"
				}
			}
		}
		result["cards"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj couponApi_) Discard(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq discardReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.CardNo == "" {
		msg = "请输入卡号"
		return
	}
	oReq.CardNo = strings.Replace(oReq.CardNo, "-", "", -1)
	if oReq.Operator == "" {
		msg = "请输入操作员"
		return
	}
	if oReq.Remark == "" {
		msg = "请输入备注信息"
		return
	}
	var card model.Card
	if err := card.GetByCardNo(oReq.CardNo); err != nil {
		logger.Error(msg)
		msg = "卡号查询失败：" + err.Error()
		return
	}

	//logStruct := withdrawApplyLogResp{
	//	Operator:      oReq.Operator,
	//	Remark:        "提现申请手动打款:" + oReq.Remark,
	//	WithdrawApply: withdrawApply,
	//	CreateAt:      jsontime.Now(),
	//}

	logStruct := struct {
		Operator string    `json:"operator"`
		Remark   string    `json:"remark"`
		CreateAt time.Time `json:"create_at"`
	}{
		Operator: oReq.Operator,
		Remark:   oReq.Remark,
		CreateAt: time.Now(),
	}
	userEnv := utils.MakeInterfaceMap()
	userEnv["user_agent"] = c.Request.UserAgent()

	logJson := utils.GetJsonFromStruct(logStruct)
	operationLog := model.OperationLog{
		OperatorUserId: claims.UserId,
		LogType:        enums.OperationLogTypeEnum.DisCard,
		OrigWhere:      enums.OperationOrigWhereEnum.Card,
		OrigId:         card.ID,
		Ip:             utils.GetClientIp(c.Request.Header),
		LogJson:        logJson,
		UserEnv:        utils.GetJsonFromStruct(userEnv),
	}
	if err := operationLog.Save(); err != nil {
		msg = "保存日志失败"
		logger.Error(msg, err)
		return
	}

	if card.Status != 1 {
		msg = "该卡不是有效状态，不能操作"
		return
	}

	if err := card.DisCard(oReq.Remark); err != nil {
		msg = "作废失败，" + err.Error()
		return
	} else {
		msg = "作废成功"
		code = 0
		return
	}
}
