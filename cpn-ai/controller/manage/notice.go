package manage

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
)

type noticeApi_ struct {
}

var NoticeApi noticeApi_

type saveNoticeReq struct {
	NoticeId   uint              `json:"notice_id"`
	NoticeType int               `json:"notice_type"`
	ShowMethod string            `json:"show_method"`
	Title      string            `json:"title"`
	Content    string            `json:"content"`
	StartTime  jsontime.JsonTime `json:"start_time"`
	EndTime    jsontime.JsonTime `json:"end_time"`
	OrderIndex float64           `json:"order_index"`
	Remark     string            `json:"remark"`
	Status     int               `json:"status"`
}

type listNoticeReq struct {
	NoticeId   uint              `json:"notice_id"`
	NoticeType int               `json:"notice_type"`
	ShowTime   jsontime.JsonTime `json:"show_time"`
	ShowMethod string            `json:"show_method"`
	Kw         string            `json:"kw"`
	Status     int               `json:"status"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
}

type noticeResp struct {
	ID            uint              `json:"id"`
	Uuid          string            `json:"uuid"`
	NoticeType    int               `json:"notice_type"`
	NoticeTypeTxt string            `json:"notice_type_txt"`
	ShowMethod    string            `json:"show_method"`
	ShowMethodTxt string            `json:"show_method_txt"`
	Title         string            `json:"title"`
	Content       string            `json:"content"`
	Remark        string            `json:"remark"`
	StartTime     jsontime.JsonTime `json:"start_time"`
	EndTime       jsontime.JsonTime `json:"end_time"`
	OrderIndex    float64           `json:"order_index"`
	Status        int               `json:"status"`
	StatusTxt     string            `json:"status_txt"`
	UpdatedAt     jsontime.JsonTime `json:"updated_at"`
	CreatedAt     jsontime.JsonTime `json:"created_at"`
}

func (obj noticeApi_) Save(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		msg = "权限不足"
		return
	}

	var oReq saveNoticeReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	fmt.Println(utils.GetJsonFromStruct(oReq))
	if oReq.Title == "" {
		msg = "请输入标题"
		return
	}

	if enums.NoticeShowEnum.Name(oReq.ShowMethod) == "" {
		msg = "显示方式不正确"
		return
	}

	if enums.NoticeTypeEnum.Name(oReq.NoticeType) == "" {
		msg = "公告类型不正确"
		return
	}

	var notice model.Notice
	if oReq.NoticeId > 0 {
		if err := notice.GetById(oReq.NoticeId); err != nil {
			msg = "查询失败"
			logger.Error(msg, err)
			return
		}
	} else {
		notice.Uuid = utils.GetUUID()
	}

	notice.NoticeType = oReq.NoticeType
	notice.ShowMethod = oReq.ShowMethod
	notice.Title = oReq.Title
	notice.Content = oReq.Content
	notice.OrderIndex = oReq.OrderIndex
	notice.StartTime = oReq.StartTime.Time()
	notice.EndTime = oReq.EndTime.Time()
	notice.Status = oReq.Status
	notice.Remark = oReq.Remark

	if err := notice.Save(); err != nil {
		msg = "保存失败"
		logger.Error(msg, err, " oReq:", utils.GetJsonFromStruct(oReq))
		return
	}
	var resp noticeResp
	if err := utils.Scan(notice, &resp); err != nil {
		logger.Error(err)
		msg = err.Error()
		return
	} else {
		resp.NoticeTypeTxt = enums.NoticeTypeEnum.Name(notice.NoticeType)
		resp.ShowMethodTxt = enums.NoticeShowEnum.Name(notice.ShowMethod)
		resp.StatusTxt = "无效"
		if resp.Status == 1 {
			resp.StatusTxt = "有效"
		}
		result["notice"] = resp
	}

	msg = "保存成功"
	code = 0
}

func (obj noticeApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq listNoticeReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var notice model.Notice
	ary := make([]noticeResp, 0)
	queryParm := make(map[string]interface{})
	if oReq.NoticeId > 0 {
		queryParm["id"] = oReq.NoticeId
	}
	if oReq.NoticeType > 0 {
		queryParm["notice_type"] = oReq.NoticeType
	}
	if oReq.ShowMethod != "" {
		queryParm["show_method"] = oReq.ShowMethod
	}
	if oReq.ShowTime.NotDefault() {
		queryParm["show_time"] = oReq.ShowTime.Time()
	}
	if oReq.Status >= 0 {
		queryParm["status"] = oReq.Status
	}
	if oReq.Kw != "" {
		queryParm["kw"] = oReq.Kw
	}

	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 20
	}

	if _, err := notice.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		for i := 0; i < len(ary); i++ {
			ary[i].NoticeTypeTxt = enums.NoticeTypeEnum.Name(ary[i].NoticeType)
			ary[i].ShowMethodTxt = enums.NoticeShowEnum.Name(ary[i].ShowMethod)
			ary[i].StatusTxt = "无效"
			if ary[i].Status == 1 {
				ary[i].StatusTxt = "有效"
			}
		}
		result["notices"] = ary
		code = 0
	}
}
