package manage

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"net/http"
	"runtime"
	"time"
)

type _statApi struct {
}

var StatApi _statApi

type statDayReq struct {
	StatDay int64  `json:"stat_day"`
	Action  string `json:"action"`
}

type statDaysReq struct {
	StatStart int64  `json:"stat_start"`
	StatEnd   int64  `json:"stat_end"`
	Order     string `json:"order"`
	Page      int    `json:"page"`
	PageSize  int    `json:"page_size"`
}

type podUsageData struct {
	PodId    uint `json:"pod_id"`
	Users    int  `json:"users"`
	Usages   int  `json:"usages"`
	Duration uint `json:"duration"`
}

type statDayResp struct {
	TotalRegisterUsers        int                    `json:"total_register_users"`
	TotalCertUsers            int                    `json:"total_cert_users"`
	TotalRechargeUsers        int                    `json:"total_recharge_users"`
	TotalRechargeAmount       decimal.Decimal        `json:"total_recharge_amount"`
	TotalRechargeMaxAmount    decimal.Decimal        `json:"total_recharge_max_amount"`
	TotalRechargeRemainUsers  int                    `json:"total_recharge_remain_users"`
	TotalRechargeRemainAmount decimal.Decimal        `json:"total_recharge_remain_amount"`
	TotalBuyCardUsers         int                    `json:"total_buy_card_users"`
	TotalBuyCardAmount        decimal.Decimal        `json:"total_buy_card_amount"`
	TotalCostAmount           decimal.Decimal        `json:"total_cost_amount"`
	TotalCostRemainAmount     decimal.Decimal        `json:"total_cost_remain_amount"`
	TotalCostCardAmount       decimal.Decimal        `json:"total_cost_card_amount"`
	DayRegisterUsers          int                    `json:"day_register_users" `
	DayCertUsers              int                    `json:"day_cert_users"`
	DayRechargeUsers          int                    `json:"day_recharge_users"`
	DayRechargeRegisterUsers  int                    `json:"day_recharge_register_users"`
	DayRechargeFirstUsers     int                    `json:"day_recharge_first_users"`
	DayRechargeRepeatUsers    int                    `json:"day_recharge_repeat_users"`
	DayRechargeAmount         decimal.Decimal        `json:"day_recharge_amount"`
	DayRechargeMaxAmount      decimal.Decimal        `json:"day_recharge_max_amount"`
	DayRechargeRemainUsers    int                    `json:"day_recharge_remain_users"`
	DayRechargeRemainAmount   decimal.Decimal        `json:"day_recharge_remain_amount"`
	DayBuyCardUsers           int                    `json:"day_buy_card_users"`
	DayBuyCardAmount          decimal.Decimal        `json:"day_buy_card_amount"`
	DayCostAmount             decimal.Decimal        `json:"day_cost_amount"`
	DayCostRemainAmount       decimal.Decimal        `json:"day_cost_remain_amount"`
	DayCostCardAmount         decimal.Decimal        `json:"day_cost_card_amount"`
	DayonRegisterUsers        decimal.Decimal        `json:"dayon_register_users"`
	DayonRechargeUsers        decimal.Decimal        `json:"dayon_recharge_users"`
	DayPodUsageIds            uint                   `json:"day_pod_usage_ids"`
	DayPodUsageUsers          uint                   `json:"day_pod_usage_users"`
	DayPodUsageCount          uint                   `json:"day_pod_usage_count"`
	DayPodUsageDuration       uint                   `json:"day_pod_usage_duration"`
	DayPodUsageDataMap        map[string]interface{} `json:"day_pod_usage_data"`
	DayPodUsageData           string                 `json:"-"`
	StatAt                    jsontime.JsonTime      `json:"stat_at"`
	StatDay                   int64                  `json:"stat_day"`
	Status                    int                    `json:"status"`
	StatusTxt                 string                 `json:"status_txt"`
}

func (obj _statApi) StatDay(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq statDayReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	statDayStr := utils.Int642String(oReq.StatDay) //20241128
	if len(statDayStr) != 8 {
		msg = "日期参数错误"
		return
	}
	day := utils.String2Int(statDayStr[6:])
	if day == 0 {
		msg = "日期参数错误"
		return
	}
	statAt := time.Date(utils.String2Int(statDayStr[:4]), time.Month(utils.String2Int(statDayStr[4:6])), day, 0, 0, 0, 0, time.Now().Location())

	if oReq.Action == "repair" {
		if err := service.StatDayService.RepairStat(statAt); err != nil {
			logger.Error(err, " StatDay:", oReq.StatDay)
			msg = err.Error()
			return
		}
		msg = "修复统计数据成功"
	} else {
		if err := service.StatDayService.GenStat(statAt); err != nil {
			logger.Error(err, " StatDay:", oReq.StatDay)
			msg = err.Error()
			return
		}
		msg = "统计成功"
	}

	code = 0
}

func (obj _statApi) StatDays(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq statDaysReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var statDay model.StatDay
	var ary = make([]statDayResp, 0)

	queryParm := make(map[string]interface{})
	if oReq.StatStart > 0 {
		queryParm["stat_start"] = oReq.StatStart
	}
	if oReq.StatEnd > 0 {
		queryParm["stat_end"] = oReq.StatEnd
	}
	if oReq.Order != "" {
		queryParm["order"] = oReq.Order
	}
	if total, err := statDay.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		for i := 0; i < len(ary); i++ {
			if ary[i].Status == 1 {
				ary[i].StatusTxt = "统计完成"
			} else {
				ary[i].StatusTxt = "统计中"
			}

			if ary[i].DayPodUsageData != "" {
				ary[i].DayPodUsageDataMap = utils.GetMapFromJson(ary[i].DayPodUsageData)
			}
		}
		result["stat_days"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj _statApi) StatPullPods(c *gin.Context) {

	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	var statDay model.StatDay
	if pods, err := statDay.GetPodUsageAndImageInfo(); err != nil {
		logger.Error("获取pod使用数据失败", err)
	} else {
		result["data"] = pods
	}

}
