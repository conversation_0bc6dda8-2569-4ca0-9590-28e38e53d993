package manage

import (
	"cpn-ai/common/logger"
	"cpn-ai/model"
	"cpn-ai/service"
	"github.com/gin-gonic/gin"
	"net/http"
)

type trainApi_ struct {
}

var TrainApi trainApi_

type trainReq struct {
	QTaskId  string `json:"q_task_id"`
	JobsUuid string `json:"jobs_uuid"`
}

func (obj trainApi_) JobsList(c *gin.Context) {

}

func (obj trainApi_) GetJob(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	//claims := c.Value("center_claims").(*middleware.CenterClaims)
	//if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
	//	msg = "权限不足"
	//	return
	//}

	var oReq trainReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.QTaskId == "" {
		var trainJobs model.TrainJobs
		if err := trainJobs.GetByUuid(oReq.JobsUuid); err != nil {
			msg = err.Error()
			logger.Error(msg)
			return
		}
		oReq.QTaskId = trainJobs.QTaskId
	}

	if oReq.QTaskId == "" {
		msg = "参数错误"
		return
	}

	if task, err := service.TrainService.JobItem(oReq.QTaskId); err != nil {
		msg = err.Error()
		logger.Error(msg)
		return
	} else {
		result["task"] = task
		code = 0
		return
	}

}
