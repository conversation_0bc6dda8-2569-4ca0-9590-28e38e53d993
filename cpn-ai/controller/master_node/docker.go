package master_node

import (
	"cpn-ai/common/logger"
	"cpn-ai/service"
	"github.com/gin-gonic/gin"
	"net/http"
)

type dockerApi_ struct {
}

var DockerApi dockerApi_

type dockerDetailReq struct {
	VirtualId   uint   `json:"virtual_id"`
	StartupMark string `json:"startup_mark"`
}

func (obj dockerApi_) Detail(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}

	}()

	var oReq dockerDetailReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if _, state, ginH, err := service.NodeService.GetDockerDetail(oReq.VirtualId, oReq.StartupMark); err != nil {
		msg = "获取docker详情失败"
		result["err"] = err.Error()
		logger.Error(msg, err, state)
		return
	} else {
		c.JSON(http.StatusOK, ginH)
		code = 0
	}
}
