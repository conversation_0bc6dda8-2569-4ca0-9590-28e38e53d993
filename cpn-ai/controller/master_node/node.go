package master_node

import (
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type nodeApi_ struct {
}

var NodeApi nodeApi_

type reportLiveReq struct {
	NodeId        uint `json:"node_id"`
	TotalVirtual  int  `json:"total_virtual"`
	TotalInstance int  `json:"total_instance"`
	TotalGpus     int  `json:"total_gpus"`
	FreeGpus      int  `json:"free_gpus"`
}

type reportStartupSuccessReq struct {
	StartupMark string                 `json:"startup_mark"`
	Docker      map[string]interface{} `json:"docker"`
}

type liveReq struct {
	StartupMark string `json:"startup_mark"`
}

type gpuModelReq struct {
	GpuModelId   uint   `json:"gpu_model_id"`
	GpuModelName string `json:"gpu_model_name"`
}

type podLoadReq struct {
	PodId uint `json:"pod_id"`
}

type podLoadResp struct {
	ID            uint   `json:"id"`
	Uuid          string `json:"uuid"`
	Title         string `json:"title"`
	PodName       string `json:"pod_name"`
	Category      int    `json:"category"`
	PortMaps      string `json:"port_maps"`
	ImageName     string `json:"image_name"`
	ImageTag      string `json:"image_tag"`
	Command       string `json:"command"`
	NeedGpus      int    `json:"need_gpus"`
	StartupElapse uint   `json:"startup_elapse"`
	DataFolder    string `json:"data_folder"`
	VirtualIds    string `json:"virtual_ids"`
	Status        int    `json:"status"`
}

type podImageLoadReq struct {
	ImageId uint `json:"image_id"`
}

type podImageLoadResp struct {
	ID          uint    `json:"id"`
	ParentId    uint    `json:"parent_id"`
	PodId       uint    `json:"pod_id"`
	StorageMode int     `json:"storage_mode"`
	ImageType   int     `json:"image_type"`
	ImageName   string  `json:"image_name"`
	ImageTag    string  `json:"image_tag"`
	Size        float64 `json:"size"`
	Status      int     `json:"status"`
}
type virtualLoadReq struct {
	Region uint `json:"region"`
}

type gpuModelResp struct {
	ID        uint   `json:"id"`
	Uuid      string `json:"uuid"`
	GpuName   string `json:"gpu_name"`
	MemoryG   int    `json:"memory_g"`
	MemoryM   int    `json:"memory_m"`
	Status    int    `json:"status"`
	StatusTxt string `json:"status_txt"`
}

type virtualLoadResp struct {
	ID            uint    `json:"id"`
	Region        int     `json:"region"`
	HostPort      string  `json:"host_port"`
	Host          string  `json:"host"`
	Port          int     `json:"port"`
	SshUser       string  `json:"ssh_user"`
	SshPassword   string  `json:"ssh_password"`
	GpuModelId    uint    `json:"gpu_model_id"`
	ImageIds      string  `json:"image_ids"`
	PodIds        string  `json:"pod_ids"`
	ContainerCpus float64 `json:"container_cpus"`
	ContainerMem  string  `json:"container_mem"`
	Status        int     `json:"status"`
}

func (obj nodeApi_) InstanceLive(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.NodeClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq liveReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var instance model.Instance
	if err := instance.GetByStartupMark(oReq.StartupMark); err != nil {
		if err == gorm.ErrRecordNotFound {
			result["live"] = 0
			result["startup_mark"] = oReq.StartupMark
			code = 0
			msg = "记录不存在"
			return
		} else {
			logger.Error(err, oReq)
			msg = "查询出错"
			return
		}
	} else {
		result["startup_mark"] = instance.StartupMark
		result["status"] = instance.Status
		if instance.Status == enums.InstanceStatusEnum.Running {
			code = 0
			msg = "存活"
			result["live"] = 1
			return
		} else {
			code = 0
			msg = "已失效"
			result["live"] = 0
			return
		}
	}
}

func (obj nodeApi_) Pods(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims == nil {
	//	msg = "请先登录"
	//	code = 2
	//	return
	//}

	var oReq podLoadReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var pod model.Pod

	var ary = make([]podLoadResp, 0)
	if _, err := pod.List(&ary, oReq.PodId, 0, 0, 0, 0, -1, "", 1, -1, 1, 1000); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		result["items"] = ary
	}
	msg = ""
	code = 0
}

func (obj nodeApi_) PodImages(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims == nil {
	//	msg = "请先登录"
	//	code = 2
	//	return
	//}

	var oReq podImageLoadReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var podImage model.PodImage

	var ary = make([]podImageLoadResp, 0)
	if _, err := podImage.List(&ary, oReq.ImageId, 0, 0, 0, "", -1, 1, 100); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		result["items"] = ary
	}
	msg = ""
	code = 0
}

func (obj nodeApi_) GpuModels(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims == nil {
	//	msg = "请先登录"
	//	code = 2
	//	return
	//}

	var oReq gpuModelReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var gpuModel model.GpuModel

	var ary = make([]gpuModelResp, 0)
	if total, err := gpuModel.List(&ary, oReq.GpuModelId, oReq.GpuModelName, -1, 1, 100); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		result["items"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj nodeApi_) Virtuals(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败2"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.NodeClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}
	//var oReq virtualLoadReq
	//if err := c.ShouldBindJSON(&oReq); err != nil {
	//	msg = "参数解析失败"
	//	logger.Error(msg, err)
	//	return
	//}
	//logger.Info("virtualLoadReq", oReq)

	var virtual model.Virtual
	logger.Info("开始获取节点数据", claims.NodeId, "    ", claims)
	var ary = make([]virtualLoadResp, 0)
	if _, err := virtual.List(&ary, claims.NodeId, -1, 1, 1000); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		result["items"] = ary
	}
	msg = ""
	code = 0
}

func (obj nodeApi_) ReportLive(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq reportLiveReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var node model.Node
	if err := node.GetById(oReq.NodeId); err != nil {
		msg = "查询节点出错"
		logger.Error(msg, err, oReq)
		return
	}

	m := map[string]interface{}{"total_virtual": oReq.TotalVirtual, "free_gpus": oReq.FreeGpus, "total_gpus": oReq.TotalGpus, "total_instance": oReq.TotalInstance, "last_check_time": time.Now()}
	if err := node.Updates(m); err != nil {
		msg = "更新上报数据出错"
		logger.Error(msg, err, oReq)
		return
	}
	msg = "上报成功"
	code = 0
}

func (obj nodeApi_) ReportStartupSuccess(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	logger.Info("ReportStartupSuccess")
	var oReq reportStartupSuccessReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("ReportStartupSuccess", oReq)

	tmpJson := utils.GetJsonFromStruct(oReq.Docker)
	var tmpDocker service.Docker
	if err := utils.GetStructFromJson(&tmpDocker, tmpJson); err != nil || tmpDocker.ID == "" {
		msg = "数据解析失败"
		logger.Error(err, oReq)
		return
	}

	if tmpDocker.StartupMark != oReq.StartupMark {
		msg = "数据错误"
		logger.Error(msg, oReq)
		return
	}

	if str, err := service.InstanceNodeService.StartupSuccess(oReq.StartupMark, &tmpDocker); err != nil {
		msg = str
		logger.Error(msg, err, oReq)
		return
	} else {
		msg = str
		if msg == "" {
			msg = "启动完成上报成功"
		}
		logger.Info("启动完成上报成功 ", str, " ", tmpDocker)
		code = 0
	}
}
