package controller

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/model"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

type noticeApi_ struct {
}

var NoticeApi noticeApi_

type listNoticeReq struct {
	NoticeUuid string            `json:"notice_uuid"`
	NoticeType int               `json:"notice_type"`
	ShowTime   jsontime.JsonTime `json:"show_time"`
	ShowMethod string            `json:"show_method"`
	Kw         string            `json:"kw"`
	Status     int               `json:"status"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
}

type noticeResp struct {
	Uuid          string            `json:"uuid"`
	NoticeType    int               `json:"-"`
	NoticeTypeTxt string            `json:"-"`
	ShowMethod    string            `json:"show_method"`
	Title         string            `json:"title"`
	Content       string            `json:"content"`
	OrderIndex    float64           `json:"-"`
	StartTime     jsontime.JsonTime `json:"start_time"`
}

func (obj noticeApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq listNoticeReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var notice model.Notice
	ary := make([]noticeResp, 0)
	queryParm := make(map[string]interface{})
	if oReq.NoticeUuid != "" {
		if err := notice.GetByUuid(oReq.NoticeUuid); err != nil {
			logger.Error(err, " oReq:", utils.GetJsonFromStruct(oReq))
		}
		queryParm["id"] = notice.ID
	}

	if oReq.Kw != "" {
		queryParm["kw"] = oReq.Kw
	}

	queryParm["show_time"] = time.Now()
	queryParm["status"] = 1

	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 20
	}

	if _, err := notice.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		for i := 0; i < len(ary); i++ {
			ary[i].NoticeTypeTxt = enums.NoticeTypeEnum.Name(ary[i].NoticeType)
			//ary[i].ShowMethodTxt = enums.NoticeShowEnum.Name(ary[i].ShowMethod)
		}
		result["notices"] = ary
		code = 0
	}
}
