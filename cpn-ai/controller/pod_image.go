package controller

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/internal/ccm/dockerfile"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/service/tasklog"
	"cpn-ai/structs"
	"fmt"
	"net/http"
	"os"
	"slices"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type podImageApi_ struct {
}

var PodImageApi podImageApi_

type pushStatusReq struct {
	InstanceUuid string `json:"instance_uuid"`
	ImageUuid    string `json:"image_uuid"`
}

type podImageStatusResp struct {
	Uuid           string `json:"uuid"`
	Status         int    `json:"status"`
	StatusTxt      string `json:"status_txt"`
	AuditStatus    int    `json:"audit_status"`
	AuditStatusTxt string `json:"audit_status_txt"`
	Done           bool   `json:"done"`
}

type podImageReq struct {
	InstanceUuid string `json:"instance_uuid"`
	ImageUuid    string `json:"image_uuid"`
	PodId        uint   `json:"pod_id"`
	ImageType    int    `json:"image_type"`
	ImageName    string `json:"image_name"`
	Status       int    `json:"status"`
	Kw           string `json:"kw"`
	Page         int    `json:"page"`
	PageSize     int    `json:"page_size"`

	ImageHistory bool `json:"image_history"`
}

type podImageSetReq struct {
	ImageUuid string `json:"image_uuid"`
	Title     string `json:"title"`
	Share     string `json:"share"`
}

type podImageDelReq struct {
	ImageUuid string `json:"image_uuid"`
	Force     bool   `json:"force"`
}

type podImageSquashReq struct {
	ImageUuid string `json:"image_uuid"`
	Query     bool   `json:"query"`
	Cancel    bool   `json:"cancel"`
	ToLayer   int    `json:"to_layer"`
}

type podImageResp struct {
	ID    uint   `json:"-"`
	Uuid  string `json:"uuid"`
	PodId uint   `json:"-"`
	//ImageType int               `json:"image_type"`
	//ImageName string            `json:"image_name"`
	StorageMode    int    `json:"storage_mode"`
	StorageModeTxt string `json:"storage_mode_txt"`
	Title          string `json:"title"`
	ImageTag       string `json:"image_tag"`

	LayerCount int `json:"layer_count"`
	Squash     int `json:"squash"` //-1running 0 1should 2must

	//ccm
	Key        string                        `json:"key,omitempty"`
	Components map[string]dockerfile.Version `json:"components,omitempty"`

	ImageMeta    string `json:"-"`
	ImageName    string `json:"-"`
	ImageType    int    `json:"-"`
	ImageHistory any    `json:"image_history,omitempty"`

	Size           float64           `json:"size"`
	SizeTxt        string            `json:"size_txt"`
	Status         int               `json:"status"`
	StatusTxt      string            `json:"status_txt"`
	Share          string            `json:"share"`
	Reason         string            `json:"reason"`
	AuditStatus    int               `json:"audit_status"`
	AuditStatusTxt string            `json:"audit_status_txt"`
	LastSaveTime   jsontime.JsonTime `json:"last_save_time"`
	CreatedAt      jsontime.JsonTime `json:"created_at"`
	UpdatedAt      jsontime.JsonTime `json:"updated_at"`
	Pod            podInstanceResp   `json:"pod" gorm:"-"`
}

func (obj podImageApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	oReq.Status = -1
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	mPod := make(map[uint]podInstanceResp)
	ccmDockerfiles := make(map[int]*dockerfile.Dockerfile)

	var podImage model.PodImage

	queryParm := make(map[string]interface{})
	queryParm["user_id"] = claims.UserId
	queryParm["image_type"] = enums.ImageTypeEnum.Private
	if oReq.ImageName != "" {
		queryParm["image_name"] = oReq.ImageName
	}

	if oReq.ImageUuid != "" {
		if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
			msg = "查询镜像信息出错"
			logger.Error(msg, err)
			return
		}
		queryParm["id"] = podImage.ID
	}

	result["sum_size"] = 0
	result["free_size"] = common.ImageStoreFreeSize
	result["limit_size"] = common.ImageStoreLimitSize
	result["hour_price"] = common.ImageStoreHourPrice
	var stats = make([]structs.StatImageStore, 0)
	if err := podImage.StatStore(&stats, claims.UserId, 1, 1000); err != nil {
		msg = "统计出错"
		logger.Error(err)
		return
	} else {
		if len(stats) == 1 {
			result["sum_size"] = stats[0].SumSize
			var user model.User
			if err := user.GetById(claims.UserId); err != nil {
				logger.Error(err)
			} else {
				if user.PrivateImageSize != stats[0].SumSize.IntPart() {
					service.WarnService.PrivateImageStore(claims.UserId, user.PrivateImageSize, stats[0].SumSize.IntPart())
					if err := user.SetPrivateImageSize(stats[0].SumSize.IntPart()); err != nil {
						logger.Error(err, " 更新用户镜像存储失败 userId:", claims.UserId)
					}
				}
			}
		}
	}

	{
		var user model.User
		if err := user.GetById(claims.UserId); err != nil {
			logger.Error(err, user.ID)
		}
		queryParmShare := make(map[string]interface{})
		queryParmShare["image_type"] = enums.ImageTypeEnum.Private
		tmpAry := make([]string, 0)
		if user.Mobile != "" {
			tmpAry = append(tmpAry, user.Mobile)
		} else if user.DisplayName != "" {
			tmpAry = append(tmpAry, user.DisplayName)
		}
		queryParmShare["share"] = tmpAry

		if len(tmpAry) > 0 {
			var ary = make([]podImageResp, 0)
			if _, err := podImage.ListPro(&ary, queryParmShare, 1, 100); err != nil {
				msg = "查询失败"
				logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
				return
			} else {
				for i := 0; i < len(ary); i++ {
					podId := ary[i].PodId
					if podId == 0 {
						continue
					}
					if _, ok := mPod[podId]; !ok {
						var pod model.Pod
						if err := pod.GetById(podId); err != nil {
							logger.Error(err, " imageUuid:", ary[i].Uuid)
						} else {
							if pod.Logo != "" {
								pod.Logo = fmt.Sprintf("%s%s?", config.DiffusionDomain, pod.Logo)
							}
							if pod.Cover != "" {
								pod.Cover = fmt.Sprintf("%s%s", config.DiffusionDomain, pod.Cover)
							}
							var resp podInstanceResp
							if err := utils.Scan(pod, &resp); err != nil {
								logger.Error(err, " imageUuid:", ary[i].Uuid)
							} else {
								resp.Desc = ""
								resp.Markdown = ""
								mPod[podId] = resp
							}
						}
					}

					if _, ok := mPod[podId]; ok {
						ary[i].Pod = mPod[podId]
						if ary[i].Title == "" {
							ary[i].Title = mPod[podId].Title
						}
					}

					if ary[i].Status == 1 {
						ary[i].StatusTxt = "有效"
					} else {
						ary[i].StatusTxt = "无效"
					}
					if ary[i].Size > 0 {
						ary[i].SizeTxt = fmt.Sprintf("%.2fGiB", ary[i].Size/1024/1024/1024)
					}
					ary[i].StorageModeTxt = enums.ImageStorageModeEnum.Name(ary[i].StorageMode)
					ary[i].AuditStatusTxt = enums.ImageAuditStatusEnum.Name1(ary[i].AuditStatus)
					if ary[i].AuditStatus == enums.ImageAuditStatusEnum.PushFail && ary[i].Reason != "" {
						ary[i].AuditStatusTxt += "(" + ary[i].Reason + ")"
					}
				}
				result["shares"] = ary
			}
		}
	}

	var ary = make([]podImageResp, 0)
	if total, err := podImage.ListPro(&ary, queryParm, 1, 100); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		for i := 0; i < len(ary); i++ {
			if oReq.ImageHistory {
				// b, err := os.ReadFile("/mnt/pod-data/docker-image-meta/" + strconv.FormatUint(uint64(ary[i].ID), 10) + ".json")
				// if err == nil {
				// 	var m map[string]any
				// 	err = json.Unmarshal(b, &m)
				// 	if err == nil {
				// 		ary[i].ImageHistory = m["History"]
				// 	}
				// }

				hubImagePath := service.GetHubImagePath(ary[i].ImageType, ary[i].ImageName, ary[i].ImageTag)
				if hubImagePath != "" {
					manifests, err := service.GetHubImageManifests(hubImagePath)
					if err == nil {
						blobs, err := service.GetHubImageBlobs(hubImagePath, manifests.Config.Digest)
						if err == nil {
							a := blobs.History
							j := 0
							for k, h := range a {
								if h.EmptyLayer {
									continue
								}
								if j < len(manifests.Layers) {
									a[k].HubImageDigest = manifests.Layers[j]
									a[k].MediaType = ""
									j++
								}
							}
							slices.Reverse(a)
							ary[i].ImageHistory = a
						}
					}
				}
			}

			podId := ary[i].PodId
			if podId == 0 {
				continue
			}
			if _, ok := mPod[podId]; !ok {
				var pod model.Pod
				if err := pod.GetById(podId); err != nil {
					logger.Error(err, " imageUuid:", ary[i].Uuid)
				} else {
					if pod.Logo != "" {
						pod.Logo = fmt.Sprintf("%s%s?", config.DiffusionDomain, pod.Logo)
					}
					if pod.Cover != "" {
						pod.Cover = fmt.Sprintf("%s%s", config.DiffusionDomain, pod.Cover)
					}
					var resp podInstanceResp
					if err := utils.Scan(pod, &resp); err != nil {
						logger.Error(err, " imageUuid:", ary[i].Uuid)
					} else {
						resp.Desc = ""
						resp.Markdown = ""
						mPod[podId] = resp
					}
				}
			}

			if _, ok := mPod[podId]; ok {
				ary[i].Pod = mPod[podId]
				if ary[i].Title == "" {
					ary[i].Title = mPod[podId].Title
				}
			}

			if service.IsSquashing(ary[i].Uuid) {
				ary[i].Squash = -1
			} else if ary[i].LayerCount > 120 {
				ary[i].Squash = 2
			} else if ary[i].LayerCount > 30 {
				ary[i].Squash = 1
			}

			if a := strings.Split(ary[i].ImageTag, "-"); len(a) == 3 && a[0] == "ccm" { //or pod.ClassType+pod.ImageName
				id, _ := strconv.Atoi(a[1])
				row, ok := ccmDockerfiles[id]
				if !ok {
					row = dockerfile.GetByID(id)
					ccmDockerfiles[id] = row
				}
				if row != nil {
					ary[i].Key = row.Key
					ary[i].Components = row.Components
				}
				//SHOULD NOT
				if ary[i].Key == "" {
					ary[i].Key = strconv.Itoa(id)
				}
				if ary[i].Components == nil {
					ary[i].Components = make(map[string]dockerfile.Version)
				}
			}

			if ary[i].Status == 1 {
				ary[i].StatusTxt = "有效"
			} else {
				ary[i].StatusTxt = "无效"
			}
			if ary[i].Size > 0 {
				ary[i].SizeTxt = fmt.Sprintf("%.2fGiB", ary[i].Size/1024/1024/1024)
			}
			ary[i].StorageModeTxt = enums.ImageStorageModeEnum.Name(ary[i].StorageMode)
			ary[i].AuditStatusTxt = enums.ImageAuditStatusEnum.Name1(ary[i].AuditStatus)
			if ary[i].AuditStatus == enums.ImageAuditStatusEnum.PushFail && ary[i].Reason != "" {
				ary[i].AuditStatusTxt += "(" + ary[i].Reason + ")"
			}

			if ary[i].AuditStatus == enums.ImageAuditStatusEnum.Pushing || ary[i].AuditStatus == enums.ImageAuditStatusEnum.Commiting {

				if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(ary[i].ID)); err != nil {
					logger.Error(err)
				} else {
					if item, _, err := tasklog.Last(logKey); err != nil {
						if err != gorm.ErrEmptySlice {
							logger.Error(err)
						}
					} else {
						ary[i].AuditStatusTxt = item.Msg
						if item.Percent > 0 && item.Percent < 1 {
							ary[i].AuditStatusTxt = fmt.Sprintf("%s%.2f%%", item.Msg, item.Percent*100)
						}
					}
				}
			}
		}
		result["images"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj podImageApi_) PrivatePodImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.InstanceUuid == "" {
		msg = "实例ID不能为空"
		return
	}

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "查询实例信息失败"
		logger.Error(msg, err, oReq.InstanceUuid)
		return
	}

	var pod model.Pod
	if err := pod.GetById(instance.PodId); err != nil {
		msg = "获取POD信息失败"
		logger.Error(msg, err)
		return
	}

	podImage := model.PodImage{
		Title:    pod.Title,
		ImageTag: instance.ImageTag,
	}
	if err := podImage.GetPrivatePodImage(instance.UserId, instance.PodId, instance.ImageTag); err != nil {
		msg = "查询个人POD镜像信息出错"
		if err == gorm.ErrRecordNotFound {
			msg = "个人POD镜像不存在"
		}
		logger.Error(msg, err)
		return
	} else {
		var resp podImageResp
		if err := utils.Scan(podImage, &resp); err != nil {
			msg = "个人POD镜像不存在"
			logger.Error(msg)
			return
		} else {
			var pod model.Pod
			podId := resp.PodId
			if err := pod.GetById(podId); err != nil {
				logger.Error(err, " imageUuid:", resp.Uuid)
			} else {
				if pod.Logo != "" {
					pod.Logo = fmt.Sprintf("%s%s?", config.DiffusionDomain, pod.Logo)
				}
				if pod.Cover != "" {
					pod.Cover = fmt.Sprintf("%s%s", config.DiffusionDomain, pod.Cover)
				}
				var respPod podInstanceResp
				if err := utils.Scan(pod, &respPod); err != nil {
					logger.Error(err, " imageUuid:", resp.Uuid)
				} else {
					respPod.Markdown = ""
					resp.Pod = respPod
					if resp.Title == "" {
						resp.Title = respPod.Title
					}
				}
			}
			result["image"] = resp
			code = 0
		}
	}
}

func (obj podImageApi_) SetTitle(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podImageSetReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Title == "" {
		msg = "请输入标题"
		logger.Error(msg, oReq)
		return
	}

	var podImage model.PodImage
	if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
		msg = "查询镜像信息出错"
		logger.Error(msg, err)
		return
	}

	if podImage.UserId != claims.UserId {
		msg = "无权限"
		logger.Error(msg, " podImageId:", podImage.ID)
		return
	}
	if podImage.ImageType != enums.ImageTypeEnum.Private {
		msg = "镜像类型不正确"
		logger.Error(msg, " podImageId:", podImage.ID)
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(podImage.PodUuid); err != nil {
		msg = "Pod信息获取失败"
		logger.Error(msg, err, podImage.PodUuid)
		return
	}

	if err := podImage.SetTitle(oReq.Title); err != nil {
		msg = "设置失败"
		logger.Error(msg, err, oReq)
		return
	}
	msg = "设置成功"
	code = 0
}

func (obj podImageApi_) SetShare(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podImageSetReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	//if oReq.Share == "" {
	//	msg = "请输入分享用户"
	//	logger.Error(msg, oReq)
	//	return
	//}

	if oReq.Share != "" {
		if !strings.HasPrefix(oReq.Share, "|") {
			oReq.Share = "|" + oReq.Share
		}
		if !strings.HasSuffix(oReq.Share, "|") {
			oReq.Share = oReq.Share + "|"
		}
	}

	var podImage model.PodImage
	if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
		msg = "查询镜像信息出错"
		logger.Error(msg, err)
		return
	}

	if podImage.UserId != claims.UserId {
		msg = "无权限"
		logger.Error(msg, " podImageId:", podImage.ID)
		return
	}
	if podImage.ImageType != enums.ImageTypeEnum.Private {
		msg = "镜像类型不正确"
		logger.Error(msg, " podImageId:", podImage.ID)
		return
	}

	if err := podImage.SetShare(oReq.Share); err != nil {
		msg = "设置失败"
		logger.Error(msg, err, oReq)
		return
	}
	msg = "设置成功"
	code = 0
}

func (obj podImageApi_) Squash(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podImageSquashReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ImageUuid == "" {
		msg = "参数错误"
		logger.Error(msg, oReq)
		return
	}

	var podImage model.PodImage
	if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
		msg = "查询镜像信息出错"
		logger.Error(msg, err)
		return
	}

	if podImage.UserId != claims.UserId {
		msg = "无权限"
		logger.Error(msg, " podImageId:", podImage.ID)
		return
	}
	if !(podImage.ImageType == enums.ImageTypeEnum.Private ||
		podImage.ImageType == enums.ImageTypeEnum.Public) { //kol
		msg = "镜像类型不正确"
		logger.Error(msg, " podImageId:", podImage.ID)
		return
	}

	if oReq.ToLayer > 0 {
		//强制合并
	} else if !(oReq.Query || oReq.Cancel) && podImage.LayerCount <= 30 {
		msg = "镜像暂不需要进行层合并"
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(podImage.PodUuid); err != nil {
		msg = "Pod信息获取失败"
		logger.Error(msg, err, podImage.PodUuid)
		return
	}
	if podImage.AuditStatus == enums.ImageAuditStatusEnum.Pushing {
		msg = "镜像上传中，不能做层合并操作"
		code = 3
		return
	}

	hubImagePath := service.GetHubImagePath(podImage.ImageType, podImage.ImageName, podImage.ImageTag)
	if hubImagePath == "" {
		msg = "镜像路径为空"
		return
	}

	var hub structs.HubRepositorie
	if err := service.GetHubImage(podImage.ImageType, podImage.ImageName, podImage.ImageTag, &hub); err != nil {
		if err == gorm.ErrRecordNotFound {
			msg = "镜像信息不存在"
			return
		} else {
			msg = "未获取到镜像信息失败"
			return
		}
	}
	if hub.Digest == "" {
		msg = "镜像ID为空"
		return
	}

	if data, err := service.SquashImage(podImage.Uuid, hubImagePath, oReq.Query, oReq.Cancel, oReq.ToLayer); err != nil {
		msg = err.Error()
		return
	} else {
		result = data
	}

	msg = "操作成功"
	code = 0
}

func (obj podImageApi_) Delete(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podImageDelReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ImageUuid == "" {
		msg = "参数错误"
		logger.Error(msg, oReq)
		return
	}

	var podImage model.PodImage
	if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
		msg = "查询镜像信息出错"
		logger.Error(msg, err)
		return
	}

	if podImage.UserId != claims.UserId {
		msg = "无权限"
		logger.Error(msg, " podImageId:", podImage.ID)
		return
	}
	if podImage.ImageType != enums.ImageTypeEnum.Private {
		msg = "镜像类型不正确"
		logger.Error(msg, " podImageId:", podImage.ID)
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(podImage.PodUuid); err != nil {
		msg = "Pod信息获取失败"
		logger.Error(msg, err, podImage.PodUuid)
		return
	}
	if oReq.Force == false {
		if podImage.AuditStatus == enums.ImageAuditStatusEnum.Pushing {
			msg = "镜像上传中，不能做删除操作"
			code = 1003
			return
		}
	}

	//
	//if podImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass {
	//	if pod.ImageTags == "" || pod.ImageTags == podImage.ImageTag {
	//		if pod.Status == 1 {
	//			msg = "这是该Pod唯一审核通过的版本，请先下架该Pod再删除"
	//			return
	//		}
	//	}
	//}

	if podImage.LastSaveTime.After(common.DefaultTime) {
		msg = "需要删除镜像文件"
		if podImage.ImageType == enums.ImageTypeEnum.Private {
			if err := service.DeleteHubByImageName(podImage.ImageType, podImage.ImageName); err != nil {
				msg = "删除镜像文件失败"
				logger.Error(err)
				//return
			}
		}
	}

	if podImage.StorageMode == enums.ImageStorageModeEnum.RegistryDisk || podImage.StorageMode == enums.ImageStorageModeEnum.PrivateDisk {
		if privateImageSavePath, err := service.GetPrivateImageSavePathByPodImage(podImage); err != nil {
			logger.Error(err)
		} else {
			if len(privateImageSavePath) < 10 || !strings.HasSuffix(privateImageSavePath, ".tar") {
				msg = "路径不正确"
				logger.Error("路径不正确", privateImageSavePath)
				return
			}
			info, err := os.Stat(privateImageSavePath)
			if err != nil {
				if os.IsNotExist(err) {
					msg = "路径不存在"
					logger.Error(msg, privateImageSavePath, oReq)
				} else {
					msg = "获取路径信息时出错"
					logger.Error(msg, privateImageSavePath, oReq)
					return
				}
			} else {
				if info.Mode().IsRegular() {
					if err = os.Remove(privateImageSavePath); err != nil {
						msg = "删除文件出错"
						logger.Error(msg, err)
						return
					}
				} else {
					msg = "路径类型不正确"
					logger.Error(msg, privateImageSavePath, oReq)
					return
				}
			}
		}
	}

	if err := podImage.SetDelete(); err != nil {
		msg = "删除失败"
		logger.Error(msg, err, oReq)
		return
	}

	//if err := podImage.Delete(); err != nil {
	//	msg = "删除失败"
	//	logger.Error(msg, err, oReq)
	//	return
	//}

	msg = "删除成功"
	code = 0
}

func (obj podImageApi_) ResumeLastImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podImageDelReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ImageUuid == "" {
		msg = "参数错误"
		logger.Error(msg, oReq)
		return
	}

	var podImage model.PodImage
	if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
		msg = "查询镜像信息出错"
		logger.Error(msg, err)
		return
	}

	if podImage.UserId != claims.UserId {
		msg = "无权限"
		logger.Error(msg, " podImageId:", podImage.ID)
		return
	}
	if podImage.ImageType != enums.ImageTypeEnum.Private {
		msg = "镜像类型不正确"
		logger.Error(msg, " podImageId:", podImage.ID)
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(podImage.PodUuid); err != nil {
		msg = "Pod信息获取失败"
		logger.Error(msg, err, podImage.PodUuid)
		return
	}

	if podImage.AuditStatus != enums.ImageAuditStatusEnum.PushFail {
		msg = "只有保存失败的镜像才能做恢复操作"
		logger.Error(msg)
		return
	}

	var instance model.Instance
	if err := instance.HasRunningImage(claims.UserId, podImage.ID); err != nil {
		if err != gorm.ErrRecordNotFound {
			msg = "查询失败"
			logger.Error("instance.HasRunningImage err:", err)
			return
		}
	} else {
		msg = "该镜像正在运行中，请先关闭该镜像"
		return
	}

	var hub structs.HubRepositorie
	if err := service.GetHubImage(podImage.ImageType, podImage.ImageName, podImage.ImageTag, &hub); err != nil {
		if err == gorm.ErrRecordNotFound {
			msg = "镜像信息不存在"
			return
		} else {
			msg = "未获取到镜像信息失败"
			return
		}
	}
	if err := podImage.ResumeAuditStatus(); err != nil {
		msg = "设置失败"
		logger.Error(msg, err)
		return
	}
	msg = "恢复完成"
	code = 0
}

func (obj podImageApi_) Status(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq pushStatusReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var podImage model.PodImage
	if oReq.ImageUuid != "" {
		if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
			msg = "获取镜像数据失败"
			logger.Error(msg, oReq.ImageUuid)
			return
		}
	} else if oReq.InstanceUuid != "" {
		var instance model.Instance
		if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
			msg = "获取实例数据失败"
			logger.Error(msg, oReq.InstanceUuid)
			return
		}
		if instance.SaveImageId == 0 {
			msg = "无镜像信息"
			logger.Error(msg, oReq.InstanceUuid)
			return
		} else {
			if err := podImage.GetById(instance.SaveImageId); err != nil {
				msg = "获取镜像数据失败"
				logger.Error(msg, oReq.ImageUuid)
				return
			}
		}
	}

	resp := podImageStatusResp{
		Uuid:           podImage.Uuid,
		Status:         podImage.Status,
		AuditStatus:    podImage.AuditStatus,
		AuditStatusTxt: enums.ImageAuditStatusEnum.Name(podImage.AuditStatus),
		Done:           true,
	}

	if podImage.AuditStatus == enums.ImageAuditStatusEnum.Pushing || podImage.AuditStatus == enums.ImageAuditStatusEnum.Commiting {
		resp.Done = false
		if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(podImage.ID)); err != nil {
			logger.Error(err)
		} else {
			if item, _, err := tasklog.Last(logKey); err != nil {
				if err != gorm.ErrEmptySlice {
					logger.Error(err)
				}
			} else {
				resp.AuditStatusTxt = item.Msg
				if item.Percent > 0 && item.Percent <= 1 {
					resp.AuditStatusTxt = fmt.Sprintf("%s%.2f%%", item.Msg, item.Percent*100)
				}
			}
		}
	}
	result["image"] = resp
	msg = ""
	code = 0
}
