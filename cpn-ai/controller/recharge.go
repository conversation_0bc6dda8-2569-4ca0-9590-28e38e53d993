package controller

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/service/pay"
	"cpn-ai/structs"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/skip2/go-qrcode"
	"net/http"
	"net/url"
	"strings"
)

type rechargeApi struct {
}

type productListReq struct {
	ProductId uint   `json:"product_id"`
	Store     string `json:"store"`
	GroupName string `json:"group_name"`
}

type rechargeReq struct {
	Action      string `json:"action"`
	CustomParam string `json:"custom_param"`
	ProductId   uint   `json:"product_id"`
	PayChannel  string `json:"pay_channel"`
	Gateway     string `json:"gateway"`
	DoMain      string `json:"do_main"`
	Coin        int    `json:"coin"`
	Price       string `json:"price"`
}

type rechargeProductResp struct {
	ProductId   uint            `json:"product_id"`
	ProductCode string          `json:"product_code"`
	GroupName   string          `json:"group_name"`
	ShowTitle   string          `json:"show_title"`
	Description string          `json:"description"`
	Tip         string          `json:"tip"`
	Coin        int             `json:"coin"`
	Price       decimal.Decimal `json:"price"`
	OrigPrice   decimal.Decimal `json:"orig_price"`
}

type rechargeListReq struct {
	KW       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type rechargeListItemResp struct {
	UserId     uint              `json:"-"`
	CreatedAt  jsontime.JsonTime `json:"created_at"'`
	PayTime    jsontime.JsonTime `json:"pay_time"'`
	OutTradeNo string            `json:"out_trade_no"`
	Amount     decimal.Decimal   `json:"amount"`
}

type queryOrderReq struct {
	OutTradeNo string `json:"out_trade_no"`
}

type queryOrderResp struct {
	OutTradeNo string `json:"out_trade_no"`
	State      int    `json:"state"`
}

func (obj rechargeApi) GetProductList(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	insider := false
	//tokenHeader := c.Request.Header.Get("Authorization")
	//if tokenHeader != "" {
	//	claims, err := middleware.GetClaimsByToken(tokenHeader)
	//	if claims == nil || err != nil {
	//		logger.Error(err)
	//	} else {
	//		insider = service.UserService.IsInsiderUser(claims.UserId)
	//	}
	//}

	var oReq productListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var rechargeProduct model.RechargeProduct
	ary := make([]model.RechargeProduct, 0)
	if err := rechargeProduct.List(&ary, rechargeProduct.ID, "suanyun.cyuai.aigc", oReq.Store, oReq.GroupName); err != nil {
		logger.Error(err)
		msg = "获取订购产品失败"
		return
	}

	outAry := make([]rechargeProductResp, 0)
	for _, item := range ary {
		if !insider && strings.Contains(item.ShowTitle+item.Description+item.GroupName, "测试") {
			continue
		}
		resp := rechargeProductResp{
			ProductId:   item.ID,
			ProductCode: item.ProductCode,
			ShowTitle:   item.ShowTitle,
			Description: item.Description,
			Tip:         item.Tip,
			GroupName:   item.GroupName,
			Coin:        item.Coin,
			Price:       item.Price,
			OrigPrice:   item.OrigPrice,
		}
		outAry = append(outAry, resp)
	}

	result["items"] = outAry
	msg = "产品价格信息"
	code = 0
}

func (obj rechargeApi) Launch(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq rechargeReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		code = 2
		msg = "请先登录"
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	referer := c.Request.Referer()
	logger.Info("Launch referer", referer)

	var product model.RechargeProduct
	if err := product.GetById(oReq.ProductId); err != nil {
		logger.Error(err)
		msg = "未找到购买商品数据"
		return
	}
	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		logger.Error(err)
		msg = "获取用户信息失败"
		return
	}

	//if product.ProductCategory == enums.ProductCategoryEnum.Subscription {
	//	if user.MemberExpires.After(time.Now()) {
	//		logger.Error("您的会员还未过期，请到期后再续费  userID:", claims.UserId)
	//		errmsg.Abort(c, errmsg.FAIL, "您的会员还未过期，请到期后再续费")
	//		return
	//	}
	//}

	outTradeNo := ""
	if product.ProductCategory == enums.ProductCategoryEnum.Subscription {
		outTradeNo, _ = model.OrderNo.NewByOrderType(enums.OrderTypeEnum.Subscribe, 0)
	} else {
		outTradeNo, _ = model.OrderNo.NewByOrderType(enums.OrderTypeEnum.RechargeBuy, 0)
	}
	if outTradeNo == "" {
		msg = "生成单号失败"
		err := errors.New(msg)
		logger.Error(err, oReq)
		return
	}

	amoutPrice := decimal.Zero
	if amount, err := decimal.NewFromString(oReq.Price); err != nil {
		logger.Error(err)
		msg = "充值金额不正确"
		return
	} else {
		amoutPrice = amount
	}

	if amoutPrice.LessThanOrEqual(decimal.Zero) {
		logger.Error("充值金额不正确", amoutPrice.String())
		msg = "充值金额不正确"
		return
	} else if product.Price.IsZero() == false && amoutPrice.Equal(product.Price) == false {
		logger.Error(claims.UserId, "充值金额不一致 ", oReq)
		msg = "充值金额不一致"
		return
	}

	//if product.Coin != oReq.Coin {
	//	logger.Error(claims.UserId, "星星数量不一致 ", oReq)
	//	errmsg.Abort(c, errmsg.FAIL, "星星数量不一致")
	//	return
	//}

	if tmpKey := enums.PayGatewayEnum.GetKey(oReq.Gateway); tmpKey == "" {
		msg = "支付网关参数错误"
		logger.Error(msg, oReq)

		return
	}

	if tmpKey := enums.PayChannelEnum.GetKey(oReq.PayChannel); tmpKey == "" {
		msg = "支付渠道参数错误"
		logger.Error(msg, oReq)
		return
	}

	recharge := model.Recharge{
		UserId:                claims.UserId,
		OutTradeNo:            outTradeNo,
		ProductId:             oReq.ProductId,
		ProductCode:           product.ProductCode,
		ProductCategory:       product.ProductCategory,
		Coin:                  product.Coin,
		Amount:                product.Price,
		PayChannel:            oReq.PayChannel,
		Gateway:               oReq.Gateway,
		PayCallbackJson:       "{}",
		PayRefundCallbackJson: "{}",
		CustomParam:           "{}",
	}
	if product.Price.IsZero() {
		recharge.Amount = amoutPrice
	}

	if oReq.CustomParam != "" {
		recharge.CustomParam = oReq.CustomParam
	}

	if err := recharge.Save(); err != nil || recharge.ID == 0 {
		msg = "充值订单生成失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("oReq:", utils.GetJsonFromStruct(oReq))
	if oReq.PayChannel == enums.PayChannelEnum.WechatPay {
		redirectUrl := service.GetSchemeHost(c.Request.Referer())
		if redirectUrl != "" {
			redirectUrl = redirectUrl + "/console/paygress"
		}
		logger.Info("redirectUrl:", redirectUrl)
		if oReq.Gateway == enums.PayGatewayEnum.Web {
			logger.Info("发起微信native支付")
			notifyUrl := fmt.Sprintf("https://www.%s/api/wechatpay/notify", service.GetSiteDomain(c.Request.Referer()))
			nativeRsp, err := service.WechatpayService.TradePayNative(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description, notifyUrl)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, nativeRsp)
				return
			}
			result["pay_url"] = nativeRsp.Response.CodeUrl
			if nativeRsp.Response.CodeUrl == "" {
				msg = "生成支付链接失败"
				logger.Error(msg)
				return
			}

			qrCode, err := qrcode.New(nativeRsp.Response.CodeUrl, qrcode.Medium)
			if err != nil {
				msg = "生成二维码失败"
				logger.Error(msg, err)
				return
			}
			pngBytes, err := qrCode.PNG(256)
			if err != nil {
				msg = "生成二维码失败"
				logger.Error(msg, err)
				return
			}
			result["qr_code"] = "data:image/png;base64," + base64.StdEncoding.EncodeToString(pngBytes)
			result["trade_no"] = recharge.OutTradeNo
			result["out_trade_no"] = recharge.OutTradeNo

		} else if oReq.Gateway == enums.PayGatewayEnum.H5 {
			//notifyUrl := fmt.Sprintf("https://www.%s/api_online/wechatpay/notify", c.Request.Referer())
			//if config.Env == enums.EnvEnum.ONLINE {
			//	notifyUrl = fmt.Sprintf("%sapi_online/wechatpay/notify", c.Request.Referer())
			//}
			notifyUrl := fmt.Sprintf("https://www.%s/api/wechatpay/notify", service.GetSiteDomain(c.Request.Referer()))
			logger.Info("notifyUrl:", notifyUrl)
			h5Rsp, err := service.WechatpayService.TradePayH5(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description, notifyUrl)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, h5Rsp)
				return
			}
			logger.Info("pay_url:", h5Rsp.Response.H5Url)
			// 解析原始 URL
			parsedURL, err := url.Parse(h5Rsp.Response.H5Url)
			if err != nil {
				msg = "Url解析失败"
				return
			}
			// 创建 URL 参数并追加到现有的 query string 中
			query := parsedURL.Query()
			query.Set("redirect_url", redirectUrl)

			// 将修改后的查询字符串重新设置到 URL
			parsedURL.RawQuery = query.Encode()

			rawUrl := parsedURL.String()
			logger.Info("rawUrl:", rawUrl)

			result["pay_url"] = rawUrl
			result["out_trade_no"] = recharge.OutTradeNo
		} else if oReq.Gateway == enums.PayGatewayEnum.App {
			prepayRsp, err := service.WechatpayService.TradePayApp(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, prepayRsp)
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["prepayid"] = prepayRsp.Response.PrepayId
			payParams, err := service.WechatpayService.PaySignOfApp(prepayRsp.Response.PrepayId)
			if err != nil {
				logger.Error(err)
			}
			result["payParams"] = payParams

		} else if oReq.Gateway == enums.PayGatewayEnum.Jsapi {
			//prepayRsp, err := service.WechatpayService.TradePayJsapi(user.Openid, recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			//if err != nil {
			//	logger.Error(err, prepayRsp)
			//	errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
			//	return
			//}
			//result["out_trade_no"] = recharge.OutTradeNo
			//result["prepayid"] = prepayRsp.Response.PrepayId
			//payParams, err := service.WechatpayService.PaySignOfJSAPI(prepayRsp.Response.PrepayId)
			//if err != nil {
			//	logger.Error(err)
			//}
			//result["payParams"] = payParams
			msg = "发送订单失败"
			logger.Error(msg, "enums.PayGatewayEnum.Jsapi 支付类型为创建")
			return
		} else {
			msg = "参数错误"
			logger.Error(msg)
			return
		}
	} else if oReq.PayChannel == enums.PayChannelEnum.AliPay {
		returnUrl := fmt.Sprintf("https://www.%s/console/paygress", service.GetSiteDomain(c.Request.Referer()))
		if oReq.Gateway == enums.PayGatewayEnum.Wap {
			payUrl, err := pay.AlipayService.TradeWapPay(returnUrl, recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			//payUrl, err := service.AlipayService.TradeWapPay(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, payUrl)
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["pay_url"] = payUrl
		} else if oReq.Gateway == enums.PayGatewayEnum.Web {
			payUrl, err := pay.AlipayService.TradePagePay(returnUrl, recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			//payUrl, err := service.AlipayService.TradePagePay(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, payUrl)
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["pay_url"] = payUrl
		} else if oReq.Gateway == enums.PayGatewayEnum.Native {
			payRsp, err := pay.AlipayService.TradePrecreate(returnUrl, recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			//payRsp, err := service.AlipayService.TradePrecreate(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, payRsp)
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["pay_rsp"] = payRsp
		} else if oReq.Gateway == enums.PayGatewayEnum.App {
			payParam, err := pay.AlipayService.TradeAppPay(returnUrl, recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			//payParam, err := service.AlipayService.TradeAppPay(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, payParam)
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["pay_param"] = payParam
		} else {
			msg = "参数错误"
			logger.Error("参数错误", oReq)
			return
		}
	} else if oReq.PayChannel == enums.PayChannelEnum.AppleIap {
		result["out_trade_no"] = recharge.OutTradeNo
	} else {
		msg = "支付参数错误"
		logger.Error(msg, oReq)
		return
	}

	if err := recharge.GetById(recharge.ID); err != nil {
		msg = "充值订单生成失败"
		logger.Error(msg, err)
		return
	}
	msg = "支付订单创建成功"
	code = 0
	return

}

func (obj rechargeApi) LaunchBuy(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq rechargeReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		code = 2
		msg = "请先登录"
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.Action == enums.RechargeActionEnum.BuyCard {

	} else {
		msg = "参数错误"
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		logger.Error(err)
		msg = "获取用户信息失败"
		return
	}

	desc := ""
	needAmount := decimal.Zero
	if oReq.Action == enums.RechargeActionEnum.BuyCard {
		var param structs.RechargeCustomParam
		if err := utils.GetStructFromJson(&param, oReq.CustomParam); err != nil || param.Action == "" {
			msg = "参数错误"
			logger.Error(msg, " CustomParam:", oReq.CustomParam)
			return
		}
		desc = "购买算力卡优惠卷:" + param.CouponCode
		var coupon model.Coupon
		if err := coupon.GetByCouponCode(param.CouponCode); err != nil {
			msg = "优惠卷查询失败"
			logger.Error(msg, err, param.CouponCode)
			return
		}
		if coupon.Status == 0 {
			msg = "该优惠卷暂时不可用"
			logger.Error(msg, param.CouponCode)
			return
		}

		today := jsontime.Today().Time()
		if coupon.ExpireDate.Before(today) {
			msg = "该优惠卷已过期，请使用其它优惠卷"
			return
		}

		if coupon.Quantity > 0 {
			leaveQuantity := coupon.Quantity - coupon.SoldCount
			if leaveQuantity <= 0 {
				msg = "该优惠卷已售完"
				return
			}
			if leaveQuantity < param.Quantity {
				msg = fmt.Sprintf("当前只有%d张可售优惠卷", leaveQuantity)
				return
			}
		}

		if coupon.SingleMax > 0 {

			queryParm := make(map[string]interface{})
			queryParm["buy_user_id"] = claims.UserId
			queryParm["coupon_id"] = coupon.ID

			var card model.Card
			if total, err := card.List(nil, queryParm, 1, 10000); err != nil {
				msg = "查询用户购买情况失败"
				logger.Error(msg, err)
				return
			} else {
				if param.Quantity+int(total) > coupon.SingleMax {
					msg = fmt.Sprintf("本优惠卷每人最多只能购买%d张", coupon.SingleMax)
					if total > 0 {
						msg = fmt.Sprintf("本优惠卷每人最多只能购买%d张，您前面已成功购买了%d张", coupon.SingleMax, total)
					}
					return
				}
			}
		}
		needAmount = coupon.SalePrice.Mul(decimal.NewFromInt(int64(param.Quantity)))
	}

	outTradeNo, _ := model.OrderNo.NewByOrderType(enums.OrderTypeEnum.RechargeBuy, 0)
	if outTradeNo == "" {
		msg = "生成单号失败"
		err := errors.New(msg)
		logger.Error(err, oReq)
		return
	}

	amoutPrice := decimal.Zero
	if amount, err := decimal.NewFromString(oReq.Price); err != nil {
		logger.Error(err)
		msg = "充值金额不正确"
		return
	} else {
		amoutPrice = amount
	}

	if amoutPrice.LessThanOrEqual(decimal.Zero) {
		logger.Error("充值金额不正确", amoutPrice.String())
		msg = "充值金额不正确"
		return
	}

	if amoutPrice.Equal(needAmount) == false {
		logger.Error(claims.UserId, "充值金额不一致 ", oReq)
		msg = "充值金额不一致"
		return
	}

	if tmpKey := enums.PayGatewayEnum.GetKey(oReq.Gateway); tmpKey == "" {
		msg = "支付网关参数错误"
		logger.Error(msg, oReq)

		return
	}

	if tmpKey := enums.PayChannelEnum.GetKey(oReq.PayChannel); tmpKey == "" {
		msg = "支付渠道参数错误"
		logger.Error(msg, oReq)
		return
	}

	recharge := model.Recharge{
		Action:     oReq.Action,
		UserId:     claims.UserId,
		OutTradeNo: outTradeNo,
		//ProductId:             oReq.ProductId,
		//ProductCode:           product.ProductCode,
		//ProductCategory:       product.ProductCategory,
		//Coin:                  product.Coin,
		Amount:                amoutPrice,
		PayChannel:            oReq.PayChannel,
		Gateway:               oReq.Gateway,
		PayCallbackJson:       "{}",
		PayRefundCallbackJson: "{}",
		CustomParam:           oReq.CustomParam,
	}

	if err := recharge.Save(); err != nil || recharge.ID == 0 {
		msg = "订单生成失败"
		logger.Error(msg, err)
		return
	}

	if oReq.PayChannel == enums.PayChannelEnum.WechatPay {
		redirectUrl := service.GetSchemeHost(c.Request.Referer())
		if redirectUrl != "" {
			redirectUrl = redirectUrl + "/console/buycardgress"
		}
		if oReq.Gateway == enums.PayGatewayEnum.Web {
			logger.Info("发起微信native支付")
			notifyUrl := fmt.Sprintf("https://www.%s/api/wechatpay/notify", service.GetSiteDomain(c.Request.Referer()))
			nativeRsp, err := service.WechatpayService.TradePayNative(recharge.OutTradeNo, recharge.Amount, desc, notifyUrl)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, nativeRsp)
				return
			}
			result["pay_url"] = nativeRsp.Response.CodeUrl
			if nativeRsp.Response.CodeUrl == "" {
				msg = "生成支付链接失败"
				logger.Error(msg)
				return
			}

			qrCode, err := qrcode.New(nativeRsp.Response.CodeUrl, qrcode.Medium)
			if err != nil {
				msg = "生成二维码失败"
				logger.Error(msg, err)
				return
			}
			pngBytes, err := qrCode.PNG(256)
			if err != nil {
				msg = "生成二维码失败"
				logger.Error(msg, err)
				return
			}
			result["qr_code"] = "data:image/png;base64," + base64.StdEncoding.EncodeToString(pngBytes)
			result["trade_no"] = recharge.OutTradeNo
			result["out_trade_no"] = recharge.OutTradeNo

		} else if oReq.Gateway == enums.PayGatewayEnum.H5 {
			//notifyUrl := fmt.Sprintf("https://www.%s/api_online/wechatpay/notify", service.GetSiteDomain(c.Request.Referer()))
			notifyUrl := fmt.Sprintf("https://www.%s/api/wechatpay/notify", service.GetSiteDomain(c.Request.Referer()))
			h5Rsp, err := service.WechatpayService.TradePayH5(recharge.OutTradeNo, recharge.Amount, desc, notifyUrl)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, h5Rsp)
				return
			}
			logger.Info("pay_url:", h5Rsp.Response.H5Url)
			// 解析原始 URL
			parsedURL, err := url.Parse(h5Rsp.Response.H5Url)
			if err != nil {
				msg = "Url解析失败"
				return
			}
			// 创建 URL 参数并追加到现有的 query string 中
			query := parsedURL.Query()
			query.Set("redirect_url", redirectUrl)

			// 将修改后的查询字符串重新设置到 URL
			parsedURL.RawQuery = query.Encode()

			rawUrl := parsedURL.String()
			logger.Info("rawUrl:", rawUrl)

			result["pay_url"] = rawUrl
			result["out_trade_no"] = recharge.OutTradeNo
		} else if oReq.Gateway == enums.PayGatewayEnum.App {
			prepayRsp, err := service.WechatpayService.TradePayApp(recharge.OutTradeNo, recharge.Amount, desc)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, prepayRsp)
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["prepayid"] = prepayRsp.Response.PrepayId
			payParams, err := service.WechatpayService.PaySignOfApp(prepayRsp.Response.PrepayId)
			if err != nil {
				logger.Error(err)
			}
			result["payParams"] = payParams

		} else if oReq.Gateway == enums.PayGatewayEnum.Jsapi {
			//prepayRsp, err := service.WechatpayService.TradePayJsapi(user.Openid, recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			//if err != nil {
			//	logger.Error(err, prepayRsp)
			//	errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
			//	return
			//}
			//result["out_trade_no"] = recharge.OutTradeNo
			//result["prepayid"] = prepayRsp.Response.PrepayId
			//payParams, err := service.WechatpayService.PaySignOfJSAPI(prepayRsp.Response.PrepayId)
			//if err != nil {
			//	logger.Error(err)
			//}
			//result["payParams"] = payParams
			msg = "发送订单失败"
			logger.Error(msg, "enums.PayGatewayEnum.Jsapi 支付类型为创建")
			return
		} else {
			msg = "参数错误"
			logger.Error(msg)
			return
		}
	} else if oReq.PayChannel == enums.PayChannelEnum.AliPay {
		returnUrl := fmt.Sprintf("https://www.%s/console/buycardgress", service.GetSiteDomain(c.Request.Referer()))
		if config.Env == enums.EnvEnum.ONLINE {
			returnUrl = fmt.Sprintf("https://online.hz01.%s/console/buycardgress", service.GetSiteDomain(c.Request.Referer()))
		}

		if oReq.Gateway == enums.PayGatewayEnum.Wap {
			payUrl, err := pay.AlipayService.TradeWapPay(returnUrl, recharge.OutTradeNo, recharge.Amount, desc)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, payUrl)
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["pay_url"] = payUrl
		} else if oReq.Gateway == enums.PayGatewayEnum.Web {
			payUrl, err := pay.AlipayService.TradePagePay(returnUrl, recharge.OutTradeNo, recharge.Amount, desc)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, payUrl)
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["pay_url"] = payUrl
		} else if oReq.Gateway == enums.PayGatewayEnum.Native {
			payRsp, err := pay.AlipayService.TradePrecreate(returnUrl, recharge.OutTradeNo, recharge.Amount, desc)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, payRsp)
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["pay_rsp"] = payRsp
		} else if oReq.Gateway == enums.PayGatewayEnum.App {
			payParam, err := pay.AlipayService.TradeAppPay(returnUrl, recharge.OutTradeNo, recharge.Amount, desc)
			if err != nil {
				msg = "发送订单失败"
				logger.Error(msg, err, payParam)
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["pay_param"] = payParam
		} else {
			msg = "参数错误"
			logger.Error("参数错误", oReq)
			return
		}
	} else if oReq.PayChannel == enums.PayChannelEnum.AppleIap {
		result["out_trade_no"] = recharge.OutTradeNo
	} else {
		msg = "支付参数错误"
		logger.Error(msg, oReq)
		return
	}

	if err := recharge.GetById(recharge.ID); err != nil {
		msg = "充值订单生成失败"
		logger.Error(msg, err)
		return
	}
	msg = "支付订单创建成功"
	code = 0
	return

}

func (obj rechargeApi) QueryByOutTradeNo(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	var oReq queryOrderReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		code = 2
		msg = "请先登录"
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var recharge model.Recharge
	if err := recharge.GetByOutTradeNo(oReq.OutTradeNo); err != nil {
		msg = "查询订单出错"
		logger.Error(msg, err)
		return
	}
	if recharge.UserId != claims.UserId {
		msg = "无权限"
		logger.Error(msg, " 用户ID不一致", recharge.UserId, "   ", claims.UserId)
		return
	}

	result["out_trade_no"] = recharge.OutTradeNo
	result["state"] = recharge.State
	result["state_txt"] = enums.RechargeStateEnum.GetShowTitle(recharge.State)
	msg = "订单信息"
	code = 0

}

func (obj rechargeApi) GetBalance(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	var listReq rechargeListReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		code = 2
		msg = "请先登录"
		return
	}

	if err := c.ShouldBindJSON(&listReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if listReq.PageSize < 1 {
		listReq.PageSize = 1
	}
	if listReq.Page < 1 {
		listReq.Page = 1
	}

	var recharge model.Recharge
	ary := make([]rechargeListItemResp, 0)
	if total, err := recharge.List(&ary, claims.UserId, listReq.KW, enums.RechargeStateEnum.TRADE_SUCCESS, listReq.Page, listReq.PageSize); err != nil {
		msg = "查询出错"
		logger.Error(msg, err)
		return
	} else {
		result["items"] = ary
		result["total"] = total
	}
	code = 0
}

var RechargeApi rechargeApi
