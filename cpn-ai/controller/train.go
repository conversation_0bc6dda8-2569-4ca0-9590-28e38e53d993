package controller

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/myimg"
	"cpn-ai/common/redisqueue"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/structs"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

type trainApi_ struct {
}

var TrainApi trainApi_

type trainImagesReq struct {
	ImagesUuid string `json:"images_uuid"`
	ImageUrl   string `json:"image_url"`
	Status     int    `json:"status"`
	Kw         string `json:"kw"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type trainTagsReq struct {
	ImagesUuid string `json:"images_uuid"`
	TagsUuid   string `json:"tags_uuid"`
	ImageUrl   string `json:"image_url"`
	Caption    string `json:"caption"`
}

type trainJobsReq struct {
	ImagesUuid string `json:"images_uuid"`
	TagsUuid   string `json:"tags_uuid"`
	JobsUuid   string `json:"jobs_uuid"`
}

type trainCropTagReq struct {
	ImagesUuid   string  `json:"images_uuid"`
	CropMethod   string  `json:"crop_method"`
	CropSize     string  `json:"crop_size"`
	TagThreshold float64 `json:"tag_threshold"`
	TagAlg       string  `json:"tag_alg"`
	TriggerWord  string  `json:"trigger_word"`
}

type trainTagsTrainReq struct {
	JobsLevel   string `json:"jobs_level"`
	TagsUuid    string `json:"tags_uuid"`
	LoraName    string `json:"lora_name"`
	TriggerWord string `json:"trigger_word"`

	ModelName       string `json:"model_name"`
	ModelNameOrPath string `json:"model_name_or_path"`

	JobParam structs.TrainJobParam `json:"job_param"`

	Steps                     int     `json:"steps"`
	Lr                        float64 `json:"lr"`
	Rank                      int     `json:"rank"`
	NumRepeats                int     `json:"num_repeats"`
	SaveSafetensorsEverySteps int     `json:"save_safetensors_every_steps"`
	Sample1                   string  `json:"sample_1"`
	Sample2                   string  `json:"sample_2"`
	Sample3                   string  `json:"sample_3"`
	SampleSteps               int     `json:"sample_steps"`
	BatchSize                 int     `json:"batch_size"`
}

type trainImageUploadReq struct {
	CouponCode string `json:"coupon_code"`
	CouponUuid string `json:"coupon_uuid"`
	Status     int    `json:"status"`
	Kw         string `json:"kw"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type trainModelListReq struct {
	ModelUuid string `json:"model_uuid"`
	Status    int    `json:"status"`
	Kw        string `json:"kw"`
	Page      int    `json:"page"`
	PageSize  int    `json:"page_size"`
}

type trainImagesListReq struct {
	ImagesUuid string `json:"images_uuid"`
	Status     int    `json:"status"`
	Kw         string `json:"kw"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type trainTagsListReq struct {
	ImagesUuid string `json:"images_uuid"`
	TagsUuid   string `json:"tags_uuid"`
	Status     int    `json:"status"`
	Kw         string `json:"kw"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}
type trainJobsListReq struct {
	JobsUuid string `json:"jobs_uuid"`
	TagsUuid string `json:"tags_uuid"`
	Status   int    `json:"status"`
	Kw       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type trainModelItemResp struct {
	Uuid         string         `json:"uuid"`
	Title        string         `json:"title"`
	Name         string         `json:"name"`
	Param        string         `json:"-"`
	DefaultParam map[string]any `json:"default_param"`
	Index        int            `json:"index"`
	Status       int            `json:"status"`
	StatusTxt    string         `json:"status_txt"`
}

type trainImagesItemResp struct {
	Uuid         string    `json:"uuid"`
	Title        string    `json:"title"`
	Count        int       `json:"count"`
	ImageUrls    []string  `json:"image_urls"`
	LastUseAt    time.Time `json:"last_use_at"`
	LastUseMilli int64     `json:"last_use_milli"`
	Remark       string    `json:"remark"`
	Status       int       `json:"status"`
}

type trainTagsItemResp struct {
	Uuid         string                    `json:"uuid"`
	ImagesUuid   string                    `json:"images_uuid" `
	Title        string                    `json:"title"`
	CropMethod   string                    `json:"crop_method"`
	CropSize     string                    `json:"crop_size"`
	TagThreshold float64                   `json:"tag_threshold"`
	TagAlg       string                    `json:"tag_alg"`
	TriggerWord  string                    `json:"trigger_word"`
	TagParams    string                    `json:"-"`
	TagParamsObj structs.CropTagParam      `json:"tag_params" gorm:"-"`
	Count        int                       `json:"count"`
	Remark       string                    `json:"remark"`
	PushAt       time.Time                 `json:"push_at"`
	StartAt      time.Time                 `json:"start_at"`
	EndAt        time.Time                 `json:"end_at"`
	ImageUrls    []string                  `json:"image_urls"`
	Captions     []string                  `json:"captions"`
	LastUseAt    time.Time                 `json:"last_use_at"`
	LastUseMilli int64                     `json:"last_use_milli"`
	Reason       string                    `json:"reason"`
	Status       model.TrainTagsStatusEnum `json:"status"`
	StatusTxt    string                    `json:"status_txt"`
	QTaskId      string                    `json:"q_task_id"`
}

type trainJobsItemResp struct {
	Uuid          string                    `json:"uuid"`
	ImagesUuid    string                    `json:"images_uuid" `
	TagsUuid      string                    `json:"tags_uuid" `
	Level         string                    `json:"level"`
	Title         string                    `json:"title"`
	LoraName      string                    `json:"lora_name"`
	TrainParams   string                    `json:"-"`
	TrainJobParam structs.TrainJobParam     `json:"train_params" gorm:"-"`
	Count         int                       `json:"count"`
	Remark        string                    `json:"remark"`
	PushAt        time.Time                 `json:"push_at"`
	StartAt       time.Time                 `json:"start_at"`
	EndAt         time.Time                 `json:"end_at"`
	ImageUrls     []string                  `json:"image_urls"`
	LastUseAt     time.Time                 `json:"last_use_at"`
	LastUseMilli  int64                     `json:"last_use_milli"`
	Reason        string                    `json:"reason"`
	Status        model.TrainJobsStatusEnum `json:"status"`
	StatusTxt     string                    `json:"status_txt"`
	QTaskId       string                    `json:"q_task_id"`
}

func (obj trainApi_) Models(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainModelListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var trainModels model.TrainModels
	var ary = make([]trainModelItemResp, 0)

	queryParm := make(map[string]interface{})

	if oReq.ModelUuid != "" {
		queryParm["model_uuid"] = oReq.ModelUuid
	}

	if oReq.Status >= 0 {
		queryParm["status"] = oReq.Status
	}

	if total, err := trainModels.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		//empty := make([]string, 0)
		//for i := 0; i < len(ary); i++ {
		//	ary[i].ImageUrls = empty
		//	ary[i].LastUseMilli = ary[i].LastUseAt.UnixMilli()
		//}
		result["models"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj trainApi_) ImageUpload(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	imagesUuid, _ := c.GetPostForm("images_uuid")

	var trainImages model.TrainImages
	if imagesUuid == "" {
		imagesUuid = utils.GetUUID()
		trainImages = model.TrainImages{
			Uuid:      imagesUuid,
			UserId:    claims.UserId,
			LastUseAt: time.Now(),
			Status:    1,
		}
		if err := trainImages.Save(); err != nil {
			msg = "保存数据集失败"
			logger.Error(msg, err)
			return
		}
	} else {
		if err := trainImages.GetByUuid(imagesUuid); err != nil {
			msg = "查询数据集出错"
			logger.Error(msg, err)
			return
		}
	}

	if trainImages.Uuid == "" {
		msg = "生成数据集失败"
		return
	}
	imagesUuid = trainImages.Uuid
	result["images_uuid"] = imagesUuid

	trainStorage := service.TrainService.GetTrainStorage()
	imagesBasePath := path.Join(trainStorage, "images", imagesUuid)
	viewsBasePath := path.Join(trainStorage, "views", imagesUuid)
	if _, err := os.Stat(imagesBasePath); err != nil {
		if os.IsNotExist(err) {
			if err := os.MkdirAll(imagesBasePath, os.ModePerm); err != nil {
				msg = "创建数据集文件夹出错"
				logger.Error(msg, err)
				return
			}
		}
	}

	if _, err := os.Stat(viewsBasePath); err != nil {
		if os.IsNotExist(err) {
			if err := os.MkdirAll(viewsBasePath, os.ModePerm); err != nil {
				msg = "创建缩略图文件夹出错"
				logger.Error(msg, err)
				return
			}
		}
	}

	f, errf := c.FormFile("file")
	if errf != nil {
		msg = "图片上传失败"
		logger.Error(msg, errf)
		return
	}
	ext := strings.ToLower(path.Ext(f.Filename)) // 输出 .html
	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" && ext != ".webp" {
		msg = "目前图片格式只支持（.jpg .jpeg .png .webp）"
		return
	}
	if f.Size > 10*1024*1024 {
		msg = "单张图片大小不能超过10M"
		return
	}
	imageName := utils.GetUUID()
	imageFileName := imageName + ext
	imageFilePath := path.Join(imagesBasePath, imageFileName)

	viewFilePath := path.Join(viewsBasePath, imageName+ext)

	logger.Info("imageFilePath:", imageFilePath)
	logger.Info("viewFilePath:", viewFilePath)
	if err := c.SaveUploadedFile(f, imageFilePath); err != nil {
		msg = "图片保存失败"
		logger.Error(msg, err)
		return
	} else {
		if img, str, err := myimg.FileToImg(imageFilePath); err != nil {
			msg = "读取图片文件失败"
			logger.Error(msg, str, err)
		} else {
			smallImg := myimg.ResizeImg(180, 180, img, true)
			if err := myimg.ImgToJpegFile(smallImg, viewFilePath, 80); err != nil {
				msg = "保存缩略图失败"
				logger.Error(msg, err)
				return
			}
		}
		msg = "文件上传成功"
		code = 0
		return
	}
}

func (obj trainApi_) ImageDel(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainImagesReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.ImageUrl == "" {
		msg = "参数错误"
		return
	}

	if !strings.Contains(oReq.ImageUrl, oReq.ImagesUuid) {
		msg = "参数不匹配"
		return
	}
	imagesUuid := oReq.ImagesUuid
	var trainImages model.TrainImages
	if err := trainImages.GetByUuid(imagesUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}
	if trainImages.UserId != claims.UserId {
		msg = "无权限"
		return
	}
	//if trainImages.Status == 1 {
	//	msg = "该数据集正在打标中，不能删除"
	//	return
	//}

	imageFileName := path.Base(oReq.ImageUrl) // 输出 name.html
	//ext := path.Ext(baseName)                      // 输出 .html

	trainStorage := service.TrainService.GetTrainStorage()
	imagesBasePath := path.Join(trainStorage, "images", imagesUuid)
	viewsBasePath := path.Join(trainStorage, "views", imagesUuid)

	imageFilePath := path.Join(imagesBasePath, imageFileName)
	viewFilePath := path.Join(viewsBasePath, imageFileName)

	if err := os.Remove(viewFilePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除缩略图失败"
			logger.Error(msg, err, viewFilePath)
			return
		}
	}

	if err := os.Remove(imageFilePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除图片失败"
			logger.Error(msg, err, imageFilePath)
			return
		}
	}
	msg = "删除成功"
	code = 0
}

func (obj trainApi_) ImageShow(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims == nil {
	//	msg = "请先登录"
	//	code = 2
	//	return
	//}
	// 1. 提取路径参数
	// 获取 URL 中 "/api/train/img/" 之后的所有内容，例如 "view/df35c2bf2f504c4a98e0959140a6244e/c1fc798bb3854f56aa527f2e4685083a.png"
	imagePathSegment := c.Param("imagePathSegment")

	// 2. 构建完整的本地文件系统路径
	// filepath.Join 会正确处理路径分隔符
	fullLocalPath := filepath.Join(service.TrainService.GetTrainStorage(), imagePathSegment)

	// 3. 检查文件是否存在并获取文件信息
	fileInfo, err := os.Stat(fullLocalPath)
	if os.IsNotExist(err) {
		msg = "图片不存在"
		return
	}
	if err != nil {
		msg = "获取图片失败"
		logger.Error(msg, err)
		return
	}

	// 4. 检查它是否是文件 (而不是目录)
	if fileInfo.IsDir() {
		msg = "不是文件路径"
		return
	}

	// 5. 根据文件扩展名设置正确的  Content-Type
	var contentType string
	switch filepath.Ext(fullLocalPath) { // 使用完整路径来获取扩展名
	case ".png":
		contentType = "image/png"
	case ".jpg", ".jpeg":
		contentType = "image/jpeg"
	case ".gif":
		contentType = "image/gif"
	default:
		msg = "不支持该图片格式"
		return
	}

	// 6. 发送文件内容
	c.Header("Content-Type", contentType)
	c.File(fullLocalPath) // 直接发送文件
}

func (obj trainApi_) ImagesItem(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainImagesListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ImagesUuid == "" {
		msg = "数据集参数错误"
		return
	}

	var trainImages model.TrainImages
	if err := trainImages.GetByUuid(oReq.ImagesUuid); err != nil {
		if err == gorm.ErrRecordNotFound {
			msg = "该图片集信息不存在"
			return
		}
		msg = "获取数据集数据失败"
		logger.Error(msg, err)
		return
	}
	imagesUuid := trainImages.Uuid

	var imagesItem trainImagesItemResp
	if err := utils.Scan(trainImages, &imagesItem); err != nil {
		msg = "转换数据失败"
		logger.Error(msg, err)
	}
	imagesItem.LastUseMilli = trainImages.LastUseAt.UnixMilli()

	trainStorage := service.TrainService.GetTrainStorage()
	imagesBasePath := path.Join(trainStorage, "images", imagesUuid)
	if arr, err := readDir(imagesBasePath); err != nil {
		msg = "获取图片列表出错"
		logger.Error(msg, err)
		return
	} else {
		aryUrl := make([]string, 0)
		for _, item := range arr {
			tmpUrl := service.TrainService.GetTrainImgDomain() + path.Join("views", imagesItem.Uuid, item.Name)
			aryUrl = append(aryUrl, tmpUrl)
		}
		if len(aryUrl) != imagesItem.Count {
			if err := trainImages.SetCount(len(aryUrl)); err != nil {
				logger.Error(err)
			}
			imagesItem.Count = trainImages.Count
		}
		imagesItem.ImageUrls = aryUrl
		result["images_item"] = imagesItem
	}
	msg = "图片集信息"
	code = 0
}

func (obj trainApi_) ImagesList(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainImagesListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var trainImages model.TrainImages
	var ary = make([]trainImagesItemResp, 0)

	queryParm := make(map[string]interface{})

	if oReq.ImagesUuid != "" {
		queryParm["images_uuid"] = oReq.ImagesUuid
	}

	if oReq.Status >= 0 {
		queryParm["status"] = oReq.Status
	}

	queryParm["user_id"] = claims.UserId

	if total, err := trainImages.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		empty := make([]string, 0)
		for i := 0; i < len(ary); i++ {
			ary[i].ImageUrls = empty
			ary[i].LastUseMilli = ary[i].LastUseAt.UnixMilli()
		}
		result["images"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj trainApi_) ImagesClear(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainImagesReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	imagesUuid := oReq.ImagesUuid
	var trainImages model.TrainImages
	if err := trainImages.GetByUuid(imagesUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}
	if trainImages.UserId != claims.UserId {
		msg = "无权限"
		return
	}
	//if trainImages.Status == 1 {
	//	msg = "该数据集正在打标中，不能删除"
	//	return
	//}

	if len(imagesUuid) < 30 {
		msg = "参数错误"
		return
	}
	trainStorage := service.TrainService.GetTrainStorage()
	imagesBasePath := path.Join(trainStorage, "images", imagesUuid)
	viewsBasePath := path.Join(trainStorage, "views", imagesUuid)

	if err := os.RemoveAll(viewsBasePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除缩略图片集失败"
			logger.Error(msg, err, viewsBasePath)
			return
		}
	}

	if err := os.RemoveAll(imagesBasePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除图片集失败"
			logger.Error(msg, err, imagesBasePath)
			return
		}
	}

	if err := trainImages.SetCount(0); err != nil {
		//msg = "图片集数据失败"
		logger.Error(msg, err)
		//return
	}

	msg = "清空图片集成功"
	code = 0
}

func (obj trainApi_) ImagesDel(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainImagesReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	imagesUuid := oReq.ImagesUuid
	var trainImages model.TrainImages
	if err := trainImages.GetByUuid(imagesUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}
	if trainImages.UserId != claims.UserId {
		msg = "无权限"
		return
	}
	//if trainImages.Status == 1 {
	//	msg = "该数据集正在打标中，不能删除"
	//	return
	//}

	if len(imagesUuid) < 30 {
		msg = "参数错误"
		return
	}
	trainStorage := service.TrainService.GetTrainStorage()
	imagesBasePath := path.Join(trainStorage, "images", imagesUuid)
	viewsBasePath := path.Join(trainStorage, "views", imagesUuid)

	if err := os.RemoveAll(viewsBasePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除缩略图片集失败"
			logger.Error(msg, err, viewsBasePath)
			return
		}
	}

	if err := os.RemoveAll(imagesBasePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除图片集失败"
			logger.Error(msg, err, imagesBasePath)
			return
		}
	}

	if err := trainImages.Delete(); err != nil {
		msg = "删除图片集数据失败"
		logger.Error(msg, err)
		return
	}

	msg = "删除成功"
	code = 0
}

func (obj trainApi_) ImagesCropTag(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	lockKey := ""
	defer func() {
		if lockKey != "" {
			common.RedisUnLock(lockKey)
		}
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainCropTagReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	imagesUuid := oReq.ImagesUuid
	if len(imagesUuid) < 32 {
		msg = "参数错误"
		return
	}
	if oReq.CropMethod != "center" && oReq.CropMethod != "focus" && oReq.CropMethod != "none" {
		msg = "裁剪方式不正确"
		return
	}
	if oReq.CropSize == "" || len(strings.Split(oReq.CropSize, "*")) != 2 {
		msg = "裁剪尺寸参数错误"
		return
	}

	if oReq.TriggerWord == "" {
		msg = "请输入触发词"
		return
	}

	lockKey = enums.RedisKeyEnum.LockKey + "ImagesCropTag_" + imagesUuid
	if common.RedisLock(lockKey, 1, 1000*10) {

	} else {
		lockKey = ""
		msg = "请勿频繁操作"
		return
	}

	taggerQueueName := enums.TrainQueueNameEnum.GetTaggerQueueName(oReq.TagAlg)
	if taggerQueueName == "" {
		msg = "打标算法不正确"
		return
	}

	var trainImages model.TrainImages
	if err := trainImages.GetByUuid(imagesUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}
	if trainImages.UserId != claims.UserId {
		msg = "无权限"
		return
	}
	if trainImages.Status == model.TrainImagesStatusTagging {
		msg = "已在排队打标中，请勿重复操作"
		return
	}

	tagsUuid := utils.GetUUID()
	tagParams := structs.CropTagParam{
		TagsUuid:     tagsUuid,
		ImagesUuid:   oReq.ImagesUuid,
		CropMethod:   oReq.CropMethod,
		CropSize:     oReq.CropSize,
		CropTraget:   "all",
		TagThreshold: oReq.TagThreshold,
		TagAlg:       oReq.TagAlg,
		TriggerWord:  oReq.TriggerWord,
	}
	task := redisqueue.NewTask(tagParams)
	trainTags := model.TrainTags{
		Uuid:         tagsUuid,
		UserId:       claims.UserId,
		ImagesUuid:   oReq.ImagesUuid,
		CropMethod:   oReq.CropMethod,
		CropSize:     oReq.CropSize,
		TagThreshold: oReq.TagThreshold,
		TagAlg:       oReq.TagAlg,
		TriggerWord:  oReq.TriggerWord,
		TagParams:    utils.GetJsonFromStruct(tagParams),
		Count:        trainImages.Count,
		QTaskId:      task.QTaskId,
		Status:       model.TrainTagsStatusReady,
	}
	if err := trainTags.Save(); err != nil {
		msg = "生成打标数据失败"
		logger.Error(msg, err)
		return
	}

	trainStorage := service.TrainService.GetTrainStorage()
	tagsBasePath := path.Join(trainStorage, "tags", tagsUuid)
	if _, err := os.Stat(tagsBasePath); err != nil { //在这里创建数据集文件夹，不要在打标时创建，如果打标时文件夹不存在，则认为该数据集已删除，可以终止该数据集的打标
		if os.IsNotExist(err) {
			if err := os.MkdirAll(tagsBasePath, os.ModePerm); err != nil {
				msg = "创建数据集文件夹出错"
				logger.Error(msg, err)
				return
			}
		}
	}
	if err := trainTags.SetStatus(model.TrainTagsStatusQueue); err != nil {
		msg = "设置状态失败"
		logger.Error(msg, err)
		return
	}
	if count, msg1, err := service.TrainService.PushTaggerTask(oReq.TagAlg, task); err != nil {
		if err1 := trainTags.SetStatus(model.TrainTagsStatusReady); err != nil {
			msg = "恢复准备状态失败"
			logger.Error(msg, err1)
		}
		msg = "加入打标队列失败"
		msg = msg1
		logger.Error(msg, err)
		return
	} else {
		msg = fmt.Sprintf("加入打标队列成功，当前排在第%d位", count)
		msg = msg1
		result["tags_uuid"] = trainTags.Uuid
		code = 0
		return
	}

}

func (obj trainApi_) TagsList(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainTagsListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var trainTags model.TrainTags
	var ary = make([]trainTagsItemResp, 0)

	queryParm := make(map[string]interface{})

	if oReq.ImagesUuid != "" {
		queryParm["images_uuid"] = oReq.ImagesUuid
	}
	if oReq.TagsUuid != "" {
		queryParm["tags_uuid"] = oReq.TagsUuid
	}

	if oReq.Status >= 0 {
		queryParm["status"] = oReq.Status
	}

	queryParm["user_id"] = claims.UserId

	if total, err := trainTags.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		//empty := make([]string, 0)
		for i := 0; i < len(ary); i++ {
			//ary[i].ImageUrls = empty
			//ary[i].LastUseMilli = ary[i].LastUseAt.UnixMilli()
			ary[i].StatusTxt = model.TrainTagsStatusName(ary[i].Status)

			var parm structs.CropTagParam
			if err := utils.GetStructFromJson(&parm, ary[i].TagParams); err == nil {
				ary[i].TagParamsObj = parm
			}
		}
		result["tags"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj trainApi_) TagsItem(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainTagsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.TagsUuid == "" {
		msg = "数据集参数错误"
		return
	}

	var trainTags model.TrainTags
	if err := trainTags.GetByUuid(oReq.TagsUuid); err != nil {
		if err == gorm.ErrRecordNotFound {
			msg = "该数据集信息不存在"
			return
		}
		msg = "获取数据集数据失败"
		logger.Error(msg, err)
		return
	}
	tagsUuid := trainTags.Uuid

	var tagsItem trainTagsItemResp
	if err := utils.Scan(trainTags, &tagsItem); err != nil {
		msg = "转换数据失败"
		logger.Error(msg, err)
	}
	tagsItem.LastUseMilli = trainTags.LastUseAt.UnixMilli()

	var parm structs.CropTagParam
	if err := utils.GetStructFromJson(&parm, tagsItem.TagParams); err == nil {
		tagsItem.TagParamsObj = parm
	}

	trainStorage := service.TrainService.GetTrainStorage()
	tagsBasePath := path.Join(trainStorage, "tags", tagsUuid)
	arrImg := make([]fileInfo, 0)
	if arr, err := readDir(tagsBasePath); err != nil {
		msg = "获取数据集列表出错"
		logger.Error(msg, err)
		return
	} else {
		for _, item := range arr {
			if utils.CheckSuffix(item.Name, service.TrainImageSuffix) {
				arrImg = append(arrImg, item)
			}
		}
	}

	aryUrl := make([]string, 0)
	for _, item := range arrImg {
		//tmpUrl := path.Join("tags", tagsItem.Uuid, item.Name)
		tmpUrl := service.TrainService.GetTrainImgDomain() + path.Join("tags", tagsItem.Uuid, item.Name)
		aryUrl = append(aryUrl, tmpUrl)
	}
	if len(aryUrl) != tagsItem.Count {
		if err := trainTags.SetCount(len(aryUrl)); err != nil {
			logger.Error(err)
		}
		tagsItem.Count = trainTags.Count
	}
	tagsItem.ImageUrls = aryUrl

	captions := make([]string, 0)
	for _, item := range arrImg {
		ext := path.Ext(item.Name)                // 输出 .html
		name := strings.TrimRight(item.Name, ext) // 输出 name
		txtName := name + ".txt"
		filePath := path.Join(service.TrainService.GetTrainStorage(), "tags", tagsItem.Uuid, txtName)
		if content, err := utils.ReadFile(filePath); err != nil {
			captions = append(captions, "err:", err.Error())
		} else {
			captions = append(captions, content)
		}
	}
	tagsItem.Captions = captions
	result["tags_item"] = tagsItem

	msg = ""
	code = 0
}

func (obj trainApi_) TagsProgess(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainTagsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var trainTags model.TrainTags
	if err := trainTags.GetByUuid(oReq.TagsUuid); err != nil {
		msg = "获取数据集信息失败"
		if err == gorm.ErrRecordNotFound {
			msg = "数据集信息不存在"
		}
		logger.Error(msg, err)
		return
	}
	result["tags_uuid"] = trainTags.Uuid
	result["q_task_id"] = trainTags.QTaskId
	result["status"] = trainTags.Status
	if trainTags.Status == model.TrainTagsStatusReady || trainTags.Status == model.TrainTagsStatusTagSuc || trainTags.Status == model.TrainTagsStatusTagFail {
		result["status_txt"] = model.TrainTagsStatusName(trainTags.Status)
		code = 0
	} else {
		if rankStaus, msg1, rank, progress, err := service.TrainService.TaggerRank(trainTags.TagAlg, trainTags.QTaskId); err != nil {
			msg = msg1
			logger.Error(msg, err)
			return
		} else {
			result["rank_status"] = rankStaus
			result["rank"] = rank
			result["progress"] = progress

			if rankStaus == redisqueue.RankStatusNoData {
				result["status_txt"] = "已超时"
			} else if trainTags.Status == model.TrainTagsStatusQueue || trainTags.Status == model.TrainTagsStatusTagging {
				result["status_txt"] = rankStaus
			} else {
				result["status_txt"] = model.TrainTagsStatusName(trainTags.Status)
			}

			msg = msg1
			code = 0
			return
		}
	}

}

func (obj trainApi_) TagCaption(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainTagsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.Caption == "" {
		msg = "提示词不能为空"
		return
	}

	tagsUuid := oReq.TagsUuid
	var trainTags model.TrainTags
	if err := trainTags.GetByUuid(tagsUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}
	if trainTags.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if trainTags.Status == model.TrainTagsStatusQueue {
		msg = "该数据集在打标队列中，不能修改"
		return
	}
	if trainTags.Status == model.TrainTagsStatusTagging {
		msg = "该数据集正在打标，不能修改"
		return
	}

	if b, err := trainTags.IsTrainning(oReq.TagsUuid); err != nil {
		msg = "查询训练情况失败"
		logger.Error(msg, err)
		return
	} else {
		if b {
			msg = "该数据集正在训练中，不能修改"
			return
		}
	}

	imageFileName := path.Base(oReq.ImageUrl) // 输出 name.html
	//ext := path.Ext(baseName)                      // 输出 .html

	trainStorage := service.TrainService.GetTrainStorage()
	tagsBasePath := path.Join(trainStorage, "tags", tagsUuid)

	ext := path.Ext(imageFileName)                // 输出 .html
	name := strings.TrimRight(imageFileName, ext) // 输出 name
	txtName := name + ".txt"
	captionFilePath := path.Join(tagsBasePath, txtName)
	if err := os.WriteFile(captionFilePath, []byte(oReq.Caption), 0644); err != nil { // 0644 是文件权限
		msg = "修改提示词失败"
		logger.Error(msg)
		return
	}
	msg = "修改成功"
	code = 0
}

func (obj trainApi_) TagDel(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainTagsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.ImageUrl == "" || !strings.Contains(oReq.ImageUrl, ".") {
		msg = "参数错误"
		return
	}

	//if !strings.Contains(oReq.ImageUrl, oReq.ImagesUuid) {
	//	msg = "参数不匹配"
	//	return
	//}
	tagsUuid := oReq.TagsUuid
	var trainTags model.TrainTags
	if err := trainTags.GetByUuid(tagsUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}
	if trainTags.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if trainTags.Status == model.TrainTagsStatusQueue {
		msg = "该数据集在打标队列中，不能删除"
		return
	}
	if trainTags.Status == model.TrainTagsStatusTagging {
		msg = "该数据集正在打标，不能删除"
		return
	}

	if b, err := trainTags.IsTrainning(oReq.TagsUuid); err != nil {
		msg = "查询训练情况失败"
		logger.Error(msg, err)
		return
	} else {
		if b {
			msg = "该数据集正在训练中，不能删除"
			return
		}
	}

	imageFileName := path.Base(oReq.ImageUrl) // 输出 name.html
	//ext := path.Ext(baseName)                      // 输出 .html

	trainStorage := service.TrainService.GetTrainStorage()
	tagsBasePath := path.Join(trainStorage, "tags", tagsUuid)

	tagFilePath := path.Join(tagsBasePath, imageFileName)

	if err := os.Remove(tagFilePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除数据集图失败"
			logger.Error(msg, err, tagFilePath)
			return
		}
	}

	ext := path.Ext(imageFileName)                // 输出 .html
	name := strings.TrimRight(imageFileName, ext) // 输出 name
	txtName := name + ".txt"
	captionFilePath := path.Join(service.TrainService.GetTrainStorage(), "tags", tagsUuid, txtName)

	if err := os.Remove(captionFilePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除打标信息失败"
			logger.Error(msg, err, captionFilePath)
			return
		}
	}
	msg = "删除成功"
	code = 0
}

func (obj trainApi_) TagsDel(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainTagsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	tagsUuid := oReq.TagsUuid
	var trainTags model.TrainTags
	if err := trainTags.GetByUuid(tagsUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}
	if trainTags.UserId != claims.UserId {
		msg = "无权限"
		return
	}
	//if trainImages.Status == 1 {
	//	msg = "该数据集正在打标中，不能删除"
	//	return
	//}
	taggerQueueName := enums.TrainQueueNameEnum.GetTaggerQueueName(trainTags.TagAlg)
	if taggerQueueName != "" {
		if msg1, err := service.TrainService.TaggerAbort(trainTags.TagAlg, trainTags.QTaskId); err != nil {
			msg = msg1
			logger.Error(msg, err)
			return
		}
	}

	if len(tagsUuid) < 30 {
		msg = "参数错误"
		return
	}

	trainStorage := service.TrainService.GetTrainStorage()
	tagsBasePath := path.Join(trainStorage, "tags", tagsUuid)

	if err := os.RemoveAll(tagsBasePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除数据集失败"
			logger.Error(msg, err, tagsBasePath)
			return
		}
	}

	if err := trainTags.Delete(); err != nil {
		msg = "删除数据集失败"
		logger.Error(msg, err)
		return
	}

	msg = "数据集删除成功"
	code = 0
}

func (obj trainApi_) TagsJob(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	lockKey := ""
	defer func() {
		if lockKey != "" {
			common.RedisUnLock(lockKey)
		}
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainTagsTrainReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	tagsUuid := oReq.TagsUuid
	if len(tagsUuid) < 32 {
		msg = "参数错误"
		return
	}
	lockKey = enums.RedisKeyEnum.LockKey + "TagsJob_" + tagsUuid
	if common.RedisLock(lockKey, 1, 1000*10) {

	} else {
		lockKey = ""
		msg = "请勿频繁操作"
		return
	}

	if oReq.LoraName == "" {
		msg = "请输入Lora名称"
		return
	}

	if oReq.TriggerWord == "" {
		msg = "请输入触发词"
		return
	}

	logger.Info(oReq.JobParam)

	if enums.TrainLevelEnum.GetTrainLevelTitle(oReq.JobsLevel) == "" {
		msg = "队列等级参数错误"
		return
	}

	trainQueueName := enums.TrainQueueNameEnum.GetJobQueueName(oReq.JobsLevel)
	if trainQueueName == "" {
		msg = "获取训练队列失败"
		return
	}

	var trainTags model.TrainTags
	if err := trainTags.GetByUuid(oReq.TagsUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}
	if trainTags.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	var trainModels model.TrainModels
	if err := trainModels.GetByName(oReq.ModelName); err != nil {
		msg = "获取训练模型失败"
		logger.Error(msg, err)
		return
	}

	//if trainTags.Status == model.TrainImagesStatusTagging {
	//	msg = "已在排队打标中，请勿重复操作"
	//	return
	//}

	jobsUuid := utils.GetUUID()
	trainJobParams := structs.TrainJobParam{
		JobsUuid:        jobsUuid,
		JobsLevel:       oReq.JobsLevel,
		TagsUuid:        tagsUuid,
		TriggerWord:     oReq.TriggerWord,
		LoraName:        oReq.LoraName,
		ModelName:       oReq.ModelName,
		ModelNameOrPath: trainModels.Path,
		//SaveSafetensorsEverySteps: oReq.SaveSafetensorsEverySteps,
		//Steps:                     oReq.Steps,
		//Lr:                        oReq.Lr,
		//Rank:                      oReq.Rank,
		//NumRepeats:                oReq.NumRepeats,
		//Sample1:         strings.ReplaceAll(oReq.Sample1, "[name]", oReq.TriggerWord),
		//SampleSteps:     oReq.SampleSteps,
		//BatchSize:       oReq.BatchSize,
		//ModelNameOrPath: trainModels.Path,
	}
	if err := trainJobParams.LoadParams(oReq.JobParam, trainTags.Count, oReq.TriggerWord); err != nil {
		msg = "训练参数错误"
		logger.Error(msg, err)
		return
	}

	task := redisqueue.NewTask(trainJobParams)
	trainJobs := model.TrainJobs{
		Uuid:        jobsUuid,
		UserId:      claims.UserId,
		ImagesUuid:  trainTags.ImagesUuid,
		TagsUuid:    tagsUuid,
		Level:       oReq.JobsLevel,
		TriggerWord: oReq.TriggerWord,
		LoraName:    oReq.LoraName,
		Count:       trainTags.Count,
		TrainParams: utils.GetJsonFromStruct(trainJobParams),
		QTaskId:     task.QTaskId,
		Status:      model.TrainJobsStatusQueue,
		PushAt:      time.Now(),
	}
	if err := trainJobs.Save(); err != nil {
		msg = "生成训练记录失败"
		logger.Error(msg, err)
		return
	}

	//trainStorage := service.TrainService.GetTrainStorage()
	//jobsBasePath := path.Join(trainStorage, "jobs", tagsUuid)
	//if _, err := os.Stat(jobsBasePath); err != nil { //在这里创建数据集文件夹，不要在打标时创建，如果打标时文件夹不存在，则认为该数据集已删除，可以终止该数据集的打标
	//	if os.IsNotExist(err) {
	//		if err := os.MkdirAll(jobsBasePath, os.ModePerm); err != nil {
	//			msg = "创建训练文件夹出错"
	//			logger.Error(msg, err)
	//			return
	//		}
	//	}
	//}

	if count, msg1, err := service.TrainService.PushJobTask(oReq.JobsLevel, task); err != nil {
		msg = "加入训练队列失败"
		msg = msg1
		logger.Error(msg, err)
		return
	} else {
		msg = fmt.Sprintf("加入训练队列成功，当前排在第%d位", count)
		msg = msg1
		result["jobs_uuid"] = jobsUuid
		code = 0
		return
	}

}

func (obj trainApi_) JobsPush(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	lockKey := ""
	defer func() {
		if lockKey != "" {
			common.RedisUnLock(lockKey)
		}
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainJobsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var trainJobs model.TrainJobs
	if err := trainJobs.GetByUuid(oReq.JobsUuid); err != nil {
		msg = "查询任务失败"
		logger.Error(msg, err)
		return
	}

	tagsUuid := trainJobs.TagsUuid
	if len(tagsUuid) < 32 {
		msg = "参数错误"
		return
	}
	lockKey = enums.RedisKeyEnum.LockKey + "TagsJob_" + tagsUuid
	if common.RedisLock(lockKey, 1, 1000*10) {

	} else {
		lockKey = ""
		msg = "请勿频繁操作"
		return
	}

	if trainJobs.LoraName == "" {
		msg = "请输入Lora名称"
		return
	}

	if trainJobs.TriggerWord == "" {
		msg = "请输入触发词"
		return
	}

	if enums.TrainLevelEnum.GetTrainLevelTitle(trainJobs.Level) == "" {
		msg = "队列等级参数错误"
		return
	}

	trainQueueName := enums.TrainQueueNameEnum.GetJobQueueName(trainJobs.Level)
	if trainQueueName == "" {
		msg = "获取训练队列失败"
		return
	}

	if trainJobs.Status != model.TrainJobsStatusReady {
		msg = "只有准备中的任务才能重新加入队列"
		return
	}

	task := redisqueue.NewTask(trainJobs.TrainParams)

	if count, msg1, err := service.TrainService.PushJobTask(trainJobs.Level, task); err != nil {
		msg = "加入训练队列失败"
		msg = msg1
		logger.Error(msg, err)
		return
	} else {
		msg = fmt.Sprintf("加入训练队列成功，当前排在第%d位", count)
		msg = msg1
		result["jobs_uuid"] = trainJobs.Uuid
		code = 0
		return
	}

}

func (obj trainApi_) JobsList(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainJobsListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var trainJobs model.TrainJobs
	var ary = make([]trainJobsItemResp, 0)

	queryParm := make(map[string]interface{})

	if oReq.JobsUuid != "" {
		queryParm["jobs_uuid"] = oReq.JobsUuid
	}
	if oReq.TagsUuid != "" {
		queryParm["tags_uuid"] = oReq.TagsUuid
	}

	if oReq.Status >= 0 {
		queryParm["status"] = oReq.Status
	}

	queryParm["user_id"] = claims.UserId

	if total, err := trainJobs.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		//empty := make([]string, 0)
		for i := 0; i < len(ary); i++ {
			//ary[i].ImageUrls = empty
			//ary[i].LastUseMilli = ary[i].LastUseAt.UnixMilli()
			ary[i].StatusTxt = model.TrainJobsStatusName(ary[i].Status)
			var parm structs.TrainJobParam
			if err := utils.GetStructFromJson(&parm, ary[i].TrainParams); err == nil {
				ary[i].TrainJobParam = parm
			}
		}
		result["jobs"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj trainApi_) JobsItem(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainJobsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.JobsUuid == "" {
		msg = "训练任务参数错误"
		return
	}

	var trainJobs model.TrainJobs
	if err := trainJobs.GetByUuid(oReq.JobsUuid); err != nil {
		if err == gorm.ErrRecordNotFound {
			msg = "该训练任务不存在"
			return
		}
		msg = "获取训练任务数据失败"
		logger.Error(msg, err)
		return
	}
	var jobsItem trainJobsItemResp
	if err := utils.Scan(trainJobs, &jobsItem); err != nil {
		msg = "转换数据失败"
		logger.Error(msg, err)
	}
	jobsItem.LastUseMilli = trainJobs.LastUseAt.UnixMilli()
	var parm structs.TrainJobParam
	if err := utils.GetStructFromJson(&parm, jobsItem.TrainParams); err == nil {
		jobsItem.TrainJobParam = parm
	}
	jobsItem.StatusTxt = model.TrainJobsStatusName(jobsItem.Status)

	trainStorage := service.TrainService.GetTrainStorage()
	jobsBasePath := path.Join(trainStorage, "jobs", jobsItem.Uuid)
	samplesPath := path.Join(jobsBasePath, jobsItem.LoraName, "samples")
	arrImg := make([]fileInfo, 0)

	if _, err := os.Stat(samplesPath); err != nil {

	} else {
		if arr, err := readDir(samplesPath); err != nil {
			msg = "获取样图目录出错"
			logger.Error(msg, err)
			return
		} else {
			for _, item := range arr {
				if utils.CheckSuffix(item.Name, service.TrainImageSuffix) {
					arrImg = append(arrImg, item)
				}
			}
		}
	}
	aryUrl := make([]string, 0)
	for _, item := range arrImg {
		//tmpUrl := path.Join("tags", tagsItem.Uuid, item.Name)
		tmpUrl := service.TrainService.GetTrainImgDomain() + path.Join("jobs", jobsItem.Uuid, jobsItem.LoraName, "samples", item.Name)
		aryUrl = append(aryUrl, tmpUrl)
	}
	jobsItem.ImageUrls = aryUrl

	result["jobs_item"] = jobsItem

	msg = ""
	code = 0
}

func (obj trainApi_) JobsProgess(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainJobsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var trainJobs model.TrainJobs
	if err := trainJobs.GetByUuid(oReq.JobsUuid); err != nil {
		msg = "获取训练任务失败"
		if err == gorm.ErrRecordNotFound {
			msg = "训练任务不存在"
		}
		logger.Error(msg, err)
		return
	}

	if rankStaus, msg1, rank, progress, err := service.TrainService.JobRank(trainJobs.Level, trainJobs.QTaskId); err != nil {
		//if err1 := trainJobs.SetStatus(model.TrainJobsStatusReady); err != nil {
		//	msg = "恢复准备状态失败"
		//	logger.Error(msg, err1)
		//}
		msg = msg1
		logger.Error(msg, err)
		return
	} else {
		if rankStaus == redisqueue.RankStatusRunning {
			if trainJobs.Status == model.TrainJobsStatusQueue {
				if err1 := trainJobs.SetStatus(model.TrainJobsStatusTraining); err != nil {
					msg = "修正训练状态为训练中失败"
					logger.Error(msg, err1)
				}
			}
		}

		/*
			progressFromFile := 0
			if rankStaus == redisqueue.RankStatusRunning && progress == 0 {
				trainStorage := service.TrainService.GetTrainStorage()
				jobsBasePath := path.Join(trainStorage, "jobs", trainJobs.Uuid)
				trainLogPath := path.Join(jobsBasePath, "train.log")
				//trainLogPath = "/Users/<USER>/fsdownload/train.log"
				if _, err := os.Stat(trainLogPath); err != nil {
					if os.IsNotExist(err) {
						msg = "日志文件不存在"
					}
					msg = "获取训练日志失败"
					logger.Error(msg, err)
				} else {
					if content, err := utils.ReadFile(trainLogPath); err != nil {
						msg = "读取日志文件失败"
						logger.Error(msg, err)
					} else {
						re := regexp.MustCompile(`(\d+)%\|`)
						// 查找所有匹配
						matches := re.FindAllStringSubmatch(content, -1)
						if len(matches) > 0 {
							last := matches[len(matches)-1]
							progressFromFile = utils.String2Int(last[1])
							msg1 = fmt.Sprintf("正在执行任务，当前进度%d%%", progressFromFile)
						}
					}
				}
			}*/

		result["jobs_uuid"] = trainJobs.Uuid
		result["rank_status"] = rankStaus
		result["status"] = trainJobs.Status
		result["status_txt"] = model.TrainJobsStatusName(trainJobs.Status)
		result["q_task_id"] = trainJobs.QTaskId
		result["rank"] = rank
		result["progress"] = progress
		//if progressFromFile > 0 {
		//	result["progress"] = progressFromFile
		//}
		msg = model.TrainJobsStatusName(trainJobs.Status)
		if rankStaus == redisqueue.RankStatusWaiting || rankStaus == redisqueue.RankStatusRunning {
			msg = msg1
		}

		code = 0
		return
	}

}

func (obj trainApi_) JobsTrainLog(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainJobsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var trainJobs model.TrainJobs
	if err := trainJobs.GetByUuid(oReq.JobsUuid); err != nil {
		msg = "获取训练任务失败"
		if err == gorm.ErrRecordNotFound {
			msg = "训练任务不存在"
		}
		logger.Error(msg, err)
		return
	}

	trainStorage := service.TrainService.GetTrainStorage()
	jobsBasePath := path.Join(trainStorage, "jobs", trainJobs.Uuid)
	trainLogPath := path.Join(jobsBasePath, "train.log")
	if _, err := os.Stat(trainLogPath); err != nil {
		if os.IsNotExist(err) {
			msg = "日志文件不存在"
			return
		}
		msg = "获取训练日志失败"
		logger.Error(msg, err)
		return
	}

	if content, err := utils.ReadFile(trainLogPath); err != nil {
		msg = "读取日志文件失败"
		logger.Error(msg, err)
	} else {
		result["jobs_uuid"] = oReq.JobsUuid
		result["train_log"] = content
		code = 0
	}

}

func (obj trainApi_) JobsAbort(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainJobsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	//var trainJobs model.TrainJobs
	//if err := trainJobs.GetByUuid(oReq.JobsUuid); err != nil {
	//	msg = "获取训练任务失败"
	//	if err == gorm.ErrRecordNotFound {
	//		msg = "训练任务不存在"
	//	}
	//	logger.Error(msg, err)
	//	return
	//}

	if msg1, err := service.TrainService.JobAbort(oReq.JobsUuid); err != nil {
		msg = msg1
		logger.Error(msg, err)
		return
	} else {
		result["jobs_uuid"] = oReq.JobsUuid
		msg = msg1
		code = 0
		return
	}

}

func (obj trainApi_) JobsTerminate(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainJobsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	//var trainJobs model.TrainJobs
	//if err := trainJobs.GetByUuid(oReq.JobsUuid); err != nil {
	//	msg = "获取训练任务失败"
	//	if err == gorm.ErrRecordNotFound {
	//		msg = "训练任务不存在"
	//	}
	//	logger.Error(msg, err)
	//	return
	//}

	if msg1, err := service.TrainService.JobTerminate(oReq.JobsUuid); err != nil {
		msg = msg1
		logger.Error(msg, err)
		return
	} else {
		result["jobs_uuid"] = oReq.JobsUuid
		msg = msg1
		code = 0
		return
	}

}

func (obj trainApi_) JobsDel(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainJobsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	jobsUuid := oReq.JobsUuid
	var trainJobs model.TrainJobs
	if err := trainJobs.GetByUuid(jobsUuid); err != nil {
		msg = "获取训练任务信息失败"
		logger.Error(msg, err)
		return
	}
	if trainJobs.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if trainJobs.Status == model.TrainJobsStatusQueue {
		msg = "该任务在队列中，不能删除"
		return
	}
	if trainJobs.Status == model.TrainJobsStatusTraining {
		msg = "该任务正在训练中，不能删除"
		return
	}

	trainStorage := service.TrainService.GetTrainStorage()
	jobsBasePath := path.Join(trainStorage, "jobs", jobsUuid)

	if err := os.RemoveAll(jobsBasePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除任务数据失败"
			logger.Error(msg, err, jobsBasePath)
			return
		}
	}

	if err := trainJobs.Delete(); err != nil {
		msg = "删除训练任务数据集失败"
		logger.Error(msg, err)
		return
	}

	msg = "训练任务数据删除成功"
	code = 0
}
