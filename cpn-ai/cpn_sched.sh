#!/bin/bash

# 定义进程名和目标目录
PROCESS_NAME="cpn_sched.linux"
TARGET_DIR="/root/cpn-sched"

# 查找目标目录下的进程并结束它
PID=$(ps -ef | grep "$PROCESS_NAME" | grep -v grep | awk '{print $2}')

for pid in $PID; do
    # 获取进程的工作目录
    CWD=$(readlink -f /proc/$pid/cwd)

    # 判断进程的工作目录是否为目标目录
    if [ "$CWD" == "$TARGET_DIR" ]; then
        echo "Found process $PROCESS_NAME with PID $pid in $TARGET_DIR. Killing the process..."
        kill -9 "$pid"
    else
        echo "Process $pid is not running in $TARGET_DIR (running in $CWD). Skipping..."
    fi
done

# 修改文件权限
FILE_PATH="$TARGET_DIR/$PROCESS_NAME"

if [ -f "$FILE_PATH" ]; then
    echo "Changing file permissions for $FILE_PATH..."
    chmod 777 "$FILE_PATH"
else
    echo "File $FILE_PATH does not exist."
    exit 1
fi

cd /root/cpn-sched && nohup ./cpn_sched.linux &

echo "Operation completed successfully."