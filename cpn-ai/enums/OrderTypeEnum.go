package enums

type orderTypeEnum_ struct {
	UserGift, ManagerAdd, RechargeBuy, Cost, ManagerCost, Subscribe, RedeemCode,
	CostLLM, CostINST, CostPod, ImageStore, CloudStore, Refund,
	VerifyWithdrawAccount, Withdraw, Reward, RewardPodUsage int
}

var OrderTypeEnum = orderTypeEnum_{
	UserGift:    1,  //用户赠送
	ManagerAdd:  2,  //管理员添加
	RechargeBuy: 3,  //充值购买
	Cost:        4,  //消费
	ManagerCost: 5,  //手动扣减
	Subscribe:   6,  //订阅包年增加
	RedeemCode:  7,  //使用兑换码增加
	CostLLM:     8,  //大模型消费
	CostINST:    9,  //实例算力消费
	Refund:      10, //返还
	CostPod:     11, //pod镜像使用费
	ImageStore:  12, //私人镜像存储
	CloudStore:  13, //私人云存储

	//User 表RewardAmount
	VerifyWithdrawAccount: 14, //验证提现账户
	Withdraw:              15, //提现reward
	Reward:                16, //佣金奖励
	RewardPodUsage:        17, //Pod镜像使用佣金
}

func OrderTypenEnumName(v int) string {
	if val, ok := OrderTypeMap[v]; ok {
		return val
	}
	return ""
}

var OrderTypeMap = map[int]string{
	1:  "用户赠送",
	2:  "管理员添加",
	3:  "充值购买",
	4:  "消费",
	5:  "手动扣减",
	6:  "订阅包年增加",
	7:  "兑换码",
	8:  "语言大模型",
	9:  "容器实例",
	10: "返还",
	11: "Pod镜像",
	12: "镜像存储",
	13: "云存储",
	14: "验证账户",
	15: "提现",
	16: "佣金",
	17: "Pod使用佣金",
}
