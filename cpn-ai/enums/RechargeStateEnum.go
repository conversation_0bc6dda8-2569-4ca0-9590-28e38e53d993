package enums

type rechargeStateEnum_ struct { // 0新创建 1已付款 2已退款 3交易不存在 4交易已关闭 9已取消
	Normal, TRADE_SUCCESS, TRADE_NOT_EXIST, TRADE_CLOSED int
}

var RechargeStateEnum = rechargeStateEnum_{
	Normal:          0,
	TRADE_SUCCESS:   1,
	TRADE_NOT_EXIST: 3,
	TRADE_CLOSED:    4,
}

func (c rechargeStateEnum_) GetShowTitle(value int) string {
	if value == 0 {
		return "未支付"
	}
	if value == 1 {
		return "支付成功"
	}
	if value == 2 {
		return "已退款"
	}
	if value == 3 {
		return "交易不存在"
	}
	if value == 4 {
		return "交易已关闭"
	}
	return "等待支付结果"
}
