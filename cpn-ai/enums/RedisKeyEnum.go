package enums

import "reflect"

type redisKeyEnum_ struct {
	AlarmKey,
	LockKey,
	CpnSchedIpLocation,
	CpnSchedControlVirtual,
	CpnSchedKolOutImage,
	CpnSchedPod, CpnSchedRunningPod, CpnSchedVirtualGpuFrees, CpnSchedDocker, CpnSchedRunningLLM, CpnSchedChargingLLM,
	CpnSchedLlmConversation,
	CpnSchedStartupLog,
	CpnSchedTaskLog,
	CpnSchedLockedGpus,
	KOLStartNode,
	AutoSerialNumber, AccessToken, SmsReg, SmsLogin, SmsModifyPassword, InsiderUser, InsiderUsers string
	RegularService string

	ScanLogin string
}

func (c redisKeyEnum_) Get(id string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if typeVo.Field(i).Name == id {
			return vo.Field(i).Interface().(string)
		}
	}
	return ""
}

var RedisKeyEnum = redisKeyEnum_{
	AlarmKey:               "cpn-sched:alarm:", //报警延迟前缀
	LockKey:                "cpn-sched:hash:",  //lockkey前缀
	CpnSchedIpLocation:     "cpn-sched:list:ip_location",
	CpnSchedControlVirtual: "cpn-sched:hash:control_virtual", //所有控制的虚拟主机
	CpnSchedKolOutImage:    "cpn-sched:key:kol_out_image:",   //:后面加userId 这个设置保持3天

	CpnSchedPod:             "cpn-sched:hash:pod",               //所有可启动的 pod列表
	CpnSchedVirtualGpuFrees: "cpn-sched:zhash:virtual_gpufrees", //单个虚拟机空闲的cpu数量  key,virtualNode.HostPort,空闲数量
	CpnSchedDocker:          "cpn-sched:hash:docker",            //所有启动的docker field是dockerId value是DockerItem的json
	CpnSchedRunningPod:      "cpn-sched:zhash:running_pod:",     //key后面加podId, filed是[host:dockerIp,host:dockerIp,host:dockerIp]，score是当前请求数量
	CpnSchedRunningLLM:      "cpn-sched:zhash:running_llm:",     //当前大语言模型调用数量key后面加podId  score是当前请求数量
	CpnSchedChargingLLM:     "cpn-sched:list:charging_llm:",     //扣费记录 cpn-sched:zhash:charging_llm:202312191305 //5分钟一个段 从00:00开始202312190000 202312190005 202312190010 202312190015 202312190020
	CpnSchedLlmConversation: "cpn-sched:zhash:llm:conversation", //当前大语言模型用户聊天记录
	CpnSchedStartupLog:      "cpn-sched:list:startuplog:",       //后面加StartupMark docker 启动日志
	CpnSchedTaskLog:         "cpn-sched:list:tasklog:",          //后面加任务枚举和任务唯一码 任务日志
	CpnSchedLockedGpus:      "cpn-sched:hash:locked_gpus",       //启动docker时临时锁定的Gpu

	KOLStartNode: "cpn-sched:hash:kol_start_node",

	AutoSerialNumber:  "suanyun-ai:auto_serial_number",
	AccessToken:       "suanyun-ai:user:access_token:",
	SmsReg:            "suanyun-ai:sms:reg:",
	SmsLogin:          "suanyun-ai:sms:login:",
	SmsModifyPassword: "suanyun-ai:sms:modify_password:",
	RegularService:    "suanyun-ai:hash:regular", //定时任务

	ScanLogin: "center-ai:scan_login:",
}
