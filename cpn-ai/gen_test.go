package main

import (
	"fmt"
	"io/ioutil"
	"path"
	"regexp"
	"runtime"
	"strings"
	"testing"
)

// 测试函数
func TestGen(t *testing.T) {
	// 测试用例
	GenResponseModel("Pod")
}

func GenResponseModel(modelName string) {

	_, thisFilePath, _, ok := runtime.Caller(0)
	if !ok {
		fmt.Println("Failed to get caller information")
		return
	}
	modelDirectory := strings.Replace(thisFilePath, "test/shell.go", "", -1)
	fmt.Println("Source code file path:", thisFilePath)
	modelPath := path.Join(modelDirectory, "model/"+modelName+".go")
	fmt.Println(modelPath)

	// 读取文本文件内容
	content, err := ioutil.ReadFile(modelPath)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}
	input := string(content)
	index := strings.Index(input, fmt.Sprintf("type %s struct {", modelName))
	endIndex := strings.Index(input, fmt.Sprintf("func (%s) TableName() string {", modelName))

	input = input[index:endIndex]

	re := regexp.MustCompile("gorm:([^`]+)")

	matches := re.FindAllStringSubmatch(input, -1)
	ary := make([]string, 0)
	for _, match := range matches {
		if len(match) >= 2 {
			//fmt.Println(match[0])
			input = strings.Replace(input, " "+match[0], "", -1)
			input = strings.Replace(input, match[0], "", -1)
			ary = append(ary, match[0])
		}
	}

	input = strings.Replace(input, "time.Time", "jsontime.JsonTime", -1)

	gormModel := "\tID        uint              `json:\"id\"`\n\tCreatedAt jsontime.JsonTime `json:\"created_at\"`\n\tUpdatedAt jsontime.JsonTime `json:\"updated_at\"`"
	input = strings.Replace(input, "gorm.Model", gormModel, -1)
	fmt.Println("====================================================")
	fmt.Println("")
	fmt.Println(input)
	fmt.Println("")
	fmt.Println("====================================================")
	return
}
