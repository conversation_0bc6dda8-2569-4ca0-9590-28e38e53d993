#!/bin/bash

APP_DIR="/root/cpn-sched"
APP_NAME="cpn_sched.linux"
LOCAL_FILE="./$APP_NAME"
BACKUP_FILE="$APP_DIR/$APP_NAME.bak$(date +%Y%m%d%H%M%S)"

echo "==== 自动部署开始 ===="
echo "当前目录: $(pwd)"
echo "目标目录: $APP_DIR"
echo "程序名称: $APP_NAME"

# 1. 备份旧版本
if [ -f "$APP_DIR/$APP_NAME" ]; then
    echo "备份旧版本到 $BACKUP_FILE"
    cp "$APP_DIR/$APP_NAME" "$BACKUP_FILE"
else
    echo "警告：未找到旧版本，跳过备份"
fi

# 2. 删除旧版本
echo "删除旧版本 $APP_NAME"
rm -f "$APP_DIR/$APP_NAME"

# 3. 拷贝新版本
if [ ! -f "$LOCAL_FILE" ]; then
    echo "错误：当前目录下未找到新版本文件 $LOCAL_FILE"
    exit 1
fi

echo "拷贝新版本到 $APP_DIR"
cp "$LOCAL_FILE" "$APP_DIR/"

# 4. 杀死旧进程
echo "杀死旧进程"
pkill -f "$APP_NAME"

# 等待1秒
sleep 1

# 5. 启动新版本
cd "$APP_DIR" || { echo "错误：无法进入目录 $APP_DIR"; exit 1; }
chmod +x "$APP_NAME"

echo "使用 nohup 启动新版本..."
nohup "./$APP_NAME" > "$APP_DIR/nohup.out" 2>&1 &

echo "部署完成，进程已启动 PID: $!"
