#!/bin/bash

# 设置路径和文件名
APP_DIR=/root/cpn-sched
APP_NAME=cpn_sched.linux
LOCAL_FILE="./$APP_NAME"  # 本地当前目录的文件路径

echo "APP_DIR: $APP_DIR"
echo "APP_NAME: $APP_NAME"
echo "当前执行目录: $(pwd)"

# 第1步：备份老数据
if [ -f "$APP_DIR/$APP_NAME" ]; then
    BACKUP_FILE="$APP_DIR/$APP_NAME.bak$(date +%Y%m%d%H%M%S)"
    echo "第1步：备份老版本为 $BACKUP_FILE"
    cp "$APP_DIR/$APP_NAME" "$BACKUP_FILE"
else
    echo "警告：$APP_NAME 不存在，无法备份"
fi

# 第2步：删除老版本
echo "第2步：删除旧版本 $APP_NAME"
rm -f "$APP_DIR/$APP_NAME"

# 等待3秒
sleep 3

# 第3步：拷贝新版本
if [ ! -f "$LOCAL_FILE" ]; then
    echo "错误：本地不存在新版本文件 $LOCAL_FILE，无法拷贝"
    exit 1
fi

echo "第3步：拷贝新版本到 $APP_DIR"
cp "$LOCAL_FILE" "$APP_DIR/"

# 切换目录并设置可执行权限
cd "$APP_DIR" || { echo "目录不存在: $APP_DIR"; exit 1; }
chmod +x "$APP_NAME"
echo "完成：$APP_NAME 已部署并授权执行"
