#!/bin/bash
APP_DIR=/www/wwwroot/hz08.suanyun.cn/api
APP_NAME=cpn_node.linux

# 打印调试信息
echo "APP_DIR: $APP_DIR"
echo "APP_NAME: $APP_NAME"
echo "当前执行目录: $(pwd)"

# 确认文件是否存在
if [ -f $APP_DIR/$APP_NAME ]; then
    echo "第1步：备份老数据"
    cp $APP_DIR/$APP_NAME $APP_DIR/$APP_NAME.bak$(date +%Y%m%d%H%M%S)
else
    echo "警告：$APP_NAME 不存在，无法备份"
fi

echo "第2步：删除$APP_NAME"
rm -f $APP_DIR/$APP_NAME

sleep 3

echo "第3步：拷贝新版本"
cp $APP_NAME $APP_DIR/
cd $APP_DIR || { echo "目录不存在: $APP_DIR"; exit 1; }
chmod +x $APP_NAME

#echo "第4步：停止正在运行的进程"
#screen -S $APP_NAME -X quit
#echo "第4步完成：$APP_NAME 已停止"

#echo "第4步：使用screen启动实例$APP_NAME"
#screen -S $APP_NAME -d -m ./$APP_NAME
#echo "第4步完成：新版本已启动"




#!/bin/bash

# 项目名称
PROJECT_NAME=cpn_node_hz08
url="http://192.168.200.13:17491/project/go/restart_project"
bt_sign="NQpWxe5l3pY3Az83c5cmzSeVUqDN9qCC"

# 获取当前时间戳，单位为毫秒
timestamp=$(date +%s%3N)

# 计算MD5加密签名
md5_sign=$(echo -n "$bt_sign" | md5sum | awk '{print $1}')

# 计算请求签名
request_sign=$(echo -n "$timestamp$md5_sign" | md5sum | awk '{print $1}')

# 构造请求参数（硬编码的 data 参数）
encoded_param="%7B%22project_name%22%3A%22$PROJECT_NAME%22%7D"
request_params="request_time=$timestamp&request_token=$request_sign&data=$encoded_param"

# 发送POST请求
echo "开始重启项目：$PROJECT_NAME, URL: $url PARM: $request_params"
response=$(curl -s -X POST -d "$request_params" -H "Content-Type: application/x-www-form-urlencoded" "$url")

# 输出响应数据
echo "重启响应数据：$response"

