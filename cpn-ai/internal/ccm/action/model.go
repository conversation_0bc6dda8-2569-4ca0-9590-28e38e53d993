package action

import (
	"cpn-ai/internal/ccm"
	"time"

	"gorm.io/gorm"
)

type ActionStatus int

const (
	NoStatus ActionStatus = iota
	Pending
	Running
	Succeeded
	Failed
	AllStatus = NoStatus
)

func (as ActionStatus) IsFinished() bool {
	return as == Succeeded || as == Failed
}
func (as ActionStatus) IsUnfinished() bool {
	return as == Pending || as == Running
}
func (as ActionStatus) String() string {
	switch as {
	case Pending:
		return "pending"
	case Running:
		return "running"
	case Succeeded:
		return "succeeded"
	case Failed:
		return "failed"
	}
	return "unknown"
}

type Action struct {
	ccm.Model
	NodeID    int          `json:"node_id,omitempty" gorm:"column:node_id;type:INTEGER;not null;default:0"`
	VirtualID int          `json:"virtual_id,omitempty" gorm:"column:virtual_id;type:INTEGER;not null;default:0"`
	Name      string       `json:"name,omitempty" gorm:"column:name;type:VARCHAR(255);not null;default:'';index:idx_target"`
	Key       string       `json:"key,omitempty" gorm:"column:key;type:VARCHAR(255);not null;default:'';index:idx_target"`
	Meta      ccm.M        `json:"meta,omitempty" gorm:"column:meta;type:JSON;not null;serializer:json"`
	Status    ActionStatus `json:"status,omitempty" gorm:"column:status;type:TINYINT;not null;default:0"`
	Message   string       `json:"message,omitempty" gorm:"column:message;type:TEXT;not null"`
	StartTime *time.Time   `json:"start_time,omitempty" gorm:"column:start_time;type:TIMESTAMP"`
	EndTime   *time.Time   `json:"end_time,omitempty" gorm:"column:end_time;type:TIMESTAMP"`
}

var ActionModel *Action

func (*Action) TableName() string {
	return ccm.TablePrefix + "_action"
}
func (row *Action) Ended() bool {
	switch row.Status {
	case Succeeded, Failed:
		return true
	default:
		return false
	}
}

func listByTarget(name, key string) *gorm.DB {
	if name == "" || key == "" {
		return nil
	}
	return ccm.DB.Model(ActionModel).Where(ccm.Eq("name", name)).Where(ccm.Eq("key", key)).Order(ccm.Desc("id"))
}
func ListByTarget(name, key string) []*Action {
	return ccm.FindRows[Action](listByTarget(name, key))
}

func ListByIDs(ids []int) map[int]*Action {
	if len(ids) == 0 {
		return nil
	}
	rows := ccm.FindRows[Action](ccm.DB.Model(ActionModel).Where(ccm.IN("id", ids)).Order(ccm.Desc("id")))
	return ccm.Slice2Map(rows, func(row *Action) int { return row.ID })
}

func ListUnfinished(name string) []*Action {
	if name == "" {
		return nil
	}
	return ccm.FindRows[Action](ccm.DB.Model(ActionModel).
		Where("name = ?", name).
		Where(ccm.IN("status", []ActionStatus{Pending, Running})).
		Order("id"))
}

func GetByID(id int) *Action {
	if id <= 0 {
		return nil
	}
	return ccm.GetRow[Action]("id", id)
}
