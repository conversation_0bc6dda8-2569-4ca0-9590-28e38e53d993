package dockerfile

import (
	"bytes"
	"cpn-ai/internal/ccm"
	_ "embed"
	"text/template"
)

//go:embed start.sh
var startScript string

//go:embed Dockerfile.tpl
var tpl string

func (row *Dockerfile) FileBuffer() (buf bytes.Buffer) {
	t, err := template.New("Dockerfile.tpl").Parse(tpl)
	ccm.MustNoError(err)

	f := func(name string) Version {
		v, ok := row.Components[name]
		ccm.Must(ok && v != "")
		return v
	}

	m := ccm.M{
		"repository":      Repository,
		"cudaVersion":     f("CUDA").MustNew(),
		"minicondaScript": "https://repo.anaconda.com/miniconda/Miniconda3-" + python2conda[f("Python")] + "-Linux-x86_64.sh",
	}
	if v, ok := row.Components["PyTorch"]; ok {
		m["torchVersion"] = v.MustNew()
	}
	if v, ok := row.Components["TensorFlow"]; ok {
		m["tensorflowVersion"] = v.MustNew()
	}
	if v, ok := row.Components["PaddlePaddle"]; ok {
		m["paddlepaddleVersion"] = v.MustNew()
	}

	ccm.MustNoError(t.Execute(&buf, m))
	return
}
