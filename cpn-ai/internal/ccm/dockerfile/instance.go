package dockerfile

import (
	"cpn-ai/internal/ccm"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

type StartInstanceRequest struct {
	PodUuid   string `json:"pod_uuid"` //ccm
	ImageUuid string `json:"image_uuid"`
	ImageTag  string `json:"image_tag"`

	GpuModelUuid string `json:"gpu_model_uuid"`
	Gpus         int    `json:"gpus"`

	ChargingType int             `json:"charging_type"`
	ChargingNum  int             `json:"charging_num"`
	Amount       decimal.Decimal `json:"amount"`

	// InstanceUuid string `json:"instance_uuid"`
	NodeId    int  `json:"node_id"`
	VirtualId int  `json:"virtual_id"`
	NoCard    bool `json:"no_card"`
}

func StartInstance(cx *gin.Context) {
	sir := new(StartInstanceRequest)

	if err := ccm.DecodeJSON(cx, sir); err != nil {
		ccm.WriteError(cx, err)
		return
	}

	podUuid := sir.PodUuid
	if podUuid == "ccm" {
		podUuid = "xxx"
	}

	//TODO

	ccm.WriteData(cx, podUuid)
}
