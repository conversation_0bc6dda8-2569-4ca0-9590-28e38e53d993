package dockerfile

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"

	"cpn-ai/internal/ccm"
	"cpn-ai/internal/ccm/json"

	semver "github.com/Masterminds/semver/v3"
	"gorm.io/gorm"
)

const (
	//Repository = "192.168.200.5/chenyu/public/base-env"
	Repository = "10.20.103.240/chenyu/public/base-env"
	TagPrefix  = "ccm"
	MaxBuild   = 20
)

var (
	DockerfileModel *Dockerfile
	BasicKeys       = json.NewKeys(DockerfileModel, "name", "intro", "components", "key", "tag", "image")
)

type Image struct {
	ID      string `json:"id,omitempty"`
	Size    int64  `json:"size,omitempty"`
	History int    `json:"history,omitempty"`
	Layers  int    `json:"layers,omitempty"`
	Created string `json:"created,omitempty"` //time.RFC3339Nano
}

type Dockerfile struct {
	ccm.Model
	Name         string             `json:"name,omitempty" gorm:"column:name;type:VARCHAR(255);not null;default:''"`
	Intro        string             `json:"intro,omitempty" gorm:"column:intro;type:TEXT;not null"`
	Components   map[string]Version `json:"components,omitempty" gorm:"column:components;type:JSON;not null;serializer:json"`
	Image        Image              `json:"image,omitempty" gorm:"column:image;type:JSON;not null;serializer:json"`
	Key          string             `json:"key,omitempty" gorm:"column:key;type:VARCHAR(255);not null;default:'';uniqueIndex"`
	Tag          string             `json:"tag,omitempty" gorm:"column:tag;type:VARCHAR(255);not null;default:'';index"`
	BuildSeconds int                `json:"build_seconds,omitempty" gorm:"column:build_seconds;type:INTEGER;not null;default:0"`
	BuildCount   int                `json:"build_count,omitempty" gorm:"column:build_count;type:INTEGER;not null;default:0"`
	LastActionID int                `json:"last_action_id,omitempty" gorm:"column:last_action_id;type:INTEGER;not null;default:0"`
	//TODO：使用统计
}

func GenTag(dockerfileID, actionID int) string {
	ccm.Must(dockerfileID > 0 && actionID > 0)
	return fmt.Sprintf("%s-%d-%d", TagPrefix, dockerfileID, actionID)
}

func ImageName(tag string) string {
	if tag == "" {
		return ""
	}
	return Repository + ":" + tag
}

func (*Dockerfile) TableName() string {
	return ccm.TablePrefix + "_dockerfile"
}

func (row *Dockerfile) Save() {
	ccm.MustNoError(ccm.DB.Save(row).Error)
}

func GetByID(id int) *Dockerfile {
	if id <= 0 {
		return nil
	}
	return ccm.GetRow[Dockerfile]("id", id)
}
func GetByKey(key string) *Dockerfile {
	if key == "" {
		return nil
	}
	return ccm.GetRow[Dockerfile]("key", key)
}

func ListAll(components map[string]Version) *gorm.DB {
	db := ccm.DB.Model(DockerfileModel).Order(ccm.Desc("id"))
	for k, v := range components {
		db = db.Where("components->>'$."+k+"' = ?", string(v))
	}
	return db
}
func ListByTag(components map[string]Version) *gorm.DB {
	return ListAll(components).Where(ccm.Neq("tag", ""))
}

func (row *Dockerfile) Hash() {
	h := sha256.New()

	// for _, name := range slices.Sorted(maps.Keys(row.Components)) {
	// 	_, err := h.Write([]byte(name + "\t" + string(row.Components[name]) + "\n"))
	// 	ccm.MustNoError(err)
	// }

	buf := row.FileBuffer()
	h.Write(buf.Bytes())

	row.Key = hex.EncodeToString(h.Sum(nil))
}

func componentNames() (a []string) {
	for _, c := range Components {
		if len(c.Children) > 0 {
			for _, child := range c.Children {
				a = append(a, child.Name)
			}
		} else {
			a = append(a, c.Name)
		}
	}
	return
}

func (row *Dockerfile) Validate() error {
	if len(row.Components) == 0 {
		return fmt.Errorf("no components")
	}

	check := func(c Component, v Version) error {
		ccm.Must(len(c.Versions) > 0)
		for _, cv := range c.Versions {
			if cv.Version == v {
				if len(cv.Dependencies) > 0 {
					for dn, dc := range cv.Dependencies {
						dv, ok := row.Components[dn]
						if !ok || !dc.Check(dv) {
							return fmt.Errorf("component %s %s depend on %s: %s", c.Name, v, dn, dc)
						}
					}
				}
				return nil
			}
		}
		return fmt.Errorf("component %s version not exist: %s", c.Name, v)
	}

	m := make(map[string]struct{})
	has := func(name string) (Version, bool) {
		m[name] = struct{}{}
		v, ok := row.Components[name]
		return v, ok
	}

	for _, c := range Components {
		if len(c.Children) > 0 {
			first := -1
			for i, child := range c.Children {
				ccm.Must(child.Children == nil && !child.Required) //暂只支持二级
				if v, ok := has(child.Name); ok {
					if err := check(child, v); err != nil {
						return err
					}
					if first == -1 {
						first = i
					} else {
						return fmt.Errorf("component %s duplicated: %s, %s", c.Name, c.Children[first].Name, child.Name)
					}
				}
			}
			if c.Required && first == -1 {
				return fmt.Errorf("component %s required", c.Name)
			}
		} else {
			if v, ok := has(c.Name); ok {
				if err := check(c, v); err != nil {
					return err
				}
			} else if c.Required {
				return fmt.Errorf("component %s required", c.Name)
			}
		}
	}

	for name := range row.Components {
		if _, ok := m[name]; !ok {
			return fmt.Errorf("unknown component: %s", name)
		}
	}

	return nil
}

type Version string

func trim0(s string) (string, bool) {
	if len(s) > 0 {
		i := 0
		for j := 0; j < len(s); j++ {
			if s[j] == '0' {
				if i == j {
					i++
				}
			} else if s[j] == '-' || s[j] == '+' {
				if i > 0 && i == j {
					i--
				}
				return s[i:], false
			} else if !('1' <= s[j] && s[j] <= '9') {
				return s, false
			}
		}
		if i == len(s) {
			return "0", true
		}
		return s[i:], true
	}
	return s, false
}

func (s Version) trim0() string {
	a, ok := strings.Split(string(s), "."), true
	for i := 0; ok && i < len(a); i++ {
		a[i], ok = trim0(a[i])
	}
	return strings.Join(a, ".")
}

func (s Version) New() *semver.Version {
	v, err := semver.NewVersion(s.trim0())
	if err != nil {
		return nil
	}
	return v
}

func (s Version) MustNew() *semver.Version {
	v, err := semver.NewVersion(s.trim0())
	ccm.MustNoError(err)
	return v
}

type Constraint string

func (s Constraint) Check(v Version) bool {
	w, err := semver.NewVersion(string(v))
	return err == nil && s.MustNew().Check(w)
}

func (s Constraint) MustNew() *semver.Constraints {
	c, err := semver.NewConstraint(string(s))
	ccm.MustNoError(err)
	return c
}

type Component struct {
	Name string `json:"name,omitempty"` //TODO：翻译？

	Required bool `json:"required,omitempty"` //root
	X        bool `json:"x,omitempty"`        //leaf/leaf's parent

	//leaf
	Version      Version               `json:"version,omitempty"`
	Dependencies map[string]Constraint `json:"dependencies,omitempty"`

	Versions []Component `json:"versions,omitempty"` //leaf's parent
	Children []Component `json:"children,omitempty"` //leaf's grandpa and above
}
