package ccm

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

func First(cx *gin.Context, k string) (s string, n int) {
	s = cx.Request.FormValue(k)
	n = len(cx.Request.Form[k])
	return
}

func FirstBool(cx *gin.Context, k string) (bool, int) {
	k, n := First(cx, k)
	if n > 0 {
		b, err := strconv.ParseBool(k)
		if err == nil {
			return b, n
		}
	}
	return false, -n
}

func FirstInt(cx *gin.Context, k string) (int, int) {
	k, n := First(cx, k)
	if n > 0 {
		i, err := strconv.ParseInt(k, 10, 0)
		if err == nil {
			return int(i), n
		}
	}
	return 0, -n
}

func FirstInt64(cx *gin.Context, k string) (int64, int) {
	k, n := First(cx, k)
	if n > 0 {
		i, err := strconv.ParseInt(k, 10, 64)
		if err == nil {
			return i, n
		}
	}
	return 0, -n
}

func FirstUint(cx *gin.Context, k string) (uint, int) {
	k, n := First(cx, k)
	if n > 0 {
		u, err := strconv.ParseUint(k, 10, 0)
		if err == nil {
			return uint(u), n
		}
	}
	return 0, -n
}

func FirstUint64(cx *gin.Context, k string) (uint64, int) {
	k, n := First(cx, k)
	if n > 0 {
		u, err := strconv.ParseUint(k, 10, 64)
		if err == nil {
			return u, n
		}
	}
	return 0, -n
}

func FirstFloat32(cx *gin.Context, k string) (float32, int) {
	k, n := First(cx, k)
	if n > 0 {
		f, err := strconv.ParseFloat(k, 32)
		if err == nil {
			return float32(f), n
		}
	}
	return 0, -n
}

func FirstFloat64(cx *gin.Context, k string) (float64, int) {
	k, n := First(cx, k)
	if n > 0 {
		f, err := strconv.ParseFloat(k, 64)
		if err == nil {
			return f, n
		}
	}
	return 0, -n
}

func ParamBool(cx *gin.Context, name string) (bool, bool) {
	b, err := strconv.ParseBool(cx.Param(name))
	if err != nil {
		return false, false
	}
	return b, true
}

func ParamInt(cx *gin.Context, name string) (int, bool) {
	i, err := strconv.ParseInt(cx.Param(name), 10, 0)
	if err != nil {
		return 0, false
	}
	return int(i), true
}

func ParamInt64(cx *gin.Context, name string) (int64, bool) {
	i, err := strconv.ParseInt(cx.Param(name), 10, 64)
	if err != nil {
		return 0, false
	}
	return i, true
}

func ParamUint(cx *gin.Context, name string) (uint, bool) {
	u, err := strconv.ParseUint(cx.Param(name), 10, 0)
	if err != nil {
		return 0, false
	}
	return uint(u), true
}

func ParamUint64(cx *gin.Context, name string) (uint64, bool) {
	u, err := strconv.ParseUint(cx.Param(name), 10, 64)
	if err != nil {
		return 0, false
	}
	return u, true
}

func ParamFloat32(cx *gin.Context, name string) (float32, bool) {
	f, err := strconv.ParseFloat(cx.Param(name), 32)
	if err != nil {
		return 0, false
	}
	return float32(f), true
}

func ParamFloat64(cx *gin.Context, name string) (float64, bool) {
	f, err := strconv.ParseFloat(cx.Param(name), 64)
	if err != nil {
		return 0, false
	}
	return f, true
}
