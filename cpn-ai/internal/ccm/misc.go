// TODO：log/env
package ccm

import (
	"cpn-ai/common/logger"
	"fmt"
	"runtime/debug"
)

func Recover2Error(err *error) {
	if v := recover(); v != nil {
		if e, ok := v.(error); ok && e != nil {
			*err = e
		} else {
			*err = fmt.Errorf("%v", v)
		}
		Errorf("panicked: %v\n%s", *err, debug.Stack())
	}
}

// TODO：记录文件位置有问题
func Info(args ...any) {
	logger.Info(args...)
}
func Infof(template string, args ...any) {
	logger.Infof(template, args...)
}
func Error(args ...any) {
	logger.Error(args...)
}
func Errorf(template string, args ...any) {
	logger.Errorf(template, args...)
}
