package train

import (
	"os"
	"path/filepath"
	"slices"
	"strings"
	"time"
)

var stat = make(map[string][3]int)

var imageExts = []string{".jpg", ".jpeg", ".png", ".webp"}

func InputStat(dir string) (count, size int) {
	if a, ok := stat[dir]; ok {
		if n := time.Now().Second() - a[0]; n < 0 || n > 60 {
			delete(stat, dir)
		} else {
			return a[1], a[2]
		}
	}

	a, err := os.ReadDir(dir)
	if err != nil {
		return -1, -1
	}

	for _, f := range a {
		ext := filepath.Ext(f.Name())
		if !f.IsDir() && slices.Contains(imageExts, strings.ToLower(ext)) {
			count++
			info, _ := f.Info()
			size += int(info.Size())
		}
	}

	stat[dir] = [3]int{time.Now().Second(), count, size}

	return
}
