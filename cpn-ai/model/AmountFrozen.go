package model

import (
	"cpn-ai/common/logger"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type AmountFrozenStatusEnum int

const (
	AmountFrozenStatusEnumFrozen   AmountFrozenStatusEnum = 1
	AmountFrozenStatusEnumUnfrozen                        = 2
)

type AmountFrozen struct {
	gorm.Model
	OrderNo   string          `json:"order_no" gorm:"type:varchar(50);not null;default:'';comment:业务单号;uniqueIndex"`
	OrderType int             `json:"order_type" gorm:"type:integer;not null;default:0;comment:业务类型"`
	UserId    uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	Amount    decimal.Decimal `json:"amount" gorm:"type:decimal(16,2);not null;default:0;comment:冻结金额"`
	Remark    string          `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:内部备注"`
	Status    int             `json:"status" gorm:"type:tinyint;not null;default:0;comment:状态 0 1冻结中 2已解冻"`
}

func (AmountFrozen) TableName() string {
	return "T_AmountFrozen"
}

func (o *AmountFrozen) GetNewObject(orderNo string, orderType int, userId uint, amount decimal.Decimal) error {
	*o = AmountFrozen{
		OrderNo:   orderNo,
		OrderType: orderType,
		UserId:    userId,
		Amount:    amount,
		Status:    1,
	}
	if err := o.CheckNewObject(); err != nil {
		return err
	}
	return nil
}

func (o *AmountFrozen) CheckNewObject() error {
	if o.ID > 0 {
		return errors.New("is not new object")
	}
	if o.OrderNo == "" {
		return errors.New("order_no is empty")
	}
	if o.UserId <= 0 {
		return errors.New("userId is empty")
	}
	if o.Amount.LessThanOrEqual(decimal.Zero) {
		return errors.New("amount is zero")
	}
	if o.Status != 1 {
		return errors.New("status is irregular")
	}
	return nil
}

func (o *AmountFrozen) GetByOrderNo(orderNo string) error {
	return DB.First(o, "order_no=?", orderNo).Error
}

func (o *AmountFrozen) Frozen(tx *gorm.DB) error {
	if o.ID != 0 {
		return errors.New("不是新的记录")
	}
	if err := o.CheckNewObject(); err != nil {
		return errors.New("参数错误")
	}

	var user User
	if err := tx.First(&user, o.UserId).Error; err != nil {
		logger.Error(err)
		return err
	}
	if user.RewardAmount.Sub(user.RewardFrozen).LessThan(o.Amount) {
		err := errors.New("余额不足")
		logger.Error("冻结失败 err:", err)
		return err
	}
	result := tx.Model(&user).Updates(map[string]interface{}{"reward_frozen": user.RewardFrozen.Add(o.Amount)})
	if err := result.Error; err != nil {
		logger.Error(err)
		return err
	}
	if result.RowsAffected != 1 {
		err := errors.New(fmt.Sprintf("用户更新记录不为一，当前更新记录数为%d", result.RowsAffected))
		logger.Error(err)
		return err
	}
	return tx.Debug().Save(o).Error
}

// 解冻并扣款，需要产生流水记录
func (o *AmountFrozen) UnFrozenDeduct(tx *gorm.DB, orderNo string, user *User) error {
	if o.ID <= 0 {
		return errors.New("解冻记录不存在")
	}

	leaveAmount := user.RewardAmount.Sub(o.Amount)
	if leaveAmount.LessThan(decimal.Zero) {
		err := errors.New("余额不足")
		logger.Error("扣款失败 err:", err)
		return err
	}
	leaveFrozen := user.RewardFrozen.Sub(o.Amount)
	if leaveFrozen.LessThan(decimal.Zero) {
		err := errors.New("解冻金额不足")
		logger.Error("解冻失败 err:", err)
		return err
	}

	result := tx.Model(o).Where("order_no=? and status=1", orderNo).Update("status", 2)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected != 1 {
		return errors.New(fmt.Sprintf("解冻失败,影响记录为%d", result.RowsAffected))
	}

	result1 := tx.Model(user).Updates(map[string]interface{}{"reward_amount": leaveAmount, "reward_frozen": leaveFrozen})
	if err := result1.Error; err != nil {
		logger.Error(err)
		return err
	}
	if result1.RowsAffected != 1 {
		err := errors.New(fmt.Sprintf("用户更新记录不唯一，当前更新记录数为%d", result1.RowsAffected))
		logger.Error(err)
		return err
	}
	return nil
}

// 解冻并取消，不需要产生流水
func (o *AmountFrozen) UnFrozenCancel(tx *gorm.DB, orderNo string, user *User) error { //
	if o.ID <= 0 {
		return errors.New("解冻记录不存在")
	}
	result := tx.Model(o).Where("order_no=? and status=1", orderNo).Update("status", 2)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected != 1 {
		return errors.New(fmt.Sprintf("解冻失败,影响记录为%d", result.RowsAffected))
	}

	result1 := tx.Model(&user).Updates(map[string]interface{}{"reward_frozen": user.RewardFrozen.Sub(o.Amount)})
	if err := result1.Error; err != nil {
		logger.Error(err)
		return err
	}
	if result1.RowsAffected != 1 {
		err := errors.New(fmt.Sprintf("用户更新记录不唯一，当前更新记录数为%d", result1.RowsAffected))
		logger.Error(err)
		return err
	}
	return nil
}
