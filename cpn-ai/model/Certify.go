package model

import (
	"gorm.io/gorm"
	"time"
)

type CertifyCertTypeEnum string

const (
	CertifyCertType_IDENTITY_CARD                 CertifyCertTypeEnum = "IDENTITY_CARD"
	CertifyCertType_HOME_VISIT_PERMIT_HK_MC                           = "HOME_VISIT_PERMIT_HK_MC"
	CertifyCertType_HOME_VISIT_PERMIT_TAIWAN                          = "HOME_VISIT_PERMIT_TAIWAN"
	CertifyCertType_RESIDENCE_PERMIT_HK_MC                            = "RESIDENCE_PERMIT_HK_MC"
	CertifyCertType_RESIDENCE_PERMIT_TAIWAN                           = "RESIDENCE_PERMIT_TAIWAN"
	CertifyCertType_PERMANENT_RESIDENCE_FOREIGNER                     = "PERMANENT_RESIDENCE_FOREIGNER"
)

func CertifyCertTypeEnumName(i CertifyCertTypeEnum) string {
	switch i {
	case CertifyCertType_IDENTITY_CARD:
		return "身份证"
	case CertifyCertType_HOME_VISIT_PERMIT_HK_MC:
		return "港澳居民来往内地通行证"
	case CertifyCertType_HOME_VISIT_PERMIT_TAIWAN:
		return "台湾居民来往内地通行证"
	case CertifyCertType_RESIDENCE_PERMIT_HK_MC:
		return "港澳居民居住证"
	case CertifyCertType_RESIDENCE_PERMIT_TAIWAN:
		return "台湾居民居住证"
	case CertifyCertType_PERMANENT_RESIDENCE_FOREIGNER:
		return "外国人永久居住证"
	}
	return ""
}

//
//var CertifyCertTypeAlipay = map[int]string{
//	1: "IDENTITY_CARD",
//	2: "HOME_VISIT_PERMIT_HK_MC",
//	3: "HOME_VISIT_PERMIT_TAIWAN",
//	4: "RESIDENCE_PERMIT_HK_MC",
//	5: "RESIDENCE_PERMIT_TAIWAN",
//	6: "PERMANENT_RESIDENCE_FOREIGNER",
//}

type Certify struct {
	gorm.Model
	UserId      uint                `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	CertChannel string              `json:"cert_channel" gorm:"type:varchar(50);not null;default:'';comment:验证渠道"`
	CertType    CertifyCertTypeEnum `json:"cert_type" gorm:"type:varchar(50);not null;default:'';comment:证件类型"`
	CertName    string              `json:"cert_name" gorm:"type:varchar(100);not null;default:'';comment:姓名"`
	CertNo      string              `json:"cert_no" gorm:"type:varchar(100);not null;default:'';comment:卡号"`
	CertifyId   string              `json:"certify_id" gorm:"type:varchar(50);not null;default:'';comment:"`
	CertifyUrl  string              `json:"certify_url" gorm:"type:varchar(1000);not null;default:'';comment:"`
	CertTime    time.Time           `json:"cert_time" gorm:"type:datetime;default:'1900-01-01';comment:认证时间"`
	OrderNo     string              `json:"order_no" gorm:"type:varchar(50);comment:订单编号"`
	Status      int                 `json:"status" gorm:"type:tinyint;not null;default:0;comment:状态"` //1审核通过 2不通过
	Response    string              `json:"response" gorm:"type:json;comment:返回内容"`
}

func (Certify) TableName() string {
	return "T_Certify"
}

func (o *Certify) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *Certify) GetByCertNameAndNo(userId uint, channel string, certType CertifyCertTypeEnum, certName string, certNo string) error {
	checkAt := time.Now().Add(time.Hour * -20)
	return DB.First(o, "user_id=? and cert_channel=? and cert_type=? and cert_name=? and cert_no=? and created_at>?", userId, channel, certType, certName, certNo, checkAt).Error
}

func (o *Certify) GetByOrderNo(orderNo string) error {
	return DB.First(o, "order_no=?", orderNo).Error
}

func (o *Certify) SetCertifyId(certifyId string) error {
	return DB.Model(o).Update("certify_id", certifyId).Error
}

func (o *Certify) SetCertifyUrl(certifyUrl string) error {
	return DB.Model(o).Update("certify_url", certifyUrl).Error
}

func (o *Certify) SetStatus(status int, resp string) error {
	if resp == "" {
		resp = "{}"
	}
	return DB.Model(o).Updates(Certify{Status: status, Response: resp, CertTime: time.Now()}).Error
}
