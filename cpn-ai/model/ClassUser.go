package model

import (
	"fmt"
	"strings"

	"gorm.io/gorm"
)

type ClassUser struct {
	gorm.Model
	Uuid         string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID;uniqueIndex"`
	UserId       uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;"`
	StuUserId    uint   `json:"stu_user_id" gorm:"type:bigint;not null;default:0;comment:学员用户ID;"`
	FirstMobile  string `json:"first_mobile" gorm:"type:varchar(50);not null;default:'';comment:刚加入时的手机号"`
	ClassRoomIds string `json:"class_room_ids" gorm:"type:varchar(500);not null;default:'';comment:绑定的房间ID"`
	Remark       string `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	Status       int    `json:"status" gorm:"type:int;not null;default:0;comment:状态 0无效 1有效 2暂停"` //0的时候学生看不到  1和2的时候学生能看到
}

func (ClassUser) TableName() string {
	return "T_ClassUser"
}

func (o *ClassUser) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *ClassUser) Delete() error {
	return DB.Debug().Unscoped().Delete(o).Error
}

func (o *ClassUser) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *ClassUser) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}

func (o *ClassUser) GetByUserAndStu(userId uint, stuUserId uint) error {
	return DB.First(o, "user_id=? and stu_user_id=?", userId, stuUserId).Error
}

func (o *ClassUser) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, ok := queryParm["user_id"]; ok {
		tx.Where("user_id=?", queryParm["user_id"])
	}
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["stu_user_id"]; ok {
			tx.Where("stu_user_id=?", queryParm["stu_user_id"])
		}
		if _, ok := queryParm["class_room_id"]; ok {
			kw := "%" + fmt.Sprintf("%d", queryParm["class_room_id"]) + "%"
			tx.Where("class_room_ids like ?", kw)
		}
		if _, ok := queryParm["first_mobile"]; ok {
			tx.Where("first_mobile=?", queryParm["first_mobile"])
		}
		if _, ok := queryParm["kw"]; ok {
			kw := "%" + strings.ReplaceAll(queryParm["kw"].(string), "%", "") + "%"
			tx.Where("first_mobile like ?", kw)
		}
		if _, ok := queryParm["status"]; ok {
			tx.Where(fmt.Sprintf("status %s", queryParm["status"]))
		}
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	tx.Scan(dest)
	return total, tx.Error
}

func (o *ClassUser) SetStatus(status int) error {
	return DB.Model(o).Update("status", status).Error
}

func (o *ClassUser) SetRemark(remark string) error {
	return DB.Model(o).Update("remark", remark).Error
}

func (o *ClassUser) SetClassRoomIds(roomIds string) error {
	return DB.Model(o).Update("class_room_ids", roomIds).Error
}
