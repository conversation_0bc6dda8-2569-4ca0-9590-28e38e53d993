package model

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
)

type Coupon struct {
	gorm.Model
	Uuid       string          `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID"`
	CouponCode string          `json:"coupon_code" gorm:"type:varchar(50);not null;default:'';comment:优惠码"`
	CardPrefix string          `json:"card_prefix" gorm:"type:varchar(50);not null;default:'';comment:出卡的前缀"`
	UserId     uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	Title      string          `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	SalePrice  decimal.Decimal `json:"sale_price" gorm:"type:decimal(16, 8);not null;default:0;comment:销售价格"`
	FacePrice  decimal.Decimal `json:"face_price" gorm:"type:decimal(16, 8);not null;default:0;comment:面值"`
	ExpireDate time.Time       `json:"expire_date" gorm:"type:datetime;default:'1900-01-01';comment:过期日期"`
	ValidDays  int             `json:"valid_days" gorm:"type:int;not null;default:0;comment:绑定后有效天数"`
	Quantity   int             `json:"quantity" gorm:"type:int;not null;default:0;comment:发行量"`
	SingleMax  int             `json:"single_max" gorm:"type:int;not null;default:0;comment:单账号最大购买量"`
	SoldCount  int             `json:"sold_count" gorm:"type:int;not null;default:0;comment:已销售数量"`
	PodIds     string          `json:"pod_ids" gorm:"type:varchar(1500);not null;default:'';comment:定向使用PodIDs"`
	Pods       string          `json:"pods" gorm:"type:json;comment:定向Pod"`
	Remark     string          `json:"remark" gorm:"type:varchar(200);not null;default:'';comment:内部备注"`
	Status     int             `json:"status" gorm:"type:int;not null;default:0;comment:状态 默认1有效"`
}

func (Coupon) TableName() string {
	return "T_Coupon"
}

func (o *Coupon) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *Coupon) Delete() error {
	return DB.Debug().Delete(o).Error
}

func (o *Coupon) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Coupon) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}
func (o *Coupon) GetByCouponCode(code string) error {
	return DB.First(o, "coupon_code=?", code).Error
}

func (o *Coupon) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["uuid"]; ok {
			tx.Where("uuid=?", queryParm["uuid"])
		}
		if _, ok := queryParm["coupon_code"]; ok {
			tx.Where("coupon_code=?", queryParm["coupon_code"])
		}
		if _, ok := queryParm["status"]; ok {
			tx.Where("status=?", queryParm["status"])
		}

		tx.Where("status<?", 9)
		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
		tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *Coupon) SetStatus(status int) error {
	return DB.Model(o).Update("status", status).Error
}

func (o *Coupon) UpSoldCount(tx *gorm.DB, count int) error {
	return tx.Model(o).UpdateColumn("sold_count", gorm.Expr("sold_count + ?", count)).Error
}

func (o *Coupon) Updates(m map[string]interface{}) error {
	return DB.Model(o).Debug().Updates(m).Error
}
