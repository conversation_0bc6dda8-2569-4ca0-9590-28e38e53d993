package model

import (
	"gorm.io/gorm"
	"time"
)

type Dockers struct {
	gorm.Model
	DockerId      string    `json:"docker_id" gorm:"type:varchar(50);not null;default:'';comment:容器实例ID"`
	DockerName    string    `json:"docker_name" gorm:"type:varchar(50);not null;default:'';comment:容器实例名称"`
	InstanceUuid  string    `json:"instance_uuid" gorm:"type:varchar(50);not null;default:'';comment:外部实例ID"`
	StartupMark   string    `json:"startup_mark" gorm:"type:varchar(50);not null;default:'';comment:外部启动标识"`
	PodId         uint      `json:"pod_id" gorm:"type:bigint;not null;default:0;comment:PodID"`
	PodName       string    `json:"pod_name" gorm:"type:varchar(50);not null;default:'';comment:名称"`
	PodCategory   int       `json:"pod_category" gorm:"type:int;not null;default:0;comment:类型"`
	GpuModelId    uint      `json:"gpu_model_id" gorm:"type:bigint;not null;default:0;comment:显卡型号ID"`
	VirtualId     uint      `json:"virtual_id" gorm:"type:bigint;not null;default:0;comment:虚拟机ID"`
	HostPort      string    `json:"host_port" gorm:"type:varchar(50);not null;default:'';comment:所属虚拟机"`
	Gpus          string    `json:"gpus" gorm:"type:varchar(50);not null;default:'';comment:绑定的gpu序号"`
	Heartbeat     time.Time `json:"heartbeat" gorm:"type:datetime;default:'1900-01-01';comment:心跳包时间"`
	LastCheckTime time.Time `json:"last_check_time" gorm:"type:datetime;default:'1900-01-01';comment:最后核对时间"`
	WebUrl        string    `json:"web_url" gorm:"type:varchar(50);not null;default:'';comment:weiui地址"`
	ApiBase       string    `json:"api_base" gorm:"type:varchar(50);not null;default:'';comment:api地址"`
	Status        int       `json:"status" gorm:"type:int;not null;default:0;comment:状态"`
	State         string    `json:"state" gorm:"type:varchar(50);not null;default:'';comment:docker的字符串状态"`
	Created       time.Time `json:"created" gorm:"type:datetime;default:'1900-01-01';comment:docker的创建时间"`
}

func (Dockers) TableName() string {
	return "T_Dockers"
}

func (o *Dockers) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *Dockers) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Dockers) SetStatus(status int) error {
	return DB.Model(o).Updates(Dockers{Status: status}).Error
}

func (o *Dockers) ListByVirtual(dest interface{}, virtualId uint) error {
	tx := DB.Debug().Model(o)
	tx.Where("virtual_id=? and status<4", virtualId).Scan(dest)
	return tx.Error
}

func (o *Dockers) ListForCheck(dest interface{}, lastAutoId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Where("id>? and status<4", lastAutoId)
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *Dockers) List(dest interface{}, id uint, category int, status int, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	}
	if category > 0 {
		tx.Where("category=?", category)
	}
	if status >= 0 {
		tx.Where("status=?", status)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("order_index asc, id asc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}
