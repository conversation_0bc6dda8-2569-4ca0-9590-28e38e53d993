package model

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"errors"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type Instance struct {
	gorm.Model
	Uuid                string          `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID;uniqueIndex"`
	InstanceType        int             `json:"instance_type" gorm:"type:int;not null;default:0;comment:实例类型"` //0普通 3kol制作 9算力市场
	Title               string          `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	UserId              uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	PodId               uint            `json:"pod_id" gorm:"type:bigint;not null;default:0;comment:PodID"`
	PodName             string          `json:"pod_name" gorm:"type:varchar(50);not null;default:'';comment:名称"`
	Category            int             `json:"category" gorm:"type:int;not null;default:0;comment:类型"`
	ImageId             uint            `json:"image_id" gorm:"type:bigint;not null;default:0;comment:镜像ID"`
	ImageType           int             `json:"image_type" gorm:"type:int;not null;default:0;comment:镜像类型"`
	ImageName           string          `json:"image_name" gorm:"type:varchar(250);not null;default:'';comment:镜像名称"`
	ImageTag            string          `json:"image_tag" gorm:"type:varchar(250);not null;default:'';comment:镜像标签"`
	SaveImageId         uint            `json:"save_image_id" gorm:"type:bigint;not null;default:0;comment:保存的镜像ID;index"`
	GpuModelId          uint            `json:"gpu_model_id" gorm:"type:bigint;not null;default:0;comment:显卡型号ID"`
	GpuModelName        string          `json:"gpu_model_name" gorm:"type:varchar(50);not null;default:'';comment:显卡型号名称"`
	Gpus                int             `json:"gpus" gorm:"type:int;not null;default:0;comment:GPU数量"`
	NoCard              int             `json:"no_card" gorm:"type:tinyint;not null;default:0;comment:1为无卡模式"`
	ChargingType        int             `json:"charging_type" gorm:"type:int;not null;default:0;comment:计费类型"`
	ChargingNum         int             `json:"charging_num" gorm:"type:int;not null;default:0;comment:计费数量 包天多少天 包月几个月"`
	FirstupTime         time.Time       `json:"firstup_time" gorm:"type:datetime;default:'1900-01-01';comment:第一次启动成功的时间"`
	StartTime           time.Time       `json:"start_time" gorm:"type:datetime;default:'1900-01-01';comment:包月开始时间"`
	EndTime             time.Time       `json:"end_time" gorm:"type:datetime;default:'1900-01-01';comment:包月结束时间"`            //包日 包月这个时间提前设置
	SubIncrAmount       decimal.Decimal `json:"sub_incr_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:累计包月金额"` //返还时需要用该金额去减已用金额
	LastSettleTime      time.Time       `json:"last_settle_time" gorm:"type:datetime;default:'1900-01-01';comment:最后的结算时间"`
	LastSettleAmount    decimal.Decimal `json:"last_settle_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:最后结算金额"` //包日包月预付金额
	LastPodSettleTime   time.Time       `json:"last_pod_settle_time" gorm:"type:datetime;default:'1900-01-01';comment:Pod服务费最后的结算时间"`
	StartupMark         string          `json:"startup_mark" gorm:"type:varchar(50);not null;default:'';comment:本次启动触发标记"`
	StartupMarkTime     time.Time       `json:"startup_mark_time" gorm:"type:datetime;default:'1900-01-01';comment:本次启动标记时间"`
	StartupNodeId       uint            `json:"startup_node_id" gorm:"type:bigint;not null;default:0;comment:本次启动所在的节点"`
	StartupVirtualId    uint            `json:"startup_virtual_id" gorm:"type:bigint;not null;default:0;comment:本次启动所在的虚拟机"`
	StartupImageId      uint            `json:"startup_image_id" gorm:"type:bigint;not null;default:0;comment:本次启动的镜像ID;index"`
	StartupGpus         string          `json:"startup_gpus" gorm:"type:varchar(50);not null;default:'';comment:本次启动使用的Gpu"`
	StartupMaps         string          `json:"startup_maps" gorm:"type:varchar(1500);not null;default:'';comment:本次启动地址映射"`
	StartupElapse       uint            `json:"startup_elapse" gorm:"type:bigint;not null;default:0;comment:本次启动消耗时间 单位秒"`
	StartupTime         time.Time       `json:"startup_time" gorm:"type:datetime;default:'1900-01-01';comment:本次开机时间"`
	ShutdownRegularTime time.Time       `json:"shutdown_regular_time" gorm:"type:datetime;default:'1900-01-01';comment:定时关机时间"`
	ShutdownRegularSave bool            `json:"shutdown_regular_save" gorm:"type:tinyint;not null;default:0;comment:定时关机是否保存镜像"`
	ShutdownTime        time.Time       `json:"shutdown_time" gorm:"type:datetime;default:'1900-01-01';comment:本次关机时间"`
	ShutdownReason      string          `json:"shutdown_reason" gorm:"type:varchar(50);not null;default:'';comment:关机原因"`
	ShutdownDestroy     bool            `json:"shutdown_destroy" gorm:"type:tinyint;not null;default:0;comment:销毁实例"`
	ShutdownTask        string          `json:"shutdown_task" gorm:"type:varchar(50);not null;default:'';comment:关机后续任务"`
	ReminderAt          time.Time       `json:"reminder_at" gorm:"type:datetime;default:'1900-01-01';comment:提醒时间"`
	LastUseTime         time.Time       `json:"last_use_time" gorm:"type:datetime;default:'1900-01-01';comment:最后一次使用时间"`
	LastCheckTime       time.Time       `json:"last_check_time" gorm:"type:datetime;default:'1900-01-01';comment:最后一次Docker有效验证时间"`
	DockerId            string          `json:"docker_id" gorm:"type:varchar(64);not null;default:'';comment:启动的dockerId"`
	WebUrl              string          `json:"web_url" gorm:"type:varchar(100);not null;default:'';comment:PodWebUi的内部地址"`
	ApiBase             string          `json:"api_base" gorm:"type:varchar(100);not null;default:'';comment:PodApi接口的内部地址"`
	OrderNo             string          `json:"order_no" gorm:"type:varchar(50);not null;default:'';comment:订单编号"` //包日包月这里会产生订单编号
	Status              int             `json:"status" gorm:"type:int;not null;default:0;comment:状态 1正在启动 2启动成功;index"`
	LastStatus          int             `json:"last_status" gorm:"type:int;not null;default:0;comment:上一次的状态"`
	LastRecordId        uint            `json:"last_record_id" gorm:"type:bigint;not null;default:0;comment:最后一条记录"`
}

func (Instance) TableName() string {
	return "T_Instance"
}

//func (o *Instance) Save() error {
//	return DB.Debug().Save(o).Error
//}

func (o *Instance) Create() error {

	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if err := tx.Debug().Save(o).Error; err != nil {
			return err
		}
		if o.ChargingType == enums.ChargingTypeEnum.Day || o.ChargingType == enums.ChargingTypeEnum.Week || o.ChargingType == enums.ChargingTypeEnum.Month { //订阅模式直接扣费  涉及到结算的地方
			var settleRecord SettleRecord
			if err := settleRecord.New(tx, o, enums.BusinessTypeEnum.PodInstance, enums.SettleReasonEnum.CreateSubInst, o.StartTime, o.EndTime, o.LastSettleAmount); err != nil {
				logger.Error(err)
				return err
			}
		}
		return nil
	})
}

func (o *Instance) SetChargingTypeUsage2Sub(chargingType int, chargingNum int, amount decimal.Decimal) error { //按量转包日包周包月 chargingType 要转的Sub类型
	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')

		startTime := time.Now().Truncate(time.Second)
		endTime := o.CalculateEndTime(chargingType, startTime, chargingNum)
		var pod Pod
		if err := pod.GetById(o.PodId); err != nil {
			logger.Error(err)
			return err
		}
		var gpuModel GpuModel
		if err := gpuModel.GetById(o.GpuModelId); err != nil {
			logger.Error(err)
			return err
		}
		price := gpuModel.CalculateAmount(chargingType, chargingNum, o.Gpus)
		if price.Equal(decimal.Zero) {
			err := errors.New("金额计算为零")
			logger.Error(err)
			return err
		}

		if price.Equal(amount) == false {
			msg := "与前端金额计算不一致"
			err := errors.New(msg)
			logger.Error(err, o.Uuid)
			return err
		}

		//更新为包日包周包月 startTime更新为包月的开始时间  涉及到扣费的地方
		subIncrAmount := o.SubIncrAmount.Add(price)
		if err := tx.Model(o).Updates(Instance{ChargingType: chargingType, ChargingNum: chargingNum, StartTime: startTime, EndTime: endTime, SubIncrAmount: subIncrAmount, LastSettleAmount: price, LastSettleTime: startTime}).Error; err != nil {
			logger.Error(err)
			return err
		}

		if o.ChargingType == enums.ChargingTypeEnum.Usage {
			err := errors.New("付费方式不正确")
			logger.Error(err)
			return err
		} else {
			var settleRecord SettleRecord
			if err := settleRecord.New(tx, o, enums.BusinessTypeEnum.PodInstance, enums.SettleReasonEnum.Usage2Sub, startTime, endTime, price); err != nil {
				logger.Error(err)
				return err
			}
		}
		return nil
	})
}

func (o *Instance) Renewal(chargingType int, chargingNum int, amount decimal.Decimal) error { //包日包周包月sub续费

	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')

		if o.ChargingType == enums.ChargingTypeEnum.Usage {
			err := errors.New("实例计费类型不正确")
			logger.Error(err)
			return err
		}
		if chargingType == enums.ChargingTypeEnum.Usage {
			err := errors.New("实例续费计费类型不正确")
			logger.Error(err)
			return err
		}

		now := time.Now().Truncate(time.Second)
		startTime := now
		if o.EndTime.After(startTime) {
			startTime = o.EndTime
		}

		endTime := o.CalculateEndTime(chargingType, startTime, chargingNum)

		var pod Pod
		if err := pod.GetById(o.PodId); err != nil {
			logger.Error(err)
			return err
		}
		if pod.Category != enums.PodCategoryEnum.PodInstance && pod.Category != enums.PodCategoryEnum.GpuInstance {
			err := errors.New("pod类别不正确")
			logger.Error(err, pod.ID)
			return err
		}

		var gpuModel GpuModel
		if err := gpuModel.GetById(o.GpuModelId); err != nil {
			logger.Error(err)
			return err
		}
		price := gpuModel.CalculateAmount(chargingType, chargingNum, o.Gpus)
		if price.Equal(decimal.Zero) {
			err := errors.New("金额计算为零")
			logger.Error(err, pod.ID, "  ", chargingType, "  ", chargingNum)
			return err
		}

		if price.Equal(amount) == false {
			err := errors.New("与输入金额不一致")
			logger.Error(err)
			return err
		}

		if o.EndTime.Before(now) { //包月过期了，不连续了需要更新StartTime
			if err := tx.Model(o).Updates(Instance{ChargingType: chargingType, ChargingNum: chargingNum, StartTime: startTime, EndTime: endTime, SubIncrAmount: price, LastSettleAmount: price, LastSettleTime: now}).Error; err != nil {
				logger.Error(err)
				return err
			}
		} else {
			subIncrAmount := o.SubIncrAmount.Add(price)
			if err := tx.Model(o).Updates(Instance{ChargingType: chargingType, ChargingNum: chargingNum, EndTime: endTime, SubIncrAmount: subIncrAmount, LastSettleAmount: price, LastSettleTime: now}).Error; err != nil {
				logger.Error(err)
				return err
			}
		}

		var settleRecord SettleRecord //包日包月 涉及到扣费的地方
		if err := settleRecord.New(tx, o, enums.BusinessTypeEnum.PodInstance, enums.SettleReasonEnum.Renewal, startTime, endTime, price); err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (o *Instance) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Instance) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}
func (o *Instance) GetByStartupMark(startupMark string) error {
	return DB.First(o, "startup_mark=?", startupMark).Error
}

func (o *Instance) CountOfBootInOrRunning(userId uint) (int, error) {

	type Result struct {
		TotalCount int `json:"total_count"`
	}
	var dest Result
	tx := DB.Debug().Model(o)
	tx.Select("count(id) as total_count")
	tx.Where("user_id=?", userId)
	tx.Where("charging_type=?", enums.ChargingTypeEnum.Usage)
	tx.Where("status=? or status=?", enums.InstanceStatusEnum.BootInProgress, enums.InstanceStatusEnum.Running)
	tx.Scan(&dest)
	return dest.TotalCount, tx.Error
}

func (o *Instance) ListForOverDayClear(dest interface{}, lastId uint, pageSize int) error {

	//SELECT id, GREATEST(created_at, startup_time, startup_mark_time, shutdown_time) as max_time,created_at,startup_time,startup_mark_time,shutdown_time,status
	//FROM T_Instance
	//WHERE status<9 and GREATEST(created_at, startup_time, startup_mark_time, shutdown_time) < CURDATE()
	//ORDER BY id DESC;

	checkTime := jsontime.Today().Time().AddDate(0, 0, -common.InstanceOverDays)
	tx := DB.Debug().Model(o)
	tx.Where("status in(0,4) and id>? and GREATEST(created_at, startup_time, startup_mark_time, shutdown_time) <?", lastId, checkTime)
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *Instance) ListForShutdownRegular(dest interface{}, lastAutoId uint, pageSize int) error {
	checkTime := time.Now().Add(time.Hour * 48 * -1)
	tx := DB.Debug().Model(o)
	tx.Where("id>? and status=? and shutdown_regular_time>?", lastAutoId, enums.InstanceStatusEnum.Running, checkTime)
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *Instance) ListForCheckBootInProgress(dest interface{}, lastAutoId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Where("id>? and status=?", lastAutoId, enums.InstanceStatusEnum.BootInProgress)
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *Instance) ListForCheckRunningProgress(dest interface{}, lastAutoId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Where("id>? and status=?", lastAutoId, enums.InstanceStatusEnum.Running)
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *Instance) ListForMonitor(dest interface{}, lastAutoId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Where("id>? and status in(1,2)", lastAutoId)
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *Instance) ListForRecentlyPod(dest interface{}, userId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Select("pod_id,max(id) as max_id")
	tx.Where("user_id=?", userId)
	tx.Group("pod_id")
	tx.Order("max_id desc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *Instance) List(dest interface{}, id uint, userId uint, instType int, category int, status int, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	} else {
		if userId > 0 {
			tx.Where("user_id=?", userId)
		}
		if instType >= 0 {
			tx.Where("instance_type=?", instType)
		}
		if category > 0 {
			tx.Where("category=?", category)
		}
		if status >= 0 {
			tx.Where("status=?", status)
		}
		tx.Where("status<?", 9)
		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
		tx.Order("CASE  WHEN status = 1 THEN 1 WHEN status = 2 THEN 2 ELSE 3 END,id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *Instance) CountForLimit(userId uint) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	tx.Where("user_id=?", userId)
	tx.Where("status<?", 9)
	tx.Where("instance_type=? or instance_type=?", enums.InstanceTypeEnum.Normal, enums.InstanceTypeEnum.CCM)
	tx.Count(&total)
	return total, tx.Error
}

func (o *Instance) ListPro(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}

		if _, ok := queryParm["uuid"]; ok {
			uuid := queryParm["uuid"].(string)
			kw := "%" + uuid + "%"
			tx.Where("uuid like ?", kw)
		}

		if _, ok := queryParm["instance_type"]; ok {
			tx.Where("instance_type=?", queryParm["instance_type"])
		}
		if _, ok := queryParm["category"]; ok {
			tx.Where("category=?", queryParm["category"])
		}
		if _, ok := queryParm["status"]; ok {
			tx.Where("status=?", queryParm["status"])
		}
		if _, ok := queryParm["pod_id"]; ok {
			tx.Where("pod_id=?", queryParm["pod_id"])
		}
		if _, ok := queryParm["image_id"]; ok {
			tx.Where("image_id=?", queryParm["image_id"])
		}
		if _, ok := queryParm["startup_image_id"]; ok {
			tx.Where("startup_image_id=?", queryParm["startup_image_id"])
		}
		if _, ok := queryParm["save_image_id"]; ok {
			tx.Where("save_image_id=?", queryParm["save_image_id"])
		}
		if _, ok := queryParm["gpu_model_id"]; ok {
			tx.Where("gpu_model_id=?", queryParm["gpu_model_id"])
		}
		if _, ok := queryParm["startup_mark"]; ok {
			tx.Where("startup_mark=?", queryParm["startup_mark"])
		}
		if _, ok := queryParm["startup_node_id"]; ok {
			tx.Where("startup_node_id=?", queryParm["startup_node_id"])
		}
		if _, ok := queryParm["startup_virtual_id"]; ok {
			tx.Where("startup_virtual_id=?", queryParm["startup_virtual_id"])
		}
		if _, ok := queryParm["docker_id"]; ok {
			tx.Where("docker_id=?", queryParm["docker_id"])
		}
		if _, ok := queryParm["all"]; ok {

		} else {
			if _, ok := queryParm["recently"]; ok {
			} else {
				tx.Where("status<?", 9)
			}
		}
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	tx.Scan(dest)
	return total, tx.Error
}

func (o *Instance) ListSettle(dest interface{}, lastAutoId uint, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	tx.Where("status=?", enums.InstanceStatusEnum.Running)
	tx.Where("id>?", lastAutoId)
	//tx.Where("category=?", enums.ChargingTypeEnum.Usage)
	//tx.Where("last_settle_time<?", time.Now().Truncate(time.Hour))
	tx.Where("(last_settle_time < ? OR last_pod_settle_time < ?)", time.Now().Truncate(time.Hour), time.Now().Truncate(time.Hour))

	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id asc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Instance) ListForSettle(dest interface{}, lastAutoId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Where("status=?", enums.InstanceStatusEnum.Running)
	tx.Where("id>?", lastAutoId)
	tx.Where("last_settle_time < ? OR last_pod_settle_time < ?", time.Now().Truncate(time.Hour), time.Now().Truncate(time.Hour))
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *Instance) StatsRunning() (usageInstanceCount int64, noCardInstanceCount int64, kolInstanceCount int64, err error) {

	err = DB.Debug().Model(o).Where("status=2 and charging_type=? and no_card=0 ", enums.ChargingTypeEnum.Usage).Count(&usageInstanceCount).Error
	if err != nil {
		return
	}

	err = DB.Debug().Model(o).Where("status=2 and charging_type=? and no_card=1 ", enums.ChargingTypeEnum.Usage).Count(&noCardInstanceCount).Error
	if err != nil {
		return
	}

	err = DB.Debug().Model(o).Where("status=2 and instance_type=? ", enums.InstanceTypeEnum.Kol).Count(&kolInstanceCount).Error
	if err != nil {
		return
	}
	return
}

func (o *Instance) HasRunningImage(userId uint, imageId uint) error {

	return DB.Debug().First(o, "user_id=? and image_id=? and status=?", userId, imageId, enums.InstanceStatusEnum.Running).Error

}

func (o *Instance) Destroy() error {

	return DB.Transaction(func(tx *gorm.DB) error {

		if err := tx.Model(o).Update("status", enums.InstanceStatusEnum.Hidden).Error; err != nil {
			logger.Error(err)
			return err
		}
		var instRecord InstRecord
		if err := instRecord.GetByStartupMark(o.StartupMark); err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil
			}
			logger.Error(err)
			return err
		}
		if err := tx.Model(&instRecord).Update("shutdown_destroy", true).Error; err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (o *Instance) SetStatus(status int) error {
	//if status == 0 {
	//	return DB.Model(o).Updates(map[string]interface{}{"status": status, "last_status": o.Status}).Error
	//} else {
	//	return DB.Model(o).Updates(Instance{Status: status, LastStatus: o.Status}).Error
	//}
	return DB.Model(o).Update("status", status).Error
}

func (o *Instance) SetTitle(title string) error {

	return DB.Model(o).Update("title", title).Error
}

func (o *Instance) SetStartupImageId(imageId uint) error {

	return DB.Model(o).Update("startup_image_id", imageId).Error
}

func (o *Instance) SetGpuModel(gpuModelId uint, gpuModelName string) error {
	if o.Status != enums.InstanceStatusEnum.Created && o.Status != enums.InstanceStatusEnum.ShutdownComplete {
		return errors.New("当前状态不能更换显卡型号")
	}
	return DB.Model(o).Updates(map[string]interface{}{"gpu_model_id": gpuModelId, "gpu_model_name": gpuModelName}).Error
}

func (o *Instance) SetBootInProgress(noCard int) error {
	//m := map[string]interface{}{"status": enums.InstanceStatusEnum.BootInProgress, "no_card": noCard, "last_status": o.Status, "startup_mark": "", "startup_mark_time": time.Now(), "shutdown_regular_time": common.DefaultTime, "docker_id": ""}
	m := map[string]interface{}{"status": enums.InstanceStatusEnum.BootInProgress, "no_card": noCard, "last_status": o.Status}
	return DB.Model(o).Debug().Updates(m).Error
}

func (o *Instance) SetStartupMark(startupMark string, startupGpus string, nodeId uint, virtualId uint) error {
	m := map[string]interface{}{"startup_mark": startupMark, "startup_gpus": startupGpus, "startup_mark_time": time.Now(), "startup_node_id": nodeId, "startup_virtual_id": virtualId, "docker_id": "", "shutdown_regular_time": common.DefaultTime}
	//return o.Updates(m)
	return DB.Model(o).Debug().Updates(m).Error
}

func (o *Instance) SetShutdownReason(txt string) error {
	return DB.Model(o).Updates(Instance{ShutdownReason: txt}).Error
}

func (o *Instance) SetShutdownDestroy(task string) error {
	return DB.Model(o).Updates(Instance{ShutdownDestroy: true, ShutdownTask: task}).Error
	//return DB.Model(o).Update("shutdown_destroy", true).Error
}

func (o *Instance) SetShutdownTask(destroy bool, task string) error {
	return DB.Model(o).Updates(map[string]interface{}{"shutdown_destroy": destroy, "shutdown_task": task}).Error
}

func (o *Instance) SetSaveImageId(imageId uint) error {
	return DB.Model(o).Updates(Instance{SaveImageId: imageId}).Error
}

func (o *Instance) SetSaveImage(podImage *PodImage) error {

	return DB.Transaction(func(tx *gorm.DB) error {

		if err := tx.Debug().Save(podImage).Error; err != nil {
			logger.Error(err)
			return err
		}
		if podImage.ID == 0 {
			err := errors.New("生成PodImage失败")
			logger.Error(err)
			return err
		}

		result := DB.Model(o).Updates(Instance{SaveImageId: podImage.ID})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected != 1 {
			return errors.New(fmt.Sprintf("SetSaveImage失败,影响记录为%d", result.RowsAffected))
		}
		return nil
	})

}

func (o *Instance) Updates(m map[string]interface{}) error {
	return DB.Model(o).Debug().Updates(m).Error
}

func (o *Instance) SetShutdownRegularTime(t time.Time, save bool) error {
	return DB.Model(o).Updates(Instance{ShutdownRegularTime: t, ShutdownRegularSave: save}).Error
}

func (o *Instance) SetLastUseTime(t time.Time) error {
	return DB.Model(o).Updates(Instance{LastUseTime: t}).Error
}

func (o *Instance) SetLastCheckTime() error {
	return DB.Model(o).Updates(Instance{LastCheckTime: time.Now()}).Error
}

func (o *Instance) SetReminderAt() error {
	return DB.Model(o).Updates(Instance{ReminderAt: time.Now()}).Error
}

func (o *Instance) SetStatus2Running(nodeId uint, virtualId uint, gpus string, dockerId string, webUrl string, apiBase string, startupMaps string) error {
	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		now := time.Now()
		startupElapse := uint(now.Sub(o.StartupMarkTime).Seconds())

		instRecord := InstRecord{
			Uuid:                utils.GetUUID(),
			InstanceId:          o.ID,
			InstanceType:        o.InstanceType,
			UserId:              o.UserId,
			PodId:               o.PodId,
			PodName:             o.PodName,
			Category:            o.Category,
			ImageId:             o.ImageId,
			ImageType:           o.ImageType,
			ImageName:           o.ImageName,
			ImageTag:            o.ImageTag,
			SaveImageId:         o.SaveImageId,
			Gpus:                o.Gpus,
			NoCard:              o.NoCard,
			DockerId:            dockerId,
			StartupMark:         o.StartupMark,
			StartupMarkTime:     o.StartupMarkTime,
			StartupNodeId:       nodeId,
			StartupVirtualId:    virtualId,
			StartupImageId:      o.StartupImageId,
			StartupGpus:         gpus,
			StartupElapse:       startupElapse,
			StartupTime:         now,
			ShutdownRegularTime: o.ShutdownRegularTime,
			ShutdownRegularSave: o.ShutdownRegularSave,
			Status:              enums.InstanceStatusEnum.Running,
		}
		if err := tx.Debug().Save(&instRecord).Error; err != nil {
			return err
		}
		if o.FirstupTime.Before(now.AddDate(-10, 0, 0)) {
			if err := tx.Model(o).Updates(Instance{Status: enums.InstanceStatusEnum.Running, ShutdownReason: "", FirstupTime: now, StartTime: now, StartupTime: now, StartupVirtualId: virtualId, StartupGpus: gpus, StartupElapse: startupElapse, DockerId: dockerId, WebUrl: webUrl, ApiBase: apiBase, StartupMaps: startupMaps, LastRecordId: instRecord.ID}).Error; err != nil {
				return err
			}
		} else {
			if err := tx.Model(o).Updates(Instance{Status: enums.InstanceStatusEnum.Running, ShutdownReason: "", StartupTime: now, StartupVirtualId: virtualId, StartupGpus: gpus, StartupElapse: startupElapse, DockerId: dockerId, WebUrl: webUrl, ApiBase: apiBase, StartupMaps: startupMaps, LastRecordId: instRecord.ID}).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

func (o *Instance) SetStatus2Fail(reason string, nodeId uint, virtualId uint, gpus string, dockerId string, webUrl string, apiBase string, startupMaps string) error {
	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		now := time.Now()
		startupElapse := uint(now.Sub(o.StartupMarkTime).Seconds())

		instRecord := InstRecord{
			Uuid:                utils.GetUUID(),
			InstanceId:          o.ID,
			InstanceType:        o.InstanceType,
			UserId:              o.UserId,
			PodId:               o.PodId,
			PodName:             o.PodName,
			Category:            o.Category,
			ImageId:             o.ImageId,
			ImageType:           o.ImageType,
			ImageName:           o.ImageName,
			ImageTag:            o.ImageTag,
			SaveImageId:         o.SaveImageId,
			Gpus:                o.Gpus,
			NoCard:              o.NoCard,
			DockerId:            dockerId,
			StartupMark:         o.StartupMark,
			StartupMarkTime:     o.StartupMarkTime,
			StartupNodeId:       nodeId,
			StartupVirtualId:    virtualId,
			StartupImageId:      o.StartupImageId,
			StartupGpus:         gpus,
			StartupElapse:       startupElapse,
			StartupTime:         now,
			ShutdownRegularTime: o.ShutdownRegularTime,
			ShutdownRegularSave: o.ShutdownRegularSave,
			ShutdownReason:      reason,
			Status:              enums.InstanceStatusEnum.StartupFail,
		}
		if err := tx.Debug().Save(&instRecord).Error; err != nil {
			return err
		}
		if err := tx.Model(o).Updates(Instance{Status: o.LastStatus, ShutdownReason: "", StartupVirtualId: virtualId, StartupGpus: gpus, StartupElapse: startupElapse, DockerId: dockerId, WebUrl: webUrl, ApiBase: apiBase, StartupMaps: startupMaps, LastRecordId: instRecord.ID}).Error; err != nil {
			return err
		}
		if o.LastStatus == 0 {
			return tx.Model(o).Update("status", o.LastStatus).Error
		}
		return nil
	})
}

func (o *Instance) SetStartupMaps(startupMaps string) error {

	return DB.Model(o).Update("startup_maps", startupMaps).Error
}

func (o *Instance) Settle(settleReason int) error { //该函数需要Redis锁实例ID
	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		trace := fmt.Sprintf("instanceUuid:%s 结算原因:%d ", o.Uuid, settleReason)
		now := time.Now().Truncate(time.Second)

		var user User
		if err := user.GetById(o.UserId); err != nil {
			logger.Error(err)
			return err
		}
		if user.UserType == enums.UserTypeEnum.System || o.ChargingType == enums.ChargingTypeEnum.Free { //系统用户 或者免费实例 不结算
			if settleReason == enums.SettleReasonEnum.Shutdown {
				var instRecord InstRecord
				if err := tx.First(&instRecord, o.LastRecordId).Error; err != nil {
					logger.Error(o.LastRecordId, err)
					return err
				} else {
					if instRecord.Status == enums.InstanceStatusEnum.ShutdownInProgress {
						logger.Info(o.LastRecordId, "已经在关机中")
						return nil
					}
					if instRecord.Status == enums.InstanceStatusEnum.ShutdownComplete {
						logger.Info(o.LastRecordId, "已经关机了")
						return nil
					}
					sec := uint(now.Sub(instRecord.StartupTime).Seconds())
					if err := tx.Model(&instRecord).Updates(InstRecord{Status: enums.InstanceStatusEnum.ShutdownInProgress, ShutdownTime: now, ShutdownReason: o.ShutdownReason, ShutdownRegularTime: o.ShutdownRegularTime, ShutdownRegularSave: o.ShutdownRegularSave, UsageDuration: sec}).Error; err != nil {
						logger.Error(o.LastRecordId, err)
						return err
					}
					if err := tx.Model(o).Updates(Instance{Status: enums.InstanceStatusEnum.ShutdownComplete, ShutdownTime: now}).Error; err != nil {
						logger.Error(o.LastRecordId, err)
						return err
					}
				}
				return nil
			}
			err := errors.New("系统用户或者免费实例 不结算")
			logger.Error(err, o.ID, " ", settleReason)
			return err
		}

		var pod Pod
		if err := pod.GetById(o.PodId); err != nil {
			logger.Error(err)
			return err
		}

		if pod.Category == enums.PodCategoryEnum.LLM { //
			logger.Info(trace, "语言模型,不在这里扣费")
			if settleReason == enums.SettleReasonEnum.Shutdown {
				var instRecord InstRecord
				if err := tx.First(&instRecord, o.LastRecordId).Error; err != nil {
					logger.Error(err)
				} else {
					sec := uint(now.Sub(instRecord.StartupTime).Seconds())
					if err1 := tx.Model(&instRecord).Updates(InstRecord{Status: enums.InstanceStatusEnum.ShutdownInProgress, ShutdownTime: now, ShutdownReason: o.ShutdownReason, ShutdownRegularTime: o.ShutdownRegularTime, ShutdownRegularSave: o.ShutdownRegularSave, UsageDuration: sec}).Error; err1 != nil {
						logger.Error(err1)
					}
				}
				if err := tx.Model(o).Updates(Instance{Status: enums.InstanceStatusEnum.ShutdownComplete, ShutdownTime: now}).Error; err != nil {
					logger.Error(err)
					return err
				}
				return nil
			} else {
				msg := "语言模型不支持该操作"
				logger.Error(trace, msg)
				return errors.New(msg)
			}
			return nil
		}

		if settleReason == enums.SettleReasonEnum.Hourly || settleReason == enums.SettleReasonEnum.Shutdown { //Pod服务费

			if pod.Category == enums.PodCategoryEnum.PodInstance {
				settleStartTime := o.StartupTime
				if o.LastPodSettleTime.After(settleStartTime) {
					settleStartTime = o.LastPodSettleTime
				}
				if settleStartTime.Before(common.NationalDay) { //没有启动过
					logger.Info("没有启动过，不做结算", o.Uuid)
				} else {
					settleStartTime = settleStartTime.Truncate(time.Second) //2024-05-10 16:18:26 +0800 CST
					settleEndTime := now.Truncate(time.Second)
					if settleReason == enums.SettleReasonEnum.Hourly {
						timeFormat := "2006-01-02 15:00:00"
						timeString := settleEndTime.Format(timeFormat)
						if parsedTime, err := time.ParseInLocation(timeFormat, timeString, time.Local); err != nil {
							logger.Error(err)
							return err
						} else {
							settleEndTime = parsedTime
						}
					}
					if settleEndTime.After(settleStartTime) {
						seconds := settleEndTime.Sub(settleStartTime).Seconds()
						settleAmount := pod.CalculateAmount(enums.ChargingTypeEnum.Usage, int(seconds))
						if settleAmount.GreaterThan(decimal.Zero) {
							var settleRecord SettleRecord
							if err := settleRecord.New(tx, o, enums.BusinessTypeEnum.PodService, settleReason, settleStartTime, settleEndTime, settleAmount); err != nil {
								logger.Error(err)
								return err
							}
						}
						if err := tx.Model(o).Updates(Instance{LastPodSettleTime: settleEndTime}).Error; err != nil {
							logger.Error(err)
							return err
						}
					}
				}
			}

		}

		if o.ChargingType == enums.ChargingTypeEnum.Usage {
			if o.Status != enums.InstanceStatusEnum.Running {
				logger.Info(trace, "实例没有开启，按量计费无需结算", o.Uuid)
				return nil
			}
			settleStartTime := o.StartupTime
			if o.LastSettleTime.After(settleStartTime) {
				settleStartTime = o.LastSettleTime
			}
			if settleStartTime.Before(now.AddDate(-10, 0, 0)) { //没有启动过
				logger.Info("没有启动过，不做结算", o.Uuid)
				return nil
			}

			settleStartTime = settleStartTime.Truncate(time.Second)
			settleEndTime := now.Truncate(time.Second)
			if settleReason == enums.SettleReasonEnum.Hourly {
				timeFormat := "2006-01-02 15:00:00"
				timeString := settleEndTime.Format(timeFormat)
				if parsedTime, err := time.ParseInLocation(timeFormat, timeString, time.Local); err != nil {
					logger.Error(trace, err)
					return err
				} else {
					settleEndTime = parsedTime
				}
			}
			if settleEndTime.Before(o.LastSettleTime) || settleEndTime.Equal(o.LastSettleTime) {
				err := errors.New("结算时间等于或在最后结算时间之前，不做结算处理")
				logger.Error(trace, err)
				return err
			}
			if settleEndTime.After(settleStartTime) == false {
				if settleReason == enums.SettleReasonEnum.Hourly {
					return nil
				}
				err := errors.New("最后结算时间在开始结算时间之前，不做结算处理")
				logger.Error(trace, err)
				return err
			}

			seconds := settleEndTime.Sub(settleStartTime).Seconds()
			var gpuModel GpuModel
			if err := gpuModel.GetById(o.GpuModelId); err != nil {
				logger.Error(trace, " 结算未找到Gpu类型", err, "GpuModelId:", o.GpuModelId)
				return err
			}
			settleAmount := gpuModel.CalculateAmount(o.ChargingType, int(seconds), o.Gpus)

			if settleReason == enums.SettleReasonEnum.Shutdown {
				var instRecord InstRecord
				if err := tx.First(&instRecord, o.LastRecordId).Error; err != nil {
					logger.Error(err)
				} else {
					sec := uint(now.Sub(instRecord.StartupTime).Seconds())
					if err1 := tx.Model(&instRecord).Updates(InstRecord{Status: enums.InstanceStatusEnum.ShutdownInProgress, ShutdownTime: settleEndTime, ShutdownReason: o.ShutdownReason, ShutdownRegularTime: o.ShutdownRegularTime, ShutdownRegularSave: o.ShutdownRegularSave, UsageDuration: sec}).Error; err1 != nil {
						logger.Error(err1)
					}
				}
				if err := tx.Model(o).Updates(Instance{Status: enums.InstanceStatusEnum.ShutdownComplete, ShutdownTime: settleEndTime, LastSettleTime: settleEndTime, LastSettleAmount: settleAmount}).Error; err != nil {
					logger.Error(err)
					return err
				}
			} else {
				if err := tx.Model(o).Updates(Instance{LastSettleTime: settleEndTime, LastSettleAmount: settleAmount}).Error; err != nil {
					logger.Error(err)
					return err
				}
			}
			var settleRecord SettleRecord
			if err := settleRecord.New(tx, o, enums.BusinessTypeEnum.PodInstance, settleReason, settleStartTime, settleEndTime, settleAmount); err != nil {
				logger.Error(err)
				return err
			}

		} else { //包月结算，只有在转按量计费的时候才使用，还有结算pod激励费用的时候
			if settleReason == enums.SettleReasonEnum.Shutdown { //包月的Pod 如果是关机操作就直接关机
				var instRecord InstRecord
				if err := tx.First(&instRecord, o.LastRecordId).Error; err != nil {
					logger.Error(err)
				} else {
					sec := uint(now.Sub(instRecord.StartupTime).Seconds())
					if err1 := tx.Model(&instRecord).Updates(InstRecord{Status: enums.InstanceStatusEnum.ShutdownInProgress, ShutdownTime: now, ShutdownReason: o.ShutdownReason, ShutdownRegularTime: o.ShutdownRegularTime, ShutdownRegularSave: o.ShutdownRegularSave, UsageDuration: sec}).Error; err1 != nil {
						logger.Error(err1)
					}
				}
				if err := tx.Model(o).Updates(Instance{Status: enums.InstanceStatusEnum.ShutdownComplete, ShutdownTime: now}).Error; err != nil {
					logger.Error(err)
					return err
				}
			} else if settleReason == enums.SettleReasonEnum.Sub2Usage {

				if o.EndTime.Before(now) {
					msg := enums.ChargingTypeEnum.Name(o.ChargingType) + "已过期，不做结算"
					logger.Info(trace, msg)
					if err := tx.Model(o).Updates(Instance{ChargingType: enums.ChargingTypeEnum.Usage, LastSettleTime: now, LastSettleAmount: decimal.Zero, SubIncrAmount: decimal.Zero}).Error; err != nil {
						logger.Error(err)
						return err
					}
					return nil
				}

				var gpuModel GpuModel
				if err := gpuModel.GetById(o.GpuModelId); err != nil {
					return err
				}
				settleStartTime := o.StartTime
				settleEndTime := time.Now().Truncate(time.Second)
				settleAmount := gpuModel.CalculateCostedAmount(o.ChargingType, settleStartTime, settleEndTime, o.Gpus)
				if settleAmount.LessThanOrEqual(decimal.Zero) {
					err := errors.New("计算消耗的金额出错")
					logger.Error(trace, err)
					return err
				}
				refundAmount := o.LastSettleAmount.Sub(settleAmount)
				if o.SubIncrAmount.GreaterThan(decimal.Zero) {
					refundAmount = o.SubIncrAmount.Sub(settleAmount)
				}

				o.LastSettleTime = settleEndTime
				o.LastSettleAmount = settleAmount
				o.EndTime = settleEndTime

				var settleRecord SettleRecord
				settleRecord.IsRefund = true
				if refundAmount.GreaterThan(decimal.Zero) {
					settleRecord.RefundAmount = refundAmount
					if err := settleRecord.New(tx, o, enums.BusinessTypeEnum.PodInstance, settleReason, settleStartTime, settleEndTime, settleAmount); err != nil {
						logger.Error(err)
						return err
					}
				}

				if err := tx.Model(o).Updates(Instance{ChargingType: enums.ChargingTypeEnum.Usage, EndTime: settleEndTime, LastSettleTime: settleEndTime, LastSettleAmount: settleAmount, SubIncrAmount: decimal.Zero}).Error; err != nil {
					logger.Error(err)
					return err
				}

			}

		}
		return nil
	})
}

func (o *Instance) SettleSimple(settleReason int) error { //该函数需要Redis锁实例ID
	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		trace := fmt.Sprintf("instanceUuid:%s 结算原因:%d ", o.Uuid, settleReason)
		now := time.Now().Truncate(time.Second)

		if settleReason != enums.SettleReasonEnum.Shutdown && settleReason != enums.SettleReasonEnum.Hourly {
			err := errors.New("结算原因不支持")
			logger.Error(trace, err, " instanceUuid:", o.Uuid, " settleReason:", settleReason)
			return err
		}

		if o.ChargingType != enums.ChargingTypeEnum.Usage && o.ChargingType != enums.ChargingTypeEnum.Free {
			err := errors.New("结算类型不支持")
			logger.Error(trace, err, " instanceUuid:", o.Uuid, " settleReason:", settleReason, " ChargingType:", o.ChargingType)
			return err
		}

		var user User
		if err := user.GetById(o.UserId); err != nil {
			logger.Error(trace, err)
			return err
		}
		if user.UserType == enums.UserTypeEnum.System || o.ChargingType == enums.ChargingTypeEnum.Free { //系统用户 或者免费实例 不结算
			if settleReason == enums.SettleReasonEnum.Shutdown {
				var instRecord InstRecord
				if err := tx.First(&instRecord, o.LastRecordId).Error; err != nil {
					logger.Error(trace, o.LastRecordId, err)
					return err
				} else {
					if instRecord.Status == enums.InstanceStatusEnum.ShutdownInProgress {
						logger.Info(trace, o.LastRecordId, "已经在关机中")
						return nil
					}
					if instRecord.Status == enums.InstanceStatusEnum.ShutdownComplete {
						logger.Info(trace, o.LastRecordId, "已经关机了")
						return nil
					}
					sec := uint(now.Sub(instRecord.StartupTime).Seconds())
					if err := tx.Model(&instRecord).Updates(InstRecord{Status: enums.InstanceStatusEnum.ShutdownInProgress, ShutdownTime: now, ShutdownReason: o.ShutdownReason, ShutdownDestroy: o.ShutdownDestroy, ShutdownTask: o.ShutdownTask, ShutdownRegularTime: o.ShutdownRegularTime, ShutdownRegularSave: o.ShutdownRegularSave, UsageDuration: sec}).Error; err != nil {
						logger.Error(trace, o.LastRecordId, err)
						return err
					}
					if err := tx.Model(o).Updates(Instance{Status: enums.InstanceStatusEnum.ShutdownComplete, ShutdownReason: o.ShutdownReason, ShutdownTime: now}).Error; err != nil {
						logger.Error(trace, o.LastRecordId, err)
						return err
					}
				}
				return nil
			}
			err := errors.New("系统用户或者免费实例 不结算")
			logger.Info(trace, err, o.ID, " ", settleReason)
			return nil
		}

		var pod Pod
		if err := pod.GetById(o.PodId); err != nil {
			logger.Error(err)
			return err
		}

		if pod.Category != enums.PodCategoryEnum.PodInstance {
			err := errors.New("暂不支持该Pod类型")
			logger.Error(trace, err, " instanceUuid:", o.Uuid, " settleReason:", settleReason, " PodId:", o.PodId)
			return err
		}

		if settleReason == enums.SettleReasonEnum.Hourly || settleReason == enums.SettleReasonEnum.Shutdown { //Pod服务费

			if pod.Category == enums.PodCategoryEnum.PodInstance {
				settleStartTime := o.StartupTime
				if o.LastPodSettleTime.After(settleStartTime) {
					settleStartTime = o.LastPodSettleTime
				}
				if settleStartTime.Before(common.NationalDay) { //没有启动过
					logger.Info(trace, "没有启动过，不做结算")
				} else {
					settleStartTime = settleStartTime.Truncate(time.Second) //2024-05-10 16:18:26 +0800 CST
					settleEndTime := now.Truncate(time.Second)
					if settleReason == enums.SettleReasonEnum.Hourly {
						timeFormat := "2006-01-02 15:00:00"
						timeString := settleEndTime.Format(timeFormat)
						if parsedTime, err := time.ParseInLocation(timeFormat, timeString, time.Local); err != nil {
							logger.Error(trace, err)
							return err
						} else {
							settleEndTime = parsedTime
						}
					}
					if settleEndTime.After(settleStartTime) {
						seconds := settleEndTime.Sub(settleStartTime).Seconds()
						settleAmount := pod.CalculateAmount(enums.ChargingTypeEnum.Usage, int(seconds))
						if settleAmount.GreaterThan(decimal.Zero) {
							var settleRecord SettleRecord
							if err := settleRecord.New(tx, o, enums.BusinessTypeEnum.PodService, settleReason, settleStartTime, settleEndTime, settleAmount); err != nil {
								logger.Error(trace, err)
								return err
							}
						}
						if err := tx.Model(o).Updates(Instance{LastPodSettleTime: settleEndTime}).Error; err != nil {
							logger.Error(trace, err)
							return err
						}
					}
				}
			}

		}

		if o.ChargingType == enums.ChargingTypeEnum.Usage { //算力费用 按量结算
			if o.Status != enums.InstanceStatusEnum.Running {
				logger.Info(trace, "实例没有开启，按量计费无需结算", o.Uuid)
				return nil
			}

			settleStartTime := o.StartupTime
			if o.LastSettleTime.After(settleStartTime) {
				settleStartTime = o.LastSettleTime
			}
			if settleStartTime.Before(now.AddDate(-10, 0, 0)) { //没有启动过
				logger.Info(trace, "没有启动过，不做结算", o.Uuid)
				return nil
			}

			settleStartTime = settleStartTime.Truncate(time.Second)
			settleEndTime := now.Truncate(time.Second)
			if settleReason == enums.SettleReasonEnum.Hourly {
				timeFormat := "2006-01-02 15:00:00"
				timeString := settleEndTime.Format(timeFormat)
				if parsedTime, err := time.ParseInLocation(timeFormat, timeString, time.Local); err != nil {
					logger.Error(trace, err)
					return err
				} else {
					settleEndTime = parsedTime
				}
			}
			if settleEndTime.Before(o.LastSettleTime) || settleEndTime.Equal(o.LastSettleTime) {
				err := errors.New("结算时间等于或在最后结算时间之前，不做结算处理")
				logger.Error(trace, err)
				return err
			}
			if settleEndTime.After(settleStartTime) == false {
				if settleReason == enums.SettleReasonEnum.Hourly {
					return nil
				}
				err := errors.New("最后结算时间在开始结算时间之前，不做结算处理")
				logger.Error(trace, err)
				return err
			}

			seconds := settleEndTime.Sub(settleStartTime).Seconds()
			var gpuModel GpuModel
			if err := gpuModel.GetById(o.GpuModelId); err != nil {
				logger.Error(trace, " 结算未找到Gpu类型", err, "GpuModelId:", o.GpuModelId)
				return err
			}
			settleAmount := gpuModel.CalculateAmount(o.ChargingType, int(seconds), o.Gpus)
			if o.NoCard == 1 {
				//logger.Info(trace, "无卡启动，算力计费无需结算", o.Uuid, "   ", settleEndTime)
				//settleAmount = decimal.Zero
				logger.Info(trace, "无卡启动，算力计费为", common.NoCardPrice, " 元每小时", o.Uuid, "   ", settleEndTime)
				settleAmount = common.NoCardPrice
			}

			if settleReason == enums.SettleReasonEnum.Shutdown {
				var instRecord InstRecord
				if err := tx.First(&instRecord, o.LastRecordId).Error; err != nil {
					logger.Error(trace, err)
				} else {
					sec := uint(now.Sub(instRecord.StartupTime).Seconds())
					if err1 := tx.Model(&instRecord).Updates(InstRecord{Status: enums.InstanceStatusEnum.ShutdownInProgress, ShutdownTime: settleEndTime, ShutdownReason: o.ShutdownReason, ShutdownDestroy: o.ShutdownDestroy, ShutdownTask: o.ShutdownTask, ShutdownRegularTime: o.ShutdownRegularTime, ShutdownRegularSave: o.ShutdownRegularSave, UsageDuration: sec}).Error; err1 != nil {
						logger.Error(trace, err1)
					}
				}
				if err := tx.Model(o).Updates(Instance{Status: enums.InstanceStatusEnum.ShutdownComplete, ShutdownReason: o.ShutdownReason, ShutdownTime: settleEndTime, LastSettleTime: settleEndTime, LastSettleAmount: settleAmount}).Error; err != nil {
					logger.Error(trace, err)
					return err
				}
			} else {
				if err := tx.Model(o).Updates(Instance{LastSettleTime: settleEndTime, LastSettleAmount: settleAmount}).Error; err != nil {
					logger.Error(trace, err)
					return err
				}
			}

			if settleAmount.Equal(decimal.Zero) {
				logger.Info(trace, "结算金额为0，不做流水")
			} else {
				var settleRecord SettleRecord
				if err := settleRecord.New(tx, o, enums.BusinessTypeEnum.PodInstance, settleReason, settleStartTime, settleEndTime, settleAmount); err != nil {
					logger.Error(trace, err)
					return err
				}
			}
		}
		return nil
	})
}

func (o *Instance) CalculateEndTime(chargingType int, startTime time.Time, num int) time.Time {
	if chargingType == enums.ChargingTypeEnum.Day {
		return startTime.Add(time.Duration(num) * 24 * time.Hour)
	} else if chargingType == enums.ChargingTypeEnum.Week {
		return startTime.Add(time.Duration(num) * 7 * 24 * time.Hour)
	} else if chargingType == enums.ChargingTypeEnum.Month {
		return startTime.AddDate(0, num, 0)
	}
	return common.DefaultTime
}
