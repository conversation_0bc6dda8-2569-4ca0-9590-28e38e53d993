package model

import (
	"cpn-ai/common/logger"
	"cpn-ai/config"
	"encoding/json"
	"fmt"
	"reflect"

	"gorm.io/gorm"
)

type M = map[string]any
type A = []any

type InstitStatus = int

const (
	InstitHide InstitStatus = iota + 1
	InstitShow
)

const institPath = "cpn/instit_static/"

var InstitStatic = config.DiffusionFilePath + institPath

func institUrl(size, name string) string {
	if name == "" {
		return ""
	}
	return config.DiffusionDomain + institPath + size + name
}
func InstitLogoUrl(name string) string {
	return institUrl("logo_", name)
}
func InstitCoverUrl(name string) string {
	return institUrl("", name)
}
func InstitAvatarUrl(name string) string {
	return institUrl("avatar_", name)
}
func InstitContactUrl(name string) string {
	return institUrl("", name)
}
func InstitVideoUrl(name string) string {
	return institUrl("", name)
}
func InstitLinkUrl(name string) string {
	return institUrl("link_", name)
}

type Instit struct {
	gorm.Model
	Uuid   string       `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串id;uniqueIndex"`
	UserId uint         `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户id;index"`
	Name   string       `json:"name" gorm:"type:varchar(50);not null;default:'';comment:名称"`
	Intro  string       `json:"intro" gorm:"type:varchar(1500);not null;default:'';comment:简介"`
	Logo   string       `json:"logo" gorm:"type:varchar(100);not null;default:'';comment:标识图片"`
	Cover  string       `json:"cover" gorm:"type:varchar(100);not null;default:'';comment:封面图片"`
	Detail string       `json:"detail" gorm:"type:text;comment:详情"`
	Status InstitStatus `json:"status" gorm:"type:integer;not null;default:1;comment:状态：1不展示，2展示"`
}

const InstitTableName = "T_Instit"

func (Instit) TableName() string {
	return InstitTableName
}
func (Instit) Id2Uuid(a []uint) (map[uint]string, error) {
	var rows []struct {
		Id   uint
		Uuid string
	}
	err := DB.Debug().Table(InstitTableName).Select("id", "uuid").Where("id in ?", a).Scan(&rows).Error
	m := make(map[uint]string, len(rows))
	for _, row := range rows {
		m[row.Id] = row.Uuid
	}
	return m, err
}

func (o *Instit) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *Instit) Delete() error {
	return DB.Debug().Unscoped().Delete(o).Error
}

func (o *Instit) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Instit) GetByIdFromCache(id uint) error {
	key := fmt.Sprintf("model_instit_%d", id)
	if data, err := cache.Get(key); err == nil {
		if err := json.Unmarshal(data, o); err == nil {
			return nil
		} else {
			logger.Error(key, " err:", err)
		}
	}

	if err := DB.First(o, id).Error; err != nil {
		return err
	} else {
		if data, err := json.Marshal(*o); err == nil {
			if err := cache.Set(key, data); err == nil {
				return nil
			}
		}
		return nil
	}
}

func (o *Instit) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}

func (o *Instit) List(dest any, queryParams M, page, pageSize int) (total int64, _ error) {
	tx := DB.Debug().Model(o)
	if _, ok := queryParams["id"]; ok {
		tx.Where("id=?", queryParams["id"])
	} else {
		if _, ok := queryParams["user_id"]; ok {
			tx.Where("user_id=?", queryParams["user_id"])
		}

		if _, ok := queryParams["status"]; ok {
			tx.Where("status=?", queryParams["status"])
		}

		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	tx.Scan(dest)
	if _, ok := queryParams["id"]; ok {
		v := reflect.ValueOf(dest)
		if v.Kind() == reflect.Pointer {
			v = v.Elem()
		}
		if v.Kind() == reflect.Slice && v.Len() == 1 {
			total = 1
		}
	}
	return total, tx.Error
}

func (o *Instit) SetName(s string) error {
	return DB.Model(o).Updates(M{"name": s}).Error
}
func (o *Instit) SetIntro(s string) error {
	return DB.Model(o).Updates(M{"intro": s}).Error
}
func (o *Instit) SetLogo(s string) error {
	return DB.Model(o).Updates(M{"logo": s}).Error
}
func (o *Instit) SetCover(s string) error {
	return DB.Model(o).Updates(M{"cover": s}).Error
}
func (o *Instit) SetDetail(s string) error {
	return DB.Model(o).Updates(M{"detail": s}).Error
}
func (o *Instit) SetStatus(i InstitStatus) error {
	return DB.Model(o).Updates(M{"status": i}).Error
}

func (o *Instit) Create() error {
	return DB.Model(o).Debug().Create(o).Error
}
func (o *Instit) Updates(m M) error {
	return DB.Model(o).Debug().Updates(m).Error
}
