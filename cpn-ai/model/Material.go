package model

import (
	"fmt"
	"gorm.io/gorm"
)

type Material struct {
	gorm.Model
	Uuid   string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID;uniqueIndex"`
	Title  string `json:"title" gorm:"type:varchar(150);not null;default:'';comment:标题"`
	Path   string `json:"path" gorm:"type:varchar(250);not null;default:'';comment:相对路径"`
	Ext    string `json:"ext" gorm:"type:varchar(50);not null;default:'';comment:文件后缀"`
	Remark string `json:"remark" gorm:"type:varchar(250);not null;default:'';comment:备注"`
	Status int    `json:"status" gorm:"type:int;not null;default:0;comment:状态 0无效 1有效"` //
}

func (Material) TableName() string {
	return "T_Material"
}

func (o *Material) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *Material) Delete() error {
	return DB.Debug().Unscoped().Delete(o).Error
}

func (o *Material) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Material) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}

func (o *Material) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["stu_user_id"]; ok {
			tx.Where("stu_user_id=?", queryParm["stu_user_id"])
		}
		if _, ok := queryParm["class_room_id"]; ok {
			kw := "%" + fmt.Sprintf("%d", queryParm["class_room_id"]) + "%"
			tx.Where("class_room_ids like ?", kw)
		}
		if _, ok := queryParm["kw"]; ok {
			kw := "%" + queryParm["kw"].(string) + "%"
			tx.Where("first_mobile like ?  ?", kw)
		}
		if _, ok := queryParm["status"]; ok {
			tx.Where(fmt.Sprintf("status %s", queryParm["status"]))
		}
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	tx.Scan(dest)
	return total, tx.Error
}

func (o *Material) SetStatus(status int) error {
	return DB.Model(o).Update("status", status).Error
}

func (o *Material) SetInfo(title string, remark string) error {
	return DB.Model(o).Updates(Material{Title: title, Remark: remark}).Error
}
