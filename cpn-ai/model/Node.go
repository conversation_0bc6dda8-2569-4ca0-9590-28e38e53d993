package model

import (
	"gorm.io/gorm"
	"time"
)

type Node struct {
	gorm.Model
	Uuid          string    `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID"`
	Name          string    `json:"name" gorm:"type:varchar(50);not null;default:'';comment:节点名称(唯一)"`
	Title         string    `json:"title" gorm:"type:varchar(50);not null;default:'';comment:节点标题"`
	ApiBaseUrl    string    `json:"api_base_url" gorm:"type:varchar(200);not null;default:'';comment:api地址"`
	Domain        string    `json:"domain" gorm:"type:varchar(50);not null;default:'';comment:节点域名"`
	FreeGpus      int       `json:"free_gpus" gorm:"type:int;not null;default:0;comment:当前空余显卡数量"`
	TotalGpus     int       `json:"total_gpus" gorm:"type:int;not null;default:0;comment:总显卡数量"`
	TotalInstance int       `json:"total_instance" gorm:"type:int;not null;default:0;comment:实例数量"`
	TotalVirtual  int       `json:"total_virtual" gorm:"type:int;not null;default:0;comment:机器数量"`
	LastCheckTime time.Time `json:"last_check_time" gorm:"type:datetime;default:'1900-01-01';comment:最后上报时间"`
	AccessToken   string    `json:"access_token" gorm:"type:varchar(32);not null;default:'';comment:登录token"` // this token is for system management
	Remark        string    `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	Status        int       `json:"status" gorm:"type:int;not null;default:0;comment:状态"`
}

//Price {"hour":1.66,"day":38.65,"week":245.43,"month":847.80}

func (Node) TableName() string {
	return "T_Node"
}

func (o *Node) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *Node) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Node) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}

func (o *Node) List(dest interface{}, id uint, status int, page int, pageSize int, order string) (int64, error) {
	if order == "" {
		order = "id asc"
	}
	var total int64
	tx := DB.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	} else {
		if status >= 0 {
			tx.Where("status=?", status)
		}
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order(order).Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Node) SetAccessToken(token string) error {
	return DB.Model(o).Update("access_token", token).Error
}

func (o *Node) SetStatus(status int) error {
	return DB.Model(o).Update("status", status).Error
}

func (o *Node) Updates(m map[string]interface{}) error {
	return DB.Model(o).Debug().Updates(m).Error
}
