package model

//import (
//	"gorm.io/gorm"
//)
//
//type ReserveOrderDetail struct {
//	gorm.Model
//	GpuModelUuid string `json:"gpu_model_uuid" gorm:"type:char(36);not null;comment:显卡型号UUID"`
//	InstanceUUID string `json:"instance_uuid" gorm:"type:char(36);not null;comment:实例UUID"`
//	Status       int    `json:"status" gorm:"type:int;not null;default:0;comment:状态 参考InstanceStatusEnum"`
//}
//
//func (ReserveOrderDetail) TableName() string {
//	return "T_ReserveOrderDetail"
//}
//
//func (d *ReserveOrderDetail) GetByReserveUuid(dest interface{}, reserveUuid string) error {
//	return DB.Where("reserve_uuid = ?", reserveUuid).Find(dest).Error
//}
