package model

import (
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
)

type RewardBalance struct {
	gorm.Model
	UserId         uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	OrderType      int             `json:"order_type" gorm:"type:integer;not null;default:0;comment:业务类型"`
	OrderNo        string          `json:"order_no" gorm:"type:varchar(50);not null;default:'';comment:业务单号"`
	BeforeOccurred decimal.Decimal `json:"before_occurred" gorm:"type:decimal(16, 8);not null;default:0;comment:变动前额度"`
	OccurredAmount decimal.Decimal `json:"occurred_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:发生额"`
	AfterOccurred  decimal.Decimal `json:"after_occurred" gorm:"type:decimal(16, 8);not null;default:0;comment:变动后额度"`
	Show           string          `json:"show" gorm:"type:varchar(50);not null;default:'';comment:前端显示文本"`
	Info           string          `json:"info" gorm:"type:json;comment:详情"`
	Remark         string          `json:"remark" gorm:"type:varchar(250);not null;default:'';comment:备注"`
	Operator       string          `json:"operator" gorm:"type:varchar(50);not null;default:'';comment:操作者"`
	OperatorId     uint            `json:"operator_id" gorm:"type:bigint;not null;default:0;comment:操作者ID"`
}

func (RewardBalance) TableName() string {
	return "T_RewardBalance"
}

func (o *RewardBalance) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["card_id"]; ok {
			tx.Where("card_id=?", queryParm["card_id"])
		}
		if _, ok := queryParm["pod_id"]; ok {
			tx.Where("pod_id=?", queryParm["pod_id"])
		}
		if _, ok := queryParm["instance_id"]; ok {
			tx.Where("instance_id=?", queryParm["instance_id"])
		}
		if _, ok := queryParm["order_type"]; ok {
			tx.Where("order_type=?", queryParm["order_type"])
		}
		if _, ok := queryParm["kw"]; ok {
			tx.Where("remark like ?", "%"+queryParm["kw"].(string)+"%")
		}

		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
		tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	if dest != nil {
		tx.Scan(dest)
	}
	return total, tx.Error
}

func (o *RewardBalance) ListForCheckBalance(dest interface{}, userId uint, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o).Where("user_id=?", userId)
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id asc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *RewardBalance) GetByOrderNo(orderNo string) error {
	return DB.First(o, "order_no=?", orderNo).Error
}

func (o *RewardBalance) ExistsOrderNo(orderNo string) (bool, error) {
	err := DB.First(o, "order_no=?", orderNo).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return false, nil // 记录不存在
	}
	if err != nil {
		return false, err // 出现其他错误
	}
	return true, nil // 记录存在
}

func (balance *RewardBalance) New(tx *gorm.DB) error {

	if err := balance.CheckNewObject(); err != nil {
		logger.Error(err)
		return err
	}

	var user User
	if err := tx.First(&user, balance.UserId).Error; err != nil || user.ID == 0 {
		logger.Error(err, " orderNo:", balance.OrderNo, " balance.OccurredAmount:", balance.OccurredAmount, " userID:", user.ID)
		return err
	}
	if balance.OccurredAmount.LessThan(decimal.Zero) { //小于0，提现成功，解锁并且添加流水
		var amountFrozen AmountFrozen
		if err := tx.First(&amountFrozen, "order_no=?", balance.OrderNo).Error; err != nil {
			logger.Error(err)
			return err
		}

		after := user.RewardAmount.Add(balance.OccurredAmount)
		balance.BeforeOccurred = user.RewardAmount
		balance.AfterOccurred = after

		if !amountFrozen.Amount.Equal(balance.OccurredAmount.Neg()) || amountFrozen.UserId != balance.UserId || amountFrozen.Status != 1 {
			err := errors.New("冻结记录信息不匹配")
			logger.Error(err)
			return err
		}
		if err := amountFrozen.UnFrozenDeduct(tx, balance.OrderNo, &user); err != nil { //解冻函数里已经更新的用户表
			logger.Error("冻结失败", err)
			return err
		}
		if err := tx.Save(balance).Error; err != nil {
			logger.Error(err)
			return err
		}
	} else {

		after := user.RewardAmount.Add(balance.OccurredAmount)
		balance.BeforeOccurred = user.RewardAmount
		balance.AfterOccurred = after

		if err := tx.Save(balance).Error; err != nil {
			logger.Error(err)
			return err
		}

		result := tx.Model(&user).Updates(map[string]interface{}{"reward_amount": after})
		if err := result.Error; err != nil {
			logger.Error(err)
			return err
		}

		if result.RowsAffected != 1 {
			err := errors.New(fmt.Sprintf("更新用户amount失败 userId:%d", user.ID))
			logger.Error(err)
			return err
		}
	}

	return nil
}

func (balance *RewardBalance) GetNewObject(orderNo string, userId uint, orderType int, occurred decimal.Decimal, show string, info string, remark string, operatorId uint, operator string) error {
	if info == "" {
		info = "{}"
	}
	balance.OrderNo = orderNo
	balance.UserId = userId
	balance.OrderType = orderType
	balance.Show = show
	balance.Info = info
	balance.Remark = remark
	balance.Operator = operator
	balance.OperatorId = operatorId
	balance.OccurredAmount = occurred
	err := balance.CheckNewObject()
	if err != nil {
		return err
	}
	return nil
}

func (balance *RewardBalance) CheckNewObject() error {
	if balance.ID != 0 {
		return errors.New("存在ID")
	}
	if balance.OrderNo == "" {
		return errors.New("单号不正确")
	}

	if balance.UserId <= 0 {
		return errors.New("未找到用户")
	}
	if balance.OccurredAmount.Equal(decimal.Zero) {
		return errors.New("没有发生金额")
	}
	if balance.OrderType == 0 {
		return errors.New("没有业务类型")
	}

	if balance.OrderType == enums.OrderTypeEnum.Reward {
		if balance.OccurredAmount.LessThanOrEqual(decimal.Zero) {
			return errors.New("金额与业务类型不匹配")
		}
	}
	if balance.OrderType == enums.OrderTypeEnum.Withdraw {
		if balance.OccurredAmount.GreaterThanOrEqual(decimal.Zero) {
			return errors.New("金额与业务类型不匹配")
		}
		if balance.OccurredAmount.LessThan(decimal.NewFromInt(-1000)) {
			return errors.New("单笔提现金额不能大于1000")
		}
	}
	return nil
}

func (o *RewardBalance) StatsUserAmountCost(userId uint, rewardMonth time.Time) (decimal.Decimal, error) {
	var totalAmount decimal.Decimal

	firstOfMonth := time.Date(rewardMonth.Year(), rewardMonth.Month(), 1, 0, 0, 0, 0, rewardMonth.Location())
	lastOfMonth := firstOfMonth.AddDate(0, 1, 0)

	tx := DB.Debug().Model(o).
		Select("COALESCE(sum(occurred_amount)) as total_occurred").
		Where("user_id = ?", userId).
		Where("card_id=0").
		Where("occurred_amount<0").
		Where("(created_at >=? and created_at <?)", firstOfMonth, lastOfMonth).
		Scan(&totalAmount)
	return totalAmount, tx.Error
}
