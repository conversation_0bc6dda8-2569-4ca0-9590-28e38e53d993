package model

import (
	"gorm.io/gorm"
)

type Team struct {
	gorm.Model
	Uuid   string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:Team字符串ID;uniqueIndex"`
	UserId uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	Title  string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:Team标题"`
	Status int    `json:"status" gorm:"type:int;not null;default:0;comment:状态 1可用"`
}

func (Team) TableName() string {
	return "T_Team"
}
