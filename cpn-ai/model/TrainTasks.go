package model

import (
	"gorm.io/gorm"
	"time"
)

type TrainTasks struct {
	gorm.Model
	Uuid          string    `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:训练集字符串ID;uniqueIndex"`
	UserId        uint      `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	TrainTagsUuid string    `json:"train_tags_uuid" gorm:"type:varchar(50);not null;default:'';comment:打标数据集字符串ID;index"`
	Title         string    `json:"title" gorm:"type:varchar(50);not null;default:'';comment:文本"`
	TrainParams   string    `json:"train_params" gorm:"type:json;comment:训练参数"`
	Count         int       `json:"count" gorm:"type:int;not null;default:0;comment:图片集数量"`
	Remark        string    `json:"remark" gorm:"type:varchar(150);not null;default:'';comment:备注"`
	StartAt       time.Time `json:"start_at" gorm:"type:datetime;default:'1900-01-01';comment:训练开始时间"`
	EndAt         time.Time `json:"end_at" gorm:"type:datetime;default:'1900-01-01';comment:训练结束时间"`
	LastUseAt     time.Time `json:"last_use_at" gorm:"type:datetime;default:'1900-01-01';comment:最后使用时间"`
	Reason        string    `json:"reason" gorm:"type:varchar(150);not null;default:'';comment:原因"`
	Status        int       `json:"status" gorm:"type:int;not null;default:0;comment:状态 1排队中 2训练中 3训练完成 4训练失败"`
}

func (TrainTasks) TableName() string {
	return "T_TrainTasks"
}
