package model

import "gorm.io/gorm"

type UserAuth struct {
	gorm.Model
	UserId       uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	UserType     int    `json:"user_type" gorm:"type:int;not null;default:1;comment:用户类型 1算云用户"`
	IdentityType string `json:"identity_type" gorm:"type:varchar(50);not null;default:'';comment:登录类型（手机号 邮箱 用户名）或第三方应用名称（微信 微博等）"` //email phone weibo username weixin
	Identifier   string `json:"identifier" gorm:"type:varchar(50);not null;default:'';comment:标识（手机号 邮箱 用户名或第三方应用的唯一标识）"`
	Credential   string `json:"credential" gorm:"type:varchar(20);not null;default:'';comment:密码凭证（站内的保存密码，站外的不保存或保存token）"`
	Password     string `json:"password" gorm:"type:varchar(100);not null;default:'';comment:密码"`
}

//register_time
//register_ip
//login_time
//login_ip
