package model

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type UserToken struct {
	gorm.Model
	Uuid       string          `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID"`
	UserId     uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	Name       string          `json:"name" gorm:"type:varchar(50);not null;default:'';comment:名称"`
	SecretKey  string          `json:"secret_key" gorm:"type:varchar(100);not null;default:'';comment:秘钥"`
	CostAmount decimal.Decimal `json:"cost_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:消费累计金额"`
	MaxAmount  decimal.Decimal `json:"max_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:最大消耗金额"`
	Remark     string          `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	Status     int             `json:"status" gorm:"type:int;not null;default:1;comment:状态"`
}

func (UserToken) TableName() string {
	return "T_UserToken"
}

func (o *UserToken) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *UserToken) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *UserToken) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}

func (o *UserToken) GetByKey(secretKey string) error {
	return DB.First(o, "secret_key=?", secretKey).Error
}

func (o *UserToken) IncreaseConsumedAmount(cost decimal.Decimal) error {
	return DB.Model(o).Updates(UserToken{CostAmount: o.CostAmount.Add(cost)}).Error
}

func (o *UserToken) SetName(name string) error {
	return DB.Model(o).Updates(UserToken{Name: name}).Error
}

func (o *UserToken) SetStatus(stauts int) error {
	return DB.Model(o).Update("status", stauts).Error
}

func (o *UserToken) DeleteByUuid(uuid string) error {
	err := DB.Debug().Where("uuid=?", uuid).Delete(o).Error
	return err
}

func (o *UserToken) List(dest interface{}, userId uint, status int, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if userId > 0 {
		tx.Where("user_id=?", userId)
	}
	if status > 0 {
		tx.Where("status=?", status)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}
