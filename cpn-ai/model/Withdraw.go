package model

import (
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"errors"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type WithdrawPayStatusEnum int

const (
	WithdrawPayStatusWating  WithdrawPayStatusEnum = 0
	WithdrawPayStatusPaying                        = 1
	WithdrawPayStatusSuccess                       = 2
	WithdrawPayStatusFailed                        = 3
	WithdrawPayStatusNoTrade                       = 4
	WithdrawPayStatusClosed                        = 5
)

func WithdrawPayStatusName(i WithdrawPayStatusEnum) string {
	switch i {
	case WithdrawPayStatusWating:
		return "待付款"
	case WithdrawPayStatusPaying:
		return "付款中"
	case WithdrawPayStatusSuccess:
		return "付款成功"
	case WithdrawPayStatusFailed:
		return "付款失败"
	case WithdrawPayStatusNoTrade:
		return "交易不存在"
	case WithdrawPayStatusClosed:
		return "交易关闭"
	}
	return ""
}

type Withdraw struct {
	gorm.Model
	Nanoid            string                `json:"nanoid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID;uniqueIndex"`
	BusinessType      int                   `json:"business_type" gorm:"type:integer;not null;default:0;comment:业务类型"`
	OrderType         int                   `json:"order_type" gorm:"type:integer;not null;default:0;comment:订单类型"`
	OrderId           uint                  `json:"order_id" gorm:"type:bigint;not null;default:0;comment:关联表ID,根据业务类型"`
	WithdrawAccountId uint                  `json:"withdraw_account_id" gorm:"type:bigint;not null;default:0;comment:用户提现账户ID"`
	OrderNo           string                `json:"order_no" gorm:"type:varchar(50);not null;default:'';comment:商户系统内部订单号，冻结资金的单号"`
	UserId            uint                  `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	OutTradeNo        string                `json:"out_trade_no" gorm:"type:varchar(50);not null;default:'';comment:提现生成的商户系统内部订单号，用于查询第三方支付"`
	Amount            decimal.Decimal       `json:"amount" gorm:"type:decimal(16,2);not null;default:0;comment:提现金额"`
	Description       string                `json:"description" gorm:"type:varchar(50);not null;default:'';comment:描述，Image形象店-深圳腾大-QQ公仔"`
	Gateway           string                `json:"gateway" gorm:"type:varchar(10);not null;default:'';comment:支付方式 对应枚举PayGatewayEnum"`
	TrueName          string                `json:"true_name" gorm:"type:varchar(150);not null;default:'';comment:真实姓名"`
	BankName          string                `json:"bank_name" gorm:"type:varchar(150);not null;default:'';comment:开户行"`
	PayInfo           string                `json:"pay_info" gorm:"type:json;comment:支付信息"`
	PayChannel        string                `json:"pay_channel" gorm:"type:varchar(50);not null;default:'';comment:收款渠道 支付渠道"`
	PayAccount        string                `json:"pay_account" gorm:"type:varchar(50);not null;default:'';comment:收款账号"`
	PayRequestTime    time.Time             `json:"pay_request_time" gorm:"type:datetime;default:'1900-01-01';comment:支付请求时间"`
	PayTradeId        string                `json:"pay_trade_id" gorm:"type:varchar(50);not null;default:'';comment:支付平台的交易单号"`
	PayRspJson        string                `json:"pay_rsp_json" gorm:"type:json;comment:接口调用返回的数据"`
	PayCallbackJson   string                `json:"pay_callback_json"  gorm:"type:json;comment:支付平台回调数据"`
	PayCallbackTime   time.Time             `json:"pay_callback_time" gorm:"type:datetime;default:'1900-01-01';comment:支付平台回调时间"`
	PaySuccessTime    time.Time             `json:"pay_success_time" gorm:"type:datetime;default:'1900-01-01';comment:支付成功时间"`
	CustomParam       string                `json:"custom_param" gorm:"type:json;comment:携带参数"`
	Remark            string                `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:内部备注"`
	PayStatus         WithdrawPayStatusEnum `json:"pay_status" gorm:"type:tinyint;not null;default:0;comment:状态 0新创建 1付款中 2已付款 3付款失败 4交易不存在 9已取消"`
}

//State           int             `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0新创建 1已付款 2已退款 3交易不存在 9已取消"`

func (Withdraw) TableName() string {
	return "T_Withdraw"
}

func (w *Withdraw) BeforeSave(tx *gorm.DB) (err error) {
	if w.ID == 0 {
		if w.PayInfo == "" {
			w.PayInfo = "{}" // 设置为空对象
		}
		if w.PayRspJson == "" {
			w.PayRspJson = "{}" // 设置为空对象
		}
		if w.PayCallbackJson == "" {
			w.PayCallbackJson = "{}" // 设置为空对象
		}
		if w.CustomParam == "" {
			w.CustomParam = "{}" // 设置为空对象
		}
	}
	return nil
}

/*
func (o *Withdraw) GetNewObject(wAccountId uint, businessType int, orderType int, userId uint, amount decimal.Decimal, payChannle string, payAccount string) error {
	*o = Withdraw{
		Nanoid:            utils.Generate12NanoId(),
		BusinessType:      businessType,
		OrderType:         orderType,
		UserId:            userId,
		WithdrawAccountId: wAccountId,
		Amount:            amount,
		PayChannel:        payChannle,
		PayAccount:        payAccount,
	}
	if err := o.CheckNewObject(); err != nil {
		return err
	}
	return nil
}*/

func (o *Withdraw) CheckNewObject() error {
	if o.ID > 0 {
		return errors.New("is not new object")
	}
	if o.Nanoid == "" {
		return errors.New("nanoid is empty")
	}
	if o.BusinessType <= 0 {
		return errors.New("businessType is zero")
	}
	if o.OrderType <= 0 {
		return errors.New("orderType is zero")
	}
	if o.UserId <= 0 {
		return errors.New("userId is empty")
	}
	if o.WithdrawAccountId <= 0 {
		return errors.New("wAccountId is zero")
	}
	if o.Amount.LessThanOrEqual(decimal.Zero) {
		return errors.New("amount is zero")
	}
	if o.PayChannel == "" {
		return errors.New("payChannle is empty")
	}
	if o.PayAccount == "" {
		return errors.New("PayAccount is empty")
	}
	return nil
}

func (o *Withdraw) GetById(id uint) error {
	err := DB.First(o, id).Error
	return err
}

func (o *Withdraw) GetByNanoid(nanoid string) error {
	err := DB.Debug().First(o, "nanoid = ?", nanoid).Error
	return err
}

func (o *Withdraw) GetByOutTradeNo(outTradeNo string) error {
	err := DB.Debug().First(o, "out_trade_no = ?", outTradeNo).Error
	return err
}

func (o *Withdraw) GetByPayTradeId(payType string, tradeId string) error {
	return DB.Debug().First(o, "pay_channel=? and  pay_trade_id = ?", payType, tradeId).Error
}

func (o *Withdraw) GetListForCheck(lastId uint, limit int) ([]Withdraw, error) {
	ary := make([]Withdraw, 0)
	tx := DB.Debug().Model(o).Where("id>? and pay_status<=1 ", lastId)
	tx.Limit(limit).Order("id asc").Scan(&ary)
	return ary, tx.Error
}

func (o *Withdraw) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["order_no"]; ok {
			tx.Where("order_no=?", queryParm["order_no"])
		}
		if _, ok := queryParm["out_trade_no"]; ok {
			tx.Where("out_trade_no=?", queryParm["out_trade_no"])
		}
		if _, ok := queryParm["business_type"]; ok {
			tx.Where("business_type=?", queryParm["business_type"])
		}
		if _, ok := queryParm["order_id"]; ok {
			tx.Where("order_id=?", queryParm["order_id"])
		}
		if _, ok := queryParm["pay_status"]; ok {
			tx.Where("pay_status=?", queryParm["pay_status"])
		}
		tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *Withdraw) PaySuccess(payTradeId string, outTradeNo string, payCallbackJson string, paySuccessTime time.Time) error {
	if o.PayStatus != WithdrawPayStatusPaying {
		err := errors.New("记录状态不正确")
		logger.Error(err)
		return err
	}
	if o.OutTradeNo != outTradeNo {
		err := errors.New("单号不一致")
		logger.Error(err)
		return err
	}
	return DB.Transaction(func(tx *gorm.DB) error {

		{
			result := tx.Model(o).Updates(map[string]interface{}{"pay_status": WithdrawPayStatusSuccess, "pay_trade_id": payTradeId, "pay_callback_json": payCallbackJson, "pay_success_time": paySuccessTime, "pay_callback_time": time.Now()})
			if err := result.Error; err != nil {
				return err
			}
		}

		show := fmt.Sprintf("佣金提现")
		var balance RewardBalance
		if err := balance.GetNewObject(o.OrderNo, o.UserId, enums.OrderTypeEnum.Withdraw, o.Amount.Neg(), show, "", "", o.UserId, ""); err != nil {
			logger.Error(err)
			return err
		}
		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}

		//var user User
		//if err := tx.First(&user, o.UserId).Error; err != nil {
		//	logger.Error(err)
		//	return err
		//}
		//
		//var amountFrozen AmountFrozen
		//if err := tx.First(&amountFrozen, "order_no=?", o.OrderNo).Error; err != nil {
		//	logger.Error(err)
		//	return err
		//}
		//if err := amountFrozen.UnFrozenDeduct(tx, o.OrderNo, &user); err != nil {
		//	logger.Error(err)
		//	return err
		//}

		var withdrawApply WithdrawApply
		if err := tx.First(&withdrawApply, o.OrderId).Error; err != nil {
			logger.Error(err)
			return err
		}
		if withdrawApply.OrderNo != o.OrderNo {
			err := errors.New("订单号不一致")
			logger.Error(err)
			return err
		}
		if withdrawApply.PayStatus != WithdrawPayStatusPaying {
			err := errors.New("支付状态不正确")
			logger.Error(err)
			return err
		}

		{
			result := tx.Model(&withdrawApply).Updates(map[string]interface{}{"pay_status": WithdrawPayStatusSuccess})
			if result.Error != nil {
				return result.Error
			}
			if result.RowsAffected != 1 {
				return errors.New(fmt.Sprintf("更新申请记录失败,影响记录为%d", result.RowsAffected))
			}
		}

		return nil
	})
}

func (o *Withdraw) SetPayRspJson(rspJson string) error {
	return DB.Model(o).Updates(map[string]interface{}{"pay_rsp_json": rspJson, "pay_request_time": time.Now()}).Error
}

func (o *Withdraw) SetPayCallbackJson(callbackJson string, payStatus WithdrawPayStatusEnum) error {
	return DB.Model(o).Updates(map[string]interface{}{"pay_status": payStatus, "pay_callback_json": callbackJson, "pay_callback_time": time.Now()}).Error
}

func (o *Withdraw) StatisticRecharge(start time.Time, end time.Time) (int64, decimal.Decimal, error) {
	var total int64
	var amount decimal.Decimal
	tx := DB.Debug().Model(o).Where("created_at > ? AND created_at < ?", start, end).Where("state = 1")
	if err := tx.Pluck("SUM(amount) as totalAmount", &amount).Error; err != nil {
		return 0, amount, err
	}
	if err := tx.Count(&total).Error; err != nil {
		return 0, amount, err
	}
	return total, amount, tx.Error
}

func (o *Withdraw) Stats(dest interface{}) error {
	tx := DB.Debug().Model(o).
		Select("DATE(pay_time) as pay_date, SUM(amount) as total_amount ,count(distinct user_id) as user_count,count( user_id) as pay_count")
	tx.Where("status=1")
	tx.Group("DATE(pay_time)")
	tx.Order("pay_date desc").Scan(dest)
	return tx.Error
}

func (o *Withdraw) ExistsPayTradeId(payType string, tradeId string) (bool, error) {
	var recharge Recharge
	if err := DB.Debug().First(&recharge, "pay_channel=? and  pay_trade_id = ?", payType, tradeId).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

//func (o *Withdraw) SetState(payCallbackJson string, state int) error {
//	return DB.Model(o).Updates(Recharge{PayCallbackJson: payCallbackJson, State: state}).Error
//}

func (o *Withdraw) SetPrepayId(prepayId string) error {
	return DB.Model(o).Updates(Recharge{PayTradeId: prepayId}).Error
}

func (o *Withdraw) SetAppleVerifyInfo(jsonStr string) error {
	return DB.Model(o).Updates(Recharge{PayCallbackJson: jsonStr}).Error
}
