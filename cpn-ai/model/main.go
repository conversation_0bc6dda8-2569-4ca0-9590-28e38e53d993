package model

import (
	"context"
	"cpn-ai/common"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/internal/ccm"
	"os"
	"strings"
	"time"

	"github.com/allegro/bigcache/v3"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

var DB *gorm.DB

var cache *bigcache.BigCache // 内存缓存
func createRootAccountIfNeed() error {
	var user User
	//if user.Status != common.UserStatusEnabled {
	if err := DB.First(&user).Error; err != nil {
		common.SysLog("no user exists, create a root user for you: username is root, password is 123456")
		hashedPassword, err := common.Password2Hash("123456")
		if err != nil {
			return err
		}
		rootUser := User{
			Username:    "root",
			Password:    hashedPassword,
			Role:        common.RoleRootUser,
			Status:      common.UserStatusEnabled,
			DisplayName: "Root User",
			//AccessToken: common.GetUUID(),
			//Quota:       *********,
		}
		DB.Create(&rootUser)
	}
	return nil
}

func chooseDB() (*gorm.DB, error) {
	SQL_DSN := os.Getenv("SQL_DSN")
	if SQL_DSN == "" {
		SQL_DSN = config.SQL_DSN
	}

	if SQL_DSN != "" {
		dsn := SQL_DSN
		if strings.HasPrefix(dsn, "postgres://") {
			// Use PostgreSQL
			common.SysLog("using PostgreSQL as database")
			common.UsingPostgreSQL = true
			return gorm.Open(postgres.New(postgres.Config{
				DSN:                  dsn,
				PreferSimpleProtocol: true, // disables implicit prepared statement usage
			}), &gorm.Config{
				PrepareStmt: true, // precompile SQL
			})
		}
		// Use MySQL
		common.SysLog("using MySQL as database")
		return gorm.Open(mysql.Open(dsn), &gorm.Config{
			PrepareStmt: true, // precompile SQL
		})
	}
	// Use SQLite
	common.SysLog("SQL_DSN not set, using SQLite as database")
	common.UsingSQLite = true
	return gorm.Open(sqlite.Open(common.SQLitePath), &gorm.Config{
		PrepareStmt: true, // precompile SQL
	})
}

func InitDB() (err error) {
	{
		//配置缓存自动清理
		configCache := bigcache.Config{
			Shards:             64,
			LifeWindow:         60 * time.Second, // 缓存生存时间
			CleanWindow:        1 * time.Minute,  // 每分钟清理一次过期条目
			MaxEntriesInWindow: 1000,             // 最大条目数
			MaxEntrySize:       500,              // 每个条目的最大字节数
			Verbose:            true,
		}
		ctx := context.Background()
		cache, err = bigcache.New(ctx, configCache)
		if err != nil {
			return err
		}
	}

	db, err := chooseDB()
	if err == nil {
		if common.DebugEnabled {
			db = db.Debug()
		}
		DB, ccm.DB = db, db
		if sqlDB, err := DB.DB(); err != nil {
			return err
		} else {
			sqlDB.SetMaxIdleConns(utils.GetOrDefault("SQL_MAX_IDLE_CONNS", 100))
			sqlDB.SetMaxOpenConns(utils.GetOrDefault("SQL_MAX_OPEN_CONNS", 1000))
			sqlDB.SetConnMaxLifetime(time.Second * time.Duration(utils.GetOrDefault("SQL_MAX_LIFETIME", 60)))
		}

		if !common.IsMasterNode {
			return nil
		}
		common.SysLog("database migration started")
		//if err = db.AutoMigrate(&User{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&CertifyStudent{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&Node{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&CertifyStudent{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&CertifyCompany{}); err != nil {
		//	return err
		//}ma

		//if err = db.AutoMigrate(&User{}); err != nil {
		//	return err
		//}
		//aaaaa
		//if err = db.AutoMigrate(&StatDay{}); err != nil {
		//	return err
		//}
		//
		//if err = db.AutoMigrate(&User{}); err != nil {
		//	return err
		//}

		//1.8aaaaa
		//if err = db.AutoMigrate(&Instance{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&InstRecord{}); err != nil {
		//	return err
		//}

		//if err = db.AutoMigrate(&WithdrawApply{}); err != nil {
		//	return err
		//}

		//if err = db.AutoMigrate(&Instit{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&Teacher{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&Course{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&Tutorial{}); err != nil {
		//	return err
		//}

		//1.8
		//if err = db.AutoMigrate(&PodImage{}); err != nil {
		//	return err
		//}

		//triggerSQL := `
		//CREATE TRIGGER set_default_preferences
		//BEFORE INSERT ON T_OperationLog
		//FOR EACH ROW
		//BEGIN
		//	IF NEW.content IS NULL THEN
		//		SET NEW.content = '{}';
		//	END IF;
		//END;
		//`
		//
		//if err := db.Exec(triggerSQL).Error; err != nil {
		//	fmt.Println("Error creating trigger:", err)
		//} else {
		//	fmt.Println("Trigger created successfully")
		//}

		//if err = db.AutoMigrate(&ImageLabel{}); err != nil {
		//	return err
		//}

		//if err = db.AutoMigrate(&RewardRecord{}); err != nil {
		//	return err
		//}
		//
		//if err = db.AutoMigrate(&RewardBalance{}); err != nil {
		//	return err
		//}
		////
		//if err = db.AutoMigrate(&AmountFrozen{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&UserCost{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&UserSub{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&Withdraw{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&WithdrawAccount{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&WithdrawApply{}); err != nil {
		//	return err
		//}
		//if config.Env == enums.EnvEnum.DEV || true {
		//	if err = db.AutoMigrate(&ClassRoom{}); err != nil {
		//		return err
		//	}
		//	if err = db.AutoMigrate(&ClassUser{}); err != nil {
		//		return err
		//	}
		//}

		//if err = db.AutoMigrate(&User{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&UserLog{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&UserToken{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&Label{}); err != nil {
		//	return err
		//}
		//if err = db.AutoMigrate(&Pod{}); err != nil {
		//	return err
		//}
		/*
			if err = db.AutoMigrate(&Conversation{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&Instance{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&InstRecord{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&SettleStore{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&SettleRecord{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&AmountBalance{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&Pod{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&PodImage{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&Label{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&OperationLog{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&GpuModel{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&Gpus{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&Dockers{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&Node{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&Virtual{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&Recharge{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&RechargeProduct{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&Option{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&Coupon{}); err != nil {
				return err
			}
			if err = db.AutoMigrate(&Card{}); err != nil {
				return err
			}*/

		//migrate.AutoMigrate()

		common.SysLog("database migrated")
		//err = createRootAccountIfNeed()
		return err
	} else {
		common.FatalLog(err)
	}
	return err
}

func CloseDB() error {
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}
	err = sqlDB.Close()
	return err
}
