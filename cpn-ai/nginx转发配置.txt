server {
    listen 80;
    server_name *.hz01.suanyun.cn;
    proxy_http_version  1.1;  #1.1版本才支持wss

    #启用支持websocket连接的配置
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_connect_timeout 60; #配置等待服务器响应时间
    proxy_read_timeout 600;
    proxy_send_timeout 600;
    proxy_redirect off;

    # 设置客户端请求的最大大小为 10MB
    client_max_body_size 10m;

    location /loadpodmapping {
      set_by_lua_block $instanceUuid {
          local pattern = "(.+).hz01.suanyun.cn"
          local result = string.match(ngx.var.host, pattern)
          if result then
                  local l = string.len(result)
                  if l>=16 then
                      return result
                  end
          end
          return ""
      }
      proxy_pass http://127.0.0.1:6001/api/sys/weburl2nginx?instance_uuid=$instanceUuid&action=add;
      proxy_set_header authorization "ac4f334sddbfe701dda3783124ef6822a";
      proxy_set_header Accept-Encoding "identity";

      #return 200 "Captured value: ";
    }


    location / {
        add_header Cache-Control no-cache;
        set $redirected_url "";


        set_by_lua_block $redirected_url {
          local a = 'http://***************:12088'
          return a
        }



 access_by_lua_block{
            local pattern = "(.+).hz01.suanyun.cn"
            local result = string.match(ngx.var.host, pattern)
            local instanceUuid =''
            if result then
                if string.len(result)>=16 then
                    instanceUuid = result
                end
            end

            if instanceUuid=="" then
                ngx.header.content_type = "text/plain; charset=utf-8"
                ngx.status=404
                ngx.say("不是标准的Pod地址.")
                ngx.flush(true)
                ngx.exit(ngx.HTTP_OK)
            end

            local dict = ngx.shared.my_shared_data
            local v = dict:get(instanceUuid)
            if v then
                local lastChar = string.sub(v, -1)
                if lastChar == '/' then
                    v = string.sub(v, 1, -2)
                end
                -- 设置最后使用时间
                local lastusetimeKey = "lastusetime_"..instanceUuid
                dict:set(lastusetimeKey, os.time())
                ngx.var.redirected_url = v
                -- ngx.say(v)
                -- ngx.say("ok")
                -- ngx.flush(true)
            else
                local res = ngx.location.capture("/loadpodmapping", { method = ngx.HTTP_GET})
              -- ngx.say(res.status)
                if res.status ~= ngx.HTTP_OK then
                    ngx.status=404
                    ngx.say("请求Pod信息失败，请刷新重试.")
                    ngx.flush(true)
                    ngx.exit(ngx.HTTP_OK)
                end

                     local web_url=''
                     if string.find(res.body, "http") == nil then
                       -- ngx.say("res.body 中不包含 'http' 字符串")
                          ngx.header.content_type = "text/plain; charset=utf-8"
                          ngx.status=404
                          ngx.say("Pod不存在,请确认是否已启动.")
                          ngx.flush(true)
                          ngx.exit(ngx.HTTP_OK)
                     else
                          local start_pos, end_pos = string.find(res.body, '"web_url":"')
                          if start_pos and end_pos then
                            local url_start = end_pos + 1
                            local url_end = string.find(res.body, '"', url_start)
                            if url_end then
                                web_url = string.sub(res.body, url_start, url_end - 1)
                                dict:set(instanceUuid,web_url)
                                local lastChar = string.sub(web_url, -1)
                                if lastChar == '/' then
                                      web_url = string.sub(web_url, 1, -2)
                                end
                                ngx.var.redirected_url = web_url
                            end
                          end
                      end
                      if web_url=='' then
                          ngx.header.content_type = "text/plain; charset=utf-8"
                          ngx.status=404
                          ngx.say("未获取到Pod地址,请尝试重新启动Pod.")
                          ngx.flush(true)
                          ngx.exit(ngx.HTTP_OK)
                      end
            end
        }

        if ($redirected_url ~ "^http") {

            proxy_pass $redirected_url;
        }

        if ($redirected_url !~ "^http") {
            # $redirected_url 不以 htt 开头
            # return 404 $redirected_url;
            # return 200 $redirected_url;
            # proxy_pass $redirected_url;
        }
    }


    access_log  /www/wwwlogs/hz01.suanyun.cn.log;
    error_log  /www/wwwlogs/hz01.suanyun.cn.error.log;
}

