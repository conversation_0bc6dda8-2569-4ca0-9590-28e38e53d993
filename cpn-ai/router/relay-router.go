package router

import (
	"cpn-ai/controller"
	"cpn-ai/middleware"

	"github.com/gin-gonic/gin"
)

func SetRelayRouter(router *gin.Engine) {
	router.Use(middleware.CORS())
	// https://platform.openai.com/docs/api-reference/introduction
	//modelsRouter := router.Group("/v1/models")
	//modelsRouter.Use(middleware.TokenAuth())
	//{
	//	modelsRouter.GET("", controller.ListModels)
	//	modelsRouter.GET("/:model", controller.RetrieveModel)
	//}

	relayV1Router := router.Group("/v1")

	relayV1Router.GET("/conv/answer_stream", controller.ConversationApi.AnswerStream)
	relayV1Router.Use(middleware.TokenAuth())
	{

		relayV1Router.POST("/conv/new", controller.ConversationApi.New)
		relayV1Router.POST("/conv/del", controller.ConversationApi.Del)
		relayV1Router.POST("/conv/ask_question", controller.ConversationApi.AskQuestion)
		//relayV1Router.GET("/conv/answer_stream", controller.ConversationApi.AnswerStream)
		relayV1Router.POST("/conv/answer", controller.ConversationApi.SaveAnswer)
		relayV1Router.POST("/conv/list", controller.ConversationApi.List)

		//https://suanyun.cyuai.com/fastchat/v1/models
		//relayV1Router.POST("/completions", controller.FastchatApi)

		relayV1Router.POST("/chat/completions", controller.FastchatApi.ChatCompletions)
		relayV1Router.POST("/models", controller.FastchatApi.Models)
		//relayV1Router.POST("/edits", controller.Relay)
		//relayV1Router.POST("/images/generations", controller.Relay)
		//relayV1Router.POST("/images/edits", controller.RelayNotImplemented)
		//relayV1Router.POST("/images/variations", controller.RelayNotImplemented)
		//relayV1Router.POST("/embeddings", controller.Relay)
		//relayV1Router.POST("/engines/:model/embeddings", controller.Relay)
		//relayV1Router.POST("/audio/transcriptions", controller.Relay)
		//relayV1Router.POST("/audio/translations", controller.Relay)
		//relayV1Router.GET("/files", controller.RelayNotImplemented)
		//relayV1Router.POST("/files", controller.RelayNotImplemented)
		//relayV1Router.DELETE("/files/:id", controller.RelayNotImplemented)
		//relayV1Router.GET("/files/:id", controller.RelayNotImplemented)
		//relayV1Router.GET("/files/:id/content", controller.RelayNotImplemented)
		//relayV1Router.POST("/fine-tunes", controller.RelayNotImplemented)
		//relayV1Router.GET("/fine-tunes", controller.RelayNotImplemented)
		//relayV1Router.GET("/fine-tunes/:id", controller.RelayNotImplemented)
		//relayV1Router.POST("/fine-tunes/:id/cancel", controller.RelayNotImplemented)
		//relayV1Router.GET("/fine-tunes/:id/events", controller.RelayNotImplemented)
		//relayV1Router.DELETE("/models/:model", controller.RelayNotImplemented)
		//relayV1Router.POST("/moderations", controller.Relay)
	}
}
