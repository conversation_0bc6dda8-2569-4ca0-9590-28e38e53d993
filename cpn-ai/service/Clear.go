package service

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/service/tasklog"
	"cpn-ai/structs"
	"errors"
	"fmt"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"gorm.io/gorm"
)

// docker 存在 实例已经关机
type clear_ struct {
	dockerQueue *common.Queue
}

var Clear clear_

func init() {
	Clear.dockerQueue = &common.Queue{}
	Clear.dockerQueue.Cond = sync.NewCond(new(sync.Mutex))
}

func (d *clear_) ClearDocker() error {
	//item := Clear.dockerQueue.Dequeue()

	logger.Info("ClearDocker开始等待")
	Clear.dockerQueue.Cond.L.Lock()
	Clear.dockerQueue.Cond.Wait()
	Clear.dockerQueue.Cond.L.Unlock()
	return nil
}

/*
func (d *clear_) HandleReloadInitVirtual() {
	for _, virtual := range SchedService.VirtualNodes {
		checkTime := time.Now().Add(-60 * 5 * time.Second)
		logger.Info(virtual.HostPort, " HandleReloadInitVirtual LastCheckTime：", virtual.LastCheckTime)
		if virtual.LastCheckTime.Before(checkTime) {
			logger.Info(virtual.HostPort, "HandleReloadInitVirtual进入执行")
			VirtualNodeInitQueue.Enqueue(virtual.HostPort)
		}
	}
}*/

func (d *clear_) HandleShutdownRegular() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	logger.Info("HandleShutdownRegular 开始循环")

	var instance model.Instance
	lastAutoId := uint(0)
	const pageSize = 10

	for {
		var ary = make([]model.Instance, 0)
		if err := instance.ListForShutdownRegular(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info("HandleShutdownRegular 获取到", len(ary), "条需要处理的数据")
		for i := 0; i < len(ary); i++ {
			inst := ary[i]
			lastAutoId = inst.ID
			instanceUuid := inst.Uuid
			startupMark := inst.StartupMark
			shutdownRegularTime := inst.ShutdownRegularTime
			shutdownRegularSave := inst.ShutdownRegularSave
			pre := fmt.Sprintf("HandleShutdownRegular instanceUuid:%s  startupMark:%s shutdownRegularTime:%s", instanceUuid, startupMark, shutdownRegularTime.Format("2006-01-02 15:04:05"))
			logger.Info(pre, "开始处理定时关机的实例")

			if shutdownRegularTime.After(time.Now()) {
				logger.Info(pre, "未到关机时间，不处理")
				continue
			}
			logger.Info(pre, "开始关闭实例")
			if shutdownRegularSave {
				if inst.SaveImageId > 0 {
					//TODO: 先不处理保存失败的问题
					var podImage model.PodImage
					if err := podImage.GetById(inst.SaveImageId); err != nil {
						logger.Error(pre, "查询保存镜像ID失败 SaveImageId", err, inst.SaveImageId)
						continue
					} else {
						if podImage.Status != 9 && podImage.AuditStatus != enums.ImageAuditStatusEnum.PushSuccess && podImage.AuditStatus != enums.ImageAuditStatusEnum.AuditPass {
							logger.Error(pre, "保存镜像未成功，不处理 SaveImageId：", inst.SaveImageId)
							continue
						}
					}
				}
				//h, err := InstanceNodeService.SaveImage(instanceUuid, "", "", enums.ImageStorageModeEnum.Registry, true)
				//if err != nil {
				//	logger.Error(pre, fmt.Sprintf(" 定时保存镜像后关机处理出错：%v", h), err)
				//}
				if txt, err := InstanceNodeService.Shutdown(instanceUuid, enums.ShutdownReasonEnum.Regular, false, tasklog.TaskEnum.SaveImageAndShutdown); err != nil {
					logger.Error(pre, fmt.Sprintf(" 定时关机处理出错：%s", txt), err)
				} else {
					logger.Info(pre, fmt.Sprintf("定时关机执行完成：%s", txt))
				}
			} else if txt, err := InstanceNodeService.Shutdown(instanceUuid, enums.ShutdownReasonEnum.Regular, true, tasklog.TaskEnum.ShutdownAndDestroy); err != nil {
				logger.Error(pre, fmt.Sprintf(" 定时关机处理出错：%s", txt), err)
			} else {
				logger.Info(pre, fmt.Sprintf("定时关机执行完成：%s", txt))
			}
		}
		if len(ary) < pageSize {
			break
		}
	}
	return nil
}

func (d *clear_) HandleBootInProgress() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	logger.Info("HandleBootInProgress 开始循环")

	var instance model.Instance
	lastAutoId := uint(0)
	const pageSize = 10

	for {
		var ary = make([]model.Instance, 0)
		if err := instance.ListForCheckBootInProgress(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info("HandleBootInProgress 获取到", len(ary), "条需要处理的数据")
		for i := 0; i < len(ary); i++ {
			inst := ary[i]
			lastAutoId = inst.ID
			instanceUuid := inst.Uuid
			startupMark := inst.StartupMark
			startupMarkTime := inst.StartupMarkTime
			pre := fmt.Sprintf("HandleBootInProgress instanceUuid:%s  startupMark:%s ", instanceUuid, startupMark)
			logger.Info(pre, "开始处理启动中的实例")

			if startupMarkTime.After(time.Now().Add(-time.Minute * 3)) {
				logger.Info(pre, "启动没有到3分钟，不处理")
				continue
			}

			if booting, ginH, err := NodeService.TasklogBootIn(inst.StartupNodeId, inst.StartupMark); err != nil {
				logger.Error(pre, err, "  ginH:", utils.GetJsonFromStruct(ginH))
			} else {
				logger.Info(pre, "booting:", booting, "  ginH:", utils.GetJsonFromStruct(ginH))
				if booting == false {
					logger.Info(pre, "将实例进入启动失败逻辑")
					if m, err := InstanceNodeService.StartupFail(inst.Uuid, inst.StartupMark, nil, "无启动日志"); err != nil {
						logger.Error(pre, "启动实例失败，重置启动前实例状态失败", m, err)
					} else {
						logger.Info(pre, "已将实例置为启动失败")
					}
				} else {
					logger.Info(pre, "实例还在启动中，不处理")
				}
			}
			{
				b, s, err := InstanceNodeService.StartupLoop(inst.ID)
				logger.Info("StartupLoop b:", b, "   s:", s, " err:", err)
			}
		}
		if len(ary) < pageSize {
			break
		}
	}
	return nil
}

func (d *clear_) HandleCheckRunningProgress() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	logger.Info("HandleCheckRunningProgress 开始循环")

	lastAutoId := uint(0)
	pageSize := 10
	for {
		var ary = make([]model.Instance, 0)
		var instance model.Instance
		if err := instance.ListForCheckRunningProgress(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info("HandleCheckRunningProgress 获取到", len(ary), "条需要检测的数据 lastAutoId:", lastAutoId)
		for i := 0; i < len(ary); i++ {
			inst := ary[i]
			lastAutoId = inst.ID
			pre := fmt.Sprintf("HandleCheckRunningProgress instanceId:%d,  instanceUuid:%s  startupMark:%s startupVirtualId:%d ", inst.ID, inst.Uuid, inst.StartupMark, inst.StartupVirtualId)
			//logger.Info(pre, "开始检测运行中的实例")
			if inst.StartupTime.After(time.Now().Add(-time.Minute * 3)) {
				logger.Info(pre, "启动没有到3分钟，不处理")
				continue
			}

			if _, state, _, err := NodeService.GetDockerDetail(inst.StartupVirtualId, inst.StartupMark); err != nil {
				logger.Error(pre, err, "  ", err)
			} else {
				if state == enums.DockerStatusEnum.NotExist {
					logger.Info(pre, "有实例在运行中，但是Docker已经不存在，需要处理")
					logKey := "HandleCheckRunningProgress_" + inst.Uuid
					EmailService.AddNeedSend(logKey, pre+"有实例在运行中，但是Docker已经不存在，需要处理，检查时间:"+jsontime.Now().String())
				}
			}
		}
		if len(ary) < pageSize {
			break
		}
	}
	return nil
}

func (d *clear_) HandleShutdownInProgress() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("HandleShutdownInProgress奔溃:", e)
		}
	}()
	logger.Info("HandleShutdownInProgress 开始循环")

	var list model.InstRecord
	lastAutoId := uint(0)
	const pageSize = 10

	for {
		var ary = make([]model.InstRecord, 0)
		if err := list.ListForCheckShutdownInProgress(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info("HandleShutdownInProgress 获取到", len(ary), "条需要处理的数据")
		for i := 0; i < len(ary); i++ {
			instRecord := ary[i]
			lastAutoId = ary[i].ID
			checkTime := time.Now().Add(-60 * 5 * time.Second)
			if ary[i].ShutdownTime.Before(checkTime) {
				logger.Info("实例进入关闭失败逻辑", ary[i].ID)
				if err := InstanceNodeService.ShutdownInstRecord(ary[i].StartupMark, false); err != nil {
					logger.Error(err, ary[i].StartupMark)

					content := fmt.Sprintf(`记录ID：%d，实例ID：%d，StartupMark：%s，检测时间：%s，关闭的时间：%s Err:%s`, instRecord.ID, instRecord.InstanceId, ary[i].StartupMark, checkTime, instRecord.ShutdownTime, err.Error())
					emailReq := EmailReq{
						From:    "",
						To:      "<EMAIL>,<EMAIL>",
						Subject: "实例关闭失败，需要人工介入 " + time.Now().Format(jsontime.TimeFormat),
						Content: content,
					}
					EmailService.SendWarn(emailReq)
				}
			}
		}
		if len(ary) < pageSize {
			break
		}
	}
	return nil
}

func (d *clear_) HandleResetNginxProgress() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	commandKey := "HandleResetNginxProgress"
	if _, ok := TaskRunning.Load(commandKey); ok {
		err := errors.New("批量重置实例映射，正在运行中")
		logger.Error(err)
		return err
	}

	atomic.AddInt32(&TaskRunningCount, 1)
	commandRunningValue := structs.RunningCommand{Msg: "批量重置实例映射", StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	TaskRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&TaskRunningCount, -1)
		TaskRunning.Delete(commandKey)
	}()
	logger.Info("HandleResetNginxProgress 开始循环")

	lastAutoId := uint(0)
	pageSize := 10
	for {
		var ary = make([]model.Instance, 0)
		var instance model.Instance
		if err := instance.ListForCheckRunningProgress(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info("HandleResetNginxProgress 获取到", len(ary), "条需要检测的数据 lastAutoId:", lastAutoId)
		for i := 0; i < len(ary); i++ {
			inst := ary[i]
			lastAutoId = inst.ID
			pre := fmt.Sprintf("HandleResetNginxProgress instanceId:%d,  instanceUuid:%s  startupMark:%s startupVirtualId:%d ", inst.ID, inst.Uuid, inst.StartupMark, inst.StartupVirtualId)
			//logger.Info(pre, "开始重置实例Nginx")
			if ginH, err := InstanceNodeService.SetNginx(inst.Uuid); err != nil {
				logger.Error(pre, " ginH:", utils.GetJsonFromStruct(ginH), "err:", err)
			} else {
				logger.Info(pre, " ginH:", utils.GetJsonFromStruct(ginH))
			}

			if val, ok := TaskRunning.Load(commandKey); ok {
				commandRunningValue = val.(structs.RunningCommand)
				commandRunningValue.Progress = fmt.Sprintf("%d", lastAutoId)
				TaskRunning.Store(commandKey, commandRunningValue)
			}
			time.Sleep(time.Millisecond * 100)
		}
		if len(ary) < pageSize {
			break
		}
	}
	return nil
}

func (d *clear_) HandleMonitor() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	monitorTime := time.Now().Truncate(time.Minute)
	monitorAt := monitorTime.Unix()

	logger.Info("HandleMonitor 开始循环")

	lastAutoId := uint(0)
	pageSize := 20
	//mPodMonitor := make(map[uint]int)
	//mGpuMonitor := make(map[uint]int)

	kolInstanceCount := 0              //总的实例数量
	kolRunningCount := 0               //运行中的实例数量
	kolBootInCount := 0                //启动中的实例数量
	kolGpuCount := 0                   //占用的Gpu数量
	kolNoCardCount := 0                //无卡的实例数量
	kolUserCount := make(map[uint]int) //不同的使用人数
	kolPodCount := make(map[uint]int)  //不同的Pod数量

	usageInstanceCount := 0              //总的实例数量
	usageRunningCount := 0               //运行中的实例数量
	usageBootInCount := 0                //启动中的实例数量
	usageGpuCount := 0                   //占用的Gpu数量
	usageNoCardCount := 0                //无卡的实例数量
	usageUserCount := make(map[uint]int) //不同的使用人数
	usagePodCount := make(map[uint]int)  //不同的Pod数量

	//内部用户
	insiderInstanceCount := 0              //总的实例数量
	insiderRunningCount := 0               //运行中的实例数量
	insiderBootInCount := 0                //启动中的实例数量
	insiderGpuCount := 0                   //占用的Gpu数量
	insiderNoCardCount := 0                //无卡的实例数量
	insiderUserCount := make(map[uint]int) //不同的使用人数
	insiderPodCount := make(map[uint]int)  //不同的Pod数量

	podInstanceCount := make(map[uint]int) //总的实例数量
	podRunningCount := make(map[uint]int)  //运行中的实例数量
	podBootInCount := make(map[uint]int)   //启动中的实例数量
	podGpuCount := make(map[uint]int)      //占用的Gpu数量
	podNoCardCount := make(map[uint]int)   //无卡的实例数量
	podUserCount := make(map[uint]int)     //不同的使用人数

	insiderUser := make(map[uint]int)
	{
		var user model.User
		listInsider := make([]model.User, 0)
		if err := user.ListInsider(&listInsider); err == nil {
			for _, item := range listInsider {
				insiderUser[item.ID] = item.Insider
			}
		}
	}
	totalCount := 0
	for {
		var ary = make([]model.Instance, 0)
		var instance model.Instance
		if err := instance.ListForMonitor(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info("HandleMonitor 获取到", len(ary), "条需要检测的数据 lastAutoId:", lastAutoId)

		for i := 0; i < len(ary); i++ {
			inst := ary[i]
			lastAutoId = inst.ID
			totalCount++

			if inst.InstanceType == enums.InstanceTypeEnum.Kol {
				kolInstanceCount++
				if inst.Status == enums.InstanceStatusEnum.Running {
					kolRunningCount++
				} else if inst.Status == enums.InstanceStatusEnum.BootInProgress {
					kolBootInCount++
				}
				if inst.NoCard == 1 {
					kolNoCardCount++
				} else {
					kolGpuCount++
				}
				kolUserCount[inst.UserId] = 1
				kolPodCount[inst.PodId] = 1
				continue
			}

			usageInstanceCount++
			if inst.Status == enums.InstanceStatusEnum.Running {
				usageRunningCount++
			} else if inst.Status == enums.InstanceStatusEnum.BootInProgress {
				usageBootInCount++
			}
			if inst.NoCard == 1 {
				usageNoCardCount++
			} else {
				usageGpuCount += inst.Gpus
			}
			usageUserCount[inst.UserId] = 1
			usagePodCount[inst.PodId] = 1

			if _, ok := insiderUser[inst.UserId]; ok {

				insiderInstanceCount++
				if inst.Status == enums.InstanceStatusEnum.Running {
					insiderRunningCount++
				} else if inst.Status == enums.InstanceStatusEnum.BootInProgress {
					insiderBootInCount++
				}
				if inst.NoCard == 1 {
					insiderNoCardCount++
				} else {
					insiderGpuCount += inst.Gpus
				}
				insiderUserCount[inst.UserId] = 1
				insiderPodCount[inst.PodId] = 1

			}

			if _, ok := podInstanceCount[inst.PodId]; ok {
				podInstanceCount[inst.PodId] += 1
			} else {
				podInstanceCount[inst.PodId] = 1
			}
			if inst.Status == enums.InstanceStatusEnum.Running {
				if _, ok := podRunningCount[inst.PodId]; ok {
					podRunningCount[inst.PodId] += 1
				} else {
					podRunningCount[inst.PodId] = 1
				}
			} else if inst.Status == enums.InstanceStatusEnum.BootInProgress {
				if _, ok := podBootInCount[inst.PodId]; ok {
					podBootInCount[inst.PodId] += 1
				} else {
					podBootInCount[inst.PodId] = 1
				}
			}

			if inst.NoCard == 1 {
				if _, ok := podNoCardCount[inst.PodId]; ok {
					podNoCardCount[inst.PodId] += 1
				} else {
					podNoCardCount[inst.PodId] = 1
				}
			} else {
				if _, ok := podGpuCount[inst.PodId]; ok {
					podGpuCount[inst.PodId] += inst.Gpus
				} else {
					podGpuCount[inst.PodId] = inst.Gpus
				}
			}

			if _, ok := podUserCount[inst.PodId]; ok {
				podUserCount[inst.PodId] += 1
			} else {
				podUserCount[inst.PodId] = 1
			}

		}
		if len(ary) < pageSize {
			break
		}
	}
	logger.Info("运行实例统计完成，共遍历了", totalCount, "条记录")
	kolData := structs.MonitorData{
		InstanceCount: kolInstanceCount,
		RunningCount:  kolRunningCount,
		BootinCount:   kolBootInCount,
		GpuCount:      kolGpuCount,
		NoCardCount:   kolNoCardCount,
		UserCount:     len(kolUserCount),
		PodCount:      len(kolPodCount),
	}

	usageData := structs.MonitorData{
		InstanceCount: usageInstanceCount,
		RunningCount:  usageRunningCount,
		BootinCount:   usageBootInCount,
		GpuCount:      usageGpuCount,
		NoCardCount:   usageNoCardCount,
		UserCount:     len(usageUserCount),
		PodCount:      len(usagePodCount),
	}

	insiderData := structs.MonitorData{
		InstanceCount: insiderInstanceCount,
		RunningCount:  insiderRunningCount,
		BootinCount:   insiderBootInCount,
		GpuCount:      insiderGpuCount,
		NoCardCount:   insiderNoCardCount,
		UserCount:     len(insiderUserCount),
		PodCount:      len(insiderPodCount),
	}

	podData := make(map[uint]structs.MonitorData)
	for podId, val := range podInstanceCount {
		item := structs.MonitorData{
			InstanceCount: val,
		}
		if count, ok := podRunningCount[podId]; ok {
			item.RunningCount = count
		}
		if count, ok := podBootInCount[podId]; ok {
			item.BootinCount = count
		}
		if count, ok := podGpuCount[podId]; ok {
			item.GpuCount = count
		}
		if count, ok := podNoCardCount[podId]; ok {
			item.NoCardCount = count
		}
		if count, ok := podUserCount[podId]; ok {
			item.UserCount = count
		}
		podData[podId] = item
	}

	gpuData := make(map[uint]structs.MonitorGpuData)
	if aryGpuModelMonitor, _, err := InstanceNodeService.GpuModels(0); err != nil {
		logger.Error(err)
		return err
	} else {
		for _, item := range aryGpuModelMonitor {
			tmp := structs.MonitorGpuData{
				GpuModelId: item.ID,
				TotalCount: item.TotalGpus,
				UseCount:   item.TotalGpus - item.FreeGpus,
				FreeCount:  item.FreeGpus,
			}
			gpuData[item.ID] = tmp
		}
	}

	monitor := model.Monitor{
		KolData:     utils.GetJsonFromStruct(kolData),
		UsageData:   utils.GetJsonFromStruct(usageData),
		InsiderData: utils.GetJsonFromStruct(insiderData),
		PodData:     utils.GetJsonFromStruct(podData),
		GpuData:     utils.GetJsonFromStruct(gpuData),
		MonitorTime: monitorTime,
		MonitorAt:   monitorAt,
	}

	if err := monitor.Save(); err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			return nil
		} else {
			logger.Error(err)
			return err
		}
	} else {
		return nil
	}

}

func (d *clear_) HandleImagePushingProgress() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	logger.Info("HandleImagePushingProgress 开始循环")

	lastAutoId := uint(0)
	pageSize := 10
	for {
		var ary = make([]model.PodImage, 0)
		var podImage model.PodImage
		if err := podImage.ListForCheckPushInProgress(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info("HandleImagePushingProgress 获取到", len(ary), "条需要检测的数据 lastAutoId:", lastAutoId)
		for i := 0; i < len(ary); i++ {
			image := ary[i]
			lastAutoId = image.ID
			pre := fmt.Sprintf("HandleImagePushingProgress imageId:%d,  imageType:%s  podId:%d commitStartTime:%s ", image.ID, enums.ImageTypeEnum.Name(image.ImageType), image.PodId, image.CommitStartTime)
			logger.Info(pre, " 正在推送中的镜像")
			if image.CommitStartTime.Add(time.Minute * 5).After(time.Now()) {
				logger.Info(pre, "镜像正在推送中，提交时间在5分钟之内，不处理")
				continue
			}
			if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(image.ID)); err != nil {
				logger.Error(pre, " err:", err)
			} else {
				if bOut, err := tasklog.OutTime(logKey, 60*5); err != nil {
					logger.Error(pre, " err:", err)
				} else {
					if bOut {
						logger.Info(pre, "镜像正在推送中，但是进度日志超时")
						EmailService.AddNeedSend(logKey, pre+"镜像正在推送中，但是进度日志超时(5分钟没响应)，检查时间:"+jsontime.Now().String())

					} else {
						logger.Info(pre, "正在推送中 logKey:", logKey)
					}
				}
			}
		}
		if len(ary) < pageSize {
			break
		}
	}
	return nil
}

func (d *clear_) HandleImagePushFail() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	logger.Info("HandleImagePushFail 开始循环")

	lastAutoId := uint(0)
	pageSize := 10
	for {
		var ary = make([]model.PodImage, 0)
		var podImage model.PodImage
		if err := podImage.ListForCheckPushFail(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info("HandleImagePushFail 获取到", len(ary), "条需要检测的数据 lastAutoId:", lastAutoId)
		for i := 0; i < len(ary); i++ {
			image := ary[i]
			lastAutoId = image.ID
			pre := fmt.Sprintf("HandleImagePushFail imageId:%d,  imageType:%s  podId:%d commitStartTime:%s ", image.ID, enums.ImageTypeEnum.Name(image.ImageType), image.PodId, image.CommitStartTime)
			logger.Info(pre, " 推送失败的镜像")
			if image.CommitStartTime.Add(time.Minute * 5).After(time.Now()) {
				logger.Info(pre, "推送失败的镜像，提交时间在5分钟之内，不处理")
				continue
			}
			if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(image.ID)); err != nil {
				logger.Error(pre, " err:", err)
			} else {
				if bOut, err := tasklog.OutTime(logKey, 60*5); err != nil {
					logger.Error(pre, " err:", err)
				} else {
					if bOut {
						logger.Info(pre, "镜像推送失败，需要手动处理")
						num := time.Now().Unix() - image.CommitStartTime.Unix()
						ss := utils.Seconds2Time(num)
						EmailService.AddNeedSend(logKey, pre+"镜像推送失败，离推送时间已有"+ss+"，检查时间:"+jsontime.Now().String())
					} else {
						logger.Info(pre, "镜像推送失败 logKey:", logKey)
					}
				}
			}
		}
		if len(ary) < pageSize {
			break
		}
	}
	return nil
}

/*
func (d *clear_) TestSaveImageContinue(image model.PodImage) error {

	diff := time.Now().Sub(image.CommitStartTime)
	if diff > 30*time.Minute {
		err := errors.New("超过30分钟了，不处理")
		logger.Error(err, " imageId：", image.ID)
		return err
	}

	testKey := enums.RedisKeyEnum.LockKey + "TestSaveImageContinue:" + utils.Uint2String(image.ID)
	if testCount, err := common.RedisGet(testKey); err != nil {
		logger.Error(testKey, " err:", err)
		return err
	} else {
		if testCount == "" {
			testCount = "0"
		}
		iTestCount, _ := strconv.Atoi(testCount)
		if iTestCount < 1 {
			iTestCount = iTestCount + 1
			if err := common.RedisSet(testKey, strconv.Itoa(iTestCount), time.Minute*50); err != nil {
				msg := "设置保存镜像尝试次数失败"
				logger.Error(msg, " ", testKey)
				return err
			}
			if ginH, err := InstanceNodeService.SaveImageContinue(image.ID, oReq.InstanceUuid); err != nil {
				result["errInfo"] = err.Error()
				logger.Error(err)
				msg = err.Error()
			} else {
				code = 0
				if ginH == nil {
					c.JSON(http.StatusOK, "ginH is nil")
				} else {
					c.JSON(http.StatusOK, ginH)
				}
			}

		}
	}
}*/

func (d *clear_) BatchUpdatePodAuthorName() error {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	commandKey := "BatchUpdatePodAuthorName"
	if _, ok := TaskRunning.Load(commandKey); ok {
		err := errors.New("批量更新Pod作者名称，正在运行中")
		logger.Error(err)
		return err
	}

	atomic.AddInt32(&TaskRunningCount, 1)
	commandRunningValue := structs.RunningCommand{Msg: "批量更新Pod作者名称", StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	TaskRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&TaskRunningCount, -1)
		TaskRunning.Delete(commandKey)
	}()

	logger.Info("UpdatePodAuthorName 开始循环")

	lastAutoId := uint(0)
	pageSize := 20
	//mapUser := make(map[uint]string)
	for {
		var ary = make([]model.Pod, 0)
		var pod model.Pod
		if err := pod.ListByLastAutoId(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}

		logger.Info("UpdatePodAuthorName 获取到", len(ary), "条需要跟新AuthorName的数据 lastAutoId:", lastAutoId)
		for i := 0; i < len(ary); i++ {
			item := ary[i]
			lastAutoId = item.ID

			if err := UpdatePodKeywords(item.ID); err != nil {
				logger.Error(err)
			}

			//displayName := ""
			//if val, ok := mapUser[item.UserId]; ok {
			//	displayName = val
			//} else {
			//	if item.UserId > 0 {
			//		var user model.User
			//		if err := user.GetById(item.UserId); err != nil {
			//			logger.Error(err, " userId:", item.UserId)
			//		} else {
			//			mapUser[user.ID] = user.DisplayName
			//			displayName = user.DisplayName
			//		}
			//	}
			//}
			//if displayName != "" && item.AuthorName != displayName {
			//	if err := item.SetAuthorName(displayName); err != nil {
			//		logger.Error(err)
			//		return err
			//	}
			//}

			if val, ok := TaskRunning.Load(commandKey); ok {
				commandRunningValue = val.(structs.RunningCommand)
				commandRunningValue.Progress = fmt.Sprintf("%d", lastAutoId)
				TaskRunning.Store(commandKey, commandRunningValue)
			}
			time.Sleep(time.Millisecond * 10)
		}
		if len(ary) < pageSize {
			break
		}

	}
	logger.Info("UpdatePodAuthorName 更新完成")
	return nil
}

type respPodImage struct {
	ID          uint   `json:"id"`
	ImageType   int    `json:"image_type"`
	ImageName   string `json:"image_name"`
	ImageTag    string `json:"image_tag"`
	AuditStatus int    `json:"audit_status"`
	Status      int    `json:"status"`
	PodId       uint   `json:"pod_id"`
	PodUuid     string `json:"pod_uuid" `
	PodTitle    string `json:"pod_title"`
}

func (d *clear_) PatchCheckPodImage() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	commandKey := "PatchCheckPodImage"

	if _, ok := TaskRunning.Load(commandKey); ok {
		err := errors.New("正在执行中，请勿重复操作")
		logger.Error(err)
		return err
	}

	atomic.AddInt32(&TaskRunningCount, 1)
	commandRunningValue := structs.RunningCommand{Command: "批量核对镜像仓库", StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	TaskRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&TaskRunningCount, -1)
		TaskRunning.Delete(commandKey)
	}()

	logger.Info("CheckPodImage 开始循环")

	lastAutoId := uint(0)
	pageSize := 20
	privateCount := 0
	publicCount := 0
	for {
		var ary = make([]model.PodImage, 0)
		var podImageList model.PodImage
		if err := podImageList.ListByLastAutoId(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}

		logger.Info("CheckPodImage 获取到", len(ary), "条需要检查的镜像数据 lastAutoId:", lastAutoId)
		for i := 0; i < len(ary); i++ {
			podImage := ary[i]
			lastAutoId = podImage.ID

			hubStatus := model.PodImageHubStatusNeedCheck
			msg := ""
			var hub structs.HubRepositorie
			if err := GetHubImageWithTag(podImage.ImageType, podImage.ImageName, podImage.ImageTag, &hub); err != nil {
				if err == gorm.ErrRecordNotFound {
					msg = "镜像在Hub仓库不存在"
					hubStatus = model.PodImageHubStatusNotExist
				} else {
					msg = "从Hub仓库获取镜像信息失败"
					logger.Error(msg, err, " imageId:", podImage.ID)
				}
			} else {
				if hub.Size == 0 {
					msg = "未知错误"
					hubStatus = model.PodImageHubStatusError
				} else {
					msg = "存在"
					hubStatus = model.PodImageHubStatusExist
				}
			}

			if val, ok := TaskRunning.Load(commandKey); ok {
				commandRunningValue = val.(structs.RunningCommand)
				commandRunningValue.ImageId = podImage.ID
				commandRunningValue.Msg = msg
				TaskRunning.Store(commandKey, commandRunningValue)
			}

			if err := podImage.SetHubStatus(hubStatus); err != nil {
				msg = msg + " 设置失败"
				logger.Error(msg, err, " podImageId:", podImage.ID)
				return err
			}
			time.Sleep(time.Second)
		}
		if len(ary) < pageSize {
			break
		}
		//return nil
	}
	logger.Info("CheckPodImage 更新完成 涉及", publicCount, "个公共镜像  ", privateCount, "个私人镜像")
	return nil
}

func (d *clear_) CheckPodImage(podImage model.PodImage) error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	commandKey := fmt.Sprintf("CheckPodImage_%d", podImage.ID)

	if _, ok := TaskRunning.Load(commandKey); ok {
		err := errors.New("正在执行中，请勿重复操作")
		logger.Error(err)
		return err
	}

	atomic.AddInt32(&TaskRunningCount, 1)
	commandRunningValue := structs.RunningCommand{Command: "单个核对镜像仓库", StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	TaskRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&TaskRunningCount, -1)
		TaskRunning.Delete(commandKey)
	}()

	logger.Info("CheckPodImage 开始循环")

	hubStatus := model.PodImageHubStatusNeedCheck
	msg := ""
	var hub structs.HubRepositorie
	if err := GetHubImageWithTag(podImage.ImageType, podImage.ImageName, podImage.ImageTag, &hub); err != nil {
		if err == gorm.ErrRecordNotFound {
			msg = "镜像在Hub仓库不存在"
			hubStatus = model.PodImageHubStatusNotExist
		} else {
			msg = "从Hub仓库获取镜像信息失败"
			logger.Error(msg, err, " imageId:", podImage.ID)
		}
	} else {
		if hub.Size == 0 {
			msg = "未知错误"
			hubStatus = model.PodImageHubStatusError
		} else {
			msg = "存在"
			hubStatus = model.PodImageHubStatusExist
		}
	}

	if val, ok := TaskRunning.Load(commandKey); ok {
		commandRunningValue = val.(structs.RunningCommand)
		commandRunningValue.ImageId = podImage.ID
		commandRunningValue.Msg = msg
		TaskRunning.Store(commandKey, commandRunningValue)
	}

	if err := podImage.SetHubStatus(hubStatus); err != nil {
		msg = msg + " 设置失败"
		logger.Error(msg, err, " podImageId:", podImage.ID)
		return err
	}
	time.Sleep(time.Second)

	//return nil

	return nil
}

func (d *clear_) ClearPrivateImageFromHubByUser(userId uint) error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	commandKey := fmt.Sprintf("ClearPrivateImageFromHubByUser_%d", userId)

	if _, ok := TaskRunning.Load(commandKey); ok {
		err := errors.New("正在执行中，请勿重复操作")
		logger.Error(err)
		return err
	}

	atomic.AddInt32(&TaskRunningCount, 1)
	commandRunningValue := structs.RunningCommand{Command: "批量核对镜像仓库", StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	TaskRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&TaskRunningCount, -1)
		TaskRunning.Delete(commandKey)
	}()

	logger.Info(commandKey + " 开始循环")

	lastAutoId := uint(0)
	pageSize := 10
	for {
		var ary = make([]model.PodImage, 0)
		var podImage model.PodImage
		if err := podImage.ListForPrivateImage(&ary, userId, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info(commandKey+" 获取到", len(ary), "条需要检测的数据 lastAutoId:", lastAutoId)
		for i := 0; i < len(ary); i++ {
			image := ary[i]
			lastAutoId = image.ID
			if image.UserId != userId || image.ImageType != enums.ImageTypeEnum.Private || image.StorageMode != enums.ImageStorageModeEnum.Registry {
				logger.Error("条件不匹配 image:", utils.GetJsonFromStruct(image))
				continue
			}
			pre := fmt.Sprintf("HandleImagePushingProgress imageId:%d,  imageType:%s  podId:%d commitStartTime:%s ", image.ID, enums.ImageTypeEnum.Name(image.ImageType), image.PodId, image.CommitStartTime)
			logger.Info(pre, " 正在处理镜像")

			if image.Status == 9 {
				if image.HubStatus != 2 { //需要核对

				}
			}
		}
		if len(ary) < pageSize {
			break
		}
	}
	return nil
}

func (d *clear_) ClearOverDayInstance() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	commandKey := fmt.Sprintf("ClearOverDayInstance")

	if _, ok := TaskRunning.Load(commandKey); ok {
		err := errors.New("正在执行中，请勿重复操作")
		logger.Error(err)
		return err
	}

	atomic.AddInt32(&TaskRunningCount, 1)
	commandRunningValue := structs.RunningCommand{Command: "清理过期的实例", StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	TaskRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&TaskRunningCount, -1)
		TaskRunning.Delete(commandKey)
	}()

	logger.Info(commandKey + " 开始循环")

	lastAutoId := uint(0)
	pageSize := 50
	loop := 0
	for {
		var ary = make([]model.Instance, 0)
		var instance model.Instance
		if err := instance.ListForOverDayClear(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info(commandKey+" 获取到", len(ary), "条需要处理的实例数据 lastAutoId:", lastAutoId)
		for i := 0; i < len(ary); i++ {
			item := ary[i]
			lastAutoId = item.ID

			if val, ok := TaskRunning.Load(commandKey); ok {
				commandRunningValue = val.(structs.RunningCommand)
				commandRunningValue.Msg = fmt.Sprintf("正在处理实例ID:%d", item.ID)
				TaskRunning.Store(commandKey, commandRunningValue)
			}
			if msg, err := InstanceNodeService.OverDayClear(item.Uuid); err != nil {
				logger.Error("清理到期实例失败", msg, err, " instanceId:", item.ID, " instanceUuid:", item.Uuid)
			} else {
				logger.InfoSpace("清理到期实例成功", msg, " instanceId:", item.ID, " instanceUuid:", item.Uuid, item.CreatedAt, item.StartupTime, item.StartupMarkTime, item.ShutdownTime)
			}
			time.Sleep(time.Second * 1)
		}
		if len(ary) < pageSize {
			break
		}

		loop++
		if loop > 1000 {
			break
		}
	}
	return nil
}

//
//func (d *clear_) HandlePushInProgress() error {
//	defer func() {
//		if e := recover(); e != nil {
//			logger.Error("HandlePushInProgress奔溃:", e)
//		}
//	}()
//	logger.Info("HandlePushInProgress 开始循环")
//
//	var list model.PodImage
//	var ary = make([]model.PodImage, 0)
//	lastAutoId := uint(0)
//	pageSize := 10
//
//	for {
//		if err := list.ListForCheckPushInProgress(&ary, lastAutoId, pageSize); err != nil {
//			logger.Error(err)
//			return err
//		}
//		logger.Info("HandlePushInProgress 获取到", len(ary), "条需要处理的数据")
//		for i := 0; i < len(ary); i++ {
//			podImage := ary[i]
//			lastAutoId = ary[i].ID
//			checkTime := time.Now().Add(-60 * 15 * time.Second)
//			if ary[i].CommitStartTime.Before(checkTime) {
//				logger.Info("推送进入超时逻辑 imageId:", ary[i].ID)
//
//				if pushIn, err := tasklog.PushIn(ary[i].ID); err != nil {
//					logger.Error(err)
//				} else {
//					if pushIn == false {
//						if err := podImage.SetAuditStatus(enums.ImageAuditStatusEnum.PushFail); err != nil {
//							msg := "设置推送状态为失败出错"
//							logger.Error(msg, err, " imageId:", podImage.ID)
//						} else {
//							msg := "已将推送状态设置为失败"
//							logger.Error(msg, err, " imageId:", podImage.ID)
//						}
//					}
//				}
//			}
//		}
//		if len(ary) < pageSize {
//			break
//		}
//	}
//	return nil
//}

/*
func (d *clear_) HandleShutdownInProgress11() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("HandleShutdownInProgress奔溃:", e)
		}
	}()
	logger.Info("HandleShutdownInProgress 开始循环")

	var list model.InstRecord
	var ary = make([]model.InstRecord, 0)
	lastAutoId := uint(0)
	pageSize := 10

	for {
		if err := list.ListForCheckShutdownInProgress(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info("HandleShutdownInProgress 获取到", len(ary), "条需要处理的数据")
		for i := 0; i < len(ary); i++ {
			instRecord := ary[i]
			lastAutoId = ary[i].ID
			checkTime := time.Now().Add(-60 * 5 * time.Second)
			if ary[i].ShutdownTime.Before(checkTime) {
				logger.Info("实例进入关闭失败逻辑", ary[i].ID)

				done := false

				if instRecord.StartupVirtualId == 0 {
					done = true
				} else {
					var virtual model.Virtual
					if err := virtual.GetById(instRecord.StartupVirtualId); err != nil {
						logger.Error(err)
					} else {
						hostPort := fmt.Sprintf("%s:%d", virtual.Host, virtual.Port)
						virtualNode := SchedService.VirtualNodeFromRedis(hostPort)
						if virtualNode == nil {
							done = true
						} else {
							find := false
							for _, docker := range virtualNode.Dockers {
								if docker.StartupMark == instRecord.StartupMark {
									find = true
									break
								}
							}
							if find {
								done = false
							}
						}
					}
				}

				if done {
					if instRecord.Status == enums.InstanceStatusEnum.ShutdownComplete {
						logger.Info("已经关闭，无需处理", instRecord.ID)
						continue
					}
					if err := instRecord.SetStatus2ShutdownComplete(); err != nil {
						logger.Error(err, instRecord.ID)
						continue
					} else {
						logger.Info("instRecord关闭完成", instRecord.ID)
					}
				}
			}
		}
		if len(ary) < pageSize {
			break
		}
	}
	return nil
}
*/
