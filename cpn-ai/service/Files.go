package service

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"encoding/json"
	"errors"
	"time"
)

type UserPrivateSizeResp struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Result struct {
		Data map[string]string
	}
}

type DirSizeRequest struct {
	BasePath string `json:"base_path"`
}

type SizeResponse struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Result struct {
		SumSize int64 `json:"sum_size"`
	}
}

func UserPrivateStatMap() (userPrivateSizeResp UserPrivateSizeResp) {
	err := PostFileStorage("/file/stat", nil, &userPrivateSizeResp)
	if err != nil || userPrivateSizeResp.Code != 0 {
		logger.Error("获取用户的私有空间大小调用失败，将使用默认大小进行,接口 file/stat")
		emailReq := EmailReq{
			From:    "",
			To:      "<EMAIL>,<EMAIL>",
			Subject: "实例关闭失败，需要人工介入 " + time.Now().Format(jsontime.TimeFormat),
			Content: "获取用户的私有空间大小调用失败，将使用默认大小进行,接口 file/stat",
		}
		EmailService.SendWarn(emailReq)
	}
	return userPrivateSizeResp
}

func GetDirSizeRemote(userId uint, dirPath string) (int64, error) {
	reqBody := DirSizeRequest{
		BasePath: dirPath,
	}
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		logger.Error("JSON序列化失败:", err)
		return 0, err
	}
	var result SizeResponse
	err = PostFileStorage("/file/dir_size", jsonData, &result)
	if err != nil {
		return 0, err
	}

	if result.Code != 0 {
		return 0, errors.New(result.Msg)
	}

	logger.Info("文件大小统计完成", dirPath)
	return result.Result.SumSize, nil
}
