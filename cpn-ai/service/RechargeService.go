package service

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/model"
	"errors"
	"strings"
	"time"
)

type _recharge struct {
}

var RechargeService _recharge

func (o *_recharge) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("RechargeService奔溃:", e)
		}
	}()
	logger.Info("RechargeService.Run 开始循环获取")
	lastId := uint(0)
	for {
		var recharge model.Recharge
		ary, err := recharge.GetListForCheck(lastId, 10)
		if err != nil {
			logger.Error(err, "  ", lastId)
			continue
		}
		logger.Info("查询到", len(ary), "条需要处理的订单数据")
		for _, item := range ary {
			lastId = item.ID
			if item.CreatedAt.Add(time.Duration(30) * time.Minute).After(time.Now()) {
				logger.Info("已经检索到最近30分钟的充值订单，暂时不做处理")
				lastId = uint(0)
				break
			}
			if err := o.HandleRecharge(item); err != nil {
				logger.Error(err, " ID:", item.ID)
			}
		}
		if len(ary) < 10 || lastId == 0 {
			logger.Info("查询到", len(ary), "条需要处理的订单数据,充值订单处理本次轮询结束")
			break
		}
	}
}

func (o *_recharge) HandleRecharge(item model.Recharge) error {
	if item.State != 0 {
		logger.Error("已经处理", item.ID, "  ", item.State)
		return nil
	}
	if item.PayChannel == enums.PayChannelEnum.AliPay {
		trade, err := AlipayService.TradeQueryByOutTradeNo(item.OutTradeNo)
		if err != nil {
			//{"code":"40004","msg":"Business Failed","sub_code":"ACQ.TRADE_NOT_EXIST","sub_msg":"交易不存在"}
			if strings.Contains(err.Error(), "TRADE_NOT_EXIST") {
				if err := item.SetState(err.Error(), enums.RechargeStateEnum.TRADE_NOT_EXIST); err != nil {
					logger.Error(err)
				}
				return nil
			}
			logger.Error(err)
			return err
		}
		logger.Info(trade)
		if trade.Response.TradeStatus == "TRADE_SUCCESS" {
			payTime, err := time.ParseInLocation("2006-01-02 15:04:05", trade.Response.SendPayDate, time.Local) //这里按照当前时区转
			if err != nil {
				logger.Error(err)
				return err
			}

			if err := o.HandlePaySuccessRecharge(item.OutTradeNo, trade.Response.TradeNo, payTime, utils.GetJsonFromStruct(trade)); err != nil {
				logger.Error(err)
				return err
			}
		} else if trade.Response.TradeStatus == "TRADE_CLOSED" {
			if err := item.SetState(utils.GetJsonFromStruct(trade), enums.RechargeStateEnum.TRADE_CLOSED); err != nil {
				logger.Error(err)
			}
			return nil
		} else {
			logger.Error("不是支付成功标识", item.OutTradeNo, "  ", trade.Response.TradeStatus) //TRADE_CLOSED  WAIT_BUYER_PAY
			//2024-06-21T18:01:07.575+0800	ERROR	service/RechargeService.go:91	不是支付成功标识r2024061414474600031937  WAIT_BUYER_PAY
		}
	} else if item.PayChannel == enums.PayChannelEnum.WechatPay {
		trade, err := WechatpayService.TradeQueryByOutTradeNo(item.OutTradeNo)
		if err != nil {
			logger.Error(err)
			return err
		}
		if trade.Code == 404 {
			if strings.Contains(trade.Error, "ORDER_NOT_EXIST") { //订单不存在
				if err := item.SetState(utils.GetJsonFromStruct(trade), enums.RechargeStateEnum.TRADE_NOT_EXIST); err != nil {
					logger.Error(err)
					return err
				}
				return nil
			}
		}
		if trade.Response.TradeState == "CLOSED" {
			if err := item.SetState(utils.GetJsonFromStruct(trade), enums.RechargeStateEnum.TRADE_CLOSED); err != nil {
				logger.Error(err)
			}
			return nil
		} else if trade.Response.TradeState == "SUCCESS" {
			//logger.Info(trade)
			outTradeNo := trade.Response.OutTradeNo
			tradeNo := trade.Response.TransactionId
			payTime, err := time.Parse(time.RFC3339, trade.Response.SuccessTime)
			if err != nil {
				logger.Error(err)
				return err
			}
			if err := o.HandlePaySuccessRecharge(outTradeNo, tradeNo, payTime, utils.GetJsonFromStruct(trade)); err != nil {
				logger.Error(err)
				return err
			}
		} else if trade.Response.TradeState == "NOTPAY" {
			logger.Info("订单未支付，暂时不处理")
		} else {
			logger.Error("不是支付成功标识", "trade:  ", utils.GetJsonFromStruct(trade))
		}
	}

	return nil
}

func (o *_recharge) HandlePaySuccessRecharge(outTradeNo string, tradeNo string, payTime time.Time, payCallbackJson string) error {

	lockKey := config.Bundle + ":HandlePaySuccessRecharge_" + outTradeNo
	defer common.RedisUnLock(lockKey)
	lock := common.RedisLock(lockKey, 1, 5*1000)
	if lock {
		logger.Info("开始处理支付成功逻辑", outTradeNo)
		var recharge model.Recharge
		if err := recharge.GetByOutTradeNo(outTradeNo); err != nil {
			logger.Error(err)
			return err
		}
		if recharge.State == 1 {
			logger.Info("支付状态已成功，不处理")
			return nil
		}
		if recharge.State != 0 {
			logger.Info("支付状态已变动，不处理")
			return errors.New("支付状态已变动，不处理")
		}

		var product model.RechargeProduct
		if recharge.ProductId > 0 {
			if err := product.GetById(recharge.ProductId); err != nil {
				logger.Error(err)
				return err
			}
		}

		recharge.PayTradeId = tradeNo
		recharge.PayTime = payTime
		recharge.PayCallbackJson = payCallbackJson
		if product.ProductCategory == enums.ProductCategoryEnum.Subscription {
			expiresDate := time.Time{}
			if enums.SubDurationEnum.Get(product.SubDuration) == "" {
				logger.Error("销售产品信息错误", product)
				return errors.New("销售产品信息错误")
			}
			expiresDate = enums.SubDurationEnum.GetExpiresDate(product.SubDuration, recharge.PayTime)
			if expiresDate.Equal(recharge.PayTime) {
				logger.Error("过期时间获取失败", product, expiresDate, product.SubDuration, recharge.PayTime)
				return errors.New("过期时间获取失败")
			}
			recharge.ExpiresDate = expiresDate
		}
		logger.Info("开始处理支付成功事务", outTradeNo)
		if err := model.Transactions.RechargeSuccess(&recharge); err != nil {
			logger.Error(err)
			return err
		} else {
			//发送提醒
			if recharge.State == 1 {
				if recharge.Action == enums.RechargeActionEnum.BuyCard {
					go func() {
						if msg, err := WarnService.RechargeBuyCardSuccess(recharge.UserId, recharge.Amount, recharge.CreatedAt); err != nil {
							logger.Error("发送请求(RechargeAmountSuccess)失败 userId:", recharge.UserId, "  recharge.ID:", recharge.ID, "  msg:", msg, " err:", err)
						} else {
							logger.Info("发送请求(RechargeAmountSuccess)成功 userId:", recharge.UserId, "  recharge.ID:", recharge.ID)
						}
					}()
				} else {
					go func() {
						if msg, err := WarnService.RechargeAmountSuccess(recharge.UserId, recharge.Amount, recharge.CreatedAt); err != nil {
							logger.Error("发送请求(RechargeAmountSuccess)失败 userId:", recharge.UserId, "  recharge.ID:", recharge.ID, "  msg:", msg, " err:", err)
						} else {
							logger.Info("发送请求(RechargeAmountSuccess)成功 userId:", recharge.UserId, "  recharge.ID:", recharge.ID)
						}
					}()
				}
			} else {
				logger.Error("不发送请求(RechargeAmountSuccess) userId:", recharge.UserId, "  recharge.ID:", recharge.ID, " state:", recharge.State)
			}
		}

		logger.Info("支付成功逻辑处理完成", outTradeNo)
		return nil
	} else {
		logger.Error("lock 失败")
		return errors.New("lock 等待超时")
	}
}
