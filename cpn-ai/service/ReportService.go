package service

import (
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/structs"
	"encoding/json"
	"errors"
	"os"
	"unsafe"
)

type report_ struct {
}

var ReportService report_

func (obj *report_) HandleDockerCommit(startupMark string, imageId uint, reportState string) error {
	msg := ""
	var podImage model.PodImage
	if err := podImage.GetById(imageId); err != nil {
		msg = "参数错误"
		logger.Error(msg, err, " imageId:", imageId)
		return err
	}
	if podImage.AuditStatus != enums.ImageAuditStatusEnum.Commiting {
		msg = "当前镜像不在提交中状态"
		logger.Error(msg, " AuditStatus:", podImage.AuditStatus, " imageId:", imageId)
		return errors.New(msg)
	}

	if reportState == enums.ReportStateEnum.Fail {
		if err := podImage.SetAuditStatus(enums.ImageAuditStatusEnum.CommintFail); err != nil {
			msg = "设置提交状态为失败出错"
			logger.Error(msg, err, " imageId:", imageId)
			return err
		} else {
			msg = "镜像提交失败"
			return nil
		}
	}

	if reportState != enums.ReportStateEnum.Success {
		msg = "未处理的上报状态"
		logger.Error(msg, " ReportState:", reportState, " imageId:", imageId)
		return errors.New(msg)
	} else {
		if err := podImage.SetAuditStatus(enums.ImageAuditStatusEnum.CommintSuccess); err != nil {
			msg = "设置提交状态为成功出错"
			logger.Error(msg, err, " imageId:", imageId)
			return err
		} else {
			msg = "镜像提交成功"
			return nil
		}
	}
}

func (obj *report_) HandleDockerPush(startupMark string, imageId uint, reportState string, reason, imageMeta string) error {
	msg := ""
	if reportState == enums.ReportStateEnum.Success || reportState == enums.ReportStateEnum.Fail {
	} else {
		msg = "未知状态"
		logger.Error(msg, " imageId:", imageId)
		return errors.New(msg)
	}
	var podImage model.PodImage
	if err := podImage.GetById(imageId); err != nil {
		msg = "参数错误"
		logger.Error(msg, err, " imageId:", imageId)
		return err
	}
	if podImage.AuditStatus != enums.ImageAuditStatusEnum.Pushing {
		msg = "当前镜像不在推送状态"
		logger.Error(msg, " AuditStatus:", podImage.AuditStatus, " imageId:", imageId)
		return errors.New(msg)
	}

	if reportState == enums.ReportStateEnum.Fail {
		if err := podImage.SetAuditStatusWithReason(enums.ImageAuditStatusEnum.PushFail, reason); err != nil {
			msg = "设置推送状态为失败出错"
			logger.Error(msg, err, " imageId:", imageId)
			return err
		} else {
			msg = "已设置为上传失败"
			return nil
		}
	}

	logger.Info("开始获取镜像信息 imageID:", podImage.ID)
	newReportState := enums.ReportStateEnum.Fail

	defer func() {

		if newReportState == enums.ReportStateEnum.Fail {
			if err := podImage.SetAuditStatusWithReason(enums.ImageAuditStatusEnum.PushFail, reason); err != nil {
				msg = "设置推送状态为失败出错"
				logger.Error(msg, err, " imageId:", imageId)
			} else {
				msg = "已设置为上传失败"
			}
		}

	}()

	var hub structs.HubRepositorie
	if reportState == enums.ReportStateEnum.Success {
		if podImage.StorageMode == enums.ImageStorageModeEnum.PrivateDisk { //磁盘存储的镜像这里特殊处理

			if savePath, err := GetPrivateImageSavePathByPodImage(podImage); err != nil {
				logger.Error(err)
				return err
			} else {
				if info, err := os.Stat(savePath); err != nil {
					return err
				} else {
					hub.Size = uint(info.Size())
					hub.PushTime = info.ModTime()
				}

			}

		} else {
			if err := GetHubImage(podImage.ImageType, podImage.ImageName, podImage.ImageTag, &hub); err != nil {
				msg = "从Hub获取镜像信息失败"
				logger.Error(msg, " ReportState:", reportState, " imageId:", imageId)
				return errors.New(msg)
			} else {
				if hub.PushTime.Before(podImage.CommitStartTime) {
					msg = "推送时间在提交时间之前，这个是之前推送的镜像"
					return errors.New(msg)
				}
			}
		}
	}

	if hub.Size <= 0 {
		msg = "未获取到镜像信息"
		logger.Error(msg, " imageID:", podImage.ID)
		return errors.New(msg)
	}
	mm := make(map[string]any)
	{
		var im struct {
			Id           string
			Size         int64
			LayerCount   int
			HistoryCount int
		}
		err := json.Unmarshal(unsafe.Slice(unsafe.StringData(imageMeta), len(imageMeta)), &im)
		if err == nil {
			mm["size"] = im.Size
			mm["sha256"] = im.Id
			mm["layer_count"] = im.LayerCount
		}
	}
	mm["size"] = hub.Size
	mm["sha256"] = hub.Digest
	mm["last_save_time"] = hub.PushTime
	mm["audit_status"] = enums.ImageAuditStatusEnum.PushSuccess
	mm["status"] = 1
	mm["reason"] = ""
	mm["image_meta"] = imageMeta
	if err := podImage.Updates(mm); err != nil {
		msg = "保存镜像信息失败"
		logger.Error(msg, err)
		return err
	} else {
		msg = "镜像推送成功"
		newReportState = enums.ReportStateEnum.Success
		if podImage.StorageMode == enums.ImageStorageModeEnum.PrivateDisk {
			msg = "镜像成功保存到个人磁盘"
		}

		var instance model.Instance
		if err := instance.GetByStartupMark(startupMark); err != nil {
			logger.Error(err, "  startupMark:", startupMark)
		} else {
			if err := instance.SetSaveImageId(imageId); err != nil {
				logger.Error(err, "  startupMark:", startupMark)
			}
		}

		if instance.InstanceType == enums.InstanceTypeEnum.Kol {
			var pod model.Pod
			if err := pod.GetById(instance.PodId); err != nil {
				msg = "查询Pod信息出错"
				logger.Error(msg, err)
			} else {
				if pod.AuditStatus == enums.PodAuditStatusEnum.AuditPass {
					if err := pod.SetAuditStatus(enums.PodAuditStatusEnum.Makeing); err != nil {
						msg = "设置Pod状态失败"
						logger.Error(msg, err)
					}
				}
			}
		}
		return nil
	}
}
