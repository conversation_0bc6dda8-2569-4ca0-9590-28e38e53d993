package service

//
//import (
//	"cpn-ai/common/logger"
//	"cpn-ai/enums"
//	"cpn-ai/model"
//	"time"
//)
//
//type reserve_ struct{}
//
//var ReserveService reserve_
//
//// HandleReserveLock 预约锁定gpu
//func (obj *reserve_) HandleReserveLock() {
//	var reserveOrder model.ReserveOrder
//
//	allUnstart := make([]model.ReserveOrder, 0)
//
//	if err := reserveOrder.GetByStatus(allUnstart, enums.ReserveOrderStatusEnum.Unstart); err != nil {
//		logger.Error("获取未开始预约订单失败", err.Error())
//		return
//	}
//
//	for _, order := range allUnstart {
//		// 判断开始时间是否在一个小时以内
//		if order.StartTime.Sub(time.Now()).Hours() <= 1 {
//			// todo 锁定显卡
//			// 1.获取所有指定本pod或者未指定pod的对应型号的空闲GPU列表
//			// 2. 锁定
//			for i := 0; i < order.Count; i++ {
//				// 创建实例
//
//			}
//
//			order.Status = enums.ReserveOrderStatusEnum.Locked
//			if err := order.Save(); err != nil {
//				logger.Error("锁定预约订单失败", err.Error())
//				continue
//			}
//		}
//	}
//
//}
//
//func (obj *reserve_) HandleReserveShutdown() {
//	var reserveOrder model.ReserveOrder
//	var orderDetail model.ReserveOrderDetail
//
//	allUnstart := make([]model.ReserveOrder, 0)
//
//	if err := reserveOrder.GetByStatus(allUnstart, enums.ReserveOrderStatusEnum.Started); err != nil {
//		logger.Error("获取已开始预约订单失败", err.Error())
//		return
//	}
//
//	for _, order := range allUnstart {
//		// 判断已经结束的订单
//		if order.EndTime.Before(time.Now()) {
//			orderDetails := make([]model.ReserveOrderDetail, 0)
//			if err := orderDetail.GetByReserveUuid(orderDetails, order.UUID); err != nil {
//				logger.Error("获取预约订单详情失败", err.Error())
//				continue
//			}
//			for _, reserveOrderDetail := range orderDetails {
//				if reserveOrderDetail.Status == enums.InstanceStatusEnum.Running {
//					// 停止实例
//
//				}
//			}
//
//		}
//
//	}
//}
