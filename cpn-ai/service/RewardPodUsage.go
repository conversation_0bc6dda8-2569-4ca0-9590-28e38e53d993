package service

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/structs"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"sync"
	"sync/atomic"
	"time"
)

type _rewardPodUsage struct {
	GeningInviterMap sync.Map
}

type geningPodUser struct {
	PodUserId   uint      `json:"pod_user_id"`
	RewardMonth time.Time `json:"reward_month"`
	GenAt       time.Time `json:"gen_at"`
}

type groupPodUser struct {
	PodUserId   uint            `json:"pod_user_id"`
	PodUserEarn decimal.Decimal `json:"pod_user_earn"`
}

var RewardPodUsageService _rewardPodUsage

func (obj *_rewardPodUsage) RunPodReward(startAutoId uint, endAutoId uint) error {
	commandKey := utils.GetUUID()
	atomic.AddInt32(&TaskRunningCount, 1)
	commandRunningValue := structs.RunningCommand{Command: "每条流水计算Pod佣金", StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	TaskRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&TaskRunningCount, -1)
		TaskRunning.Delete(commandKey)
	}()

	lastAutoId := uint(0)
	if startAutoId > 0 {
		lastAutoId = startAutoId
	} else {
		var balance model.AmountBalance
		if err := balance.GetLastCalcPodReward(); err != nil {
			if err != gorm.ErrRecordNotFound {
				logger.Error("获取起始需要计算Pod佣金的流水失败 err:", err)
				return err
			}
		} else {
			lastAutoId = balance.ID
		}
	}

	pageSize := 100
	for {
		var ary = make([]model.AmountBalance, 0)
		var balance model.AmountBalance
		if err := balance.ListForCalcPodReward(&ary, lastAutoId, pageSize); err != nil {
			logger.Error(err)
			return err
		}
		logger.Info("RunPodReward 获取到", len(ary), "条需要计算Pod佣金的数据 lastAutoId:", lastAutoId)
		for i := 0; i < len(ary); i++ {
			item := ary[i]
			lastAutoId = item.ID

			if val, ok := TaskRunning.Load(commandKey); ok {
				commandRunningValue = val.(structs.RunningCommand)
				commandRunningValue.Progress = fmt.Sprintf("当前流水ID:%d", lastAutoId)
				TaskRunning.Store(commandKey, commandRunningValue)
			}

			pre := fmt.Sprintf("RunPodReward Id:%d orderNo:%s  podId:%d cardId:%d ", item.ID, item.OrderNo, item.PodId, item.CardId)
			if endAutoId > 0 && lastAutoId > endAutoId {
				logger.Info("已到结束Id,退出循环 endAutoId：", endAutoId)
				return nil
			}
			if item.PodCalcAt.After(common.NationalDay) {
				//logger.Info(pre, "已经计算过，不处理")
				continue
			}
			if item.OrderType != enums.OrderTypeEnum.CostPod {
				logger.Info(pre, "订单类型不符合，不处理，orderType:", item.OrderType)
				continue
			}

			if err := obj.CalcPodReward(item); err != nil {
				logger.Error(err, " obj.CalcPodReward(item) AmountBalance AutoId:", item.ID)
				logKey := "CalcPodReward"
				EmailService.AddNeedSend(logKey, fmt.Sprintf("计算Pod佣金失败，err:%s, AmountBalance AutoId:%d 检查时间:%s", err.Error(), item.ID, jsontime.Now().String()))
				return err
			}
		}
		if len(ary) < pageSize {
			break
		}
	}

	return nil
}

func (obj *_rewardPodUsage) CalcPodReward(balance model.AmountBalance) error {
	if balance.OccurredAmount.GreaterThan(decimal.Zero) {
		err := errors.New("金额不正确")
		logger.Error(err, " orderNo:", balance.OrderNo, " OccurredAmount:", balance.OccurredAmount)
		return err
	}
	//if balance.PodCalcAt.After(common.NationalDay) {
	//	err := errors.New("已经计算过，不处理")
	//	logger.Error(err, " orderNo:", balance.OrderNo, " PodCalcAt:", balance.PodCalcAt)
	//	return err
	//}
	if balance.OrderType != enums.OrderTypeEnum.CostPod {
		err := errors.New("订单类型不符合，不处理")
		logger.Error(err, " orderNo:", balance.OrderNo, " orderType:", balance.OrderType)
		return err
	}
	m := make(map[string]interface{})
	var pod model.Pod
	if err := pod.GetById(balance.PodId); err != nil {
		logger.Error(err)
		return err
	}

	discountRate := decimal.NewFromInt(1)
	if balance.CardId > 0 {
		var card model.Card
		if err := card.GetById(balance.CardId); err != nil {
			logger.Error(err)
			return err
		}
		if card.SalePrice.LessThan(card.FacePrice) && card.FacePrice.GreaterThan(decimal.Zero) {
			discountRate = card.SalePrice.Div(card.FacePrice)
		}
		m["card_discount_rate"] = discountRate
		m["card_sale_price"] = card.SalePrice
		m["card_face_price"] = card.FacePrice
	}

	podEarn := balance.OccurredAmount.Neg().Mul(discountRate)

	orderInfo := balance.OrderInfo
	if orderInfo == "" {
		orderInfo = "{}"
	}
	mOrderInfo := utils.GetMapFromJson(orderInfo)
	if mOrderInfo == nil {
		err := errors.New("转换订单附属信息失败")
		logger.Error(err, " mOrderInfo==nil orderNo:", balance.OrderNo)
		return err
	}
	mOrderInfo["PodReward"] = m
	orderInfo = utils.GetJsonFromStruct(mOrderInfo)

	if err := balance.UpdatePodReward(pod.UserId, podEarn, orderInfo); err != nil {
		logger.Error(err, " 更新流水Pod佣金失败 orderNo:", balance.OrderNo)
		return err
	} else {
		return nil
	}
}

func (obj *_rewardPodUsage) RunEveryday(calcTime time.Time) error {
	//lockKey := ""
	//defer func() {
	//	if lockKey != "" {
	//		common.RedisUnLock(lockKey)
	//	}
	//}()
	//lockKey = enums.RedisKeyEnum.LockKey + "RewardPodUsage" + "RunEveryday"
	//if !common.RedisLock(lockKey, 1, 1000*10) {
	//	lockKey = ""
	//	err := errors.New("该函数正在执行中，请勿重复操作")
	//	logger.Error(err)
	//	return err
	//}
	commandKey := utils.GetUUID()
	atomic.AddInt32(&TaskRunningCount, 1)
	commandRunningValue := structs.RunningCommand{Command: "统计Pod每月佣金", StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	TaskRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&TaskRunningCount, -1)
		TaskRunning.Delete(commandKey)
	}()
	msg := ""
	page := 1
	pageSize := 1000
	//now := time.Now()
	//now = time.Date(2024, 11, 7, 0, 0, 0, 0, time.Now().Location())
	for {
		aryGroupPodUser := make([]groupPodUser, 0)
		var amountBalance model.AmountBalance
		if err := amountBalance.StatPodRewardOfCurrentMonth(&aryGroupPodUser, 0, calcTime, page, pageSize); err != nil {
			msg = "Group统计创作者Pod佣金失败"
			logger.Error(msg, err)
			return err
		}
		logger.Info("获取到", len(aryGroupPodUser), "条创作者统计佣金")
		for _, inviter := range aryGroupPodUser {
			//if calcTime.Day() == 1 {
			//	lastMonth := calcTime.AddDate(0, -1, 0)
			//	if err := obj.GenRewardRecord(inviter.PodUserId, lastMonth, inviter.PodUserEarn); err != nil {
			//		if strings.Contains(err.Error(), "不做统计") {
			//			logger.Error("计算用户", inviter.PodUserId, "   上个月", lastMonth, "的邀Pod金失败 err:", err)
			//		} else {
			//			logger.Error("计算用户", inviter.PodUserId, "   上个月", lastMonth, "的邀Pod金失败 err:", err)
			//			return err
			//		}
			//	} else {
			//		logger.Info("计算用户", inviter.PodUserId, "   上个月", lastMonth, "的Pod佣金成功")
			//	}
			//}
			if err := obj.GenRewardRecord(inviter.PodUserId, calcTime, inviter.PodUserEarn); err != nil {
				logger.Error("计算创作者", inviter.PodUserId, "  本月", calcTime, "的Pod佣金失败 err:", err)
				return err
			} else {
				logger.Info("计算创作者", inviter.PodUserId, "  本月", calcTime, "的Pod佣金成功")
			}
			time.Sleep(time.Millisecond * 100)
		}
		if len(aryGroupPodUser) < pageSize {
			break
		}
		page += 1
	}

	return nil
}

func (obj *_rewardPodUsage) GenRewardRecord(podUserId uint, rewardMonth time.Time, podUserEarn decimal.Decimal) error {
	trace := fmt.Sprintf("podUserId:%d,  rewardMonth:%v podUserEarn:%v", podUserId, rewardMonth, podUserEarn)

	if val, ok := obj.GeningInviterMap.Load(podUserId); ok {
		err := errors.New(fmt.Sprintf("创作者%d Pod佣金正在计算中，请稍后再尝试 %v", podUserId, val))
		logger.Error(err)
		return err
	}
	gening := geningPodUser{
		PodUserId:   podUserId,
		RewardMonth: rewardMonth,
		GenAt:       time.Now(),
	}
	obj.GeningInviterMap.Store(podUserId, gening)
	defer func() {
		obj.GeningInviterMap.Delete(podUserId)
	}()

	msg := ""
	now := time.Now()
	//today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	curMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	if rewardMonth.After(curMonth.AddDate(0, 1, 0)) {
		err := errors.New("还未到统计时间")
		logger.Error(trace, err)
		return err
	}
	if rewardMonth.Before(time.Date(2024, 1, 1, 0, 0, 0, 0, now.Location())) {
		err := errors.New("该统计时间太早了")
		logger.Error(trace, err)
		return err
	}

	rewardMonthIsLastMonth := false
	if rewardMonth.Before(curMonth) {
		rewardMonthIsLastMonth = true
	}

	rewardMonth = time.Date(rewardMonth.Year(), rewardMonth.Month(), 1, 0, 0, 0, 0, rewardMonth.Location())
	rewardMonthNum := utils.String2Int(rewardMonth.Format("200601"))
	if rewardMonthNum == 0 {
		msg = "统计月份参数错误"
		err := errors.New(msg)
		logger.Error(err, " rewardMonth:", rewardMonth)
		return err
	}

	var rewardRecord model.RewardRecord
	orderType := enums.OrderTypeEnum.RewardPodUsage
	if err := rewardRecord.GetByRewardMonth(orderType, podUserId, rewardMonthNum); err != nil {
		if err != gorm.ErrRecordNotFound {
			msg = "查询Pod佣金记录失败"
			logger.Error(msg, err)
			return err
		}
	} else {
		if rewardRecord.State != enums.RewardRecordStateEnum.Stating {
			msg = "Pod佣金记录不在统计状态，不做统计"
			err := errors.New(msg)
			logger.Error(trace, err)
			return err
		}
	}

	//var stats = struct {
	//	TotalCostAmount   decimal.Decimal `json:"total_cost_amount"`
	//	TotalRewardAmount decimal.Decimal `json:"total_reward_amount"`
	//}{}
	//
	//var statsUserCost model.UserCost
	//if err := statsUserCost.StatsByInviterAndMonth(&stats, inviterUserId, rewardMonthNum); err != nil {
	//	msg = "统计用户消费数据失败"
	//	logger.Error(msg, " err:", err)
	//	return err
	//}

	if rewardRecord.ID == 0 {
		rewardRecord.Nanoid = utils.Generate12NanoId()
		rewardRecord.UserId = podUserId
		rewardRecord.RewardMonth = rewardMonthNum
	}
	rewardRecord.RewardAmount = podUserEarn
	rewardRecord.OrderType = orderType
	if rewardRecord.State != enums.RewardRecordStateEnum.Stating {
		err := errors.New("记录不在统计状态，不能更新")
		logger.Error(trace, err)
		return err
	}
	rewardRecord.InvitationReward = "{}"
	if err := rewardRecord.Save(); err != nil {
		logger.Error(trace, " 更新Pod佣金记录失败 err:", err)
		return err
	}

	if rewardMonthIsLastMonth && rewardRecord.State == enums.RewardRecordStateEnum.Stating {
		//rewardRecord.State = enums.RewardRecordStateEnum.Locked
		//需要检测上个月的流水计算是否是都完成

		var check model.AmountBalance
		if err := check.CheckPodReward(rewardMonth); err != nil {
			if err != gorm.ErrRecordNotFound {
				logger.Error(trace, "rewardMonth:", rewardMonth.String(), " 检查pod流水记录失败 err:", err)
				return err
			}
		} else {
			err = errors.New("还有未计算完的pod流水记录")
			logger.Error(trace, "rewardMonth:", rewardMonth.String(), "  err:", err)
			return err
		}

		if err := rewardRecord.StateToLock(); err != nil {
			logger.Error(trace, err)
			return err
		}
	}

	return nil
}

func (obj *_rewardPodUsage) GetRewardAmount(costAmount decimal.Decimal, invitationRewardRule structs.InvitationRewardRule, leijiAwardAmount decimal.Decimal) (decimal.Decimal, bool, error) {
	bLock := false
	if invitationRewardRule.RuleId == 1 {
		localReward := costAmount.Mul(invitationRewardRule.CostRewardPercent)

		if invitationRewardRule.LimitReward.GreaterThan(decimal.Zero) {
			limitReward := invitationRewardRule.LimitReward.Sub(leijiAwardAmount)
			if limitReward.LessThanOrEqual(decimal.Zero) {
				return decimal.Zero, true, nil
			}

			if leijiAwardAmount.Add(localReward).LessThanOrEqual(limitReward) {
				return localReward, false, nil
			} else {
				return limitReward, true, nil
			}
		} else {
			return localReward, false, nil
		}

	} else if invitationRewardRule.RuleId == 2 {
		return decimal.Zero, bLock, errors.New("未处理的奖励规则")
		//canAward := invitationRewardRule.LimitReward.Sub(beAwardAmount)
		//if canAward.LessThanOrEqual(decimal.Zero) {
		//	return decimal.Zero, nil
		//}
		//amount := costAmount.Mul(invitationRewardRule.CostRewardPercent)
		//
		//if beAwardAmount.Add(amount).LessThanOrEqual(invitationRewardRule.LimitReward) {
		//	return amount, nil
		//} else {
		//	return invitationRewardRule.LimitReward.Sub(beAwardAmount), nil
		//}
	} else {
		return decimal.Zero, bLock, errors.New("未处理的奖励规则")
	}
}

func (obj *_rewardPodUsage) DataTransfer() error {
	var balance model.AmountBalance
	if err := balance.GetById(1145818); err != nil {
		return err
	}
	if err := obj.DataItemTransfer(balance); err != nil {
		logger.Error(err)
	}
	return nil
}

func (obj *_rewardPodUsage) DataItemTransfer(balance model.AmountBalance) error {
	if balance.OccurredAmount.GreaterThan(decimal.Zero) {
		err := errors.New("金额不正确")
		return err
	}
	var calculate model.AmountCalculate
	calculate.AmountBalance = balance
	m := make(map[string]interface{})
	var pod model.Pod
	if err := pod.GetById(balance.PodId); err != nil {
		logger.Error(err)
		return err
	}

	discountRate := decimal.NewFromInt(1)
	if balance.CardId > 0 {
		var card model.Card
		if err := card.GetById(balance.CardId); err != nil {
			logger.Error(err)
			return err
		}
		if card.SalePrice.LessThan(card.FacePrice) && card.FacePrice.GreaterThan(decimal.Zero) {
			discountRate = card.SalePrice.Div(card.FacePrice)
		}
		m["card_discount_rate"] = discountRate
		m["card_sale_price"] = card.SalePrice
		m["card_face_price"] = card.FacePrice
	}

	podEarn := balance.OccurredAmount.Neg().Mul(discountRate)

	calculate.PodUserId = pod.UserId
	calculate.PodEarn = podEarn
	calculate.PodCalcAt = time.Now()
	calculate.CalcInfo = utils.GetJsonFromStruct(m)
	if err := calculate.Create(); err != nil {
		//Duplicate entry '1145818' for key 'T_AmountCalculate.PRIMARY'
		return err
	}
	return nil
}
