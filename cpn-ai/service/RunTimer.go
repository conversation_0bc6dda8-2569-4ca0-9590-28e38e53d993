package service

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/internal/ccm/train"
	"cpn-ai/model"
	"cpn-ai/service/tasklog"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/go-co-op/gocron"
)

var RunPause bool //服务是否暂停
var RunTask [1]bool

var TaskStart time.Time
var TaskRunningCount int32
var TaskRunning sync.Map

func RunTimer() {
	TaskStart = time.Now()
	logger.Info("进入RunTimer ", TaskStart)
	defer func() {
		if e := recover(); e != nil {
			logger.Error("RunTimer奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("RunTimer panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	go func() {
		logKey := "RunTimerStart"
		msg := ""
		if config.Env == enums.EnvEnum.PRODUCTION {
			msg = "正式环境，算云主服务启动"
		} else if config.Env == enums.EnvEnum.ONLINE {
			msg = "线上环境，算云主服务启动"
		}
		if msg != "" {
			logger.Info(msg)
			EmailService.AddNeedSend(logKey, msg+",启动时间:"+time.Now().Format("2006-01-02 15:04:05"))
		}
	}()

	train.Loop()

	if config.Env != enums.EnvEnum.PRODUCTION {

		//go func() {
		//	time.Sleep(time.Second * 10)
		//	logger.Info("开始清理过期实例")
		//	if err := Clear.ClearOverDayInstance(); err != nil {
		//		logger.Error("过期实例清理出错 err:", err)
		//	}
		//	logger.Info("过期实例清理完成")
		//
		//}()

		go func() {
			ticker := time.NewTicker(600 * time.Second)
			defer ticker.Stop()
			for range ticker.C {
				logger.Info("开始计算Pod佣金")
				if err := RewardPodUsageService.RunPodReward(0, 0); err != nil {
					logger.Error("计算Pod佣金出错 err:", err)
					logKey := "RewardPodUsageService.RunPodReward"
					EmailService.AddNeedSend(logKey, "计算Pod佣金出错 err:"+err.Error()+" 检查时间:"+jsontime.Now().String())
				}
				logger.Info("Pod佣金计算完成")
			}
		}()

		go func() {
			// 创建一个新的调度器
			s := gocron.NewScheduler(time.Local)
			// 每天在上午1点20分执行任务
			s.Every(1).Day().At("01:20").Do(func() {
				logger.Info("开始执行邀请佣金计算业务逻辑")
				if err := InvitationService.RunEveryday(); err != nil {
					logger.Error("每日邀请佣金计算出错 err:", err)
					logKey := "InvitationService.RunEveryday"
					EmailService.AddNeedSend(logKey, "每日邀请佣金计算出错 err:"+err.Error()+" 检查时间:"+jsontime.Now().String())
				}
				logger.Info("邀请佣金计算业务逻辑完成,延迟10秒")

				calcTime := time.Now()
				if calcTime.Day() == 1 {
					logger.Info("开始统计Pod上个月佣金")
					lastMonth := calcTime.AddDate(0, -1, 0)
					if err := RewardPodUsageService.RunEveryday(lastMonth); err != nil {
						logger.Error("统计Pod上个月佣金出错 calcTime:", lastMonth, " err:", err)
						logKey := "RewardPodUsageService.RunEveryday"
						EmailService.AddNeedSend(logKey, "统计Pod上个月佣金出错 统计时间："+lastMonth.String()+" err:"+err.Error()+" 检查时间:"+jsontime.Now().String())
					}
					logger.Info("Pod上个月佣金统计完成")
				}

				logger.Info("开始统计Pod每月佣金")
				if err := RewardPodUsageService.RunEveryday(calcTime); err != nil {
					logger.Error("统计Pod每月佣金出错 calcTime:", calcTime, " err:", err)
					logKey := "RewardPodUsageService.RunEveryday"
					EmailService.AddNeedSend(logKey, "统计Pod每月佣金出错 统计时间："+calcTime.String()+" err:"+err.Error()+" 检查时间:"+jsontime.Now().String())
				}
				logger.Info("Pod每月佣金统计完成")

				//logger.Info("开始清理过期实例")
				//if err := Clear.ClearOverDayInstance(); err != nil {
				//	logger.Error("过期实例清理出错 err:", err)
				//}
				//logger.Info("过期实例清理完成")

			})
			// 开始调度器
			logger.Info("启动每日执行定时器")
			s.StartBlocking()
		}()

		go func() {
			ticker := time.NewTicker(1 * time.Second)
			defer ticker.Stop()
			for range ticker.C {
				//logger.Info("开始执行CheckPodImage业务逻辑")
				//if err := Clear.CheckPodImage(); err != nil {
				//	logger.Error("Clear.CheckPodImage err:", err)
				//}

				logger.Info("开始执行LocationService业务逻辑")
				LocationService.Run()
				logger.Info("执行LocationService业务逻辑结束")
			}
		}()

		//go func() {
		//	time.Sleep(time.Second * 10)
		//	logger.Info("开始执行StatDayService修复业务逻辑")
		//	StatDayService.RunRepairday()
		//	logger.Info("执行StatDayService修复业务逻辑结束")
		//}()

		go func() {
			// 创建一个新的调度器
			s := gocron.NewScheduler(time.Local)
			// 每天在上午1点20分执行任务
			s.Every(1).Day().At("0:15").Do(func() {
				logger.Info("开始执行StatDayService业务逻辑")
				StatDayService.RunEveryday()
				logger.Info("执行StatDayService业务逻辑结束")
			})
			// 开始调度器
			logger.Info("启动每日执行定时器")
			s.StartBlocking()
		}()

		go func() {
			// 创建一个新的调度器
			s := gocron.NewScheduler(time.Local)
			executeTask := func() {
				if err := Clear.HandleMonitor(); err != nil {
					logger.Error("5分钟间监测实例情况 err:", err)
				} else {
					logger.Info("5分钟间监测实例情况完成")
				}
			}

			// 设置在特定时间点执行任务
			minutePoints := []int{0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55}
			for _, minute := range minutePoints {
				timeString := fmt.Sprintf("%02d * * * *", minute) // 使用 cron 表达式
				logger.Info("timeString:", timeString)
				if _, err := s.Cron(timeString).Do(executeTask); err != nil {
					logger.Error("Error scheduling task: ", err)
				}
			}
			logger.Info("5分钟实例监测调度启动")
			// 启动调度器
			s.StartBlocking()
			logger.Info("5分钟实例监测调度完成")
		}()

		logger.Info("不是PRODUCTION环境，不启动RunTimer")

		//go func() {
		//
		//	logger.Info("开始执行WithdrawService业务逻辑")
		//	WithdrawService.Run()
		//	logger.Info("WithdrawService业务逻辑执行结束")
		//}()

		//go func() {
		//	ticker := time.NewTicker(305 * time.Second)
		//	defer ticker.Stop()
		//	for range ticker.C {
		//		logger.Info("开始执行检查推送失败的镜像业务逻辑")
		//		Clear.HandleImagePushFail() //检查推送失败的镜像
		//		logger.Info("检查推送失败的镜像业务逻辑执行结束")
		//	}
		//}()

		return
	}

	go func() {
		// 创建一个新的调度器
		s := gocron.NewScheduler(time.Local)
		executeTask := func() {
			logger.Info("10分钟间隔定时关机")
			if err := Clear.HandleShutdownRegular(); err != nil {
				logger.Error("10分钟间隔定时关机 err:", err)
			} else {
				logger.Info("10分钟间隔定时关机完成")
			}

		}

		// 设置在特定时间点执行任务
		minutePoints := []int{0, 10, 20, 30, 40, 50}
		for _, minute := range minutePoints {
			timeString := fmt.Sprintf("%02d * * * *", minute) // 使用 cron 表达式
			if _, err := s.Cron(timeString).Do(executeTask); err != nil {
				logger.Error("Error scheduling task: ", err)
			}
		}
		logger.Info("10分钟调度测试启动")
		// 启动调度器
		s.StartBlocking()
		logger.Info("10分钟调度测试启动完成")
	}()

	//go func() {
	//	ticker := time.NewTicker(3 * time.Second)
	//	defer ticker.Stop()
	//	for range ticker.C { //模型扣费 这个是不是可以不要运行了
	//		logger.Info("ChargingService每隔30秒业务逻辑执行开始")
	//		ChargingService.Run()
	//		logger.Info("ChargingService每隔30秒业务逻辑执行结束")
	//	}
	//}()

	go func() {
		// 创建一个新的调度器
		s := gocron.NewScheduler(time.Local)

		// 获取当前时间
		now := time.Now()

		// 计算距离下一个整点的时间
		nextHour := now.Truncate(time.Hour).Add(time.Hour)

		// 创建一个定时任务，从下一个整点开始，每隔一小时执行一次
		s.Every(1).Hour().StartAt(nextHour).Do(func() {
			// 在每个整点执行的操作
			logger.Info("延迟3秒，执行程序整点程序...")
			time.Sleep(3 * time.Second)
			logger.Info("开始执行程序整点程序...")

			var runSettleOperationLog model.OperationLog
			runSettleLogStruct := SystemAccess{
				StartAt: time.Now(),
				Msg:     "开始执行实例整点扣费程序",
			}
			if operatorLog_, err := InstanceAccessService.SaveSystemAccess(runSettleLogStruct); err != nil {
				logger.Error("保存系统日志失败  logStruct:", runSettleLogStruct, " err:", err)
			} else {
				runSettleOperationLog = operatorLog_
			}
			if err := ChargingService.RunSettle(); err != nil {
				logger.Error("实例整点扣费程序执行失败", err)
				logKey := "RunSettle"
				EmailService.AddNeedSend(logKey, fmt.Sprintf("实例整点扣费程序执行失败，err:%s,检查时间:%s", err.Error(), jsontime.Now().String()))

				runSettleLogStruct.CompletedAt = time.Now()
				runSettleLogStruct.Msg = "实例整点扣费程序执行失败"
				runSettleLogStruct.Err = err.Error()
				runSettleLogStruct.Elapse = utils.Seconds2Time(runSettleLogStruct.CompletedAt.Unix() - runSettleLogStruct.StartAt.Unix())
				if err := InstanceAccessService.UpdateSystemAccess(runSettleOperationLog, runSettleLogStruct); err != nil {
					logger.Error(runSettleLogStruct, " 更新系统日志失败 err:", err)
				}

			} else {
				logger.Info("实例整点扣费程序执行成功")
				runSettleLogStruct.CompletedAt = time.Now()
				runSettleLogStruct.Msg = "实例整点扣费程序执行成功"
				runSettleLogStruct.Elapse = utils.Seconds2Time(runSettleLogStruct.CompletedAt.Unix() - runSettleLogStruct.StartAt.Unix())
				if err := InstanceAccessService.UpdateSystemAccess(runSettleOperationLog, runSettleLogStruct); err != nil {
					logger.Error(runSettleLogStruct, " 更新系统日志失败 err:", err)
				}
			}

			var runImageStoreSettleOperationLog model.OperationLog
			runImageStoreSettleLogStruct := SystemAccess{
				StartAt: time.Now(),
				Msg:     "开始执行镜像存储整点扣费程序",
			}
			if operatorLog_, err := InstanceAccessService.SaveSystemAccess(runImageStoreSettleLogStruct); err != nil {
				logger.Error("保存系统日志失败  logStruct:", runImageStoreSettleLogStruct, " err:", err)
			} else {
				runImageStoreSettleOperationLog = operatorLog_
			}
			if err := ChargingService.RunImageStoreSettle(); err != nil {
				logger.Error("镜像存储整点扣费程序执行失败", err)
				logKey := "RunImageStoreSettle"
				EmailService.AddNeedSend(logKey, fmt.Sprintf("镜像存储整点扣费程序执行失败，err:%s,检查时间:%s", err.Error(), jsontime.Now().String()))

				runImageStoreSettleLogStruct.CompletedAt = time.Now()
				runImageStoreSettleLogStruct.Msg = "镜像存储整点扣费程序执行失败"
				runImageStoreSettleLogStruct.Err = err.Error()
				runImageStoreSettleLogStruct.Elapse = utils.Seconds2Time(runImageStoreSettleLogStruct.CompletedAt.Unix() - runImageStoreSettleLogStruct.StartAt.Unix())
				if err := InstanceAccessService.UpdateSystemAccess(runImageStoreSettleOperationLog, runImageStoreSettleLogStruct); err != nil {
					logger.Error(runImageStoreSettleLogStruct, " 更新系统日志失败 err:", err)
				}
			} else {
				logger.Info("镜像存储整点扣费程序执行成功")

				runImageStoreSettleLogStruct.CompletedAt = time.Now()
				runImageStoreSettleLogStruct.Msg = "镜像存储整点扣费程序执行成功"
				runImageStoreSettleLogStruct.Elapse = utils.Seconds2Time(runImageStoreSettleLogStruct.CompletedAt.Unix() - runImageStoreSettleLogStruct.StartAt.Unix())
				if err := InstanceAccessService.UpdateSystemAccess(runImageStoreSettleOperationLog, runImageStoreSettleLogStruct); err != nil {
					logger.Error(runImageStoreSettleLogStruct, " 更新系统日志失败 err:", err)
				}
			}

			var runCloudStoreSettleOperationLog model.OperationLog
			runCloudStoreSettleLogStruct := SystemAccess{
				StartAt: time.Now(),
				Msg:     "开始执行云存储整点扣费程序",
			}
			if operatorLog_, err := InstanceAccessService.SaveSystemAccess(runCloudStoreSettleLogStruct); err != nil {
				logger.Error("保存系统日志失败  logStruct:", runCloudStoreSettleLogStruct, " err:", err)
			} else {
				runCloudStoreSettleOperationLog = operatorLog_
			}
			if err := ChargingService.RunCloudStoreSettle(); err != nil {
				logger.Error("云存储整点扣费程序执行失败", err)
				logKey := "RunCloudStoreSettle"
				EmailService.AddNeedSend(logKey, fmt.Sprintf("云存储整点扣费程序执行失败，err:%s,检查时间:%s", err.Error(), jsontime.Now().String()))

				runCloudStoreSettleLogStruct.CompletedAt = time.Now()
				runCloudStoreSettleLogStruct.Msg = "云存储整点扣费程序执行失败"
				runCloudStoreSettleLogStruct.Err = err.Error()
				runCloudStoreSettleLogStruct.Elapse = utils.Seconds2Time(runCloudStoreSettleLogStruct.CompletedAt.Unix() - runCloudStoreSettleLogStruct.StartAt.Unix())
				if err := InstanceAccessService.UpdateSystemAccess(runCloudStoreSettleOperationLog, runCloudStoreSettleLogStruct); err != nil {
					logger.Error(runCloudStoreSettleLogStruct, " 更新系统日志失败 err:", err)
				}
			} else {
				logger.Info("云存储整点扣费程序执行成功")

				runCloudStoreSettleLogStruct.CompletedAt = time.Now()
				runCloudStoreSettleLogStruct.Msg = "云存储整点扣费程序执行成功"
				runCloudStoreSettleLogStruct.Elapse = utils.Seconds2Time(runCloudStoreSettleLogStruct.CompletedAt.Unix() - runCloudStoreSettleLogStruct.StartAt.Unix())
				if err := InstanceAccessService.UpdateSystemAccess(runCloudStoreSettleOperationLog, runCloudStoreSettleLogStruct); err != nil {
					logger.Error(runCloudStoreSettleLogStruct, " 更新系统日志失败 err:", err)
				}
			}

		})

		// 启动调度器
		s.StartBlocking()
	}()

	//go func() {后面再仔细看一下
	//
	//	ticker := time.NewTicker(5 * time.Minute)
	//	defer ticker.Stop()
	//	for range ticker.C {
	//		logger.Info("开始执行SchedService业务逻辑")
	//		SchedService.Run()
	//		logger.Info("SchedService业务逻辑执行结束")
	//	}
	//}()

	go func() {
		ticker := time.NewTicker(305 * time.Second)
		defer ticker.Stop()
		for range ticker.C {
			logger.Info("开始执行HandleBootInProgress业务逻辑")
			//Clear.HandleReloadInitVirtual() //重新加载虚拟机信息
			Clear.HandleBootInProgress()       //处理实例正在启动的信息，主要看是否长时间一直在启动中的状态
			Clear.HandleShutdownInProgress()   //处理正在关机中的实例，如果长时间处于关机失败情况，需要人工介入
			Clear.HandleCheckRunningProgress() //处理实例运行中，但是对应Docker确定已经不存在的情况
			Clear.HandleImagePushingProgress() //检查正在推送的镜像是否超时
			Clear.HandleImagePushFail()        //检查推送失败的镜像
			logger.Info("HandleBootInProgress业务逻辑执行结束")
		}
	}()

	go func() {
		ticker := time.NewTicker(30 * time.Minute)
		defer ticker.Stop()
		for range ticker.C {
			logger.Info("开始执行StartupMark日志清理业务逻辑")
			if err := StartupLog.ScanAndHandleKeys(); err != nil {
				logger.Error(err)
			}
			logger.Info("StartupMark日志清理业务逻辑执行结束")

			logger.Info("开始执行TaskLog日志清理业务逻辑")
			if err := tasklog.ScanAndHandleKeys(); err != nil {
				logger.Error(err)
			}
			logger.Info("TaskLog日志清理业务逻辑执行结束")

			logger.Info("开始检查镜像仓库存储容量")
			if hubStat, err := StatisticsHub(); err != nil {
				logger.Error("镜像仓库容量获取失败 err:", err)
			} else {
				if hubStat.TotalStorageConsumption == 0 {
					logger.Error("镜像仓库容量获取失败 ", utils.GetJsonFromStruct(hubStat))
				} else {
					t := float64(hubStat.TotalStorageConsumption) / (float64(1024) * 4)
					if t < 1 {
						logKey := "HubStatTotalStorageConsumption"
						EmailService.AddNeedSend(logKey, fmt.Sprintf("镜像仓库存储已不足1T，当前容量%fT,检查时间:%s", jsontime.Now().String()))
					}
					logger.Info("镜像仓库存储容量检查完成,当前使用容量为", t, "T")
				}
			}

		}
	}()

	go func() {
		ticker := time.NewTicker(30 * time.Minute)
		defer ticker.Stop()
		for range ticker.C {
			logger.Info("开始执行RechargeService业务逻辑")
			RechargeService.Run()
			logger.Info("RechargeService业务逻辑执行结束")

			logger.Info("开始执行WithdrawService业务逻辑")
			WithdrawService.Run()
			logger.Info("WithdrawService业务逻辑执行结束")
		}
	}()

	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()
		for range ticker.C {
			logger.Info("开始执行EmailService.ClearNeedSend业务逻辑")
			EmailService.ClearNeedSend()
			logger.Info("EmailService.ClearNeedSend业务逻辑执行结束")
		}
	}()

	//// 处理预约锁定gpu定时任务
	//go func() {
	//	ticker := time.NewTicker(5 * time.Minute)
	//	defer ticker.Stop()
	//	for range ticker.C {
	//		logger.Info("开始执行ReserveService.HandleReserveLock(预约单锁定)业务逻辑")
	//		ReserveService.HandleReserveLock()
	//		logger.Info("ReserveService.HandleReserveLock(预约单锁定)业务逻辑执行结束")
	//	}
	//}()
	//
	//// 处理预约任务定时关机任务
	//go func() {
	//	ticker := time.NewTicker(5 * time.Minute)
	//	defer ticker.Stop()
	//	for range ticker.C {
	//		logger.Info("开始执行ReserveService.HandleReserveShutdown(预约单定时关机)业务逻辑")
	//		ReserveService.HandleReserveShutdown()
	//		logger.Info("ReserveService.HandleReserveShutdown(预约单定时关机)业务逻辑执行结束")
	//	}
	//}()
	logger.Info("进出RunTimer ", time.Now())
}
