package service

import (
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/structs"
	"encoding/json"
	"errors"
	"io"
	"maps"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"
)

var (
	squashMutex  sync.Mutex
	squashImages = make(map[string]string)
)

const squashAddr = "10.20.101.25:8192"

func IsSquashing(imageUuid string) bool {
	squashMutex.Lock()
	defer squashMutex.Unlock()
	_, ok := squashImages[imageUuid]
	return ok
}

func updatePodImageHubInfo(podImage *model.PodImage, updates map[string]any) {
	var hub structs.HubRepositorie

	if err := GetHubImage(podImage.ImageType, podImage.ImageName, podImage.ImageTag, &hub); err != nil {
		logger.Error("从Hub获取镜像信息失败", " imageId:", podImage.ID)
		return
	}

	if hub.Size <= 0 {
		logger.Error("未获取到镜像信息", " imageID:", podImage.ID)
		return
	}

	updates["size"] = hub.Size
	updates["sha256"] = hub.Digest
	updates["last_save_time"] = hub.PushTime
	updates["audit_status"] = enums.ImageAuditStatusEnum.PushSuccess
	updates["status"] = 1
	updates["reason"] = ""
}

func waitSqushImage() {
	defer func() {
		if err := recover(); err != nil {
			logger.Error("wait squash image failed:", err)
		}
		time.Sleep(time.Minute)
		go waitSqushImage()
	}()

	for {
		m := func() map[string]string {
			squashMutex.Lock()
			defer squashMutex.Unlock()
			return maps.Clone(squashImages)
		}()

		for imageUuid, imageName := range m {
			m, err := SquashImage(imageUuid, imageName, true, false, 0)
			if err != nil {
				if !strings.Contains(err.Error(), "dial tcp "+squashAddr) {
					logger.Error("wait squash image failed:", imageUuid, imageName, err)
				}
				continue
			}
			if m == nil {
				delete(squashImages, imageUuid)
			} else if finish, _ := m["finish"].(bool); finish {
				delete(squashImages, imageUuid)
				if m["error"] == nil {
					var podImage model.PodImage
					err := podImage.GetByUuid(imageUuid)
					if err == nil {
						updates := make(map[string]any)
						updates["layer_count"] = 0
						// if s, ok := m["image_meta"].(string); ok && s != "" {
						// 	var im struct {
						// 		types.ImageInspect
						// 		History []image.HistoryResponseItem `json:"History"`
						// 	}
						// 	b := unsafe.Slice(unsafe.StringData(s), len(s))
						// 	err = json.Unmarshal(b, &im)
						// 	if err == nil {
						// 		_ = os.WriteFile("/mnt/pod-data/docker-image-meta/"+strconv.FormatUint(uint64(podImage.ID), 10)+".json", b, 0644)

						// 		updates["size"] = im.Size
						// 		updates["sha256"] = im.ID
						// 		updates["layer_count"] = len(im.RootFS.Layers)

						// 		if b, err := json.Marshal(map[string]any{
						// 			"Id":           im.ImageInspect.ID,
						// 			"Size":         im.ImageInspect.Size,
						// 			"LayerCount":   len(im.RootFS.Layers),
						// 			"HistoryCount": len(im.History),
						// 		}); err == nil {
						// 			updates["image_meta"] = unsafe.String(unsafe.SliceData(b), len(b))
						// 		} else {
						// 			updates["image_meta"] = ""
						// 		}
						// 	}
						// }

						var manifests HubImageManifests
						// var blobs HubImageBlobs
						manifests, _ = GetHubImageManifests(imageName)
						// if err == nil {
						// 	blobs, err = GetHubImageBlobs(imageName, manifests.Config.Digest)
						// }
						updates["layer_count"] = len(manifests.Layers)
						updatePodImageHubInfo(&podImage, updates)
						err = podImage.Updates(updates)
					}
					if err != nil {
						logger.Error("set squash image failed:", imageUuid, imageName, err)
					}
				}
			}
		}

		time.Sleep(time.Minute)
	}
}

func init() {
	go waitSqushImage()
}

func SquashImage(imageUuid string, imageName string, query, cancel bool, toLayer int) (map[string]any, error) {
	if !(query || cancel) {
		squashMutex.Lock()
		if _, ok := squashImages[imageUuid]; !ok {
			squashImages[imageUuid] = imageName
		}
		squashMutex.Unlock()
	}

	q := url.Values{}
	q.Set("image_uuid", imageUuid)
	q.Set("image_name", imageName)
	q.Set("query", strconv.FormatBool(query))
	q.Set("cancel", strconv.FormatBool(cancel))
	if toLayer > 0 {
		q.Set("l", strconv.Itoa(toLayer))
	}

	res, err := http.Get("http://" + squashAddr + "/squash?" + q.Encode())
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	var result struct {
		Code    int            `json:"code"`
		Message string         `json:"message"`
		Data    map[string]any `json:"data"`
	}

	b, err := io.ReadAll(res.Body)
	if err == nil {
		err = json.Unmarshal(b, &result)
	}
	if err != nil {
		return nil, err
	}

	if result.Code == 404 {
		return nil, nil
	} else if result.Code != 0 {
		return nil, errors.New(result.Message)
	}

	return result.Data, nil
}

type HubImageDigest struct {
	MediaType string `json:"mediaType,omitempty"`
	Size      int    `json:"size"`
	Digest    string `json:"digest"`
}
type HubImageManifests struct {
	SchemaVersion int              `json:"schemaVersion"`
	MediaType     string           `json:"mediaType"`
	Config        HubImageDigest   `json:"config"`
	Layers        []HubImageDigest `json:"layers"`
}

func GetHubImageManifests(imageName string) (result HubImageManifests, err error) {
	u := "http://10.20.103.240/v2"
	i := strings.IndexByte(imageName, '/')
	if i == -1 {
		err = errors.New("invalid image name: " + imageName)
		return
	}
	j := strings.LastIndexByte(imageName, ':')
	if j == -1 {
		err = errors.New("invalid image tag: " + imageName)
		return
	}
	u += imageName[i:j]
	u += "/manifests/" + imageName[j+1:]

	req, err := http.NewRequest("GET", u, nil)
	if err != nil {
		return
	}
	req.Header.Set("Authorization", "Basic YWRtaW46WmV5dW4xMjM0JV4mKg==")
	req.Header.Set("Accept", "application/vnd.docker.distribution.manifest.v2+json")

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return
	}
	defer res.Body.Close()

	b, err := io.ReadAll(res.Body)
	if err == nil {
		err = json.Unmarshal(b, &result)
	}
	return
}

type HubImageHistory struct {
	Created    string `json:"created"`
	CreatedBy  string `json:"created_by"`
	Comment    string `json:"comment"`
	EmptyLayer bool   `json:"empty_layer"`
	HubImageDigest
}
type HubImageBlobs struct {
	History []HubImageHistory `json:"history"`
}

func GetHubImageBlobs(imageName, digest string) (result HubImageBlobs, err error) {
	u := "http://10.20.103.240/v2"
	i := strings.IndexByte(imageName, '/')
	if i == -1 {
		err = errors.New("invalid image name: " + imageName)
		return
	}
	j := strings.LastIndexByte(imageName, ':')
	if j == -1 {
		err = errors.New("invalid image tag: " + imageName)
		return
	}
	u += imageName[i:j]

	if !strings.HasPrefix(digest, "sha256:") {
		err = errors.New("invalid config digest: " + digest)
		return
	}

	u += "/blobs/" + digest

	req, err := http.NewRequest("GET", u, nil)
	if err != nil {
		return
	}
	req.Header.Set("Authorization", "Basic YWRtaW46WmV5dW4xMjM0JV4mKg==")
	req.Header.Set("Accept", "application/vnd.docker.container.image.v1+json")

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return
	}
	defer res.Body.Close()

	b, err := io.ReadAll(res.Body)
	if err == nil {
		err = json.Unmarshal(b, &result)
	}
	return
}
