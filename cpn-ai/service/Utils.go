package service

import (
	"bytes"
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/structs"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"path"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unsafe"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type GinH struct {
	Code   int                    `json:"code"`
	Msg    string                 `json:"msg"`
	Result map[string]interface{} `json:"result"`
}

func PostFileStorage(postUrl string, postData []byte, result interface{}) error {
	// 创建HTTP客户端（设置超时避免阻塞）
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 构造请求
	req, err := http.NewRequest("POST", config.FileStorageInterfaceUrl+postUrl, bytes.NewBuffer(postData))
	if err != nil {
		logger.Error("创建HTTP请求失败:", err)
		return err
	}

	// 设置Headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Connection", "keep-alive")
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("HTTP请求失败:", err)
		return err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		logger.Error("接口返回非200状态码:", resp.StatusCode, "响应内容:", string(body))
		return errors.New("接口错误: %s" + resp.Status)
	}
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error("读取响应内容失败:", err)
		return err
	}

	if err = json.Unmarshal(body, &result); err != nil {
		logger.Error("JSON解析失败:", err, "原始响应:", string(body))
		return errors.New("JSON解析失败:" + err.Error() + "原始响应:" + string(body))
	}
	return err
}

func Post11(postUrl string, postData map[string]interface{}) (map[string]interface{}, int) {
	code := 1
	bytesData, _ := json.Marshal(postData)

	if !strings.HasPrefix(postUrl, "http") {
		logger.Error("请求Posturl错误", postUrl)
		return nil, code
	}
	req, err := http.NewRequest("POST", postUrl, bytes.NewBuffer(bytesData))
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, code
	}
	// 设置请求头
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	req.Header.Set("Authorization", "token") // 设置其他自定义的请求头

	// 发送请求
	client := http.DefaultClient
	res, err := client.Do(req)
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, code
	}
	defer res.Body.Close()

	content, err := ioutil.ReadAll(res.Body)
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, code
	}
	str := *(*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	str = strings.TrimSpace(str)
	if len(str) >= 2 && str[0] == '{' && str[len(str)-1] == '}' {
		m := utils.GetMapFromJson(str)
		if m == nil {
			logger.Error("将返回的内容格式化成Map出错", postUrl, postData, str)
			return nil, code
		}
		code = 0
		return m, code
	} else {
		logger.Error("返回的内容不是json格式", postUrl, postData, str)
		return nil, code
	}
}

func PostNode(nodeId uint, postUrl string, postData map[string]interface{}) (map[string]interface{}, int) {
	code := 1
	bytesData, _ := json.Marshal(postData)

	var node model.Node
	if err := node.GetById(nodeId); err != nil {
		logger.Error(err)
		return nil, code
	}
	//postUrl = strings.Replace(postUrl, "api/", "api_online/", -1)
	if !strings.HasPrefix(postUrl, "http") {
		postUrl = utils.UrlJoin(node.ApiBaseUrl, postUrl)
	}
	req, err := http.NewRequest("POST", postUrl, bytes.NewBuffer(bytesData))
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, code
	}
	// 设置请求头
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	req.Header.Set("Authorization", middleware.CreateNodeToken(node.ID, node.AccessToken)) // 设置其他自定义的请求头

	//logger.Info("PostNode:", postUrl, " Authorization:", req.Header.Get("Authorization"))

	// 发送请求
	client := http.DefaultClient
	res, err := client.Do(req)
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, code
	}
	defer res.Body.Close()

	content, err := ioutil.ReadAll(res.Body)
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, code
	}
	str := *(*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	str = strings.TrimSpace(str)
	//logger.Info(postUrl, "  ", postData, " PostNode Result:||", str, "||")
	if str == "" {
		logger.Error("将返回的内容为空", postUrl, postData, str)
		return nil, code
	}
	if m := utils.GetMapFromJson(str); m == nil {
		logger.Error("将返回的内容格式化成Map出错", postUrl, postData, str)
		return nil, code
	} else {
		code = 0
		return m, code
	}
}

func PostNodeForGin(nodeId uint, postUrl string, postData map[string]interface{}) (gin.H, error) {
	postAuthorization := ""
	if val, ok := postData["Authorization"]; ok {
		postAuthorization = val.(string)
		delete(postData, "Authorization")
	}

	bytesData, _ := json.Marshal(postData)

	var node model.Node
	if nodeId > 0 {
		if err := node.GetById(nodeId); err != nil {
			logger.Error(err, "nodeId:", nodeId)
			return nil, err
		}
	}

	//postUrl = strings.Replace(postUrl, "api/", "api_online/", -1)
	if !strings.HasPrefix(postUrl, "http") {
		if nodeId == 16 && nodeId < 0 { //gz01.chenyu.cn
			apiBaseUrl := fmt.Sprintf("https://node%d.%s/", nodeId, node.Domain)
			postUrl = utils.UrlJoin(apiBaseUrl, postUrl)
		} else {
			postUrl = utils.UrlJoin(node.ApiBaseUrl, postUrl)
		}
	}
	req, err := http.NewRequest("POST", postUrl, bytes.NewBuffer(bytesData))
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, err
	}
	// 设置请求头
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	if node.ID > 0 {
		req.Header.Set("Authorization", middleware.CreateNodeToken(node.ID, node.AccessToken)) // 设置其他自定义的请求头
		//logger.Info("PostNode:", postUrl, " Authorization:", req.Header.Get("Authorization"))
	} else if postAuthorization != "" {
		req.Header.Set("Authorization", postAuthorization)
	}
	// 发送请求
	client := http.DefaultClient
	res, err := client.Do(req)
	if err != nil {
		logger.Error(err, postUrl, postData)
		if strings.Contains(err.Error(), "connection refused") {
			return nil, errors.New("connection refused")
		}
		return nil, err
	}
	defer res.Body.Close()

	content, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, err
	}
	str := *(*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	str = strings.TrimSpace(str)
	//logger.Info(postUrl, "  ", postData, " PostNode Result:||", "str", "||")
	//logger.Info(postUrl, "  ", postData)
	if str == "" {
		logger.Error("返回的内容为空", postUrl, postData, str)
		return nil, errors.New("返回的内容为空")
	}
	var ginH gin.H
	err = json.Unmarshal([]byte(str), &ginH)
	if err != nil {
		logger.Error(err, str)
		return nil, err
	}
	return ginH, nil
}

func GetNodeForGin(nodeId uint, getUrl string, queryParams map[string]interface{}) (gin.H, error) {
	var node model.Node
	if nodeId > 0 {
		if err := node.GetById(nodeId); err != nil {
			logger.Error(err, "nodeId:", nodeId)
			return nil, err
		}
	}

	if !strings.HasPrefix(getUrl, "http") {
		getUrl = utils.UrlJoin(node.ApiBaseUrl, getUrl)
	}

	// 将queryParams转换为查询字符串
	query := url.Values{}
	for key, value := range queryParams {
		query.Set(key, fmt.Sprintf("%v", value))
	}
	getUrl += "?" + query.Encode()

	req, err := http.NewRequest("GET", getUrl, nil)
	if err != nil {
		logger.Error(err, getUrl, queryParams)
		return nil, err
	}
	// 设置请求头
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	if node.ID > 0 {
		req.Header.Set("Authorization", middleware.CreateNodeToken(node.ID, node.AccessToken)) // 设置其他自定义的请求头

		logger.Info("GetNode:", getUrl, " Authorization:", req.Header.Get("Authorization"))
	}

	// 发送请求
	client := http.DefaultClient
	res, err := client.Do(req)
	if err != nil {
		logger.Error(err, getUrl, queryParams)
		if strings.Contains(err.Error(), "connection refused") {
			return nil, errors.New("connection refused")
		}
		return nil, err
	}
	defer res.Body.Close()

	content, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Error(err, getUrl, queryParams)
		return nil, err
	}
	str := string(content)
	str = strings.TrimSpace(str)
	//logger.Info(getUrl, "  ", queryParams, " GetNode Result:||", "str", "||")
	logger.Info(getUrl, "  ", queryParams)
	if str == "" {
		logger.Error("返回的内容为空", getUrl, queryParams, str)
		return nil, errors.New("返回的内容为空")
	}
	var ginH gin.H
	err = json.Unmarshal([]byte(str), &ginH)
	if err != nil {
		logger.Error(err, str)
		return nil, err
	}
	return ginH, nil
}

func ResultGinH(mm gin.H) (GinH, error) {
	var ginH GinH

	success := 0
	if _, ok := mm["code"]; ok {
		ginH.Code = int(mm["code"].(float64))
		success += 1
	}

	if _, ok := mm["msg"]; ok {
		ginH.Msg = mm["msg"].(string)
		success += 2
	}
	if success <= 2 {
		return ginH, errors.New("不是标准的Result格式")
	}

	if _, ok := mm["result"]; ok {
		ginH.Result = mm["result"].(map[string]interface{})
	}
	return ginH, nil
}

func Result(mm map[string]interface{}) (GinH, error) {
	var ginH GinH
	if mm == nil {
		return ginH, errors.New("mm is nil")
	}

	success := 0
	if _, ok := mm["code"]; ok {
		ginH.Code = int(mm["code"].(float64))
		success += 1
	}

	if _, ok := mm["msg"]; ok {
		ginH.Msg = mm["msg"].(string)
		success += 2
	}
	if success <= 2 {
		return ginH, errors.New("不是标准的Result格式")
	}

	if _, ok := mm["result"]; ok {
		ginH.Result = mm["result"].(map[string]interface{})
	}
	return ginH, nil
}

func GetImageTag(command string) string {
	//if modelHash == "" {
	//	return ""
	//}

	//input = `docker run -p 1{==={GpuIndex}===}022:22 -p 1{==={GpuIndex}===}800:8000 -v /root/aigc-models/llms`
	// 创建正则表达式匹配模式
	//re := regexp.MustCompile(fmt.Sprintf(`0(\d+):\d+,`, input))
	//re := regexp.MustCompile(`1\{===\{GpuIndex\}===\}(\d+):\d+`)
	//re := regexp.MustCompile(`-d .*?:([^ ]*)`)
	re := regexp.MustCompile(`hub\.suanyun\.cn/.+:([^ ]+)`)
	// 查找匹配的字符串
	matches := re.FindAllStringSubmatch(command, -1)
	for _, match := range matches {
		if len(match) >= 2 {
			return match[1]
		}
	}
	return ""
}

func StatisticsHub() (structs.HubStatistics, error) {

	//url := "https://hub.suanyun.cn/api/v2.0/statistics"
	url := "http://" + common.HubServerAddress + "/api/v2.0/statistics"
	if config.Env == enums.EnvEnum.PRODUCTION || config.Env == enums.EnvEnum.ONLINE {
		url = strings.Replace(url, "https://hub.suanyun.cn", "http://"+common.HubServerAddress, -1)
	}
	logger.Info("StatisticsHubImageUrl:", url)

	// 提供用户凭证
	username := "admin"
	password := "Zeyun1234%^&*"

	// 创建一个 HTTP 客户端
	client := &http.Client{}
	stats := structs.HubStatistics{}
	// 创建 DELETE 请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logger.Error("创建请求时出错:", err, " url:", url)
		return stats, err
	}

	// 添加认证头部
	req.SetBasicAuth(username, password)

	// 发送请求并获取响应
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("发送请求时出错:", err, " url:", url)
		return stats, err
	}

	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("读取响应时出错:", err, " url:", url)
		return stats, err
	}

	// 检查响应状态
	if resp.StatusCode == 200 { // 请求成功
		bodyStr := string(body)
		//logger.Info("响应内容:", bodyStr)

		if err := utils.GetStructFromJson(&stats, bodyStr); err != nil {
			logger.Error(err)
			return stats, err
		}
		return stats, nil
	} else {
		//{"errors":[{"code":"NOT_FOUND","message":"artifact public/0cfd39ac63b8445095f1b7c3e8bbf152@sha256:f6d81213c2496b901af2c0df1089d94443083c868c24fb73a225965a39366605 not found"}]}
		if resp.StatusCode == 404 && strings.Contains(string(body), "NOT_FOUND") {
			err := errors.New("404")
			logger.Info("404响应内容:", string(body))
			return stats, err
		} else {
			// 请求失败
			err := errors.New("请求失败")
			logger.Error("请求失败 响应状态码:", resp.StatusCode, " 响应内容:", string(body), " url:", url)
			return stats, err
		}
	}
}

func DeleteHubByImageName(imageType int, imageName string) error {
	// Harbor API 端点 URL
	//url := "https://hub.suanyun.cn/api/v2.0/projects/{private}/repositories/{cuda}/artifacts/{11.8.0-cudnn8-devel-ubuntu22.04-02-jupyter}"

	sourcePath := ""
	if imageType == enums.ImageTypeEnum.Base {
		//sourcePath = "hub.suanyun.cn/suanyun-dev/nvidia"
	} else if imageType == enums.ImageTypeEnum.Public {
		sourcePath = "public"
	} else if imageType == enums.ImageTypeEnum.Private {
		sourcePath = "private"
	} else if imageType == enums.ImageTypeEnum.PrivateInstance {
		sourcePath = "private"
	}

	if sourcePath == "" {
		err := errors.New("镜像类型不正确")
		logger.Error(err, imageType)
		return err
	}

	if imageName == "" {
		err := errors.New("镜像名称不正确")
		logger.Error(err, imageName)
		return err
	}

	imageName = sourcePath + "%252F" + imageName

	//http://*************/api/v2.0/projects/chenyu/repositories/private%252Fcac311f1b6be4aca8cb37b1278360266

	//reference 通常表示特定镜像的标识符。它可以是镜像的标签（如 latest）或镜像的 SHA256 摘要（如 sha256:abcdef1234567890...）。reference 用于唯一标识镜像仓库中的一个具体镜像版本。
	url := fmt.Sprintf("https://hub.suanyun.cn/api/v2.0/projects/chenyu/repositories/%s", imageName)
	if config.Env == enums.EnvEnum.PRODUCTION || config.Env == enums.EnvEnum.ONLINE {
		url = strings.Replace(url, "https://hub.suanyun.cn", "http://"+common.HubServerAddress, -1)
	}

	logger.Info("DeleteHubImageUrl:", url)

	// 提供用户凭证
	username := "admin"
	password := "Zeyun1234%^&*"

	// 创建一个 HTTP 客户端
	client := &http.Client{}

	// 创建 DELETE 请求
	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		logger.Error("创建请求时出错:", err, " url:", url)
		return err
	}

	// 添加认证头部
	req.SetBasicAuth(username, password)

	// 发送请求并获取响应
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("发送请求时出错:", err, " url:", url)
		return err
	}

	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("读取响应时出错:", err, " url:", url)
		return err
	}

	// 检查响应状态
	if resp.StatusCode == 200 {
		// 请求成功
		logger.Info("响应内容:", string(body))
		return nil
	} else {
		//{"errors":[{"code":"NOT_FOUND","message":"artifact public/0cfd39ac63b8445095f1b7c3e8bbf152@sha256:f6d81213c2496b901af2c0df1089d94443083c868c24fb73a225965a39366605 not found"}]}
		if resp.StatusCode == 404 && strings.Contains(string(body), "NOT_FOUND") {
			logger.Info("404响应内容:", string(body))
			return nil
		} else {
			// 请求失败
			err := errors.New("请求失败")
			logger.Error("请求失败 响应状态码:", resp.StatusCode, " 响应内容:", string(body), " url:", url)
			return err
		}
	}
}

func DeleteHubImage(imageType int, imageName string, imageTag string, sha256 string) error {
	// Harbor API 端点 URL
	//url := "https://hub.suanyun.cn/api/v2.0/projects/{private}/repositories/{cuda}/artifacts/{11.8.0-cudnn8-devel-ubuntu22.04-02-jupyter}"

	if !strings.HasPrefix(sha256, "sha256:") {
		sha256 = "sha256:" + sha256
	}

	sourcePath := ""
	if imageType == enums.ImageTypeEnum.Base {
		//sourcePath = "hub.suanyun.cn/suanyun-dev/nvidia"
	} else if imageType == enums.ImageTypeEnum.Public {
		sourcePath = "public"
	} else if imageType == enums.ImageTypeEnum.Private {
		sourcePath = "private"
	} else if imageType == enums.ImageTypeEnum.PrivateInstance {
		sourcePath = "private"
	}

	if sourcePath == "" {
		err := errors.New("镜像类型不正确")
		logger.Error(err, imageType)
		return err
	}

	if imageName == "" {
		err := errors.New("镜像名称不正确")
		logger.Error(err, imageName)
		return err
	}

	if imageTag == "" {
		err := errors.New("镜像标签不正确")
		logger.Error(err, imageTag)
		return err
	}
	imageName = sourcePath + "%252F" + imageName

	//http://*************/api/v2.0/projects/chenyu/repositories/private%252Fcac311f1b6be4aca8cb37b1278360266

	//reference 通常表示特定镜像的标识符。它可以是镜像的标签（如 latest）或镜像的 SHA256 摘要（如 sha256:abcdef1234567890...）。reference 用于唯一标识镜像仓库中的一个具体镜像版本。
	url := fmt.Sprintf("https://hub.suanyun.cn/api/v2.0/projects/%s/repositories/%s/artifacts/%s", "chenyu", imageName, imageTag)
	if sha256 != "" {
		url = fmt.Sprintf("https://hub.suanyun.cn/api/v2.0/projects/%s/repositories/%s/artifacts/%s", "chenyu", imageName, sha256)
	}
	if config.Env == enums.EnvEnum.PRODUCTION || config.Env == enums.EnvEnum.ONLINE {
		url = strings.Replace(url, "https://hub.suanyun.cn", "http://"+common.HubServerAddress, -1)
	}

	logger.Info("DeleteHubImageUrl:", url)

	// 提供用户凭证
	username := "admin"
	password := "Zeyun1234%^&*"

	// 创建一个 HTTP 客户端
	client := &http.Client{}

	// 创建 DELETE 请求
	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		logger.Error("创建请求时出错:", err, " url:", url)
		return err
	}

	// 添加认证头部
	req.SetBasicAuth(username, password)

	// 发送请求并获取响应
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("发送请求时出错:", err, " url:", url)
		return err
	}

	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("读取响应时出错:", err, " url:", url)
		return err
	}

	// 检查响应状态
	if resp.StatusCode == 200 {
		// 请求成功
		logger.Info("响应内容:", string(body))
		return nil
	} else {
		//{"errors":[{"code":"NOT_FOUND","message":"artifact public/0cfd39ac63b8445095f1b7c3e8bbf152@sha256:f6d81213c2496b901af2c0df1089d94443083c868c24fb73a225965a39366605 not found"}]}
		if resp.StatusCode == 404 && strings.Contains(string(body), "NOT_FOUND") {
			logger.Info("404响应内容:", string(body))
			return nil
		} else {
			// 请求失败
			err := errors.New("请求失败")
			logger.Error("请求失败 响应状态码:", resp.StatusCode, " 响应内容:", string(body), " url:", url)
			return err
		}
	}
}

func GetHubImageWithTag(imageType int, imageName string, imageTag string, hub *structs.HubRepositorie) error {

	/*
		参考链接  *************/chenyu/public/ee9ec1d522354ddd9b05908270b7aef5:v1.1
		project  项目  如chenyu、docker
		repository:  chenyu/public/ee9ec1d522354ddd9b05908270b7aef5中的public/ee9ec1d522354ddd9b05908270b7aef5其中斜杆要写成%2F
		repository_name: 就是repository   public%2Fee9ec1d522354ddd9b05908270b7aef5
		artifacts:就是版本号 v1.1
		reference:就是版本号 v1.1

	*/

	// Harbor API 端点 URL
	//url := "https://hub.suanyun.cn/api/v2.0/projects/{private}/repositories/{cuda}/artifacts/{11.8.0-cudnn8-devel-ubuntu22.04-02-jupyter}"

	//https://hub.suanyun.cn/api/v2.0/projects/private/repositories/8d7c74f995b44159b0dc82611bed1ba1/artifacts?with_tag=false&with_scan_overview=true&with_label=true&with_accessory=false&page_size=15&page=1

	//{project_name}   private
	//{repository_name} uuid
	sourcePath := ""
	if imageType == enums.ImageTypeEnum.Base {
		sourcePath = "suanyun-dev"
	} else if imageType == enums.ImageTypeEnum.Public {
		sourcePath = "public"
	} else if imageType == enums.ImageTypeEnum.Private {
		sourcePath = "private"
	} else if imageType == enums.ImageTypeEnum.PrivateInstance {
		sourcePath = "private"
	}
	if sourcePath == "" {
		err := errors.New("镜像类型不正确")
		logger.Error(err, imageType)
		return err
	}

	if imageName == "" {
		err := errors.New("镜像名称不正确")
		logger.Error(err, imageName)
		return err
	}

	imageName = sourcePath + "%252F" + imageName

	if imageTag == "" {
		err := errors.New("镜像标签不正确")
		logger.Error(err, imageTag)
		return err
	}

	//https://hub.suanyun.cn/api/v2.0/projects/private/repositories/8d7c74f995b44159b0dc82611bed1ba1/artifacts?with_tag=false&with_scan_overview=true&with_label=true&with_accessory=false&page_size=15&page=1
	//url := fmt.Sprintf("https://hub.suanyun.cn/api/v2.0/projects/%s/repositories/%s/artifacts?with_tag=true&with_scan_overview=true&with_label=true&with_accessory=false&page_size=15&page=1", "chenyu", imageName)
	url := fmt.Sprintf("https://hub.suanyun.cn/api/v2.0/projects/%s/repositories/%s/artifacts/%s?page=1&page_size=10&with_tag=true&with_label=false&with_scan_overview=false&with_accessory=false&with_signature=false&with_immutable_status=false", "chenyu", imageName, imageTag)

	//url := fmt.Sprintf("https://hub.suanyun.cn/api/v2.0/projects/%s/repositories/%s/artifacts?with_tag=true&with_scan_overview=true&with_label=true&with_accessory=false&page_size=15&page=1", "chenyu", imageName)

	if config.Env == enums.EnvEnum.PRODUCTION || config.Env == enums.EnvEnum.ONLINE || true {
		url = strings.Replace(url, "https://hub.suanyun.cn", "http://"+common.HubServerAddress, -1)
	}
	// 提供用户凭证
	username := "admin"
	password := "Zeyun1234%^&*"

	// 创建一个 HTTP 客户端
	client := &http.Client{}

	// 创建 DELETE 请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logger.Error("创建请求时出错:", err, " url:", url)
		return err
	}

	// 添加认证头部
	req.SetBasicAuth(username, password)

	// 发送请求并获取响应
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("发送请求时出错:", err, " url:", url)
		return err
	}

	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("读取响应时出错:", err, " url:", url)
		return err
	}

	// 检查响应状态
	if resp.StatusCode == 200 || resp.StatusCode == 404 {
		// 请求成功

		ss := string(body)

		if strings.Contains(ss, "NOT_FOUND") && strings.Contains(ss, "not found") {
			return gorm.ErrRecordNotFound
		}
		if !strings.Contains(ss, "IMAGE") {
			err := errors.New("查询失败")
			logger.Error(err, " url:", url)
			return err
		}

		var tmp structs.HubRepositorie
		if err := utils.GetStructAryFromJson(&tmp, ss); err != nil {
			logger.Error(err, "GetHubImage响应内容:", ss)
			return err
		}
		if tmp.Tags == nil {
			return gorm.ErrRecordNotFound
		}
		for _, tag := range tmp.Tags {

			if tag.Name == imageTag {
				*hub = tmp
				return nil
			}
		}
		return gorm.ErrRecordNotFound
	} else {
		// 请求失败
		err := errors.New("请求失败")
		logger.Error("请求失败 响应状态码:", resp.StatusCode, " 响应内容:", string(body), " url:", url)
		return err
	}
}

func GetHubImagePath(imageType int, imageName string, imageTag string) string {
	if imageType == enums.ImageTypeEnum.CCM {
		if imageTag == "" {
			return ""
		}
		return "10.20.103.240/chenyu/public/base-env:" + imageTag
	}

	sourcePath := ""
	if imageType == enums.ImageTypeEnum.Base {
		sourcePath = "hub.suanyun.cn/chenyu/suanyun-dev"
	} else if imageType == enums.ImageTypeEnum.Public {
		sourcePath = "hub.suanyun.cn/chenyu/public"
	} else if imageType == enums.ImageTypeEnum.Private {
		sourcePath = "hub.suanyun.cn/chenyu/private"
	} else if imageType == enums.ImageTypeEnum.PrivateInstance {
		sourcePath = "private"
	}
	sourcePath = strings.Replace(sourcePath, "hub.suanyun.cn", common.HubServerAddress, -1)
	if sourcePath != "" {
		imageStr := fmt.Sprintf("%s/%s:%s", sourcePath, imageName, imageTag)
		return imageStr
	}
	return ""
}

func GetHubImage(imageType int, imageName string, imageTag string, hub *structs.HubRepositorie) error {

	/*
		参考链接  *************/chenyu/public/ee9ec1d522354ddd9b05908270b7aef5:v1.1
		project  项目  如chenyu、docker
		repository:  chenyu/public/ee9ec1d522354ddd9b05908270b7aef5中的public/ee9ec1d522354ddd9b05908270b7aef5其中斜杆要写成%2F
		repository_name: 就是repository   public%2Fee9ec1d522354ddd9b05908270b7aef5
		artifacts:就是版本号 v1.1
		reference:就是版本号 v1.1

	*/

	// Harbor API 端点 URL
	//url := "https://hub.suanyun.cn/api/v2.0/projects/{private}/repositories/{cuda}/artifacts/{11.8.0-cudnn8-devel-ubuntu22.04-02-jupyter}"

	//https://hub.suanyun.cn/api/v2.0/projects/private/repositories/8d7c74f995b44159b0dc82611bed1ba1/artifacts?with_tag=false&with_scan_overview=true&with_label=true&with_accessory=false&page_size=15&page=1

	//{project_name}   private
	//{repository_name} uuid
	sourcePath := ""
	if imageType == enums.ImageTypeEnum.Base {
		sourcePath = "suanyun-dev"
	} else if imageType == enums.ImageTypeEnum.Public {
		sourcePath = "public"
	} else if imageType == enums.ImageTypeEnum.Private || imageType == enums.ImageTypeEnum.PrivateInstance {
		sourcePath = "private"
	}
	if sourcePath == "" {
		err := errors.New("镜像类型不正确")
		logger.Error(err, imageType)
		return err
	}

	if imageName == "" {
		err := errors.New("镜像名称不正确")
		logger.Error(err, imageName)
		return err
	}

	imageName = sourcePath + "%252F" + imageName

	if imageTag == "" {
		err := errors.New("镜像标签不正确")
		logger.Error(err, imageTag)
		return err
	}

	//https://hub.suanyun.cn/api/v2.0/projects/private/repositories/8d7c74f995b44159b0dc82611bed1ba1/artifacts?with_tag=false&with_scan_overview=true&with_label=true&with_accessory=false&page_size=15&page=1
	url := fmt.Sprintf("https://hub.suanyun.cn/api/v2.0/projects/%s/repositories/%s/artifacts?with_tag=true&with_scan_overview=true&with_label=true&with_accessory=false&page_size=15&page=1", "chenyu", imageName)
	//url := fmt.Sprintf("https://hub.suanyun.cn/api/v2.0/projects/%s/repositories/%s/artifacts?with_tag=true&with_scan_overview=true&with_label=true&with_accessory=false&page_size=15&page=1", "chenyu", imageName)

	if config.Env == enums.EnvEnum.PRODUCTION || config.Env == enums.EnvEnum.ONLINE || true {
		url = strings.Replace(url, "https://hub.suanyun.cn", "http://"+common.HubServerAddress, -1)
	}
	// 提供用户凭证
	username := "admin"
	password := "Zeyun1234%^&*"

	// 创建一个 HTTP 客户端
	client := &http.Client{}

	// 创建 DELETE 请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logger.Error("创建请求时出错:", err, " url:", url)
		return err
	}

	// 添加认证头部
	req.SetBasicAuth(username, password)

	// 发送请求并获取响应
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("发送请求时出错:", err, " url:", url)
		return err
	}

	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("读取响应时出错:", err, " url:", url)
		return err
	}

	// 检查响应状态
	if resp.StatusCode == 200 {
		// 请求成功

		ss := string(body)

		arr := make([]structs.HubRepositorie, 0)
		if err := utils.GetStructAryFromJson(&arr, ss); err != nil {
			logger.Error(err, "GetHubImage响应内容:", ss)
			return err
		}
		for _, tmp := range arr {
			if tmp.Tags == nil {
				continue
			}
			for _, tag := range tmp.Tags {

				if tag.Name == imageTag {
					*hub = tmp
					return nil
				}
			}
		}
		return gorm.ErrRecordNotFound
	} else {
		// 请求失败
		err := errors.New("请求失败")
		logger.Error("请求失败 响应状态码:", resp.StatusCode, " 响应内容:", string(body), " url:", url)
		return err
	}
}

func ShowCardNo(str string) string {
	// 检查字符串长度是否为16位
	if len(str) != 16 {
		return ""
	}

	// 将字符串切分成4段
	parts := []string{str[:4], str[4:8], str[8:12], str[12:16]}

	// 使用 strings.Join 将切分后的字符串用 - 连接起来
	return fmt.Sprintf("%s-%s-%s-%s", parts[0], parts[1], parts[2], parts[3])
}

func SendEmailPro(subject string, body string, outSeconds int64) error {

	emailReq := EmailReq{
		From:    "",
		To:      "<EMAIL>",
		Subject: subject,
		Content: time.Now().Format(jsontime.TimeFormat) + " " + body,
	}
	EmailService.SendPro(emailReq, outSeconds)
	return nil
}

func GetParentLabels(labelId uint) ([]uint, error) {
	labelIds := make([]uint, 0)
	for i := 0; i < 10; i++ {
		var tmpLabel model.Label
		if err := tmpLabel.GetById(labelId); err != nil {
			logger.Error(err, " labelId11111:", labelId)
			return labelIds, err
		} else {
			labelIds = append(labelIds, tmpLabel.ID)
			if tmpLabel.ParentId > 0 {
				labelId = tmpLabel.ParentId
			} else {
				break
			}
		}
	}
	return labelIds, nil
}

func GetParentImageLabels(labelId uint) ([]uint, error) {
	labelIds := make([]uint, 0)
	for i := 0; i < 10; i++ {
		var tmpLabel model.ImageLabel
		if err := tmpLabel.GetById(labelId); err != nil {
			logger.Error(err, " labelId11111:", labelId)
			return labelIds, err
		} else {
			labelIds = append(labelIds, tmpLabel.ID)
			if tmpLabel.ParentId > 0 {
				labelId = tmpLabel.ParentId
			} else {
				break
			}
		}
	}
	return labelIds, nil
}

func GetPrivateImageSavePathByPodImage(podImage model.PodImage) (privateImageSavePath string, err error) {
	defer func() {
		if err != nil {
			privateImageSavePath = ""
			logger.Error(err)
		}
	}()
	if podImage.StorageMode != enums.ImageStorageModeEnum.PrivateDisk {
		err = errors.New("存储类型不匹配")
		return
	}
	hubImagePath := GetHubImagePath(podImage.ImageType, podImage.ImageName, podImage.ImageTag)
	if hubImagePath == "" {
		err = errors.New("参数错误")
		return
	}
	var user model.User
	if err = user.GetById(podImage.UserId); err != nil {
		logger.Error(err)
		return
	}
	privateImageSavePath, err = GetPrivateImageSavePath(hubImagePath, user.PrivateStorage)
	return
}

func CreatePrivateImageSavePath(hubImagePath string, userDataPath string) (privateImageSavePath string, err error) {
	defer func() {
		if err != nil {
			privateImageSavePath = ""
			logger.Error(err)
		}
	}()
	msg := ""
	if userDataPath == "" {
		err = errors.New("用户存储路径为空")
		logger.Error(err)
		return
	}

	userBasePath := path.Join(config.PrivateStorage, userDataPath)
	if _, err = os.Stat(userBasePath); os.IsNotExist(err) {
		logger.Info("用户存储路径不存在 开始创建存储路径", userBasePath)
		if err = os.MkdirAll(userBasePath, os.ModePerm); err != nil {
			msg = "创建用户文件夹出错"
			logger.Error(msg, err)
			return
		}
	}
	if _, err = os.Stat(userBasePath); err != nil {
		logger.Error(err)
		return
	}

	//个人镜像数据目录
	privateImagePath := path.Join(config.PrivateStorage, userDataPath, "container_images")

	if _, err = os.Stat(privateImagePath); os.IsNotExist(err) {
		logger.Info("个人镜像存储路径不存在 开始创建存储路径", privateImagePath)
		if err = os.MkdirAll(privateImagePath, os.ModePerm); err != nil {
			msg = "创建个人镜像存储文件夹出错"
			logger.Error(msg, err, privateImagePath)
			return
		}
	}
	if _, err = os.Stat(privateImagePath); err != nil {
		logger.Error(err)
		return
	}
	tmpAry := strings.Split(hubImagePath, "/")
	if len(tmpAry) < 2 {
		err = errors.New("hubImagePath 不正确")
		logger.Error(err)
		return
	}
	jarName := tmpAry[len(tmpAry)-1]
	privateImageSavePath = path.Join(privateImagePath, jarName+".tar")
	return
}

func GetPrivateImageSavePath(hubImagePath string, userDataPath string) (privateImageSavePath string, err error) {
	defer func() {
		if err != nil {
			privateImageSavePath = ""
			logger.Error(err)
		}
	}()
	if userDataPath == "" {
		err = errors.New("用户存储路径为空")
		logger.Error(err)
		return
	}

	userBasePath := path.Join(config.PrivateStorage, userDataPath)
	if _, err = os.Stat(userBasePath); err != nil {
		logger.Error(err)
		return
	}

	//个人镜像数据目录
	privateImagePath := path.Join(config.PrivateStorage, userDataPath, "container_images")

	if _, err = os.Stat(privateImagePath); err != nil {
		logger.Error(err)
		return
	}
	tmpAry := strings.Split(hubImagePath, "/")
	if len(tmpAry) < 2 {
		err = errors.New("hubImagePath 不正确")
		logger.Error(err)
		return
	}
	jarName := tmpAry[len(tmpAry)-1]
	privateImageSavePath = path.Join(privateImagePath, jarName+".tar")
	return
}

func GetSiteDomain(referer string) string {
	referer = strings.ToLower(referer)
	if strings.Contains(referer, "suanyun.cn") {
		return "suanyun.cn"
	} else if strings.Contains(referer, "chenyu.cn") {
		return "chenyu.cn"
	} else {
		logger.Info("referer为匹配到：", referer)
		return common.SiteDomain
	}
}

func GetSchemeHost(referer string) string {
	parsedURL, err := url.Parse(referer)
	if err != nil {
		logger.Error(err)
		return ""
	}
	baseURL := parsedURL.Scheme + "://" + parsedURL.Host
	return baseURL
}

func OutDateTime(expireTime time.Time) bool {
	today := jsontime.Today().Time()
	if expireTime.Before(today) {
		return true
	}
	return false
}

func AnalyzeErrMsg(logtxt string) {
	//no space left on device
	//stderr：docker: invalid containerPort: 123456.
}
func GenSaveImageTag(tag string) string {

	// 正则表达式：匹配时间部分（yyyyMMddHHmmss 格式）
	re := regexp.MustCompile(`_\d{14}$`)

	// 获取当前时间，格式化为 yyyyMMddHHmmss
	currentTime := time.Now().Format("20060102150405")

	// 检查是否匹配到符合条件的时间部分
	if re.MatchString(tag) {
		// 如果匹配到时间部分，则替换
		tag = re.ReplaceAllString(tag, "_"+currentTime)
	} else {
		// 如果没有匹配到，则在末尾加上 _currentTime
		tag = tag + "_" + currentTime
	}
	return tag
}

func GenPrivateImageTag(userId uint, podId uint, tag string) string {

	preTag := tag
	{
		re := regexp.MustCompile(`_\d{14}$`)
		if re.MatchString(preTag) {
			preTag = re.ReplaceAllString(preTag, "")
		}

		tmpAry := strings.Split(tag, "_p")
		if len(tmpAry) >= 2 {
			tagNum := utils.String2Int(tmpAry[len(tmpAry)-1])
			if tagNum > 0 {
				tmpAry[len(tmpAry)-1] = ""
				preTag = strings.Join(tmpAry, "")
			}
		}
	}

	var ary = make([]model.PodImage, 0)
	queryParm := make(map[string]interface{})
	queryParm["user_id"] = userId
	queryParm["image_type"] = enums.ImageTypeEnum.Private
	queryParm["pod_id"] = podId
	var podImage model.PodImage
	if _, err := podImage.ListPro(&ary, queryParm, 1, 100); err != nil {
		logger.Error("GenPrivateImageSaveTag err:", err, utils.GetJsonFromStruct(queryParm))
		return ""
	} else {
		maxTag := 0
		for i := 0; i < len(ary); i++ {
			tmpAry := strings.Split(ary[i].ImageTag, "_p")
			if len(tmpAry) >= 2 {
				tagNum := utils.String2Int(tmpAry[len(tmpAry)-1])
				if tagNum > maxTag {
					maxTag = tagNum
				}
			}
		}
		tmp := strconv.Itoa(maxTag + 1)
		if len(tmp) == 1 {
			tmp = "00" + tmp
		} else if len(tmp) == 2 {
			tmp = "0" + tmp
		}
		return preTag + "_p" + tmp
	}
}

func CheckClassStuByPod(userId uint, pod model.Pod) (ok bool, msg string, err error) {

	if pod.ClassType == 0 || pod.UserId == userId {
		ok = true
		return
	}

	return CheckClassStuByPodId(userId, pod.ID)

}

func CheckClassStuByPodId(userId uint, podId uint) (ok bool, msg string, err error) {
	qParm := utils.MakeInterfaceMap()
	qParm["stu_user_id"] = userId
	qParm["status"] = ">0"
	aryClassUser := make([]model.ClassUser, 0)
	var classUser model.ClassUser
	if _, err = classUser.List(&aryClassUser, qParm, 1, 100); err != nil {
		msg = "查询学员信息失败"
		logger.Error(err)
		return
	} else {
		aryClassRoomId := make([]uint, 0)
		checkClassRoomIds := "|"
		for i := 0; i < len(aryClassUser); i++ {
			ary := strings.Split(aryClassUser[i].ClassRoomIds, "|")
			for _, val := range ary {
				if val == "" || val == "|" {
					continue
				}
				if roomId := utils.String2Uint(val); roomId > 0 {
					if !strings.Contains(checkClassRoomIds, "|"+val+"|") {
						aryClassRoomId = append(aryClassRoomId, roomId)
						checkClassRoomIds = checkClassRoomIds + val + "|"
					}
				}
			}
		}
		qParm1 := utils.MakeInterfaceMap()
		qParm1["class_room_ids"] = aryClassRoomId
		aryClassRoom := make([]model.ClassRoom, 0)
		var classRoom model.ClassRoom
		if _, err = classRoom.List(&aryClassRoom, qParm1, 1, 100); err != nil {
			msg = "查询课堂信息失败"
			logger.Error(err)
			return
		} else {
			for _, item := range aryClassRoom {
				if strings.Contains(item.PodIds, fmt.Sprintf("|%d|", podId)) {
					msg = ""
					ok = true
					return
				}
			}
			msg = "无权限"
			return
		}
	}
}

func ValidContainerMemory(containerMem string) bool {
	if strings.HasSuffix(containerMem, "g") || strings.HasSuffix(containerMem, "m") {
		str := strings.ReplaceAll(containerMem, "g", "")
		str = strings.ReplaceAll(str, "m", "")
		if val := utils.String2Int(str); val <= 0 {
			return false
		}
		return true
	} else {
		return false
	}
}

func OutOfAmount(userId uint) bool {
	msg := ""
	var user model.User
	if err := user.GetById(userId); err != nil {
		logger.Error(err)
		return true
	}
	if user.Amount.LessThanOrEqual(decimal.Zero) {
		var card model.Card
		if leaveAmount, err := card.CardValidAmount(userId, 0); err != nil {
			logger.Error("CardValidAmount   ", "err:", err, " leaveAmount:", leaveAmount.String(), " userId:", userId, "   podId:", 0)
			msg = "账户已欠费，请先充值再使用该功能"
			logger.Error(msg, " userId:", user.ID, "  amount:", user.Amount)
			return true
		} else {
			logger.Info("CardValidAmount   ", "leaveAmount:", leaveAmount.String(), " userId:", userId, "   podId:", 0)
			if leaveAmount.LessThanOrEqual(decimal.Zero) {
				msg = "账户已欠费，请先充值再使用该功能"
				logger.Error(msg, " userId:", user.ID, "  amount:", user.Amount)
				return true
			}
		}
	}
	return false
}

func IsOverDayInstance(instance model.Instance) bool {
	if instance.ID == 0 || instance.Uuid == "" {
		return false
	}
	if instance.Status == enums.InstanceStatusEnum.BootInProgress ||
		instance.Status == enums.InstanceStatusEnum.Running ||
		instance.Status == enums.InstanceStatusEnum.ShutdownInProgress ||
		instance.Status == enums.InstanceStatusEnum.Hidden {
		return false
	}
	if instance.Status != enums.InstanceStatusEnum.Created && instance.Status != enums.InstanceStatusEnum.ShutdownComplete {
		return false
	}
	today := jsontime.Today().Time()
	overDay := today.AddDate(0, 0, -common.InstanceOverDays)
	checkTime := instance.CreatedAt
	if instance.StartupMarkTime.After(checkTime) {
		checkTime = instance.StartupMarkTime
	}
	if instance.StartupTime.After(checkTime) {
		checkTime = instance.StartupTime
	}
	if instance.ShutdownTime.After(checkTime) {
		checkTime = instance.ShutdownTime
	}
	if checkTime.Before(overDay) {
		return true
	}
	return false
}

func UpdatePodKeywords(podId uint) error {
	var pod model.Pod
	if err := pod.GetById(podId); err != nil {
		logger.Error(err)
		return err
	}
	var keywords structs.Keywords
	if pod.Keywords != "" {
		if err := utils.GetStructFromJson(&keywords, pod.Keywords); err != nil {
			logger.Error(err, podId)
			return err
		}
	}
	if pod.UserId > 0 {
		var user model.User
		if err := user.GetById(pod.UserId); err != nil {
			logger.Error(err, pod.UserId)
		} else {
			keywords.AuthorName = user.DisplayName
			if pod.AuthorName != user.DisplayName {
				if err := pod.SetAuthorName(user.DisplayName); err != nil {
					logger.Error(err, pod.ID)
					return err
				}
			}
		}
	}
	labelTitles := ""
	if pod.LabelIds != "" {
		ary := strings.Split(pod.LabelIds, "|")
		for _, val := range ary {
			if val == "" {
				continue
			}
			if id := utils.String2Uint(val); id > 0 {
				var label model.Label
				if err := label.GetById(id); err != nil {
					logger.Error(err, id)
				} else {
					labelTitles += label.Title + " "
				}
			}
		}
	}
	if labelTitles != "" {
		keywords.LabelTitles = strings.TrimSpace(labelTitles)
	}
	json := utils.GetJsonFromStruct(keywords)
	if json != "" {
		if err := pod.SetKeywords(json); err != nil {
			logger.Error(err, pod.ID)
			return err
		}
	}
	return nil
}

func DirMakeSureByUserPod(userPrivateStorage string, podUuId string) error {

	if userPrivateStorage == "" {
		err := errors.New("用户目录未创建")
		logger.Error(err)
		return err
	}

	//if podUuId == "" {
	//	err := errors.New("PodUuid为空")
	//	logger.Error(err)
	//	return err
	//}

	//config.PrivateStorage = "/mnt/user-data/store0"

	dirs := make([]string, 0)
	userBasePath := path.Join(config.PrivateStorage, userPrivateStorage)
	dirs = append(dirs, userBasePath)

	if podUuId != "" {
		userPodPath := path.Join(userBasePath, "container", podUuId)
		dirs = append(dirs, userPodPath)
	}

	for i := 0; i < 3; i++ {
		if err := DirMakeSure(dirs); err == nil {
			return nil
		}
		time.Sleep(time.Second)
	}
	return DirMakeSure(dirs)
}

func DirMakeSureByUser(userId uint, podId uint) error {
	var user model.User
	if err := user.GetById(userId); err != nil {
		logger.Error(err)
		return err
	}

	dirs := make([]string, 0)
	if user.PrivateStorage == "" {
		err := errors.New("用户目录为创建")
		logger.Error(err)
		return err
	}
	userBasePath := path.Join(config.PrivateStorage, user.PrivateStorage)
	dirs = append(dirs, userBasePath)

	if podId > 0 {
		var pod model.Pod
		if err := pod.GetById(podId); err != nil {
			logger.Error(err)
			return err
		}
		if pod.Uuid == "" {
			err := errors.New("PodUuid为空")
			logger.Error(err)
			return err
		}
		userPodPath := path.Join(userBasePath, "container", pod.Uuid)
		dirs = append(dirs, userPodPath)
	}

	//config.PrivateStorage = "/mnt/user-data/store0"
	for i := 0; i < 3; i++ {
		if err := DirMakeSure(dirs); err == nil {
			return nil
		}
		time.Sleep(time.Second)
	}
	return DirMakeSure(dirs)
}

func DirMakeSure(data []string) error {
	// 目标 URL
	url := config.FileStorageInterfaceUrl + "/file/dir_make_sure"

	// 请求数据
	//data := []string{
	//	"/mnt/user-data/store0/0/xxxxxx",
	//	"/mnt/user-data/store0/0/xxxxxx/container/xxxxx",
	//}

	// 将数据编码为 JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		logger.Error("data:", data, " err:", err)
		return err
	}

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Error("Error creating request:", err)
		return err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{
		Timeout: 5 * time.Second, // 设置超时 5 秒
	}
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("Error sending request:", err)
		return err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error("Error reading response body:", err)
		return err
	}

	json := string(body)
	m := utils.GetMapFromJson(json)
	if v, ok := m["code"]; ok {
		if v.(float64) == 0 {
			return nil
		}
	}
	err = errors.New("code不为0")
	// 打印响应状态码和内容
	logger.Info("Response Status:", resp.Status)
	logger.Info("Response Body:", json)
	logger.Error(err)
	return err
}
