package service

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/service/pay"
	"errors"
	"fmt"
	"github.com/go-pay/gopay/alipay"
	"strings"
	"time"
)

type _withdraw struct {
}

var WithdrawService _withdraw

func (o *_withdraw) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("WithdrawService奔溃:", e)
		}
	}()
	logger.Info("WithdrawService.Run 开始循环获取")
	lastId := uint(0)
	pageSize := 10
	for {
		var withdraw model.Withdraw
		ary, err := withdraw.GetListForCheck(lastId, pageSize)
		if err != nil {
			logger.Error(err, "  ", lastId)
			break
		}
		logger.Info("查询到", len(ary), "条需要处理的提现订单")
		for _, item := range ary {
			lastId = item.ID
			//if item.CreatedAt.Add(time.Duration(30) * time.Minute).After(time.Now()) {
			//	logger.Info("已经检索到最近30分钟的充值订单，暂时不做处理")
			//	lastId = uint(0)
			//	break
			//}
			if err := o.HandleWithdraw(item.ID); err != nil {
				logger.Error(err, " ID:", item.ID)
			}
		}
		if len(ary) < pageSize || lastId == 0 {
			logger.Info("查询到", len(ary), "条需要处理的订单数据,提现订单处理本次轮询结束")
			break
		}
	}
	logger.Info("WithdrawService.Run 循环结束")
}

func (obj *_withdraw) HandleWithdraw(withdrawId uint) error {
	lockKey := enums.RedisKeyEnum.LockKey + fmt.Sprintf("HandleWithdraw_%d", withdrawId)
	if common.RedisLock(lockKey, 1, 1000*60) {
		defer common.RedisUnLock(lockKey)

		var withdraw model.Withdraw
		if err := withdraw.GetById(withdrawId); err != nil {
			logger.Error(err)
			return err
		}
		if withdraw.Gateway == "" {
			if withdraw.CreatedAt.After(time.Now().Add(time.Minute * 5 * -1)) {
				err := errors.New("还未到处理时间")
				logger.Error(err)
				return err
			}
		}
		if withdraw.PayStatus != model.WithdrawPayStatusWating && withdraw.PayStatus != model.WithdrawPayStatusPaying {
			logger.Error("该Withdraw已经处理", withdraw.ID, "  ", withdraw.PayStatus)
			return nil
		}
		if withdraw.Gateway == enums.PayGatewayEnum.Manual {
			msg := "手动处理，直接做走打款成功过逻辑"
			rsp := map[string]interface{}{
				"msg":  msg,
				"time": time.Now(),
			}
			json := utils.GetJsonFromStruct(rsp)
			if err := obj.WithdrawResult(withdraw.ID, "SUCCESS", "", withdraw.OutTradeNo, json, time.Now()); err != nil {
				logger.Error(err)
				return err
			}
		} else if withdraw.PayChannel == enums.PayChannelEnum.AliPay {

			if aliRsp, err2 := pay.AlipayService.FundTransOrderQuery(withdraw.OutTradeNo); err2 != nil {
				//{"code":"40004","msg":"Business Failed","sub_code":"ORDER_NOT_EXIST","sub_msg":"转账订单不存在"}
				logger.Error(err2)
				json := utils.GetJsonFromStruct(err2)
				if bizErr, b := alipay.IsBizError(err2); b {
					if bizErr.SubCode == "ORDER_NOT_EXIST" {
						if err := withdraw.SetPayCallbackJson(json, model.WithdrawPayStatusNoTrade); err != nil {
							logger.Error(err)
							return err
						}
						if withdraw.BusinessType == enums.BusinessTypeEnum.VerifyWithdrawAccount {
							var withdrawAccount model.WithdrawAccount
							if err := withdrawAccount.GetById(withdraw.WithdrawAccountId); err != nil {
								logger.Error(err)
								return err
							}
							if withdrawAccount.PayStatus == model.WithdrawPayStatusPaying {
								if err := withdrawAccount.SetPayStatus(model.WithdrawPayStatusWating); err != nil {
									logger.Error(err)
									return err
								}
							}

						} else if withdraw.BusinessType == enums.BusinessTypeEnum.Withdraw {
							var withdrawApply model.WithdrawApply
							if err := withdrawApply.GetById(withdraw.OrderId); err != nil {
								logger.Error(err)
								return err
							} else {
								if withdrawApply.WithdrawId != withdraw.ID {
									err := errors.New("提现ID不一致")
									return err
								}
								if withdrawApply.OrderNo != withdraw.OrderNo {
									logger.Error("订单号不一致")
								}
								if err := withdrawApply.SetPayFailed(); err != nil {
									logger.Error(err)
									return err
								}
							}
						}
					}
				}
			} else {
				//{"code":"10000","msg":"Success","order_fee":"0.00","order_id":"20241023020070011540060081339423","out_biz_no":"w2024102312272400001452","pay_date":"2024-10-23 12:27:52","status":"SUCCESS"}
				json := utils.GetJsonFromStruct(aliRsp)

				paySuccessTime := common.DefaultTime

				if aliRsp.Response.Status == "SUCCESS" {

					if t, err2 := time.ParseInLocation("2006-01-02 15:04:05", aliRsp.Response.PayDate, time.Local); err2 != nil {
						logger.Error(err2)
					} else {
						paySuccessTime = t
					}
				}

				if err := obj.WithdrawResult(withdraw.ID, aliRsp.Response.Status, aliRsp.Response.OrderId, aliRsp.Response.OutBizNo, json, paySuccessTime); err != nil {
					logger.Error(err)
					return err
				}

				logger.Info("未处理的FundTransOrderQuery：", json)
			}
		} else if withdraw.PayChannel == enums.PayChannelEnum.WechatPay {
			/*
				trade, err := WechatpayService.TradeQueryByOutTradeNo(item.OutTradeNo)
				if err != nil {
					logger.Error(err)
					return err
				}
				if trade.Code == 404 {
					if strings.Contains(trade.Error, "ORDER_NOT_EXIST") { //订单不存在
						if err := item.SetState(utils.GetJsonFromStruct(trade), enums.RechargeStateEnum.TRADE_NOT_EXIST); err != nil {
							logger.Error(err)
							return err
						}
						return nil
					}
				}
				if trade.Response.TradeState == "CLOSED" {
					if err := item.SetState(utils.GetJsonFromStruct(trade), enums.RechargeStateEnum.TRADE_CLOSED); err != nil {
						logger.Error(err)
					}
					return nil
				} else if trade.Response.TradeState == "SUCCESS" {
					//logger.Info(trade)
					outTradeNo := trade.Response.OutTradeNo
					tradeNo := trade.Response.TransactionId
					payTime, err := time.Parse(time.RFC3339, trade.Response.SuccessTime)
					if err != nil {
						logger.Error(err)
						return err
					}
					if err := o.HandlePaySuccessRecharge(outTradeNo, tradeNo, payTime, utils.GetJsonFromStruct(trade)); err != nil {
						logger.Error(err)
						return err
					}
				} else if trade.Response.TradeState == "NOTPAY" {
					logger.Info("订单未支付，暂时不处理")
				} else {
					logger.Error("不是支付成功标识", "trade:  ", utils.GetJsonFromStruct(trade))
				}*/
		}
		return nil
	} else {
		msg := "支付请求处理中，请勿重复操作"
		err := errors.New(msg)
		return err
	}
}

func (obj *_withdraw) WithdrawRequest(businessType int, orderId uint, nanoId string) (msg string, err error) {

	lockKey := enums.RedisKeyEnum.LockKey + fmt.Sprintf("WithdrawRequest_%d_%d", businessType, orderId)
	if common.RedisLock(lockKey, 1, 1000*60) {
		defer common.RedisUnLock(lockKey)

		var withdraw model.Withdraw
		if businessType == enums.BusinessTypeEnum.VerifyWithdrawAccount {
			var withdrawAccount model.WithdrawAccount
			if err = withdrawAccount.GetById(orderId); err != nil {
				msg = "获取账号信息失败"
				logger.Error(msg, err)
				return
			}
			if withdrawAccount.Nanoid != nanoId {
				msg = "数据不一致"
				err = errors.New(msg)
				return
			}
			if err = withdrawAccount.PayVerifyAmount(&withdraw); err != nil {
				msg = "生成打款记录失败"
				logger.Error(msg, err)
				return
			}
		} else if businessType == enums.BusinessTypeEnum.Withdraw {
			var withdrawApply model.WithdrawApply
			if err = withdrawApply.GetById(orderId); err != nil {
				msg = "记录不存在"
				logger.Error(msg, err)
				return
			}
			if withdrawApply.Nanoid != nanoId {
				msg = "数据不一致"
				err = errors.New(msg)
				return
			}
			if withdrawApply.AuditStatus != model.WithdrawAuditStatusAuditPassed {
				msg = "审核未通过"
				err = errors.New(msg)
				logger.Error(msg)
				return
			}

			if withdrawApply.PayStatus != model.WithdrawPayStatusWating && withdrawApply.PayStatus != model.WithdrawPayStatusFailed {
				msg = "支付状态不正确"
				err = errors.New("支付状态不正确")
				logger.Error(err)
				return
			}
			if err = withdrawApply.PayAmount(&withdraw); err != nil {
				msg = "生成打款记录失败"
				logger.Error(msg, err)
				return
			}
		}

		if withdraw.ID == 0 {
			msg = "未获取到支付信息"
			err = errors.New(msg)
			logger.Error(err)
			return
		}

		if withdraw.OutTradeNo == "" {
			msg = "支付单号为空"
			err = errors.New(msg)
			logger.Error(err)
			return
		}

		var withdrawAccount model.WithdrawAccount
		if err = withdrawAccount.GetById(withdraw.WithdrawAccountId); err != nil {
			msg = "查询账户信息失败"
			logger.Error(msg, err)
			return
		}
		if withdrawAccount.PayAccount != withdraw.PayAccount {
			msg = "账户信息不匹配"
			err = errors.New(msg)
			logger.Error(err)
			return
		}
		if withdraw.Gateway == enums.PayGatewayEnum.Manual {
			msg = "手动处理，不发送打款请求"
			rsp := map[string]interface{}{
				"msg":  msg,
				"time": time.Now(),
			}
			json := utils.GetJsonFromStruct(rsp)
			if err = withdraw.SetPayRspJson(json); err != nil {
				logger.Error("withdraw.SetPayRspJson err:", err)
				return
			}

			if err = WithdrawService.HandleWithdraw(withdraw.ID); err != nil {
				logger.Error(err)
				msg = "处理打款请求失败"
				return
			} else {
				msg = "处理完成"
			}

			logger.Info(msg)
			return
		} else if withdraw.PayChannel == enums.PayChannelEnum.AliPay {
			returnUrl := "" //fmt.Sprintf("https://www.%s/console/paygress", GetSiteDomain(c.Request.Referer()))
			//des := fmt.Sprintf("支付宝账号验证")
			trueName := config.DecryptAes(withdrawAccount.TrueName)
			//trueName = "伊行"
			client := pay.AlipayService.GetClientByCrt(returnUrl)
			if client == nil {
				msg = "创建支付对象失败"
				err = errors.New(msg)
				logger.Error(err)
				return
			}
			if aliRsp, err1 := pay.AlipayService.FundTransUniTransfer(client, withdraw.OutTradeNo, withdraw.Amount, withdraw.PayAccount, trueName, withdraw.Description); err1 != nil {
				//{"msg": "Business Failed", "code": "40004", "sub_msg": "请求金额不能低于0.1元", "sub_code": "EXCEED_LIMIT_SM_MIN_AMOUNT"}
				//{"msg": "Business Failed", "code": "40004", "sub_msg": "收款账号不存在或姓名有误，建议核实账号和姓名是否准确", "sub_code": "PAYEE_NOT_EXIST"}
				msg = "打款请求发送失败"
				err = err1
				logger.Error(msg, "err:", err, "  amount:", withdraw.Amount, "  payAccount:", withdraw.PayAccount, "  trueName:", trueName)

				//if bizErr,b:=alipay.IsBizError(err1);b{
				//
				//}
				json := utils.GetJsonFromStruct(err)
				if err2 := withdraw.SetPayRspJson(json); err2 != nil {
					logger.Error("withdraw.SetPayRspJson err2:", err2)
				}

				if businessType == enums.BusinessTypeEnum.VerifyWithdrawAccount {
					if strings.Contains(json, "请求金额不能低于0.1元") {
						if withdrawAccount.Status == model.WithdrawAccountStatusVerifyed {
							msg = "该账号已认证成功"
							err = errors.New(msg)
						}
						if withdrawAccount.Status == model.WithdrawAccountStatusInvalid {
							msg = "该账号已禁用"
							err = errors.New(msg)
						}
						if err2 := withdrawAccount.SetStatus(model.WithdrawAccountStatusVerifyed); err2 != nil {
							msg = "设置状态失败"
							err = err2
							logger.Error(msg, err)
							return
						} else {
							msg = "认证成功"
							err = nil
							return
						}
					} else if strings.Contains(json, "收款账号不存在或姓名有误") {
						msg = "收款账号不存在或姓名有误"
						if err2 := withdrawAccount.SetPayStatus(model.WithdrawPayStatusFailed); err2 != nil {
							msg = "设置状态失败"
							err = err2
							logger.Error(msg, err)
							return
						}
					}
				}
				return
			} else {
				//{"alipay_fund_trans_uni_transfer_response":{"code":"10000","msg":"Success","out_biz_no":"w2024102312272400001452","order_id":"20241023020070011540060081339423","pay_fund_order_id":"20241023020070011540060081339423","status":"SUCCESS","trans_date":"2024-10-23 12:27:52"},"alipay_cert_sn":"e930371054b73c0d027f57a2207f2036","sign":"QWlnGc4ePq0yejsRDSR1jygWRWc/DdYtSHo+LEsadbZEJvIRlCV3IRTaSCzS9G+YMPMj+JHYNn0BjJVmVHpV1K4UhaAcUT24vm8ZC/lQIVtzWBpIwof5r59qi6KHkagrLoYCgjivzKCjGV9TRIYX9EoZZ7qFeNM5VNga6+Kr6P5Bqz5+uqgMw+GnU98nZUHrPuWbp7ASnQi4PsFUoogshz+u33f69aVlpsbBLC0piBIeP5mRiGPOn63xiNO6LtIMhBYgNdIoHLwxQGIFwrTrYMYR9oTRC/kxo5cSkZa/ibY6wsWUY9GcLbuEgTTrMGu4jDB9Z/zWGgWZM/GcGzk27g=="}
				json := utils.GetJsonFromStruct(aliRsp)
				if err = withdraw.SetPayRspJson(json); err != nil {
					logger.Error("withdraw.SetPayRspJson err:", err)
					return
				}
				var transUniTransfer alipay.TransUniTransfer
				if err = utils.GetStructFromJson(&transUniTransfer, json); err != nil {
					logger.Error(err)
					return
				} else {

					//paySuccessTime := common.DefaultTime
					//if t, err2 := time.ParseInLocation("2006-01-02 15:04:05", transUniTransfer.TransDate, time.Local); err2 != nil {
					//	logger.Error(err2)
					//} else {
					//	paySuccessTime = t
					//}
					//if err = obj.WithdrawResult(withdraw.ID, transUniTransfer.Status, transUniTransfer.OrderId, transUniTransfer.OutBizNo, json, paySuccessTime); err != nil {
					//	logger.Error(err)
					//	return
					//}
				}
				msg = "打款请求发送成功"
				return
			}
		} else {
			msg = "该渠道暂不支持"
			err = errors.New(msg)
			return
		}
	} else {
		msg = "支付请求中，请勿重复操作"
		err = errors.New(msg)
		return
	}
}

func (obj *_withdraw) WithdrawResult(withdrawId uint, payStatus string, payTradeId string, outTradeNo string, payCallbackJson string, paySuccessTime time.Time) error {

	lockKey := enums.RedisKeyEnum.LockKey + fmt.Sprintf("WithdrawResult_%d", withdrawId)
	if common.RedisLock(lockKey, 1, 1000*60) {
		defer common.RedisUnLock(lockKey)
		var withdraw model.Withdraw
		if err := withdraw.GetById(withdrawId); err != nil {
			logger.Error(err)
			return err
		}
		if withdraw.PayStatus != model.WithdrawPayStatusPaying {
			err := errors.New("记录状态不正确")
			logger.Error(err)
			return err
		}
		if withdraw.OutTradeNo != outTradeNo {
			err := errors.New("单号不一致")
			logger.Error(err)
			return err
		}
		if payStatus == "SUCCESS" {

			if withdraw.BusinessType == enums.BusinessTypeEnum.Withdraw {
				var withdrawApply model.WithdrawApply
				if err := withdrawApply.GetById(withdraw.OrderId); err != nil {
					logger.Error(err)
					return err
				}
				if withdrawApply.OrderNo != withdraw.OrderNo {
					err := errors.New("单号不一致")
					logger.Error(err)
					return err
				}
				if err := withdraw.PaySuccess(payTradeId, outTradeNo, payCallbackJson, paySuccessTime); err != nil {
					logger.Error(err)
					return err
				}
			} else {
				err := errors.New("未处理的业务类型")
				return err
			}
		} else {
			err := errors.New("未处理的状态" + payStatus)
			return err
		}

		return nil
	} else {
		msg := "支付处理中，请勿重复操作"
		err := errors.New(msg)
		return err
	}
}
