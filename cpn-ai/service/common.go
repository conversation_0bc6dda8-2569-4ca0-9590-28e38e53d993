package service

import (
	"bufio"
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/config"
	"cpn-ai/enums"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"os"
	"path/filepath"
	"strings"
	"time"
)

func GetBlockKey(t time.Time) string {
	minute := t.Minute()

	tenDigit := (minute / 10) % 10 // 获取分钟数的十位数
	unitDigit := minute % 10       // 获取分钟数的个位数

	digit := 0
	if unitDigit >= 5 {
		digit = 5
	}
	s := t.Format("2006010215") + fmt.Sprintf("%d%d", tenDigit, digit)
	return s
}

func GetBlockKeyTime(blockKey string) (time.Time, error) {
	// 提取时间部分字符串
	timeString := blockKey[len(blockKey)-12:] // 获取字符串末尾的12个字符（假设时间部分长度为12）
	// 解析时间字符串为 time.Time 类型
	return time.Parse("200601021504", timeString)
}

func CalculationCost(chatCharging ChatCharging) decimal.Decimal {
	ratioPrompt, _ := decimal.NewFromString("0.000001")
	ratioCompletion, _ := decimal.NewFromString("0.000001")
	cost := decimal.NewFromInt(int64(chatCharging.PromptTokens)).Mul(ratioPrompt)
	cost1 := decimal.NewFromInt(int64(chatCharging.CompletionTokens)).Mul(ratioCompletion)
	return cost.Add(cost1)
}

func GetBackPathForLLM(strKey string) (string, error) {
	if _, err := time.Parse("200601021504", strKey); err != nil {
		logger.Error(err)
		return "", err
	}
	if len(strKey) != 12 || config.DiffusionFilePath == "" {
		logger.Error("参数错误 strKey:", strKey, "  DiffusionFilePath:", config.DiffusionFilePath)
		return "", errors.New("参数错误")
	}
	path := config.DiffusionFilePath + fmt.Sprintf("cpn/charg/%s/%s.txt", strKey[:8], strKey)

	return path, nil
}

func AryToFile(ary []string, filePath string) error {

	directory := filepath.Dir(filePath) // 获取目录路径
	// 创建目录，存在则不创建，不存在则创建
	if err := os.MkdirAll(directory, 0755); err != nil {
		logger.Error(err)
		return err
	}

	file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		logger.Error(err)
		return err
	}
	defer file.Close()

	// 创建一个带缓冲的写入器
	writer := bufio.NewWriter(file)

	// 将字符串数组中的每个字符串逐行写入文件
	for _, str := range ary {
		_, err := fmt.Fprintln(writer, str)
		if err != nil {
			fmt.Println(err)
			return err
		}
	}

	// 确保所有数据都被刷新到文件中
	if err := writer.Flush(); err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func CalculateEndTime(chargingType int, startTime time.Time, num int) time.Time {
	if chargingType == enums.ChargingTypeEnum.Day {
		return startTime.Add(time.Duration(num) * 24 * time.Hour)
	} else if chargingType == enums.ChargingTypeEnum.Week {
		return startTime.Add(time.Duration(num) * 7 * 24 * time.Hour)
	} else if chargingType == enums.ChargingTypeEnum.Month {
		return startTime.AddDate(0, num, 0)
	}
	return common.DefaultTime
}

func CheckPrivateStorage() error {
	if len(config.PrivateStorage) < 10 {
		logger.Error("用户存储基础路径为空", config.PrivateStorage)
		return errors.New("用户存储基础路径为空")
	}
	if strings.HasSuffix(config.PrivateStorage, "/") == false {
		logger.Error("用户存储基础路径结束符不正确", config.PrivateStorage)
		return errors.New("用户存储基础路径结束符不正确")
	}
	if _, err := os.Stat(config.PrivateStorage); os.IsNotExist(err) {
		logger.Error("用户存储基础路径不存在", err)
		return err
	}

	return nil
}
