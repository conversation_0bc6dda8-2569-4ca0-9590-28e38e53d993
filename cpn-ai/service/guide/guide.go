package guide

import "math"

func GetEventId(id int) int { //0,1,2,3,4,5
	return int(math.Pow(2, float64(id)))
}

// 判断一个数字是否是 2 的幂次方
func IsEventId(n int) bool {
	// 数字必须是正数，且 n & (n - 1) 必须为 0
	return n > 0 && (n&(n-1)) == 0
}

// 判断事件是否完成 eventID必须是2的0次 1次 2次
func IsEventCompleted(eventsStatus int, eventID int) bool {
	// 使用位运算检查第 eventID 位是否为 1 (表示已完成)
	return (eventsStatus & (1 << (eventID - 1))) != 0
}

// 设置事件为完成
func SetEventCompleted(eventsStatus int, eventID int) int {
	// 使用位运算将第 eventID 位设置为 1 (表示已完成)
	return eventsStatus | (1 << (eventID - 1))
}
