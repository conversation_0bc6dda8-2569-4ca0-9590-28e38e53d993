package pay

import (
	"cpn-ai/common/logger"
	"errors"
	"os"
	"path/filepath"
	"strings"
)

var AlipayPublicContentRSA2 []byte
var AlipayRootContent []byte
var AppPublicContent []byte

var AppPrivateContent string

func init() {
	if err := LoadAlipayCert(); err != nil {
		logger.Error("支付宝证书载入失败", err)
	} else {
		logger.Info("支付宝证书载入完成")
	}
}

func LoadAlipayCert() error {
	certPath := "./config/alipay"

	alipayPublicCrtRSA2FilePath := ""
	alipayRootCrtFilePath := ""
	appPublicCrtFilePath := ""

	appPrivateCrtFilePath := ""
	err := filepath.Walk(certPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err // 如果遇到错误，返回错误
		}

		// 检查是否是文件
		if !info.IsDir() {
			//fmt.Println("文件:", path) // 打印文件路径

			if strings.Contains(path, "alipayRootCert") {
				alipayRootCrtFilePath = path
			} else if strings.Contains(path, "alipayCertPublicKey_RSA2") {
				alipayPublicCrtRSA2FilePath = path
			} else if strings.Contains(path, "appCertPublicKey_") {
				appPublicCrtFilePath = path
			} else if strings.Contains(path, "appCertPrivateKey") {
				appPrivateCrtFilePath = path
			}
		}
		return nil
	})

	if err != nil {
		logger.Error(err)
		return err
	}
	if alipayRootCrtFilePath == "" || alipayPublicCrtRSA2FilePath == "" || appPublicCrtFilePath == "" || appPrivateCrtFilePath == "" {
		err = errors.New("未获取到全部证书路径")
		logger.Error(err)
		return err
	}

	if AlipayPublicContentRSA2, err = os.ReadFile(alipayPublicCrtRSA2FilePath); err != nil {
		logger.Error(err)
		return err
	}

	if AlipayRootContent, err = os.ReadFile(alipayRootCrtFilePath); err != nil {
		logger.Error(err)
		return err
	}

	if AppPublicContent, err = os.ReadFile(appPublicCrtFilePath); err != nil {
		logger.Error(err)
		return err
	}

	if tmp, err := os.ReadFile(appPrivateCrtFilePath); err != nil {
		logger.Error(err)
		return err
	} else {
		AppPrivateContent = string(tmp)
	}
	return nil
}
