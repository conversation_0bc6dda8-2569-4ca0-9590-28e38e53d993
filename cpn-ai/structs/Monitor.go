package structs

import "cpn-ai/common/jsontime"

type MonitorData struct {
	InstanceCount int `json:"instance_count"`
	RunningCount  int `json:"running_count"`
	BootinCount   int `json:"bootin_count"`
	GpuCount      int `json:"gpu_count"`
	NoCardCount   int `json:"no_card_count"`
	UserCount     int `json:"user_count"`
	PodCount      int `json:"pod_count"`
}

//kolInstanceCount := 0              //总的实例数量
//kolRunningCount := 0               //运行中的实例数量
//kolBootInCount := 0                //启动中的实例数量
//kolGpuCount := 0                   //占用的Gpu数量
//kolNoCardCount := 0                //无卡的实例数量
//kolUserCount := make(map[uint]int) //不同的使用人数
//kolPodCount := make(map[uint]int)  //不同的Pod数量

type MonitorGpuData struct {
	GpuModelId uint `json:"gpu_model_id"`
	TotalCount int  `json:"total_count"`
	UseCount   int  `json:"use_count"`
	FreeCount  int  `json:"free_count"`
}

type GpuModelMonitor struct {
	ID        uint              `json:"-"`
	Uuid      string            `json:"uuid"`
	Title     string            `json:"title"`
	GpuName   string            `json:"-"`
	Desc      string            `json:"desc"`
	MemoryG   int               `json:"-"`
	MemoryM   int               `json:"-"`
	Price     string            `json:"price"`
	Remark    string            `json:"remark"`
	Status    int               `json:"status"`
	StatusTxt string            `json:"status_txt"`
	CreatedAt jsontime.JsonTime `json:"created_at"`
	TotalGpus int               `json:"total_gpus"`
	FreeGpus  int               `json:"free_gpus"`
	FreeTxt   string            `json:"free_txt"`
}
