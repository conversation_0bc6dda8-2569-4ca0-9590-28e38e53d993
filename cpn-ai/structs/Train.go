package structs

import (
	"fmt"
	"strings"
)

type TrainModelCategory struct {
	Id    uint   `json:"id"`
	Title string `json:"title"`
	Logo  string `json:"logo" `
	Order int    `json:"order"`
}

type TrainModelItem struct {
	CategoryId uint   `json:"category_id"`
	Title      string `json:"title"`
	Name       string `json:"name"`
	Path       string `json:"path"`
	Order      int    `json:"order"`
}

type TrainCropTag struct {
	ImagesUuid string       `json:"images_uuid"` //原始图片集
	DirPath    string       `json:"dir_path"`    //打标文件夹目录
	FilePath   string       `json:"file_path"`   //打标文件目录,空为打标整个文件夹
	Param      CropTagParam `json:"param"`       //打标参数，包括裁剪参数
}

type CropTagParam struct {
	TagsUuid     string  `json:"tags_uuid"`
	ImagesUuid   string  `json:"images_uuid"`
	CropMethod   string  `json:"crop_method"`
	CropSize     string  `json:"crop_size"`
	CropTraget   string  `json:"crop_traget"`
	TagThreshold float64 `json:"tag_threshold"`
	TagAlg       string  `json:"tag_alg"`
	TriggerWord  string  `json:"trigger_word"`
}

type TrainJob struct {
	TagsUuid string        `json:"tags_uuid"` //原始图片集
	DirPath  string        `json:"dir_path"`  //打标文件夹目录
	FilePath string        `json:"file_path"` //打标文件目录,空为打标整个文件夹
	Param    TrainJobParam `json:"param"`     //打标参数，包括裁剪参数
}

type TrainJobParam struct {
	JobsUuid    string `json:"jobs_uuid"`
	JobsLevel   string `json:"jobs_level"`
	TagsUuid    string `json:"tags_uuid"`
	TriggerWord string `json:"trigger_word"`
	LoraName    string `json:"lora_name"`
	ModelName   string `json:"model_name"`

	//Steps                     int     `json:"steps"`
	//Lr                        float64 `json:"lr"`
	//Rank                      int     `json:"rank"`
	//NumRepeats                int     `json:"num_repeats"`
	//SaveSafetensorsEverySteps int     `json:"save_safetensors_every_steps"`
	Sample1     string `json:"sample_1"`
	Sample2     string `json:"sample_2"`
	Sample3     string `json:"sample_3"`
	SampleSteps int    `json:"sample_steps"` //默认200 每多少步生成一次预览图

	ModelNameOrPath string `json:"model_name_or_path"` //训练底膜

	//训练参数
	Repeat         int    `json:"repeat"`          //单张训练次数 1-50 默认值：20 每轮学习步数
	Epoch          int    `json:"epoch"`           //训练轮数 1-20 默认值：5
	BatchSize      int    `json:"batch_size"`      //批量大小(当前模型仅支持为1)
	MixedPrecision string `json:"mixed_precision"` //训练混合精度 默认值：bf16
	TotalSteps     int    `json:"total_steps"`     //总步数:Repeat*Epoch*图片数量

	//样图设置
	Resolution      string `json:"resolution"`       //样图分辨率 默认值：1024*1024
	Seed1           int64  `json:"seed1"`            //随机种子数 默认值：1000000001
	Sampler         string `json:"sampler"`          //采样方式(当前仅支持此采样方式预设) 默认值：Euler
	Prompts         string `json:"prompts"`          //提示词 默认值：one lady
	NegativePrompts string `json:"negative_prompts"` //负向提示词

	//保存设置
	SaveEveryNEpochs int    `json:"save_every_n_epochs"` //每N轮保存一个LoRA 1-5 默认值：1
	SavePrecision    string `json:"save_precision"`      //LoRA保存精度(当前仅支持此LoRA保存精度) 默认值：bf16

	//学习率&优化器
	LearningRate         string  `json:"learning_rate"`           //总学习率(分开设置 unet 和 text encoder 学习率后 这个值失效) 默认值：1e-4
	UnetLr               float32 `json:"unet_lr"`                 //Unet学习率 默认值：0.0001
	TextEncoderLr        float32 `json:"text_encoder_lr"`         //文本编码器学习率 默认值：0.00001
	LrWarmUp             int     `json:"lr_warm_up"`              //学习率预热(%) 0-100 默认值：0
	LrScheduler          string  `json:"lr_scheduler"`            //学习率调度器 默认值：cosine_with_restarts
	Optimizer            string  `json:"optimizer"`               //优化器 默认值：AdamW8bit
	LrSchedulerNumCycles int     `json:"lr_scheduler_num_cycles"` //重启次数 1-5 默认值：1
	TrainUnetOnly        bool    `json:"train_unet_only"`         //仅训练Unet 默认值：true
	TrainTextEncoderOnly bool    `json:"train_text_encoder_only"` //仅训练文本编码器 默认值：false
	MinSnrGamma          int     `json:"min_snr_gamma"`           //最小信噪比伽马值 0-20 默认值：0

	//网络
	EnableBlockWeights   bool    `json:"enable_block_weights"`    //启用分层学习率训练(仅适用network.lora) 默认false
	DownLrWeight         string  `json:"down_lr_weight"`          //U-Net 的 Encoder 层分层权重,共12层 默认值：1,1,1,1,1,1,1,1,1,1,1,1
	MidLrWeight          int     `json:"mid_lr_weight"`           //U-Net 的 Mid 层分层权重,共1层 默认值：1
	UpLrWeight           string  `json:"up_lr_weight"`            //U-Net 的 Decoder 层分层权重,共12层 默认值：1,1,1,1,1,1,1,1,1,1,1,1
	BlockLrZeroThreshold float32 `json:"block_lr_zero_threshold"` //分层学习率置0阈值 0-1 保留1位小数 默认值：1

	NetworkRankDim int `json:"network_rank_dim"` //网络大小(常用4~128) 1-128 默认值：32
	NetworkAlpha   int `json:"network_alpha"`    //网络Alpha(常用dim相同的值or更小) 4-128 默认值：32

	//打标设置
	ShuffleCaption   bool `json:"shuffle_caption"`   //打乱标注 默认true
	WeightedCaptions bool `json:"weighted_captions"` //使用带权重的token 默认false
	KeepNTokens      int  `json:"keep_n_tokens"`     //保持n个token 1-255 默认1
	MaxTokenLength   int  `json:"max_token_length"`  //最大token长度 75 150 225 默认75

	//噪声设置
	NoiseOffset             float32 `json:"noise_offset"`              //噪声偏移(设置金字塔噪声后这个值失效) 0-1 保留1位小数
	MultiresNoiseIterations int     `json:"multires_noise_iterations"` //多分辨率（金字塔）噪声扩散次数 0-64 默认0
	MultiresNoiseDiscount   float32 `json:"multires_noise_discount"`   //多分辨率（金字塔）衰减率 0-1 保留1位小数

	//高级设置
	Seed     int64 `json:"seed"`      //随机种子数 默认-1
	ClipSkip int   `json:"clip_skip"` //clip跳过 1或者2 默认值：1

}

func (p *TrainJobParam) GetDefault() {
	// 样图设置
	if p.SampleSteps == 0 { // 默认值：200
		p.SampleSteps = 200
	}

	// 训练参数
	if p.Repeat == 0 { // 默认值：20
		p.Repeat = 20
	}
	if p.Epoch == 0 { // 默认值：5
		p.Epoch = 5
	}
	if p.BatchSize == 0 { // 默认值：1
		p.BatchSize = 1 // 假定当前模型仅支持为1
	}
	if p.MixedPrecision == "" { // 默认值：bf16
		p.MixedPrecision = "bf16"
	}

	// 样图设置
	if p.Resolution == "" { // 默认值：1024*1024
		p.Resolution = "1024*1024"
	}
	if p.Seed1 == 0 { // 默认值：1000000001 (0通常表示未设置，如果你允许0作为有效种子，则需要更复杂的逻辑)
		p.Seed1 = 1000000001
	}
	if p.Sampler == "" { // 默认值：Euler
		p.Sampler = "Euler"
	}
	if p.Prompts == "" { // 默认值：one lady
		p.Prompts = "one lady"
	}
	// NegativePrompts 默认为空字符串，无需设置

	// 保存设置
	if p.SaveEveryNEpochs == 0 { // 默认值：1
		p.SaveEveryNEpochs = 1
	}
	if p.SavePrecision == "" { // 默认值：bf16
		p.SavePrecision = "bf16"
	}

	// 学习率&优化器
	if p.LearningRate == "" { // 默认值：1e-4
		p.LearningRate = "1e-4"
	}
	if p.UnetLr == 0 { // 默认值：0.0001
		p.UnetLr = 0.0001
	}
	if p.TextEncoderLr == 0 { // 默认值：0.00001
		p.TextEncoderLr = 0.00001
	}
	if p.LrWarmUp == 0 { // 默认值：0
		p.LrWarmUp = 0 // 如果默认值就是0，这里可以省略，但为了明确性保留
	}
	if p.LrScheduler == "" { // 默认值：cosine_with_restarts
		p.LrScheduler = "cosine_with_restarts"
	}
	if p.Optimizer == "" { // 默认值：AdamW8bit
		p.Optimizer = "AdamW8bit"
	}
	if p.LrSchedulerNumCycles == 0 { // 默认值：1
		p.LrSchedulerNumCycles = 1
	}
	// TrainUnetOnly 和 TrainTextEncoderOnly 的默认值是 false，bool 零值就是 false，无需额外设置
	if p.MinSnrGamma == 0 { // 默认值：0
		p.MinSnrGamma = 0 // 如果默认值就是0，这里可以省略，但为了明确性保留
	}

	// 网络
	// EnableBlockWeights 默认值 false，无需额外设置
	if p.DownLrWeight == "" { // 默认值：1,1,1,1,1,1,1,1,1,1,1,1
		p.DownLrWeight = "1,1,1,1,1,1,1,1,1,1,1,1"
	}
	if p.MidLrWeight == 0 { // 默认值：1
		p.MidLrWeight = 1
	}
	if p.UpLrWeight == "" { // 默认值：1,1,1,1,1,1,1,1,1,1,1,1
		p.UpLrWeight = "1,1,1,1,1,1,1,1,1,1,1,1"
	}
	if p.BlockLrZeroThreshold == 0 { // 默认值：1 (float32的0值，如果默认是1需要设置)
		p.BlockLrZeroThreshold = 1.0 // 默认值 1.0
	}

	if p.NetworkRankDim == 0 { // 默认值：32
		p.NetworkRankDim = 32
	}
	if p.NetworkAlpha == 0 { // 默认值：32
		p.NetworkAlpha = 32
	}

	// 打标设置
	// ShuffleCaption 默认值 true，但 bool 零值是 false，所以需要特别设置
	// 如果你希望json中不提供时，默认为true
	if !p.ShuffleCaption { // 如果未提供或为false
		p.ShuffleCaption = true
	}
	// WeightedCaptions 默认值 false，无需设置
	if p.KeepNTokens == 0 { // 默认值：1
		p.KeepNTokens = 1
	}
	if p.MaxTokenLength == 0 { // 默认值：75
		p.MaxTokenLength = 75
	}

	// 噪声设置
	// NoiseOffset 默认值 0，无需设置
	// MultiresNoiseIterations 默认值 0，无需设置
	// MultiresNoiseDiscount 默认值 0，无需设置

	// 高级设置
	if p.Seed == 0 { // 默认值：-1 (0值表示未设置)
		p.Seed = -1
	}
	if p.ClipSkip == 0 { // 默认值：1
		p.ClipSkip = 1
	}
}

func (p *TrainJobParam) LoadParams(v TrainJobParam, imageCount int, triggerWord string) error {
	p.GetDefault()
	if v.Repeat > 0 {
		p.Repeat = v.Repeat
	}
	if v.Epoch > 0 {
		p.Epoch = v.Epoch
	}

	p.TotalSteps = p.Repeat * p.Epoch * imageCount

	if v.Sample1 != "" {
		p.Sample1 = strings.ReplaceAll(v.Sample1, "[name]", triggerWord)
	}
	if v.Sample2 != "" {
		p.Sample2 = strings.ReplaceAll(v.Sample2, "[name]", triggerWord)
	}
	if v.Sample3 != "" {
		p.Sample3 = strings.ReplaceAll(v.Sample3, "[name]", triggerWord)
	}
	if v.SampleSteps > 0 {
		p.SampleSteps = v.SampleSteps
	}
	if err := p.ValidateParams(); err != nil {
		return err
	}
	return nil
}

// ValidateParams 校验 TrainJobParam 结构体中字段的范围和合法性
// 返回错误信息，如果所有校验通过则返回 nil
func (p *TrainJobParam) ValidateParams() error {
	// 训练参数校验
	if p.Repeat < 1 || p.Repeat > 50 {
		return fmt.Errorf("Repeat 范围必须在 1-50 之间，当前为 %d", p.Repeat)
	}
	if p.Epoch < 1 || p.Epoch > 20 {
		return fmt.Errorf("Epoch 范围必须在 1-20 之间，当前为 %d", p.Epoch)
	}
	if p.BatchSize != 1 { // 示例：仅支持为1
		return fmt.Errorf("BatchSize 当前仅支持为 1，当前为 %d", p.BatchSize)
	}
	if p.TotalSteps <= 0 {
		return fmt.Errorf("TotalSteps 当前为 %d", p.TotalSteps)
	}
	validMixedPrecisions := map[string]bool{"bf16": true, "fp16": true, "no": true} // 示例支持的精度
	if _, ok := validMixedPrecisions[p.MixedPrecision]; !ok {
		return fmt.Errorf("MixedPrecision 必须是 bf16, fp16 或 no，当前为 %s", p.MixedPrecision)
	}

	// 样图设置校验
	if p.Resolution != "1024*1024" && p.Resolution != "512*768" && p.Resolution != "768*512" && p.Resolution != "768*1024" && p.Resolution != "1024*768" {
		// 根据你的实际需求添加更多允许的分辨率
		return fmt.Errorf("Resolution 必须是指定格式，当前为 %s", p.Resolution)
	}
	validSamplers := map[string]bool{"Euler": true, "flowmatch": true} // 示例支持的采样方式
	if _, ok := validSamplers[p.Sampler]; !ok {
		return fmt.Errorf("Sampler 必须是 Euler 或 flowmatch，当前为 %s", p.Sampler)
	}

	// 保存设置校验
	if p.SaveEveryNEpochs < 1 || p.SaveEveryNEpochs > 5 {
		return fmt.Errorf("SaveEveryNEpochs 范围必须在 1-5 之间，当前为 %d", p.SaveEveryNEpochs)
	}
	validSavePrecisions := map[string]bool{"bf16": true, "fp16": true} // 示例支持的保存精度
	if _, ok := validSavePrecisions[p.SavePrecision]; !ok {
		return fmt.Errorf("SavePrecision 必须是 bf16 或 fp16，当前为 %s", p.SavePrecision)
	}

	// 学习率&优化器校验
	if p.LrWarmUp < 0 || p.LrWarmUp > 100 {
		return fmt.Errorf("LrWarmUp 范围必须在 0-100 之间，当前为 %d", p.LrWarmUp)
	}
	validLrSchedulers := map[string]bool{"cosine_with_restarts": true, "linear": true, "cosine": true} // 示例支持的调度器
	if _, ok := validLrSchedulers[p.LrScheduler]; !ok {
		return fmt.Errorf("LrScheduler 必须是指定类型，当前为 %s", p.LrScheduler)
	}
	validOptimizers := map[string]bool{"AdamW8bit": true, "AdamW": true} // 示例支持的优化器
	if _, ok := validOptimizers[p.Optimizer]; !ok {
		return fmt.Errorf("Optimizer 必须是指定类型，当前为 %s", p.Optimizer)
	}
	if p.LrSchedulerNumCycles < 1 || p.LrSchedulerNumCycles > 5 {
		return fmt.Errorf("LrSchedulerNumCycles 范围必须在 1-5 之间，当前为 %d", p.LrSchedulerNumCycles)
	}
	if p.MinSnrGamma < 0 || p.MinSnrGamma > 20 {
		return fmt.Errorf("MinSnrGamma 范围必须在 0-20 之间，当前为 %d", p.MinSnrGamma)
	}

	// 网络校验
	// DownLrWeight 和 UpLrWeight 可以通过解析字符串来校验其格式和长度
	// 例如：
	//weights := strings.Split(p.DownLrWeight, ",")
	//if len(weights) != 12 {
	//	return fmt.Errorf("DownLrWeight 必须包含12个权重，当前有 %d 个", len(weights))
	//}
	//for _, w := range weights {
	//	if _, err := fmt.ParseFloat(w, 32); err != nil {
	//		return fmt.Errorf("DownLrWeight 包含非数字值: %s", w)
	//	}
	//}
	// MidLrWeight 默认值1，且在结构体中是int，校验可以省略
	// UpLrWeight 校验与 DownLrWeight 类似

	if p.BlockLrZeroThreshold < 0 || p.BlockLrZeroThreshold > 1 {
		return fmt.Errorf("BlockLrZeroThreshold 范围必须在 0-1 之间，当前为 %f", p.BlockLrZeroThreshold)
	}

	if p.NetworkRankDim < 1 || p.NetworkRankDim > 128 {
		return fmt.Errorf("NetworkRankDim 范围必须在 1-128 之间，当前为 %d", p.NetworkRankDim)
	}
	if p.NetworkAlpha < 4 || p.NetworkAlpha > 128 { // 示例：基于常用值
		return fmt.Errorf("NetworkAlpha 范围必须在 4-128 之间，当前为 %d", p.NetworkAlpha)
	}

	// 打标设置校验
	if p.KeepNTokens < 1 || p.KeepNTokens > 255 {
		return fmt.Errorf("KeepNTokens 范围必须在 1-255 之间，当前为 %d", p.KeepNTokens)
	}
	validMaxTokenLengths := map[int]bool{75: true, 150: true, 225: true}
	if _, ok := validMaxTokenLengths[p.MaxTokenLength]; !ok {
		return fmt.Errorf("MaxTokenLength 必须是 75, 150 或 225，当前为 %d", p.MaxTokenLength)
	}

	// 噪声设置校验
	if p.NoiseOffset < 0 || p.NoiseOffset > 1 {
		return fmt.Errorf("NoiseOffset 范围必须在 0-1 之间，当前为 %f", p.NoiseOffset)
	}
	if p.MultiresNoiseIterations < 0 || p.MultiresNoiseIterations > 64 {
		return fmt.Errorf("MultiresNoiseIterations 范围必须在 0-64 之间，当前为 %d", p.MultiresNoiseIterations)
	}
	if p.MultiresNoiseDiscount < 0 || p.MultiresNoiseDiscount > 1 {
		return fmt.Errorf("MultiresNoiseDiscount 范围必须在 0-1 之间，当前为 %f", p.MultiresNoiseDiscount)
	}

	// 高级设置校验
	if p.Seed != -1 && p.Seed < 0 { // 如果允许-1作为特殊值，但其他负数不允许
		return fmt.Errorf("Seed 必须是 -1 或非负数，当前为 %d", p.Seed)
	}
	if p.ClipSkip != 1 && p.ClipSkip != 2 {
		return fmt.Errorf("ClipSkip 必须是 1 或 2，当前为 %d", p.ClipSkip)
	}

	return nil // 所有校验通过
}
