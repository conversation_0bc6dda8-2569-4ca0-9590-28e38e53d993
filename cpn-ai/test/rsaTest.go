package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"math/big"
)

func main12() {

	// 创建一个数组
	arr := []int{1, 2, 3, 4, 5}

	// 使用 Fisher-Yates 洗牌算法打乱数组
	for i := len(arr) - 1; i > 0; i-- {
		// 生成一个随机数，范围是 [0, i]
		j, err := rand.Int(rand.Reader, big.NewInt(int64(i+1)))
		if err != nil {
			fmt.Println("Error generating random number:", err)
			return
		}
		// 交换元素
		arr[i], arr[j.Int64()] = arr[j.Int64()], arr[i]
	}

	// 输出打乱后的数组
	fmt.Println(arr)

	// 生成RSA密钥对
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		fmt.Println("Error generating private key:", err)
		return
	}
	// 将私钥转换为字符串
	privateKeyPEM := string(pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(privateKey),
	}))

	// 解析私钥字符串
	block, _ := pem.Decode([]byte(privateKeyPEM))
	if block == nil {
		fmt.Println("Failed to parse PEM block containing the private key")
		return
	}
	privateKeyUse, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		fmt.Println("Error parsing private key:", err)
		return
	}

	publicKey := &privateKey.PublicKey

	publicKeyBytes, err := x509.MarshalPKIXPublicKey(publicKey)
	publicKeyPEM := string(pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyBytes,
	}))

	publicKeyPEM = `
-----BEGIN RSA PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAw/92JDPP2/IaQAhcYgco
6ur1fDm4u6U1NBFfeDa0kvZ4Ex89DRXu3FyrDhGLo/m/M5K7GTAfv6lcAzIKmcwk
WX6v0GIA6KkO13ote3U5ooJRZ53/vy/Cr0l6ms17ELsFdMTra9K7XbUwvMgrgLvm
zbFRJp5FqfxUK+y2cfSszBNtJApZ2aMv/ewjFI4uurTyfHe6fMNFNyHcKBLkTSTe
BYEyOcLDQea8sZRy0SJ1QQG1EMRWrNiEYDVXATMlMiyiuTa8p4Yxt0cL/xjyyaYo
vOgV3WZx16qVU/9vzDJoq6j1v1dbSf7OrXt8Z5bwqKWJnRBB0VnHowQb8PTEdO10
3QIDAQAB
-----END RSA PUBLIC KEY-----
`
	// 解析公钥字符串
	publicKeyBlock, _ := pem.Decode([]byte(publicKeyPEM))
	if publicKeyBlock == nil {
		fmt.Println("Failed to parse PEM block containing the public key")
		return
	}
	publicKeyInterface, err := x509.ParsePKIXPublicKey(publicKeyBlock.Bytes)
	if err != nil {
		fmt.Println("Error parsing public key:", err)
		return
	}
	publicKeyUse, ok := publicKeyInterface.(*rsa.PublicKey)
	if !ok {
		fmt.Println("Failed to cast public key to RSA public key")
		return
	}

	// 要签名的消息
	message := []byte("Hello, World!")

	// 计算消息的哈希值
	hashed := sha256.Sum256(message)

	// 使用私钥对消息进行数字签名
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKeyUse, crypto.SHA256, hashed[:])
	if err != nil {
		fmt.Println("Error signing message:", err)
		return
	}

	// 使用公钥验证签名
	err = rsa.VerifyPKCS1v15(publicKeyUse, crypto.SHA256, hashed[:], signature)
	if err != nil {
		fmt.Println("Verification failed:", err)
		return
	}

	fmt.Println("Signature verified successfully.")

	// 输出私钥和公钥的字符串形式
	fmt.Println("Private Key (PEM):")
	fmt.Println(privateKeyPEM)
	fmt.Println("\nPublic Key (PEM):")
	fmt.Println(publicKeyPEM)
}
