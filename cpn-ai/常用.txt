https://gitee.com/zcloudai/aigc-api/pulls

var ImageAuditStatusEnum = imageAuditStatusEnum_{
	Makeing:        0, //制作中
	Pushing:        1, //上传中
	PushSuccess:    2, //上传成功
	PushFail:       3, //上传失败
	AuditPass:      4, //审核通过
	Commiting:      5, //提交中
	CommintSuccess: 6, //提交成功
	CommintFail:    7, //提交失败
}


var InstanceStatusEnum = instanceStatusEnum_{
	Created:            0, //已创建
	BootInProgress:     1, //开机中
	Running:            2, //运行中
	ShutdownInProgress: 3, //关机中/直接销毁并停止计费
	ShutdownComplete:   4, //已关机/直接销毁并停止计费
	StartupFail:        5, //启动失败/只对Record表
	Hidden:             9, //隐藏，前端表现为删除
}



AllGpus
nvidia-smi --query-gpu=index,uuid,name,memory.total,utilization.gpu --format=csv,noheader

RunningGpus
nvidia-smi --query-compute-apps=gpu_uuid,pid,process_name --format=csv,noheader

docker ps -a --filter "label=startup_mark=eda14fc65d084a719f68f9d293f2ffae" --format '{{json .}}'

查看端口占用
lsof -i :14089
docker ps -a --filter "publish=15087" --format '{{json .}}'
docker ps -a --filter "id=f0d54838a4f2" --format '{{json .}}'