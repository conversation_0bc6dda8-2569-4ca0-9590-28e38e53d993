
1.新建项目文件夹(文件夹名称为项目名称)
2.用goland编辑器打开文件夹(跳出窗口强制转化)
3.设置包管理为国内镜像   Preferences->GoModules->勾选(Enable Go modules integration)->
    Environment（输入或右边按钮选择内容：GOPROXY=https://goproxy.io,direct）
4.设置Go Build  在编辑器右上角点击(Add Configuration)
   跳出的配置窗口左边点击 Add new，然后选择Go Build
   在窗口右边Run kind选择Directory
5.添加 main.go文件 修改包名为main
6.打开编辑器下面终端窗口 输入
    go mod init 项目名称(一般是项目根文件夹名)
    导入项目需要的go包

7.安装gin框架   go get -u github.com/gin-gonic/gin     -u是更新到最新版本
8.搭建目录框架



CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o cpn_sched.linux
chmod 777 ./cpn_sched.linux

在 /etc/systemd/system/ 目录下创建一个新的服务单元文件，例如 cpn_sched.service
sudo nano /etc/systemd/system/cpn_sched.service

[Unit]
Description=CPN Scheduler Service
After=network.target

[Service]
Type=simple
ExecStart=/var/www/file.chenyu.cn/api/cpn_sched.linux
WorkingDirectory=/var/www/file.chenyu.cn/api/
Restart=always
RestartSec=5s
StandardOutput=journal
StandardError=journal
User=root
Group=root

[Install]
WantedBy=multi-user.target


创建完服务单元文件后，重新加载 systemd 配置以使其生效：
sudo systemctl daemon-reload

启用服务，使其在系统启动时自动启动：
sudo systemctl enable cpn_sched.service

启动服务：
sudo systemctl start cpn_sched.service

检查服务的状态，确保其正在运行：
sudo systemctl status cpn_sched.service

如果服务未能启动，请查看日志以获取更多信息：
journalctl -u cpn_sched.service







涉及到扣费的地方

需要的接口
SchedService.RunDocker(instance.Uuid)
SchedService.RmDocker(instance.DockerId)


AllGpus
nvidia-smi --query-gpu=index,uuid,name,memory.total,utilization.gpu --format=csv,noheader

RunningGpus
nvidia-smi --query-compute-apps=gpu_uuid,pid,process_name --format=csv,noheader

192.168.200.170
192.168.200.171
192.168.200.173
192.168.200.91

hub api 接口
project_name private  public
repsitory_name  镜像名称
artifact  到版本了
reference  版本的hash256



算云版本更新:





需要修改
欠费后 磁盘存储继续扣费45天，时间到了还不续费进行磁盘清理
欠费后通知策略  0到5元的时候通知一次  欠费通知一次 欠费第5天通知一次  欠费第15天通知一次 欠费第30天通知一次 欠费第44天通知一次（不续费就要删除存储或者镜像了）
0到5元的时候通知一次
欠费通知一次
欠费50通知一次

仙宫云 云存储策略(免费25G) 连续3个月未登录或欠费50元以上，文件存储数据将被清空。
仙宫云 镜像存储(免费30G) 账户欠费7天后，7天内未被使用过的镜像会被清理。

AutoDL 文件存储(免费20G) 连续3个月未登录或欠费50元以上，文件存储数据将被清空。 文件存储在实例中的挂载目录为：/root/autodl-fs。
AutoDL 镜像存储(免费30G) 连续3个月未登录或欠费50元以上，镜像将被清空。

1.后台完成继续镜像保存功能
2.修复优惠卷查询是过期的Bug

10核  60G

12核  60G

2024.12.16
1.用户扣费最多扣款改成-5元
2.添加实例整点扣费失败提醒

2024.12.2
1.处理用户7343镜像定时保存生成的1000多条无效记录
  update T_PodImage set audit_status=3, status=9 where user_id=7343 and storage_mode=3;
  update T_PodImage set audit_status=3, status=9 where  storage_mode=3 and status<9
  处理用户镜像保存时循环Bug
  update T_PodImage set audit_status=3, status=9  where user_id=8068 and image_name='0169fb601cee4b07902b134b9f53a9d1'  and id>11771 order by id desc;

2024.11.25
1.修复后台系统登录以及XFrame-0ptions标头漏洞
2.镜像仓库清理
3.后台添加报警限制逻辑，可以设置忽略报警时长
4.初步设计的站点数据的统计功能
5.开发容器文件管理接口，已基本完成，下周Check代码,对接前端

202411.18
1.后台增加实例uuid模糊查询
2.修改镜像保存次数逻辑,超过120次禁止保存操作，如果客户来联系了，手动操作放开保存一次
3.修复Bug，没有启动命令，也没有镜像的Pod拼装，
4.部署hz03节点
5.kol申请审核时，修改逻辑，如果审核通过，直接置为审核中
6.后台增加批量重置Niginx映射
7.整理所有站点配置文档
8.批量更新Pod创作者名称
下周
研究Pod转发链接，http改成https

202411.11
1.修改后台实例列表，显示关机原因，定时关机时间
2.双11算力优惠短信群发
3.解决算云手机上微信不能支付问题
4.后台统计镜像扣费，和存储扣费情况
5.站点检测程序开发完成

下周主要工作内容
站点检测程序找机器部署
微信不能支付问题审核跟踪

202411.4
1.后台佣金记录查询增加一个按月份查询 ，输出字段多了一个金额的统计，需要显示出来
2.Kol提交镜像时做镜像大小提示
3.后台用户列表添加排序字段，可以根据用户个人存储进行排序
4.后台算力卡购买情况查询
4.修改Pod启动失败逻辑，记录启动失败原因，以及应用启动失败的docker
5.限制容器启动Gpu/Cpu参数，以及相应的管理后台
6.实时进程中添加镜像大小
7.13012299643 用户退卡，作废三张30元的卡
8.kol镜像费用默认改成0
9.修改算力卡扣费逻辑，可以用于存储扣费，注：定向算力卡不能用于存储扣费
10.Pod查询支持同时传入多个label
11.Label添加Bug处理
12.优化分片文件上传，后台可以看到上传任务，合并失败后用户可以重新发起合并
下周主要工作内容
数据库备份确认
站点检测程序开发

2024.8.29
算云更新了几个策略
1.用户欠费后镜像存储和云储存不再扣费，用户不能再进行镜像保存和文件上传
2.磁盘清理程序，半小时检查一次，磁盘空间小于100G的时候触发 按使用先后顺序进行删除，公共镜像21天没使用进行删除 个人镜像3天没使用进行删除


202310.28
1.设计开发个人镜像保存时镜像层数限制逻辑
2.后台添加手动移除容器功能
3.后台添加镜像仓库核对功能，检索出有问题的镜像
4.优化Instance表索引，速度提升明显
5.恢复丢失的镜像围棋引擎Katago
6.恢复丢失的镜像sadtalker中文汉化版
7.邀请佣金前后端磨合优化
8.修改Sk添加逻辑，改成Sk底层添加成功后再置为有效
9.优化容器启动时锁卡和解卡逻辑
下周主要工作内容
1.观察提现佣金的生成情况，配合佣金的审核发放
2.搭建多人开发环境

2023.11.25
本周工作
1.后台添加手动移除事件功能，这样可以避免重启来清除数据

2302310.21
本周工作
1.修改Sk添加逻辑，改成Sk底层添加成功后再置为有效
2.提现佣金前端功能核对
3.提现佣金后端功能核对

202310.14
本周工作
1.开发邀请佣金统计
2.开发提现账户验证
3.开放邀请佣金提现
4.设置免费空间为50G
5.新增容器信息接口
6.开发Docker仓库容量报警功能
7.完成课堂Pod权限
8.修改大模型Sk对接
9.开发镜像标签功能

2023.10.8
本周工作
1.上线新策略，实例关机时保存48小时，48小时后自动移除，后台支持马上移除，延长移除时间功能
2.后台添加强制移除实例映射功能
3.新增获取容器信息接口


2023.9.23
本周工作
1.AI学堂开发完成，后期进行优化
2.邀请码后端开发完成
4.优化Kol启动镜像时可以在后台指定特殊镜像
5.Kol镜像推送完成后，显示释放实例提示，避免下次重复推送内容
6.神笔小AI上新模型
下周主要内容
AI学堂优化

2024.9.18
本周工作
1.优化文件管理中下载功能，可以在浏览器里看到下载进度
2.新增功能 内部用户在KOl管理中启动镜像时可以指定任何镜像
3.新增后台功能，可以在后台编辑Pod的详细信息
4.Ai课堂后台接口已经基本开发完成
下周主要内容
AI课堂开发完成上线

2024.9.9
本周工作
1.优化kol镜像列表，显示镜像大小，以G单位显示
2.处理KolPod页面的搜索Bug
3.修复Pod编辑时静态资源文件夹不存在时弹出错误提示的Bug
4.重新提交晨羽AI模特软著申请资料
5.后台修复镜像取消功能
6.调整无卡启动逻辑，收费为0.3元每小时
7.修改镜像保存逻辑，公共镜像实例保存会新建一个镜像，个人镜像保存会覆盖当前个人镜像
8.优化Pod镜像搜索，可以搜索作者
8.实例监测系统后台开发完成
9.公告系统开发完成
10.Ai课堂数据表结构基本设计完成

下周主要内容
AI课堂开发


2024.9.4
算云更新
1.Pod页面上线Pod收藏功能
2.实例页面上线最近使用的Pod功能
3.修改功能实例关闭后实例记录直接隐藏
4.修复微信手机端充值，延迟显示的问题

本周工作（2024.9.2）：
1.固定显示晨羽云端OS
2.新增Pod收藏功能
3.新增Pod使用历史记录
4.应用关机后做自动删除处理
5.完成Docker磁盘存储自动检测，自动清理
6.Kol指定虚拟机，重新优化，可以在后台设置
7.后台实例列表添加取消镜像保存功能
8.后台镜像列表添加取消镜像保存功能
9.后台添加手动删除本地镜像功能
10.后台任务活动面板添加进度显示
11.后台本地镜像列表添加一键清理镜像功能
12.节点程序禁止500G以上版本镜像拉取
13.Kol启动页面新增KOl自己的镜像选择
14.优化本地镜像处理逻辑

下周主要内容
开始设计开发 AI学堂 预估2周时间


2024.6.21
1.AIPod添加标签检索功能
2.文件管理新增分片上传大文件夹功能


2024.6.13
1.新增无卡启动功能
2.新增保存镜像时中断功能

2024.6.7
1.Pod启动时显示百分比进度
2.镜像保存时显示百分比进度


2024.6.5
1.实名认证上线
2.实例页面增加版本和个人镜像标识


2024.5.30
1.镜像存储计算以及扣费
2.云存储计算以及扣费
3.密码设置功能，设置密码后可以用密码登录

2024.5.28
1.关机模式修改
2.卡机筛选时优先选择有镜像的机器


2024.5.
1.修复买了卡还增加同等金额算力的Bug
买卡又充值了的用户(不做处理了)
464
461
418
454
448
445
370


docker 常用命令
docker ps -a --format '{{json .}}' 显示全部
docker ps --format '{{json .}}'    只显示运行中的
查看端口占用
lsof -i :8080

容器ID获取容器信息
docker inspect c55e0f630d28
端口占用
sudo lsof -i :13087

端口 14087 被 Docker 的代理进程占用。
使用 docker ps --filter "publish=13087" 查找相关容器。
    docker ps -a --filter "publish=16088" --format '{{json .}}'
    docker ps -a --filter "label=startup_mark=8c6001632b434e18b150e10302a8d45c" --format '{{json .}}'

KOL技巧
bash
ps -ef  列出所有进程
ps -ef | grep nginx
kill -9 pid 停止进程
sudo rm -rf /tmp/*  删除tmp文件夹
rm -rf ~/.local/share/Trash/*  清空回收站

模型下载目录设置
mv /root/facefusion/.assets /poddata/     将默认模型下载目录移动到创作者目录下面
ln -s /poddata/.assets /root/facefusion/.assets   制作模型下载目录软链接，指向到创作者目录下面

apt update
apt install curl
pip install --upgrade pip -i https://pypi.mirrors.ustc.edu.cn/simple
pip install gradio -i https://pypi.mirrors.ustc.edu.cn/simple
pip install -U gradio -i https://pypi.mirrors.ustc.edu.cn/simple

pip install -r requirements.txt -i https://pypi.mirrors.ustc.edu.cn/simple

pip install transformers
apt install ffmpeg

https://github.com/FunAudioLLM/CosyVoice


虚拟机的几个状态
未上线：不主动做任何事情
已上线：正常运行
暂停中：新的实例不会自动发送到该机器上
删除：软删除该条记录
超时时间 大于0认为该虚拟机链接失败，不会把实例发送到该机器上



GPU服务器撤除的流程
1.后台将卡机设置成暂停状态，备注机器撤销，如果有实例联系相关人员关闭实例
2.将卡机IP发给廖云登记，由廖云发送给任嘉或阿斌
3.任嘉或者阿斌处理好机器后，通知后台，后台软删除该服务器

GPU服务器出现故障后的处理流程
1.先暂停该卡机，防止新的实例进来
2.检测故障原因，如果影响卡机上的运行实例，需要先处理运行的实例


3、GPU服务器出现故障后的处理流程

晨羽算力平台开关机功能说明
   开机：开机成功后开始计费（有算力扣费），可以看到上次关机时的镜像内容
无卡开机：开机成功后开始计费（无算力扣费，有使用费0.3元/小时），可以看到上次关机时的镜像内容
终止开机：在开机(包括无卡开机)的过程中，如果开机在拉取镜像或者等待应用启动的过程中，可以终止本次开机
   关机：关机后，运行状态会显示为【已关机】，这时将不再计费。接着后台会保存该实例的镜像，实例镜像在保存的过程中不会计费，但是只有在实例镜像保存成功后才能进行下次开机操作
保存镜像：只有在关机并且实例镜像保存成功的情况下才可以进行此操作，保存后可以在左边【POD镜像】列表中看到记录以及保存进度，50G免费，超过50G按(0.00036/GB/小时)收费
释放实例：目前只有在关机情况下可以进行该操作，释放实例后，该实例所有数据将被清除

可能受影响的镜像
select * from T_PodImage where id in(select distinct image_id from T_Instance where created_at>='2025-01-08 08:01:57.007' and created_at<='2025-01-08 18:01:57.007') and image_type=2 order by commit_start_time desc;
需要修改service.NodeService.FindContainer这个函数