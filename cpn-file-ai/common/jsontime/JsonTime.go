package jsontime

import (
	"database/sql/driver"
	"time"
)

const TimeFormat = "2006-01-02 15:04:05"

type JsonTime time.Time

func (t *JsonTime) UnmarshalJSON(data []byte) (err error) {
	if len(data) == 2 {
		*t = JsonTime(time.Time{})
		return
	}

	//now, err := time.Parse(`"`+TimeFormat+`"`, string(data))

	now, err := time.ParseInLocation(`"`+TimeFormat+`"`, string(data), time.Local)
	if err != nil {
		return err
	}

	*t = JsonTime(now)
	return
}

func (t JsonTime) MarshalJSON() ([]byte, error) {
	b := make([]byte, 0, len(TimeFormat)+2)
	b = append(b, '"')
	b = time.Time(t).AppendFormat(b, TimeFormat)
	b = append(b, '"')
	return b, nil
}

func (t JsonTime) Value() (driver.Value, error) {
	if t.String() == "0001-01-01 00:00:00" {
		return nil, nil
	}
	return []byte(time.Time(t).Format(TimeFormat)), nil
}

func (t *JsonTime) Scan(v interface{}) error {
	tTime, _ := time.Parse("2006-01-02 15:04:05 +0800 CST", v.(time.Time).String())
	*t = JsonTime(tTime)
	return nil
}

func (t JsonTime) String() string {
	return time.Time(t).Format(TimeFormat)
}

func (t JsonTime) Time() time.Time {
	return time.Time(t)
}

// Date 返回只包含日期部分的 JsonTime 对象
func (t JsonTime) Date() JsonTime {
	ti := t.Time()
	date := time.Date(
		ti.Year(),
		ti.Month(),
		ti.Day(),
		0, 0, 0, 0,
		ti.Location(),
	)
	return JsonTime(date)
}

// Date 返回只包含日期部分的 JsonTime 对象
func Today() JsonTime {
	ti := time.Now()
	date := time.Date(
		ti.Year(),
		ti.Month(),
		ti.Day(),
		0, 0, 0, 0,
		ti.Location(),
	)
	return JsonTime(date)
}

func DefaultTime() time.Time {

	// 获取 Asia/Shanghai 时区的 Location 对象
	loc, _ := time.LoadLocation("Asia/Shanghai")

	// 创建默认时间，使用 loc 时区
	return time.Date(1900, time.January, 1, 0, 0, 0, 0, loc)
}

func (t JsonTime) NotDefault() bool {
	return t.Time().After(DefaultTime())
}

func Now() JsonTime {
	// 创建默认时间，使用 loc 时区
	return JsonTime(time.Now())
}

func (t JsonTime) Unix() int64 {
	return time.Time(t).Unix()
}

func FromUnix(timestamp int64) JsonTime {
	t := time.Unix(timestamp, 0).In(time.Local)
	return JsonTime(t)
}
