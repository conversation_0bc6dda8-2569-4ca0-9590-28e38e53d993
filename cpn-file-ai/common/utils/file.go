package utils

import (
	"fmt"
	"path"
	"strings"
)

func IsValidPathOrName(paths []string) error {
	for _, pathItem := range paths {
		pathClean := path.Clean(pathItem)
		if pathClean == ".." || strings.Contains(pathClean, "../") {
			return fmt.Errorf("路径非法,path:%s", pathItem)
		}
	}
	return nil
}

func FormatFileSize(sizeInBytes int64) string {
	const (
		KB = 1024
		MB = 1024 * KB
		GB = 1024 * MB
	)

	switch {
	case sizeInBytes >= GB:
		return fmt.Sprintf("%.2f GB", float64(sizeInBytes)/float64(GB))
	case sizeInBytes >= MB:
		return fmt.Sprintf("%.2f MB", float64(sizeInBytes)/float64(MB))
	case sizeInBytes >= KB:
		return fmt.Sprintf("%.2f KB", float64(sizeInBytes)/float64(KB))
	default:
		return fmt.Sprintf("%d bytes", sizeInBytes)
	}
}
