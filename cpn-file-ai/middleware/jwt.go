package middleware

import (
	"cpn-file-ai/common/logger"
	"cpn-file-ai/common/redis-client"
	"cpn-file-ai/common/utils"
	"cpn-file-ai/config"
	"cpn-file-ai/enums"
	"errors"
	"github.com/dgrijalva/jwt-go"
	"strings"
	"time"
)

type Jwt struct {
	JwtKey []byte
}

func NewJWT() *Jwt {
	return &Jwt{
		[]byte(config.JwtKey),
	}
}

type MyClaims struct {
	UserId      uint   `json:"user_id"`
	Mobile      string `json:"mobile"`
	Username    string `json:"username"`
	DisplayName string `json:"display_name"`
	Role        int    `json:"role"`
	Status      int    `json:"status"`
	AccessToken string `json:"access_token"`
	OperatorId  uint   `json:"operator_id"` //后台管理员ID
	jwt.StandardClaims
}

type MyToken struct {
	UserId   uint `json:"user_id"`
	TokenId  uint `json:"token_id"`
	UserType int  `json:"user_type"`
	jwt.StandardClaims
}

// 定义错误
var (
	TokenExpired     error  = errors.New("Token已过期,请重新登录")
	TokenNotValidYet error  = errors.New("Token无效,请重新登录")
	TokenMalformed   error  = errors.New("Token不正确,请重新登录")
	TokenInvalid     error  = errors.New("这不是一个token,请重新登录")
	TokenIssuer      string = "Cpn"
)

func (j *Jwt) SetToken(userId uint, userName string, mobile string, displayName string, role int, accessToken string, operatorId uint) (string, error) {
	claims := MyClaims{
		UserId:      userId,
		Username:    userName,
		Mobile:      utils.FormatMobileStar(mobile),
		DisplayName: displayName,
		Role:        role,
		AccessToken: accessToken,
		StandardClaims: jwt.StandardClaims{
			NotBefore: time.Now().Unix() - 100,
			ExpiresAt: time.Now().Unix() + 31536000000,
			Issuer:    TokenIssuer,
		},
		OperatorId: operatorId,
	}
	token, err := j.CreateToken(claims)
	if err != nil {
		return "", err
	}
	return TokenIssuer + " " + token, nil
}

// CreateToken 生成token
func (j *Jwt) CreateToken(claims MyClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.JwtKey)
}

// ParserToken 解析token
func (j *Jwt) ParserToken(tokenString string) (*MyClaims, error) {
	ary := strings.Split(tokenString, " ")
	if len(ary) >= 2 {
		tokenString = ary[1]
	}
	token, err := jwt.ParseWithClaims(tokenString, &MyClaims{}, func(token *jwt.Token) (interface{}, error) {
		return j.JwtKey, nil
	})

	if err != nil {
		logger.Error(err, "   tokenString:===", tokenString, "===")
		if ve, ok := err.(*jwt.ValidationError); ok {
			if ve.Errors&jwt.ValidationErrorMalformed != 0 {
				return nil, TokenMalformed
			} else if ve.Errors&jwt.ValidationErrorExpired != 0 {
				// Token is expired
				return nil, TokenExpired
			} else if ve.Errors&jwt.ValidationErrorNotValidYet != 0 {
				return nil, TokenNotValidYet
			} else {
				return nil, TokenInvalid
			}
		}
	}

	if token != nil {
		if claims, ok := token.Claims.(*MyClaims); ok && token.Valid {
			return claims, nil
		}
		logger.Error(TokenInvalid, token)
		return nil, TokenInvalid
	}
	logger.Error(TokenInvalid, token)
	return nil, TokenInvalid
}

func SetAccessToken(accessToken string, claims MyClaims) error {
	redisKey := enums.RedisKeyEnum.AccessToken + accessToken
	return redis_client.RedisSet(redisKey, utils.GetJsonFromStruct(claims), time.Second*60*10)
}
func RemoveAccessToken(accessToken string) error {
	redisKey := enums.RedisKeyEnum.AccessToken + accessToken
	return redis_client.RedisDel(redisKey)
}
func GetAccessToken(accessToken string) (MyClaims, error) {
	var myClaims MyClaims
	redisKey := enums.RedisKeyEnum.AccessToken + accessToken
	if val, err := redis_client.RedisGet(redisKey); err != nil {
		logger.Error(err)
		return myClaims, err
	} else {
		if utils.GetStructAryFromJson(&myClaims, val); err != nil {
			return myClaims, err
		} else {
			return myClaims, nil
		}
	}
}
