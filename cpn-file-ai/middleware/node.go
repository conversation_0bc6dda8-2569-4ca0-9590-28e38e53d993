package middleware

import (
	"cpn-file-ai/common/logger"
	"github.com/dgrijalva/jwt-go"
)

const NodeJwtKey = "slslkeajsdpfejadjse"

type NodeClaims struct {
	NodeId      uint   `json:"node_id"`
	AccessToken string `json:"access_token"`
	jwt.StandardClaims
}

func createNodeToken(claims NodeClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	jwtKey := []byte(NodeJwtKey)
	return token.SignedString(jwtKey)
}

func CreateNodeToken(nodeId uint, accessToken string) string {
	claim := NodeClaims{
		NodeId:      nodeId,
		AccessToken: accessToken,
	}
	if token, err := createNodeToken(claim); err != nil {
		logger.Error(err)
		return ""
	} else {
		return token
	}
}
