package middleware

import (
	"context"
	"cpn-file-ai/common/constants"
	"cpn-file-ai/common/utils"
	"github.com/gin-gonic/gin"
)

func RequestId() func(c *gin.Context) {
	return func(c *gin.Context) {
		id := utils.GetTimeString() + utils.GetRandomString(8)
		c.Set(constants.RequestIdKey, id)
		ctx := context.WithValue(c.Request.Context(), constants.RequestIdKey, id)
		c.Request = c.Request.WithContext(ctx)
		c.<PERSON>(constants.RequestIdKey, id)
		c.Next()
	}
}
