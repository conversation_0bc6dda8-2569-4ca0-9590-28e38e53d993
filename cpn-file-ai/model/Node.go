package model

import (
	"gorm.io/gorm"
	"time"
)

type Node struct {
	gorm.Model
	Uuid          string    `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID"`
	Name          string    `json:"name" gorm:"type:varchar(50);not null;default:'';comment:节点名称(唯一)"`
	Title         string    `json:"title" gorm:"type:varchar(50);not null;default:'';comment:节点标题"`
	ApiBaseUrl    string    `json:"api_base_url" gorm:"type:varchar(200);not null;default:'';comment:api地址"`
	Domain        string    `json:"domain" gorm:"type:varchar(50);not null;default:'';comment:节点域名"`
	FreeGpus      int       `json:"free_gpus" gorm:"type:int;not null;default:0;comment:当前空余显卡数量"`
	TotalGpus     int       `json:"total_gpus" gorm:"type:int;not null;default:0;comment:总显卡数量"`
	TotalInstance int       `json:"total_instance" gorm:"type:int;not null;default:0;comment:实例数量"`
	TotalVirtual  int       `json:"total_virtual" gorm:"type:int;not null;default:0;comment:机器数量"`
	LastCheckTime time.Time `json:"last_check_time" gorm:"type:datetime;default:'1900-01-01';comment:最后上报时间"`
	AccessToken   string    `json:"access_token" gorm:"type:varchar(32);not null;default:'';comment:登录token"` // this token is for system management
	Remark        string    `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	Status        int       `json:"status" gorm:"type:int;not null;default:0;comment:状态"`
	IsGrayTest    int       `json:"is_gray_test" gorm:"type:int;not null;default:0;comment:是否是灰度测试节点"`
	Priority      int       `json:"priority" gorm:"type:int;not null;default:0;comment:优先级"`
}

//Price {"hour":1.66,"day":38.65,"week":245.43,"month":847.80}

func (Node) TableName() string {
	return "T_Node"
}

func (o *Node) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *Node) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Node) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}

func (o *Node) ListWithPriorityAndRandom(dest interface{}, id uint, status int, allowGrayTest int, page int, pageSize int) (int64, error) {
	// 按照优先级排序，同样优先级的随机排序
	var total int64
	tx := DB.Debug().Model(o)

	// 基础过滤条件
	if id > 0 {
		tx.Where("id=?", id)
	}

	if status >= 0 {
		tx.Where("status=?", status)
	}

	// 灰度节点过滤逻辑 - 无论是否指定nodeId都要应用此逻辑
	if allowGrayTest == 0 {
		tx.Where("is_gray_test = 0") // 只查询非灰度节点
	}
	// allowGrayTest == 1 时查询所有节点（不添加额外过滤条件）

	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}

	// 排序逻辑
	if allowGrayTest == 1 {
		tx.Order("is_gray_test desc, priority asc, rand()") // 灰度节点优先
	} else {
		tx.Order("priority asc, rand()") // 原有排序逻辑
	}

	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Node) List(dest interface{}, id uint, status int, page int, pageSize int, order string) (int64, error) {
	if order == "" {
		order = "id asc"
	}
	var total int64
	tx := DB.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	} else {
		if status >= 0 {
			tx.Where("status=?", status)
		}
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order(order).Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Node) SetAccessToken(token string) error {
	return DB.Model(o).Update("access_token", token).Error
}

func (o *Node) SetStatus(status int) error {
	return DB.Model(o).Update("status", status).Error
}

func (o *Node) Updates(m map[string]interface{}) error {
	return DB.Model(o).Debug().Updates(m).Error
}
