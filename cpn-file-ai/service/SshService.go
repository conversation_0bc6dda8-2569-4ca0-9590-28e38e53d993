package service

import (
	"context"
	"cpn-file-ai/common/logger"
	"cpn-file-ai/common/redis-client"
	"cpn-file-ai/common/utils"
	"cpn-file-ai/enums"
	"errors"
	"fmt"
	"golang.org/x/crypto/ssh"
	"regexp"
	"strconv"
	"strings"
	"time"
)

type SshClient struct {
	InitAt int64       `json:"init_at"`
	Client *ssh.Client `json:"-"`
}

func (o *SshClient) New(host string, sshUser string, sshPassword string) error {

	if !strings.Contains(host, ":") {
		host = host + ":22"
	}

	if sshUser == "" {
		sshUser = "root"
	}
	if sshPassword == "" {
		sshPassword = "Zeyun1234!@#$"
	}
	config := &ssh.ClientConfig{
		User: sshUser,
		Auth: []ssh.AuthMethod{
			ssh.Password(sshPassword),
			// 如果使用密钥认证，可以添加密钥认证的方式
			// ssh.PublicKeys(privateKey),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 不验证主机key（慎用）
	}

	// 建立SSH连接
	if client, err := ssh.Dial("tcp", host, config); err != nil {
		logger.Error(err)
		return err
	} else {
		o.InitAt = time.Now().Unix()
		o.Client = client
		return nil
	}
}

func (o *SshClient) Close() error {
	if o.Client == nil {
		return nil
	}
	return o.Client.Close()
}

func (o *SshClient) Exec(command string) (string, error) {
	// 创建一个新的会话
	if o.Client == nil {
		err := errors.New("sshclient is nil")
		logger.Error(err)
		return "", err
	}
	session, err := o.Client.NewSession()
	if err != nil {
		logger.Error(err, command) //read tcp *************:59422->***************:22: use of closed network connection
		return "", err
	}
	defer session.Close()

	// 设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	//ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	type result struct {
		output []byte
		err    error
	}
	resultChan := make(chan result, 1)

	go func() {
		output, err := session.CombinedOutput(command)
		resultChan <- result{output, err}
	}()

	select {
	case <-ctx.Done():
		session.Close()      // 超时则关闭session
		return "", ctx.Err() // 返回超时错误
	case res := <-resultChan:
		if res.err != nil {
			logger.Error(res.err, " command:", command)
			return "", res.err
		}
		outputStr := string(res.output)
		return outputStr, nil
	}
}

func GetDirSize(userId uint, dirPath string) (int64, error) {

	lockKey := enums.RedisKeyEnum.LockKey + "GetDirSize_" + utils.Uint2String(userId)
	if redis_client.RedisLock(lockKey, 1, 0) {
		defer redis_client.RedisUnLock(lockKey)

		var client SshClient
		//if err := client.New("*************", "root", "Zeyun1234%^&*"); err != nil {
		//	logger.Error(err)
		//	return -1, err
		//}
		if err := client.New("192.168.200.12", "root", "Zeyun1234%^&*"); err != nil {
			logger.Error(err)
			return -1, err
		}
		defer client.Close()

		logger.Info("开始统计文件大小", dirPath)
		commond := fmt.Sprintf("du -sb %s", dirPath) //du -sb /root/suanyun-user/0/a7aca236ca84
		if output, err := client.Exec(commond); err != nil {
			logger.Error(err, " output:", output)
			return -1, err
		} else { //17564270105     /root/suanyun-user/0/a7aca236ca84
			logger.Info(commond, "  ", output)
			// 正则表达式，匹配开头的数字序列
			re := regexp.MustCompile(`^\d+`)
			// 使用正则表达式查找匹配的部分
			matches := re.FindString(output)

			num, err := strconv.ParseInt(matches, 10, 64)
			if err != nil {
				logger.Error(err)
				return -1, err
			}
			logger.Info("文件大小统计完成", dirPath)
			return num, nil
		}
	} else {
		return -2, errors.New("正在统计")
	}

}
