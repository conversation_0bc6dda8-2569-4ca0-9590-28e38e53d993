package service

import (
	"bytes"
	"cpn-file-ai/common/logger"
	"cpn-file-ai/common/utils"
	"cpn-file-ai/config"
	"cpn-file-ai/middleware"
	"cpn-file-ai/model"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
	"unsafe"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

type GinH struct {
	Code   int                    `json:"code"`
	Msg    string                 `json:"msg"`
	Result map[string]interface{} `json:"result"`
}

func PostFileStorage(postUrl string, postData []byte, result interface{}, timeoutSec time.Duration) error {
	// 创建HTTP客户端（设置超时避免阻塞）
	if timeoutSec <= 0 {
		timeoutSec = 30
	}
	client := &http.Client{
		Timeout: timeoutSec * time.Second,
	}

	// 构造请求
	req, err := http.NewRequest("POST", config.FileStorageInterfaceUrl+postUrl, bytes.NewBuffer(postData))
	if err != nil {
		logger.Error("创建HTTP请求失败:", err)
		return err
	}

	// 设置Headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Connection", "keep-alive")
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("HTTP请求失败:", err)
		return err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		logger.Error("接口返回非200状态码:", resp.StatusCode, "响应内容:", string(body))
		return errors.New("接口错误: %s" + resp.Status)
	}
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error("读取响应内容失败:", err)
		return err
	}

	if err = json.Unmarshal(body, &result); err != nil {
		logger.Error("JSON解析失败:", err, "原始响应:", string(body))
		return errors.New("JSON解析失败:" + err.Error() + "原始响应:" + string(body))
	}
	return err
}

func PostNodeForGin(nodeId uint, postUrl string, postData map[string]interface{}) (gin.H, error) {
	postAuthorization := ""
	if val, ok := postData["Authorization"]; ok {
		postAuthorization = val.(string)
		delete(postData, "Authorization")
	}

	bytesData, _ := json.Marshal(postData)

	var node model.Node
	if nodeId > 0 {
		if err := node.GetById(nodeId); err != nil {
			logger.Error(err, "nodeId:", nodeId)
			return nil, err
		}
	}

	//postUrl = strings.Replace(postUrl, "api/", "api_online/", -1)
	if !strings.HasPrefix(postUrl, "http") {
		if nodeId == 16 && nodeId < 0 { //gz01.chenyu.cn
			apiBaseUrl := fmt.Sprintf("https://node%d.%s/", nodeId, node.Domain)
			postUrl = utils.UrlJoin(apiBaseUrl, postUrl)
		} else {
			postUrl = utils.UrlJoin(node.ApiBaseUrl, postUrl)
		}
	}
	req, err := http.NewRequest("POST", postUrl, bytes.NewBuffer(bytesData))
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, err
	}
	// 设置请求头
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	if node.ID > 0 {
		req.Header.Set("Authorization", middleware.CreateNodeToken(node.ID, node.AccessToken)) // 设置其他自定义的请求头
		//logger.Info("PostNode:", postUrl, " Authorization:", req.Header.Get("Authorization"))
	} else if postAuthorization != "" {
		req.Header.Set("Authorization", postAuthorization)
	}
	// 发送请求
	client := http.DefaultClient
	res, err := client.Do(req)
	if err != nil {
		logger.Error(err, postUrl, postData)
		if strings.Contains(err.Error(), "connection refused") {
			return nil, errors.New("connection refused")
		}
		return nil, err
	}
	defer res.Body.Close()

	content, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, err
	}
	str := *(*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	str = strings.TrimSpace(str)
	//logger.Info(postUrl, "  ", postData, " PostNode Result:||", "str", "||")
	//logger.Info(postUrl, "  ", postData)
	if str == "" {
		logger.Error("返回的内容为空", postUrl, postData, str)
		return nil, errors.New("返回的内容为空")
	}
	var ginH gin.H
	err = json.Unmarshal([]byte(str), &ginH)
	if err != nil {
		logger.Error(err, str)
		return nil, err
	}
	return ginH, nil
}

func ResultGinH(mm gin.H) (GinH, error) {
	var ginH GinH

	success := 0
	if _, ok := mm["code"]; ok {
		ginH.Code = int(mm["code"].(float64))
		success += 1
	}

	if _, ok := mm["msg"]; ok {
		ginH.Msg = mm["msg"].(string)
		success += 2
	}
	if success <= 2 {
		return ginH, errors.New("不是标准的Result格式")
	}

	if _, ok := mm["result"]; ok {
		ginH.Result = mm["result"].(map[string]interface{})
	}
	return ginH, nil
}

func OutOfAmount(userId uint) bool {
	msg := ""
	var user model.User
	if err := user.GetById(userId); err != nil {
		logger.Error(err)
		return true
	}
	if user.Amount.LessThanOrEqual(decimal.Zero) {
		var card model.Card
		if leaveAmount, err := card.CardValidAmount(userId, 0); err != nil {
			logger.Error("CardValidAmount   ", "err:", err, " leaveAmount:", leaveAmount.String(), " userId:", userId, "   podId:", 0)
			msg = "账户已欠费，请先充值再使用该功能"
			logger.Error(msg, " userId:", user.ID, "  amount:", user.Amount)
			return true
		} else {
			logger.Info("CardValidAmount   ", "leaveAmount:", leaveAmount.String(), " userId:", userId, "   podId:", 0)
			if leaveAmount.LessThanOrEqual(decimal.Zero) {
				msg = "账户已欠费，请先充值再使用该功能"
				logger.Error(msg, " userId:", user.ID, "  amount:", user.Amount)
				return true
			}
		}
	}
	return false
}
