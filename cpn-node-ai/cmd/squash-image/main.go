package main

import (
	"context"
	"flag"
	"fmt"
	"log"

	"cpn-ai/internal/squash"

	"github.com/docker/docker/client"
)

func main() {
	var s squash.Squash

	flag.Usage = func() {
		fmt.Fprintln(flag.CommandLine.Output(), "Usage: squash-image [flags] <image> [history-image]")
		flag.PrintDefaults()
	}

	flag.IntVar(&s<PERSON>, "l", 0, "squash to n layers, -n to reduce n layers")
	flag.StringVar(&s.Tag, "t", "", "squashed image tag")

	flag.Parse()
	switch flag.NArg() {
	case 2:
		s.ToHistory = flag.Arg(1)
		fallthrough
	case 1:
		s.FromImage = flag.Arg(0)
	default:
		flag.Usage()
		return
	}

	var err error
	s.Client, err = client.NewClientWithOpts(client.FromEnv)
	if err == nil {
		err = s.Do(context.Background())
	}
	if err != nil {
		log.Fatalln("squash failed:", err)
	}
}
