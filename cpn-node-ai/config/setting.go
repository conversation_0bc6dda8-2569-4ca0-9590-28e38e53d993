package config

import (
	"fmt"
	"gopkg.in/ini.v1"
)

var (
	Bundle       string
	CenterServer string
	AppMode      string
	Env          string
	RunTimer     bool
	ClearNginx   bool
	HttpPort     string
	JwtKey       string

	PrivateStorage string
	MasterServer   string
	NodeId         uint
	NodeToken      string

	DiffusionApi    string
	Txt2ImgFilePath string
	TempImgFilePath string

	StaticFilePath    string
	DiffusionFilePath string
	DiffusionDomain   string
	Domain            string

	TmpFilePath  string
	SaveFilePath string

	AccessKeyId     string
	AccessKeySecret string

	WeixinAppId  string
	WeixinSecret string

	SQL_DSN           string
	REDIS_CONN_STRING string
)

func init() {
	file, err := ini.Load("./config/config.ini")
	if err != nil {
		fmt.Println("配置文件读取错误", err)
	} else {
		LoadServer(file)
	}
}

func LoadServer(file *ini.File) {
	Bundle = file.Section("server").Key("Bundle").MustString("")
	CenterServer = file.Section("server").Key("CenterServer").MustString("")
	AppMode = file.Section("server").Key("AppMode").MustString("")
	Env = file.Section("server").Key("Env").MustString("")
	RunTimer = file.Section("server").Key("RunTimer").MustBool(false)
	ClearNginx = file.Section("server").Key("ClearNginx").MustBool(false)
	HttpPort = file.Section("server").Key("HttpPort").MustString("")
	JwtKey = file.Section("server").Key("JwtKey").MustString("")

	//PrivateStorage = file.Section("server").Key("PrivateStorage").MustString("")
	PrivateStorage = "/mnt/user-data/store0/"
	MasterServer = file.Section("server").Key("MasterServer").MustString("")
	NodeId = file.Section("server").Key("NodeId").MustUint(0)
	NodeToken = file.Section("server").Key("NodeToken").MustString("")

	AccessKeyId = file.Section("server").Key("AccessKeyId").MustString("")
	AccessKeySecret = file.Section("server").Key("AccessKeySecret").MustString("")

	StaticFilePath = file.Section("server").Key("StaticFilePath").MustString("")
	DiffusionFilePath = file.Section("server").Key("DiffusionFilePath").MustString("")
	DiffusionDomain = file.Section("server").Key("DiffusionDomain").MustString("")
	Domain = file.Section("server").Key("Domain").MustString("")

	SQL_DSN = file.Section("database").Key("SQL_DSN").MustString("")
	REDIS_CONN_STRING = file.Section("database").Key("REDIS_CONN_STRING").MustString("")
}
