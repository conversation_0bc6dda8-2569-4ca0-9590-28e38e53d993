package enums

import "reflect"

type envEnum_ struct {
	DEV, TEST, ONLINE, PRODUCTION string
}

func (c envEnum_) Get(id string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if typeVo.Field(i).Name == id {
			return vo.Field(i).Interface().(string)
		}
	}
	return ""
}

func (c envEnum_) GetKey(value string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if vo.Field(i).Interface().(string) == value {
			return typeVo.Field(i).Name
		}
	}
	return ""
}

var EnvEnum = envEnum_{
	DEV:        "dev",        //代码在开发人员编辑器运行,数据库是测试数据库
	TEST:       "test",       //代码部署到服务器上运行,数据库是测试数据库
	ONLINE:     "online",     //代码部署到服务器上运行,数据库是正式数据库（前端发版前最后测试用的环境）
	PRODUCTION: "production", //代码部署到服务器上运行,数据库是正式数据库（前端发版用的环境）
}
