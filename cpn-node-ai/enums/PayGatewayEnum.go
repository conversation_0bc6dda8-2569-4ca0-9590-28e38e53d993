package enums

import "reflect"

type payGatewayEnum_ struct {
	Wap, Web, App, Jsapi, Native, H5 string
}

// gateway 是字符串格式 "wap"手机网页  "page"电脑网页  "app"应用 "jsapi"小程序  "native"支付二维码  "h5"手机和电脑网页
var PayGatewayEnum = payGatewayEnum_{
	Wap:    "wap",
	Web:    "web",
	App:    "app",
	Jsapi:  "jsapi",
	Native: "native",
	H5:     "h5",
}

func (c payGatewayEnum_) Get(id string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if typeVo.Field(i).Name == id {
			return vo.Field(i).Interface().(string)
		}
	}
	return ""
}
func (c payGatewayEnum_) GetKey(value string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if vo.Field(i).Interface().(string) == value {
			return typeVo.Field(i).Name
		}
	}
	return ""
}
