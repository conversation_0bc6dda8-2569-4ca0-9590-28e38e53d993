package enums

import (
	"reflect"
)

type productCategoryEnum_ struct {
	Consumable, NonConsumable, Subscription string
}

var ProductCategoryEnum = productCategoryEnum_{
	Consumable:    "CON", //消耗型
	NonConsumable: "NON", //非消耗型
	Subscription:  "SUB", //订阅
}

var ProductCategoryNameEnum = productCategoryEnum_{
	Consumable:    "消耗型",  //消耗型
	NonConsumable: "非消耗型", //非消耗型
	Subscription:  "订阅",   //订阅
}

func (c productCategoryEnum_) GetKey(value string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if vo.Field(i).Interface().(string) == value {
			return typeVo.Field(i).Name
		}
	}
	return ""
}

func (c productCategoryEnum_) GetStructField(key string) string {
	rv := reflect.ValueOf(c)
	rt := reflect.TypeOf(c)
	if rt.Kind() != reflect.Struct {
		return ""
	}

	keyExist := false
	for i := 0; i < rt.NumField(); i++ {
		curField := rv.Field(i)
		if rt.Field(i).Name == key {
			switch curField.Kind() {
			case reflect.String, reflect.Int64, reflect.Int32, reflect.Int16, reflect.Int8, reflect.Int, reflect.Float64, reflect.Float32:
				keyExist = true
				return curField.String()
			default:
				return ""
			}
		}
	}
	if !keyExist {
		return ""
	}
	return ""
}
