package router

import (
	"cpn-ai/controller/node"
	"cpn-ai/middleware"
	"encoding/gob"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
)

func SetApiRouter(router *gin.Engine) {
	// 使用 Cookie 作为会话存储
	gob.Register(&middleware.MyClaims{})
	//store := cookie.NewStore([]byte("secret"))
	//router.Use(sessions.Sessions("mysession", store))

	router.Use(middleware.CORS())
	apiRouter := router.Group("/api")
	apiRouter.Use(gzip.Gzip(gzip.DefaultCompression))
	//apiRouter.Use(middleware.GlobalAPIRateLimit())
	{
		masterRoute := apiRouter.Group("/")
		masterRoute.Use(middleware.MasterAuth())

		masterRoute.POST("node/docker/startup", node.DockerApi.Startup)
		masterRoute.POST("node/docker/startup_abort", node.DockerApi.StartupAbort)
		masterRoute.POST("node/docker/shutdown", node.DockerApi.Shutdown)
		masterRoute.POST("node/docker/startuplog", node.DockerApi.StartupLog)
		masterRoute.POST("node/docker/status", node.DockerApi.Status)
		masterRoute.POST("node/docker/detail", node.DockerApi.Detail)
		masterRoute.POST("node/docker/apitest", node.DockerApi.ApiTest)
		masterRoute.POST("node/docker/spell_run_command", node.DockerApi.SpellRunCommand)
		masterRoute.POST("node/docker/stop", node.DockerApi.Stop)
		masterRoute.POST("node/docker/start", node.DockerApi.Start)
		masterRoute.POST("node/docker/restart", node.DockerApi.ReStart)
		masterRoute.POST("node/docker/logs", node.DockerApi.Logs)
		masterRoute.POST("node/docker/save_image", node.DockerApi.SaveImage)
		masterRoute.POST("node/docker/save_image_abort", node.DockerApi.SaveImageAbort)

		//masterRoute.POST("node/docker/save_image_and_shutdown", node.DockerApi.SaveImageAndShutdown)

		masterRoute.POST("node/docker/commit_image", node.DockerApi.CommitImage)
		masterRoute.POST("node/docker/pull_image", node.DockerApi.PullImage)

		masterRoute.POST("node/virtual/set", node.VirtualApi.Action)
		masterRoute.POST("node/virtual/action", node.VirtualApi.Action)
		masterRoute.POST("node/virtual/detail", node.VirtualApi.Detail)
		masterRoute.POST("node/virtual/set_status", node.VirtualApi.SetStatus)
		masterRoute.POST("node/virtual/remove_docker", node.VirtualApi.RemoveDocker)
		masterRoute.POST("node/virtual/local_images", node.VirtualApi.LocalImages)
		masterRoute.POST("node/virtual/remove_image", node.VirtualApi.RemoveImage)
		masterRoute.POST("node/virtual/prune_image", node.VirtualApi.PruneImage)
		masterRoute.POST("node/virtual/refresh_image", node.VirtualApi.RefreshLocalImage)

		masterRoute.POST("node/detail", node.NodeApi.Detail)
		masterRoute.POST("node/action", node.NodeApi.Action)
		masterRoute.POST("node/running_command", node.NodeApi.RunningCommand)
		masterRoute.POST("node/static", node.NodeApi.Static)

		masterRoute.POST("node/tasklog/list", node.TaskLogApi.List)
		masterRoute.POST("node/tasklog/last", node.TaskLogApi.Last)
		masterRoute.POST("node/tasklog/bootin", node.TaskLogApi.BootIn)

		masterRoute.POST("node/nginx/list_all", node.NginxApi.List)
		masterRoute.POST("node/nginx/remove_instance", node.NginxApi.RemoveByInstance)
		masterRoute.POST("node/nginx/list", node.NginxApi.List)
		masterRoute.POST("node/nginx/get", node.NginxApi.Get)

		masterRoute.GET("node/docker/set_nginx", node.DockerApi.SetNginx)
	}
}
