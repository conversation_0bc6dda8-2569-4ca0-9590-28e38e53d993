package service

import (
	"bufio"
	"context"
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/service/tasklog"
	"cpn-ai/structs"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"os"
	"path"
	"regexp"
	"slices"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/filters"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/api/types/registry"
	"github.com/docker/docker/client"
	"github.com/docker/docker/pkg/jsonmessage"
	"golang.org/x/crypto/ssh"
	"gorm.io/gorm"
)

type Virtual struct {
	ID            uint              `json:"id"`
	Region        int               `json:"region"`
	HostPort      string            `json:"host_port"`
	Host          string            `json:"host"`
	Port          int               `json:"port"`
	SshUser       string            `json:"ssh_user"`
	SshPassword   string            `json:"ssh_password"`
	SshClient     *ssh.Client       `json:"-"`
	DockerClient  *client.Client    `json:"-"`
	SshOpen       bool              `json:"ssh_open"`
	GpuFrees      int               `json:"gpu_frees"`
	Gpus          []GpuItem         `json:"gpus"` //[0,0,1,0,1,0]
	Dockers       []DockerItem      `json:"dockers"`
	ImageIds      string            `json:"image_ids"`
	PodIds        string            `json:"pod_ids"`
	ContainerCpus float64           `json:"container_cpus"`
	ContainerMem  string            `json:"container_mem"`
	InitAt        int64             `json:"init_at"`        //初始化时间
	LastInitTime  jsontime.JsonTime `json:"last_init_time"` //最后初始化成功时间
	GpuModelId    uint              `json:"gpu_model_id"`
	DockerAt      int64             `json:"docker_at"`  //启动docker时间戳,0为没有在启动
	TimeoutAt     int64             `json:"timeout_at"` //链接超时时间点
	Status        int               `json:"status"`
	mu            sync.RWMutex      // 读写锁
}

type DockerItem struct {
	ID             string              `json:"id"`
	InstanceUuid   string              `json:"instance_uuid"`
	StartupMark    string              `json:"startup_mark"`
	StartupParm    structs.StartupParm `json:"startup_parm"`
	Name           string              `json:"name"`
	PodId          uint                `json:"pod_id"`
	PodCategory    int                 `json:"pod_category"`
	ImageId        uint                `json:"image_id"`
	ImageType      int                 `json:"image_type"`
	PodName        string              `json:"pod_name"`
	VirtualId      uint                `json:"virtual_id"`
	VirtualHost    string              `json:"virtual_host"` //在哪台虚拟机上
	VirtualPort    int                 `json:"virtual_port"`
	SshPort        int                 `json:"ssh_port"` //ssh操作docker的宿主机端口
	Gpus           []int               `json:"gpus"`     //该docker启用的Gpu序列
	MapPref        string              `json:"map_pref"`
	MapPorts       []string            `json:"map_ports"`
	CreatedAt      time.Time           `json:"created_at"`
	Heartbeat      time.Time           `json:"heartbeat"`        //心跳包
	LastStateCheck jsontime.JsonTime   `json:"last_state_check"` //实例状态最后检测时间
	WebUrl         string              `json:"web_url"`
	ApiBase        string              `json:"api_base"`
	//Info         map[string]interface{} `json:"info"`
	State string `json:"state"`
}

type DockerImageItem struct {
	ID      string `json:"id"`
	Created int64  `json:"created"`
	//CreatedAt  time.Time `json:"created_at"`
	Repository   string  `json:"repository"`
	Tag          string  `json:"tag"`
	HubImagePath string  `json:"hub_image_path"`
	Size         string  `json:"size"`
	SizeG        float64 `json:"size_g"`
	SizeB        int64   `json:"size_b"`
	ImageId      uint    `json:"image_id"`
	LastUseUnix  int64   `json:"last_use_unix"`
}

type LockGpuItem struct {
	Index       int       `json:"index"`
	NeedGpus    int       `json:"need_gpus"`
	StartupMark string    `json:"startup_mark"`
	LockedAt    time.Time `json:"locked_at"`
}

type CacheShutdownItem struct {
	StartupMark string `json:"startup_mark"`
	ContainerId string `json:"container_id"`
	KeepSeconds int64  `json:"keep_seconds"`
	CacheAt     int64  `json:"cache_at"`
}

type GpuItem struct {
	Index        int       `json:"index"`
	Uuid         string    `json:"uuid"`
	MemoryG      int       `json:"memory_g"`
	MemoryM      int       `json:"memory_m"`
	Name         string    `json:"name"`
	Status       int       `json:"status"`
	InstanceUuid string    `json:"instance_uuid"`
	StartupMark  string    `json:"startup_mark"`
	LockedAt     time.Time `json:"locked_at"`
}

type GpuModelItem struct {
	ID      uint   `json:"id"`
	Uuid    string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID"`
	GpuName string `json:"gpu_name" gorm:"type:varchar(50);not null;default:'';comment:显卡名称"`
	MemoryG int    `json:"memory_g" gorm:"type:int;not null;default:0;comment:显存(单位G)"`
	MemoryM int    `json:"memory_m" gorm:"type:int;not null;default:0;comment:显存(单位M)"`
	Status  int    `json:"status" gorm:"type:int;not null;default:0;comment:状态"`
}

type PodItem struct {
	ID            uint              `json:"id"`
	Uuid          string            `json:"uuid"`
	Title         string            `json:"title"`
	PodName       string            `json:"pod_name"`
	Category      int               `json:"category"`
	PortMaps      string            `json:"port_maps"`
	PortMap       map[string]string `json:"port_map"`
	PodImage      PodImageItem      `json:"pod_image"`
	ImageName     string            `json:"image_name"`
	ImageTag      string            `json:"image_tag"`
	Command       string            `json:"command"`
	NeedGpus      int               `json:"need_gpus"`
	StartupElapse uint              `json:"startup_elapse"`
	DataFolder    string            `json:"data_folder"`
	VirtualIds    string            `json:"virtual_ids"`
	Status        int               `json:"status"`
}

type PodImageItem struct {
	ID          uint    `json:"id"`
	ParentId    uint    `json:"parent_id"`
	UserId      uint    `json:"user_id"`
	StorageMode int     `json:"storage_mode"`
	ImageType   int     `json:"image_type"`
	ImageName   string  `json:"image_name"`
	ImageTag    string  `json:"image_tag"`
	Size        float64 `json:"size"`
	Status      int     `json:"status"`
}

type SpaceItem struct {
	Filesystem string  `json:"filesystem"`
	Size       string  `json:"size"`
	Used       string  `json:"used"`
	Avail      string  `json:"avail"`
	AvailG     float64 `json:"avail_g"`
	Use        string  `json:"use"`
	MountedOn  string  `json:"mounted_on"`
}

// 安全获取 Virtual
func (o *Virtual) GetVirtual() Virtual {
	o.mu.RLock()
	defer o.mu.RUnlock()
	return *o
}

func (o *Virtual) GetDockers() []DockerItem {
	o.mu.RLock()
	defer o.mu.RUnlock()
	return o.Dockers
}

func (o *Virtual) SetTimeoutAt(at int64) {
	o.mu.Lock()
	defer o.mu.Unlock()
	o.TimeoutAt = at
}
func (o *Virtual) SetInitAt(at int64) {
	o.mu.Lock()
	defer o.mu.Unlock()
	o.InitAt = at
}

func (o *Virtual) SetImageIds(imageIds string) {
	o.mu.Lock()
	defer o.mu.Unlock()
	o.ImageIds = imageIds
}

func (o *Virtual) SetImageId(imageId uint) {
	o.mu.Lock()
	defer o.mu.Unlock()
	str := fmt.Sprintf("|%d|", imageId)
	if !strings.Contains(o.ImageIds, str) {
		if !strings.HasSuffix(o.ImageIds, "|") {
			o.ImageIds += "|"
		}
		o.ImageIds += fmt.Sprintf("%d|", imageId)
	}
}

func (o *Virtual) SetVirtualBaseInfo(virtual Virtual) {
	o.mu.Lock()
	defer o.mu.Unlock()
	o.HostPort = virtual.HostPort
	o.Host = virtual.Host
	o.Port = virtual.Port
	o.SshUser = virtual.SshUser
	o.SshPassword = virtual.SshPassword
	o.GpuModelId = virtual.GpuModelId
	o.PodIds = virtual.PodIds
	o.Status = virtual.Status
	o.ContainerCpus = virtual.ContainerCpus
	o.ContainerMem = virtual.ContainerMem
	if virtual.Status == 0 {
		o.InitAt = 0
	}
}

func (o *Virtual) SetVirtual(virtual Virtual) {
	o.mu.Lock()
	defer o.mu.Unlock()
	//logger.Info("SetVirtual:", utils.GetJsonFromStruct(virtual))
	o.TimeoutAt = virtual.TimeoutAt
	if virtual.TimeoutAt == 0 {
		o.LastInitTime = virtual.LastInitTime
		o.Dockers = virtual.Dockers
		o.Gpus = virtual.Gpus
		o.GpuFrees = virtual.GpuFrees
		if o.InitAt == 0 {
			o.InitAt = virtual.InitAt
			o.ImageIds = virtual.ImageIds
		}
	}
}

func (o *Virtual) SetDockers(dockerItems []DockerItem) {
	o.mu.Lock()
	defer o.mu.Unlock()
	o.TimeoutAt = 0
	o.Dockers = dockerItems
}

func (o *Virtual) SetDockerLastStateCheck(startupMark string) bool {
	o.mu.Lock()
	defer o.mu.Unlock()
	for i, docker := range o.Dockers {
		if docker.StartupMark == startupMark {
			//docker.LastStateCheck = jsontime.Now()
			o.Dockers[i].LastStateCheck = jsontime.Now()
			//o.Dockers[i].SshPort = 22
			//logger.Info("设置LastStateCheck i:", i, " val:", o.Dockers[i].LastStateCheck)
			return true
		}
	}
	return false
}

func (o *Virtual) RefreshFreeGpus() error {
	o.mu.RLock()
	defer o.mu.RUnlock()
	_, err := o.GpuItems()
	return err
}

func (o *Virtual) GetGpus() []GpuItem {
	o.mu.RLock()
	defer o.mu.RUnlock()
	return o.Gpus
}

func (o *Virtual) SetGpus(gpus []GpuItem) {
	o.mu.Lock()
	defer o.mu.Unlock()
	o.Gpus = gpus
}

func (o *Virtual) SshDial() (*ssh.Client, error) {
	// SSH连接配置
	if o.Port == 0 {
		o.Port = 22
	}
	if o.SshUser == "" {
		o.SshUser = "root"
	}
	if o.SshPassword == "" {
		o.SshPassword = "Zeyun1234!@#$"
	}
	config := &ssh.ClientConfig{
		Timeout: time.Second * 10,
		User:    o.SshUser,
		Auth: []ssh.AuthMethod{
			ssh.Password(o.SshPassword),
			// 如果使用密钥认证，可以添加密钥认证的方式
			// ssh.PublicKeys(privateKey),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 不验证主机key（慎用）
	}

	// 先测试 TCP 连接，确保端口可达，避免 ssh.Dial 卡死
	//if conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", o.Host, o.Port), 5*time.Second); err != nil {
	//	if o.TimeoutAt == 0 {
	//		o.TimeoutAt = time.Now().Unix()
	//	}
	//	logger.Error(o.Host, "TCP连接失败 err:", err, " TimeoutAt:", o.TimeoutAt)
	//	return nil, err
	//} else {
	//	conn.Close()
	//}

	// 建立SSH连接
	if client, err := ssh.Dial("tcp", fmt.Sprintf("%s:%d", o.Host, o.Port), config); err != nil {
		if o.TimeoutAt == 0 {
			o.TimeoutAt = time.Now().Unix()
		}
		logger.Error(o.Host, " err:", err, " TimeoutAt:", o.TimeoutAt)
		return nil, err
	} else {
		//if o.SshClient == nil || o.SshClient.Conn == nil {
		//	logger.Info("Ssh SshClient is nil")
		//} else {
		//	logger.Info("Ssh SshClient is not nil ", o.SshClient.Conn)
		//	logger.Info("o.SshClient ", utils.GetJsonFromStruct(o.SshClient.Conn))
		//}
		//logger.Info("ssh success:", o.Host, ":", o.Port)
		o.TimeoutAt = 0
		o.SshClient = client
		o.SshOpen = true
		//logger.Info("o.SshClient ", utils.GetJsonFromStruct(o.SshClient.Conn))
	}
	return o.SshClient, nil
}

func (o *Virtual) NewDockerClient() (*client.Client, error) {

	host := fmt.Sprintf("tcp://%s:2375", o.Host)
	// 创建 Docker API 客户端
	//if cli, err := client.NewClientWithOpts(client.WithHost(host), client.WithVersion("1.41"), client.WithTimeout(time.Minute*10)); err != nil {
	//	logger.Error(err, " host", host)
	//	return nil, err
	//} else {
	//	o.DockerClient = cli
	//	return cli, nil
	//}
	if cli, err := client.NewClientWithOpts(client.WithHost(host), client.WithVersion("1.41")); err != nil {
		logger.Error(err, "第一次链接 host", host)
		time.Sleep(time.Second)
		if cli, err := client.NewClientWithOpts(client.WithHost(host), client.WithVersion("1.41")); err != nil {
			logger.Error(err, "第二次链接 host", host)
			if o.TimeoutAt == 0 {
				o.TimeoutAt = time.Now().Unix()
			}
			return nil, err
		} else {
			o.DockerClient = cli
			return cli, nil
		}
	} else {
		o.DockerClient = cli
		return cli, nil
	}
}

func (o *Virtual) LoginHub() error {
	//{
	//
	//	command := `docker login hub.suanyun.cn -u admin --password 'Zeyun1234%^&*'`
	//	logger.Info(command)
	//	output, err := o.ExecCommand(command)
	//	if err != nil {
	//		logger.Error("Hub登录失败 ", o.Host, "  ", err, command)
	//		return err
	//	}
	//	if strings.Contains(output, "Login Succeeded") {
	//		return nil
	//	} else {
	//		logger.Error("Hub登录失败 ", o.Host)
	//	}
	//	return errors.New(output)
	//}
	{
		command := "docker login " + common.HubServerAddress + " -u admin --password 'Zeyun1234%^&*'"
		logger.Info(command)
		output, err := o.ExecCommand(command)
		if err != nil {
			logger.Error("Hub登录失败 ", o.Host, "  ", err, command)
			return err
		}
		if strings.Contains(output, "Login Succeeded") {
			return nil
		} else {
			logger.Error("Hub登录失败 ", o.Host)
		}
		return errors.New(output)
	}

}

func (o *Virtual) CloseSsh(from string) error {
	logger.Info("From:", from, "  CloseHost:", o.Host, "   SshOpen:", o.SshOpen)
	if o.SshOpen == false {
		logger.Info("Close不配对 ", "CloseHost:", o.Host, "   SshOpen:", o.SshOpen)
	}
	//if o.SshOpen {
	o.SshOpen = false
	if o.SshClient != nil && o.SshClient.Conn != nil {
		logger.Info(" host:", o.Host, "   o.SshClient.Conn.RemoteAddr:", o.SshClient.Conn.RemoteAddr().String())
		if err := o.SshClient.Close(); err != nil {
			logger.Error(err, " host:", o.Host, "   o.SshClient.Conn:", utils.GetJsonFromStruct(o.SshClient.Conn))
			//close tcp *************:44250->***************:22: use of closed network connection
		}
	}
	//}

	if o.DockerClient != nil {
		if err := o.DockerClient.Close(); err != nil {
			logger.Error(err, " host:", o.Host)
		}
	}
	return nil
}

func (o *Virtual) Init(save bool) error {
	logger.Info(o.Host, "(", o.ID, ")", "开始初始化：", time.Now())
	lockKey := enums.RedisKeyEnum.LockKey + "VirtualInit:" + fmt.Sprintf("%d", o.ID)
	if common.RedisLock(lockKey, 1000*10, 1000*30) {
		defer common.RedisUnLock(lockKey)
		result := make(map[string]interface{})
		if output, err := o.DockerItemsUseApi(); err != nil {
			logger.Error(o.Host, "  ", err)
			return err
		} else {
			result["docker_items"] = output
		}

		if output, err := o.GpuItems(); err != nil {
			logger.Error(o.Host, "  ", err)
			return err
		} else {
			result["gpu_items"] = output
		}

		reportLocalImages := false
		if o.Inited() == false {
			reportLocalImages = true
			save = true
			o.InitAt = time.Now().Unix()
			if err := o.LoginHub(); err != nil {
				logger.Error(err)
			}
		}
		o.LastInitTime = jsontime.Now()
		if save {
			if !o.Report() {
				logger.Error("virtual初始化保存失败", o.HostPort)
				return errors.New("保存失败")
			}
		}
		if reportLocalImages {
			queueItem := QueueItem{Action: enums.QueueActionEnum.ReportLocalImages, Data: o.ID}
			VirtualInitQueue.Enqueue(queueItem)
		}
		logger.Info(o.Host, "(", o.ID, ")", "初始化完成：", time.Now())
		return nil
	} else {
		logger.Error(o.Host, "(", o.ID, ")", "初始化等待超时：", time.Now())
		return errors.New("初始化等待超时")
	}
}

func (o *Virtual) RemoveDockerItem(dockerId string) error {
	logger.Info(o.Host, "(", o.ID, ")", "开始移除DockerId：", dockerId)
	lockKey := enums.RedisKeyEnum.LockKey + "VirtualInit:" + fmt.Sprintf("%d", o.ID)
	if common.RedisLock(lockKey, 1, 1000*10) {
		defer common.RedisUnLock(lockKey)
		aryDocker := make([]DockerItem, 0)
		for _, docker := range o.Dockers {
			if docker.ID == dockerId {
				continue
			}
			aryDocker = append(aryDocker, docker)
		}
		o.Dockers = aryDocker
		logger.Info(o.Host, "(", o.ID, ")", "移除DockerId完成：", dockerId)
		return nil
	} else {
		logger.Error(o.Host, "(", o.ID, ")", "移除DockerId超时：", dockerId)
		return errors.New("移除DockerId超时")
	}
}

func (o *Virtual) Inited() bool {
	return o.InitAt > 0
}

func (o *Virtual) ReportLocalImages(ctx context.Context) error {
	if images, err := o.LocalImages(ctx); err != nil {
		logger.Error(err, "获取本地镜像失败  virtualId: ", o.ID)
		return err
	} else {
		if ginMap, err := MasterService.ReportLocalImages(o.ID, images); err != nil {
			logger.Error("上报本地镜像失败 err:", err)
			return err
		} else {
			//logger.Info("上报本地镜像完成", utils.GetJsonFromStruct(ginMap))

			if ginH, err := Result(ginMap); err != nil {
				logger.Error("上报本地镜像解析返回结果出错 err:", err, " virtualId:", o.ID)
				return err
			} else if ginH.Code == 0 {
				if val, ok := ginH.Result["image_ids"]; ok {
					o.ImageIds = val.(string)
				}
				if val, ok := ginH.Result["search_count"]; ok {
					logger.Info("上报本地镜像完成 search_count:", val, " 字符数量：", len(o.ImageIds), " virtualId:", o.ID)
				} else {
					logger.Info("上报本地镜像完成 ", "字符数量：", len(o.ImageIds), " virtualId:", o.ID)
				}
				return nil
			} else {
				err := errors.New(ginH.Msg)
				logger.Error("上报本地镜像返回结果code!=0 err:", err, " virtualId:", o.ID)
				return err
			}
		}
	}
}

func (o *Virtual) Report() bool {
	pre := fmt.Sprintf("ReportVirtualInfo host:%s(%d)", o.Host, o.ID)
	if ginH, err := MasterService.ReportVirtualInfo(o.ID); err != nil {
		logger.Error(pre, "  err:", err)
		return false
	} else {
		if rGinH, err := ResultGinH(ginH); err != nil {
			logger.Error(pre, utils.GetJsonFromStruct(ginH), "  err:", err)
			return false
		} else {
			if rGinH.Code == 0 {
				return true
			} else {
				logger.Error(pre, utils.GetJsonFromStruct(ginH), "  err:", "code不为0")
				return false
			}
		}
	}
}

func (o *Virtual) ReportTimeoutAt() bool {

	if ginH, err := MasterService.ReportVirtualTimeout(o.ID, o.TimeoutAt); err != nil {
		logger.Error(err)
		return false
	} else {
		logger.Info("ReportTimeoutAt ginH:", utils.GetJsonFromStruct(ginH))
		return true
	}
}

func (o *Virtual) DockerItems() ([]DockerItem, error) {

	mOldDocker := make(map[string]DockerItem)
	for _, docker := range o.Dockers {
		mOldDocker[docker.StartupMark] = docker
	}

	aryDocker := make([]DockerItem, 0)
	aryCacheDocker := make([]DockerItem, 0)
	command := "docker ps -a --format '{{json .}}'"
	output, err := o.ExecCommand(command)
	//logger.Info("DockerItems output:", output)
	if err != nil {
		logger.Error(err)
		return aryDocker, err
	}
	aryLine := strings.Split(output, "\n")
	for _, line := range aryLine {
		if line == "" {
			continue
		}
		m := utils.GetMapFromJson(line)
		if m != nil {
			instanceUuid := ""
			startupMark := ""
			var startupParm structs.StartupParm
			podId := uint(0)
			podCategory := 0
			podName := ""
			imageId := uint(0)
			imageType := 0
			//virtualHostPort := ""
			state := ""
			if _, ok := m["State"]; ok {
				state = m["State"].(string)
			}

			gpus := make([]int, 0)
			mapPref := ""
			mapPorts := make([]string, 0)
			mLabel := o.getDockerLabels(m)
			if item, ok := mLabel["instance_uuid"]; ok {
				m["InstanceUuid"] = item
				instanceUuid = item.(string)
			}
			if item, ok := mLabel["startup_mark"]; ok {
				m["StartupMark"] = item
				startupMark = item.(string)
			}
			if item, ok := mLabel["startup_parm"]; ok {
				m["StartupParm"] = item
				startupParm = item.(structs.StartupParm)
			}
			if item, ok := mLabel["gpus"]; ok {
				m["Gpus"] = item
				gpus = item.([]int)
			}
			if item, ok := mLabel["map_pref"]; ok {
				m["MapPref"] = item
				mapPref = item.(string)
			}
			if item, ok := mLabel["map_ports"]; ok {
				m["MapPorts"] = item
				mapPorts = item.([]string)
			}
			if item, ok := mLabel["pod_id"]; ok {
				m["PodId"] = item
				podId = item.(uint)
			}
			if item, ok := mLabel["pod_category"]; ok {
				m["PodCategory"] = item
				podCategory = item.(int)
			}
			if item, ok := mLabel["pod_name"]; ok {
				m["PodName"] = item
				podName = item.(string)
			}
			if item, ok := mLabel["image_id"]; ok {
				m["ImageId"] = item
				imageId = item.(uint)
			}
			if item, ok := mLabel["image_type"]; ok {
				m["ImageType"] = item
				imageType = item.(int)
			}
			if item, ok := mLabel["host"]; ok {
				m["Host"] = item.(string)
			}
			if item, ok := mLabel["host_port"]; ok {
				m["HostPort"] = item
				//virtualHostPort = item.(string)
			}
			if item, ok := mLabel["ssh_port"]; ok {
				m["SshPort"] = item
			}

			createdAt := common.DefaultTime
			if _, ok := m["CreatedAt"]; ok {
				str := m["CreatedAt"].(string)
				layout := "2006-01-02 15:04:05 -0700 MST"

				loc, _ := time.LoadLocation("Asia/Shanghai")
				if t, err := time.Parse(layout, str); err != nil {
					logger.Error(err)
				} else {
					createdAt = t.In(loc)
				}
			}

			if podId > 0 {
				docker := DockerItem{
					ID:           m["ID"].(string),
					InstanceUuid: instanceUuid,
					StartupMark:  startupMark,
					StartupParm:  startupParm,
					PodId:        podId,
					PodCategory:  podCategory,
					PodName:      podName,
					ImageId:      imageId,
					ImageType:    imageType,
					VirtualId:    o.ID,
					VirtualHost:  o.Host,
					//VirtualPort:  utils.String2Int(aryTmp[1]),
					//SshPort:      dockerSshPort,
					Gpus:      gpus,
					MapPref:   mapPref,
					MapPorts:  mapPorts,
					CreatedAt: createdAt,
					State:     state,
				}

				if old, ok := mOldDocker[startupMark]; ok {
					//logger.Info("重新设置LastStateCheck startupMark:", startupMark, "  val:", old.LastStateCheck)
					docker.LastStateCheck = old.LastStateCheck
				} else {
					//logger.Info("重新设置LastStateCheck startupMark不存在：", startupMark)
				}

				//SSH端口 默认22  11022
				//API接口 默认33  11033
				//Web接口 默认88  11088
				//jupyter默认89  11089
				//webos默认87  11087

				//if docker.PodCategory == enums.PodCategoryEnum.PodInstance { //Web接口默认7860
				//	docker.WebUrl = fmt.Sprintf("http://%s:1%d088/", docker.VirtualHost, gpus[0])
				//}
				//if docker.PodCategory == enums.PodCategoryEnum.LLM { //API接口默认800
				//	docker.ApiBase = fmt.Sprintf("http://%s:1%d033/", docker.VirtualHost, gpus[0])
				//}
				for _, tmp := range mapPorts {

					if tmp == "88" {
						docker.WebUrl = GetMapUrl(docker, "88")
					} else if tmp == "33" {
						docker.ApiBase = GetMapUrl(docker, "33")
					}

				}

				if exists, err := o.ExistsCacheShutdown(startupMark); err != nil {
					logger.Error("o.ExistsCacheShutdown err:", err, " startupMark:", startupMark)
				} else {
					if exists {
						aryCacheDocker = append(aryCacheDocker, docker)
						continue
					}
				}
				aryDocker = append(aryDocker, docker)
			}
		}
	}
	//logger.Info("aryDocker:", utils.GetJsonFromStruct(aryDocker))
	o.Dockers = aryDocker
	//o.SetDockers(aryDocker)
	return aryDocker, nil
}

func (o *Virtual) DockerItemsUseApi() ([]DockerItem, error) {
	ctx := context.Background()
	mOldDocker := make(map[string]DockerItem)
	for _, docker := range o.Dockers {
		mOldDocker[docker.StartupMark] = docker
	}

	aryDocker := make([]DockerItem, 0)
	aryCacheDocker := make([]DockerItem, 0)

	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	if containers, err := o.DockerClient.ContainerList(timeoutCtx, container.ListOptions{
		Size:    false,
		All:     true,
		Since:   "",
		Filters: filters.NewArgs(
		//filters.Arg("label", "startup_mark="+startupMark),
		//filters.Arg("id", containerId),
		//filters.Arg("label", "label1"),
		//filters.Arg("label", "label2"),
		//filters.Arg("before", "container"),
		),
	}); err != nil {
		logger.Error(err, " virtual:", o.Host)
		return nil, err
	} else {
		//logger.Info("ContainerList:", len(containers))
		for _, container := range containers {
			mLabel := container.Labels
			docker := DockerItem{
				ID:          container.ID[:12],
				VirtualId:   o.ID,
				VirtualHost: o.Host,
				State:       container.State,
			}

			if val, ok := mLabel["instance_uuid"]; ok {
				docker.InstanceUuid = val
			}
			if val, ok := mLabel["startup_mark"]; ok {
				docker.StartupMark = val
			}
			if val, ok := mLabel["startup_parm"]; ok {
				if val != "" {
					var startupParm structs.StartupParm
					if err := utils.GetStructFromJson(&startupParm, val); err != nil {
						logger.Error(err, " val:", val)
					} else {
						docker.StartupParm = startupParm
					}
				}
			}
			if val, ok := mLabel["gpus"]; ok {
				gpus := make([]int, 0)
				if val != "" {
					ary1 := strings.Split(val, "_")
					for _, v := range ary1 {
						if v == "" {
							continue
						}
						gpus = append(gpus, utils.String2Int(v))
					}
				}
				docker.Gpus = gpus
			}
			if val, ok := mLabel["map_pref"]; ok {
				docker.MapPref = val
			}
			if val, ok := mLabel["map_ports"]; ok {
				mapPorts := make([]string, 0)
				if val != "" {
					ary1 := strings.Split(val, "_")
					for _, v := range ary1 {
						mapPorts = append(mapPorts, v)
					}
					docker.MapPorts = mapPorts
				}
			}
			if val, ok := mLabel["pod_id"]; ok {
				docker.PodId = utils.String2Uint(val)
			}
			if val, ok := mLabel["pod_category"]; ok {
				docker.PodCategory = utils.String2Int(val)
			}
			if _, ok := mLabel["pod_name"]; ok {

			}
			if val, ok := mLabel["image_id"]; ok {
				docker.ImageId = utils.String2Uint(val)
			}
			if val, ok := mLabel["image_type"]; ok {
				docker.ImageType = utils.String2Int(val)
			}
			//if val, ok := mLabel["host"]; ok {
			//	docker.VirtualHost = val
			//}
			//if val, ok := mLabel["host_port"]; ok {
			//	//docker.SshPort = utils.String2Int(val)
			//}
			//if val, ok := mLabel["ssh_port"]; ok {
			//	docker.SshPort = utils.String2Int(val)
			//}
			if container.Created > 0 {
				t := time.Unix(container.Created, 0)
				docker.CreatedAt = t.In(time.Local)
			}

			if docker.StartupMark != "" {
				if old, ok := mOldDocker[docker.StartupMark]; ok {
					//logger.Info("重新设置LastStateCheck startupMark:", startupMark, "  val:", old.LastStateCheck)
					docker.LastStateCheck = old.LastStateCheck
				}

				for _, tmp := range docker.MapPorts {
					if tmp == "88" {
						docker.WebUrl = GetMapUrl(docker, "88")
					} else if tmp == "33" {
						docker.ApiBase = GetMapUrl(docker, "33")
					}
				}

				if exists, err := o.ExistsCacheShutdown(docker.StartupMark); err != nil {
					logger.Error("o.ExistsCacheShutdown err:", err, " startupMark:", docker.StartupMark)
					return aryDocker, err
				} else {
					if exists {
						aryCacheDocker = append(aryCacheDocker, docker)
						continue
					}
					aryDocker = append(aryDocker, docker)
				}
			}
		}
	}
	//logger.Info("aryDocker:", utils.GetJsonFromStruct(aryDocker))
	if o.Inited() == false {

		if err := o.InitLockGpusByDocker(aryDocker); err != nil {
			logger.Error("InitLockGpusByDocker err:", err)
			return aryDocker, err
		}
	}
	o.Dockers = aryDocker
	return aryDocker, nil
}

func (o *Virtual) GpuItems() ([]GpuItem, error) {
	allGpus := o.Gpus
	if o.InitAt == 0 {
		if tmpGpus, err := o.AllGpus(); err != nil {
			logger.Error(o.Host, " ", err)
			return allGpus, err
		} else {
			allGpus = tmpGpus
		}
	}

	frees := 0
	if mm, err := o.GetLockGpus(); err != nil {
		logger.Error(err)
		return allGpus, err
	} else {
		for i := 0; i < len(allGpus); i++ {
			if _, ok := mm[allGpus[i].Index]; ok {
				allGpus[i].Status = enums.GpuStatusEnum.Used
			} else {
				allGpus[i].Status = enums.GpuStatusEnum.Free
				frees++
			}
		}
	}
	o.GpuFrees = frees
	o.Gpus = allGpus
	return allGpus, nil
}

func (o *Virtual) AllGpus() ([]GpuItem, error) {
	aryGpu := make([]GpuItem, 0)
	//command := "nvidia-smi -L"
	//command := `nvidia-smi --query-gpu=index,uuid,name --format=csv,noheader | awk 'BEGIN{print "["} {print "{"} {print "\"index\":\""$1"\","} {print "\"uuid\":\""$2"\","} {print "\"name\":\""$3"\"" } NR%1{print "},"} END{print "}"}' | sed '$ s/,$//' | sed -e '$a\]'`
	command := `nvidia-smi --query-gpu=index,uuid,name,memory.total,utilization.gpu --format=csv,noheader`
	output, err := o.ExecCommand(command)
	if err != nil {
		logger.Error(o.Host, " ", err)
		return aryGpu, err
	}
	//logger.Info("output:", output)

	aryLine := strings.Split(output, "\n")
	//logger.Info("aryLine len:", len(aryLine))
	for _, line := range aryLine {
		if line == "" {
			continue
		}
		ary := strings.Split(line, ", ")
		//logger.Info(ary)
		//println(idx, " ", ary[0], " ", ary[1], " ", ary[2], " ", ary[3])

		index := -1
		if num, err := strconv.Atoi(ary[0]); err != nil {
			logger.Error(err)
			return aryGpu, err
		} else {
			index = num
		}

		memory := 0
		if strings.Contains(ary[3], "MiB") {
			tmp := strings.Replace(ary[3], " MiB", "", -1)
			memory = utils.String2Int(tmp)
		}

		gpuItem := GpuItem{
			Index:   index,
			Uuid:    ary[1],
			Name:    ary[2],
			MemoryM: memory,
			MemoryG: memory / 1024,
			Status:  0, //0未检测 1使用中 2空闲中 4无效
		}

		aryGpu = append(aryGpu, gpuItem)
	}

	return aryGpu, nil
}

func (o *Virtual) RunningGpus() (map[string]interface{}, error) {
	mGpu := make(map[string]interface{})
	command := "nvidia-smi --query-compute-apps=gpu_uuid,pid,process_name --format=csv,noheader"
	output, err := o.ExecCommand(command)
	if err != nil {
		logger.Error(err)
		return mGpu, err
	}
	aryLine := strings.Split(output, "\n")
	//logger.Info("aryLine len:", len(aryLine))

	for _, line := range aryLine {
		if line == "" {
			continue
		}
		ary := strings.Split(line, ", ")
		uuid := ary[0]
		pid := utils.String2Int(ary[1])

		mGpu[uuid] = pid
	}

	return mGpu, nil
}

func (o *Virtual) SpellRunDockerCommand(pod PodItem, needGpus int, instanceUuid string, startupMark string, userPath string, parm structs.StartupParm) (string, error) {

	pre := fmt.Sprintf("RunDocker startMark:%s needGpus:%d userPath:%s ", startupMark, needGpus, userPath)
	freesGpus := make([]int, 0)
	gpuFristIndexStr := ""
	if needGpus == 0 {
		{
			msg := "该Pod不支持无卡启动"
			logger.Error(msg, " pod：", utils.GetJsonFromStruct(pod))
			return msg, errors.New(msg)
		}
		if lockGpuItemAry, err := o.GetLockGpuItem(startupMark); err != nil {
			msg := "未找到锁定的无卡序号"
			logger.Error(msg, err)
			return msg, err
		} else {

			if len(lockGpuItemAry) > 0 {
				gpuFristIndexStr = fmt.Sprintf("%d", lockGpuItemAry[0].Index)
			} else {
				msg := "无卡序号为空"
				logger.Error(msg, err)
				return msg, err
			}
		}
	} else {
		if lockGpuItemAry, err := o.GetLockGpuItem(startupMark); err != nil {
			msg := "未找到锁定的Gpu"
			logger.Error(pre, msg, err)
			return msg, err
		} else {
			for _, item := range lockGpuItemAry {
				freesGpus = append(freesGpus, item.Index)
			}
			if len(freesGpus) < needGpus {
				msg := "锁定的Gpu不足"
				logger.Error(pre, msg, err)
				return msg, err
			}
			if len(freesGpus) != needGpus {
				msg := "锁定的Gpu不一致"
				logger.Error(pre, msg, err)
				return msg, err
			}
			sort.Ints(freesGpus)
			gpuFristIndexStr = fmt.Sprintf("1%d", freesGpus[0])
		}

	}

	//freesGpus := make([]int, 0)
	//for _, gpu := range o.Gpus {
	//	if gpu.Status == enums.GpuStatusEnum.Locked && gpu.StartupMark == startupMark {
	//		freesGpus = append(freesGpus, gpu.Index)
	//	}
	//}
	//if len(freesGpus) != needGpus {
	//	msg := "锁定的Gpu数量不正确"
	//	err := errors.New(msg)
	//	logger.Error(err, needGpus, freesGpus, o.Gpus, startupMark, "  ", instanceUuid)
	//	return msg, err
	//}
	//sort.Ints(freesGpus)

	gpuIndexStr := utils.Int2String(freesGpus[0])
	sshPort := fmt.Sprintf("1%d022", freesGpus[0])

	gpus := ""
	devices := ""
	for _, val := range freesGpus {
		if gpus != "" {
			gpus += "_"
		}
		gpus += utils.Int2String(val)

		if devices != "" {
			devices += ","
		}
		devices += utils.Int2String(val)
	}

	usrDataMapStr := ""
	if userPath != "" {
		userBasePath := path.Join(config.PrivateStorage, userPath)
		if _, err := os.Stat(userBasePath); os.IsNotExist(err) {
			logger.Info("用户存储路径不存在 开始创建存储路径", userBasePath)
			if err := os.MkdirAll(userBasePath, os.ModePerm); err != nil {
				msg := "创建用户文件夹出错"
				logger.Error(msg, err)
			}
		}
		if info, err := os.Stat(userBasePath); err == nil && info.Mode().IsDir() {
			usrDataMapStr = " -v " + userBasePath + ":" + "/usrdata"
		}
	}

	if pod.DataFolder != "" {
		if usrDataMapStr != "" {
			podDataPath := path.Join(config.PrivateStorage, userPath, "container", pod.DataFolder)
			if _, err := os.Stat(podDataPath); os.IsNotExist(err) {
				logger.Info("Pod存储路径不存在 开始创建存储路径", podDataPath)
				if err := os.MkdirAll(podDataPath, os.ModePerm); err != nil {
					msg := "创建Pod文件夹出错"
					logger.Error(msg, err, podDataPath)
				}
			}
			if info, err := os.Stat(podDataPath); err == nil && info.Mode().IsDir() {
				usrDataMapStr = " -e DATA_FOLDER=" + pod.DataFolder + usrDataMapStr
			}
		}
	}

	//command := `docker run -p %s:%d -v /root/aigc-models/llms:/root/aigc-models/llms -e MODEL_NAMES=%s --gpus '"device=%d"' --label ssh_port=%s --label gpus=%s -d **************/suanyun/fastchat:v1.1`
	//command = fmt.Sprintf(command, hostPort, 22, pod, frees[0], hostPort, gpus)

	//command := `docker run -p {==={Host2DockerPort}===}:22 -v /root/aigc-models/llms:/root/aigc-models/llms -e MODEL_NAMES=Llama2-Chinese-7b-Chat --gpus '"device={==={Device}===}"' -d **************/suanyun/fastchat:v1.1`
	//docker run -p 1{==={GpuIndex}===}022:22 -p 1{==={GpuIndex}===}800:8000 -v /root/aigc-models/llms:/root/aigc-models/llms -e MODEL_NAMES=Llama2-Chinese-7b-Chat --gpus '"device={==={Device}===}"' {==={Labels}===} -d **************/suanyun/fastchat:v1.1
	command := pod.Command

	aryMapPort := GetMapPorts(command)
	aryMapPortStr := strings.Join(aryMapPort, "_")

	startupParm := utils.GetJsonFromStruct(parm)

	command = strings.Replace(command, "{==={GpuIndex}===}", gpuIndexStr, -1)
	command = strings.Replace(command, "{==={Device}===}", devices, -1)
	labels := fmt.Sprintf(" -e InstanceUuid=%s -v /mnt/chenyu-nvme:/mnt/chenyu-nvme:ro -v /mnt/chenyu-nvme:/chenyudata:ro --label 'instance_uuid=%s' --label 'startup_mark=%s' --label 'startup_parm=%s' --label 'host=%s' --label 'map_pref=%s'  --label 'map_ports=%s' --label 'host_port=%s' --label 'ssh_port=%s' --label 'gpus=%s' --label 'pod_id=%d' --label 'pod_category=%d' ", instanceUuid, instanceUuid, startupMark, startupParm, o.Host, gpuFristIndexStr, aryMapPortStr, o.HostPort, sshPort, gpus, pod.ID, pod.Category)

	if needGpus > 0 {
		labels = " --shm-size 16g" + labels
	}

	if usrDataMapStr != "" {
		labels = usrDataMapStr + labels
	}
	command = strings.Replace(command, "{==={Labels}===}", labels, -1)
	//command = strings.Replace(command, "**************/", "*************/", -1)
	command = strings.Replace(command, "hub.suanyun.cn", common.HubServerAddress, -1)
	logger.Info("RunDocker Command:   ", command)
	if strings.Contains(command, "{==={") || strings.Contains(command, "}===}") {
		err := errors.New("有未替换的参数")
		logger.Error(err)
		return "", err
	}
	return command, nil
}

func (o *Virtual) RunDocker(ctx context.Context, pod PodItem, needGpus int, instanceUuid string, startupMark string, userPath string, parm structs.StartupParm) (string, error) {

	command := ""
	if tmpCommand, err := o.SpellRunDockerCommand(pod, needGpus, instanceUuid, startupMark, userPath, parm); err != nil {
		return tmpCommand, err
	} else {
		command = tmpCommand
	}

	imagePath := GetImagePath(command)
	if imagePath != "" {
		hubImagePath := imagePath
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始检查镜像", pod.Title+" hubImagePath："+hubImagePath, nil)
		if err := o.PullImageUserApi(ctx, hubImagePath, pod.PodImage); err != nil {
			logger.Error(err, "镜像拉取失败", " fullImageName："+hubImagePath+" "+err.Error())
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像拉取失败", "hubImagePath："+hubImagePath+" "+err.Error(), nil)
			//return "", err
		} else {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像拉取完成", "hubImagePath："+hubImagePath, nil)
		}
	}

	//output, err := o.ExecCommandWithLog(startupMark, command)
	output, err := o.ExecCommandPro(ctx, command)
	if err != nil {
		logger.Error(command, err, output) //Process exited with status 125
		return output, err
	}
	if len(output) == 64 {
		//status, err := o.DockerStatus(output)
		//logger.Info(command, "dockerStatus:", status, err)
		return output, nil
	} else {
		msg := "未获取到DockerId"
		err := errors.New(msg)
		logger.Error(err)
		return "", err
	}
}

func (o *Virtual) SpellRunDockerProCommand11(instanceUuid string, startupMark string, pod PodItem, needGpus int, userDataPath string, parm structs.StartupParm) (string, error) {

	pre := fmt.Sprintf("RunDockerPro startMark:%s needGpus:%d userDataPath:%s ", startupMark, needGpus, userDataPath)
	var aryCommand []string
	aryCommand = append(aryCommand, "docker run")
	gpuFristIndexStr := ""
	gpus := ""
	if needGpus == 0 {
		if lockGpuItemAry, err := o.GetLockGpuItem(startupMark); err != nil {
			msg := "未找到锁定的无卡序号"
			logger.Error(msg, err)
			return msg, err
		} else {

			if len(lockGpuItemAry) > 0 {
				gpuFristIndexStr = fmt.Sprintf("%d", lockGpuItemAry[0].Index)
			} else {
				msg := "无卡序号为空"
				err = errors.New(msg)
				logger.Error(pre, err)
				return msg, err
			}
		}
	} else {

		freesGpus := make([]int, 0)
		if lockGpuItemAry, err := o.GetLockGpuItem(startupMark); err != nil {
			msg := "未找到锁定的Gpu"
			logger.Error(pre, msg, err)
			return msg, err
		} else {
			for _, item := range lockGpuItemAry {
				freesGpus = append(freesGpus, item.Index)
			}
			if len(freesGpus) < needGpus {
				msg := "锁定的Gpu不足"
				err = errors.New(msg)
				logger.Error(pre, err)
				return msg, err
			}
			if len(freesGpus) != needGpus {
				msg := "锁定的Gpu不一致"
				err = errors.New(msg)
				logger.Error(pre, err)
				return msg, err
			}
			sort.Ints(freesGpus)
		}

		gpuFristIndexStr = fmt.Sprintf("1%d", freesGpus[0])

		devices := ""
		for _, val := range freesGpus {
			if gpus != "" {
				gpus += "_"
			}
			gpus += utils.Int2String(val)

			if devices != "" {
				devices += ","
			}
			devices += utils.Int2String(val)
		}
		//gpu参数
		gpusStr := fmt.Sprintf(`--gpus '"device=%s"' --shm-size 16g`, devices)
		aryCommand = append(aryCommand, gpusStr)

	}

	{
		n := 1
		if needGpus == 8 {
			n = 0
		} else if slices.Contains([]int{1, 2, 4}, needGpus) {
			n = needGpus
		}
		if n > 0 {
			if n < 2 {
				aryCommand = append(aryCommand, fmt.Sprintf(`--cpus="%d"`, 30*n))
			}
			aryCommand = append(aryCommand, fmt.Sprintf(`--memory="%dg"`, 90*n))
		}
	}

	//端口映射
	aryMapPort := make([]string, 0)
	hasJup := false
	hasWebOs := false
	if pod.PortMaps != "" {
		ary := utils.GetMapAryFromJson(pod.PortMaps)
		if ary == nil || len(ary) == 0 {
			msg := "映射端口解析错误"
			logger.Error(msg, pod.PortMaps)
			return msg, errors.New(msg)
		}

		for i := 0; i < len(ary); i++ {
			if ary[i]["host_port"].(string) == "" {
				continue
			}
			if utils.String2Int(ary[i]["host_port"].(string)) == 0 {
				continue
			}
			if ary[i]["container_port"].(string) == "" {
				continue
			}
			if utils.String2Int(ary[i]["container_port"].(string)) == 0 {
				continue
			}
			if ary[i]["host_port"].(string) == "89" {
				hasJup = true
			}
			if ary[i]["host_port"].(string) == "87" {
				hasWebOs = true
			}
			tmp := fmt.Sprintf("-p %s0%s:%s", gpuFristIndexStr, ary[i]["host_port"].(string), ary[i]["container_port"])
			aryCommand = append(aryCommand, tmp)
			aryMapPort = append(aryMapPort, ary[i]["host_port"].(string))
		}
	}

	if parm.InstanceType == enums.InstanceTypeEnum.Kol && hasJup == false {
		tmp := fmt.Sprintf("-p %s0%s:%s", gpuFristIndexStr, "89", "8888")
		aryCommand = append(aryCommand, tmp)
		aryMapPort = append(aryMapPort, "89")
	}

	if parm.InstanceType == enums.InstanceTypeEnum.Kol && hasWebOs == false {
		tmp := fmt.Sprintf("-p %s0%s:%s", gpuFristIndexStr, "87", "7002")
		aryCommand = append(aryCommand, tmp)
		aryMapPort = append(aryMapPort, "87")
	}

	//logger.Info("SpellRunDockerProCommand Pod:", utils.GetJsonFromStruct(pod))

	podImage := pod.PodImage
	modelFolder := pod.PodName
	if parm.InstanceType == enums.InstanceTypeEnum.Kol {
		modelFolder = pod.Uuid
	} else if podImage.ID > 0 {
		modelFolder = pod.Uuid
	}

	//logger.Info("SpellRunDockerProCommand modelFolder:", modelFolder)

	//模型目录
	if modelFolder != "" {
		shareModePath := path.Join("/root/suanyun-share/", modelFolder)
		logger.Info("shareModePath:", shareModePath)

		if parm.InstanceType == enums.InstanceTypeEnum.Kol {
			if _, err := os.Stat(shareModePath); os.IsNotExist(err) {
				logger.Info("Pod模型目录不存在 开始创建：", shareModePath)
				if err := os.MkdirAll(shareModePath, os.ModePerm); err != nil {
					msg := "创建Pod模型目录失败"
					logger.Error(msg, "  ", shareModePath, err)
					return msg, err
				}
			}
			if info, err := os.Stat(shareModePath); err == nil && info.Mode().IsDir() {
				tmp := fmt.Sprintf("-v %s:/models", shareModePath)
				aryCommand = append(aryCommand, tmp)

				tmp1 := fmt.Sprintf("-v %s:/poddata", shareModePath)
				aryCommand = append(aryCommand, tmp1)
			} else {
				msg := "Pod模型目录不存在"
				logger.Error(msg, "  shareModePath：", shareModePath, "  err:", err, "    IsDir:", info.Mode().IsDir())
				return msg, err
			}
		} else {
			if info, err := os.Stat(shareModePath); err == nil && info.Mode().IsDir() {
				tmp := fmt.Sprintf("-v %s:/models:ro", shareModePath)
				aryCommand = append(aryCommand, tmp)

				tmp1 := fmt.Sprintf("-v %s:/poddata:ro", shareModePath)
				aryCommand = append(aryCommand, tmp1)
			} else {
				msg := "Pod模型路径不存在"
				logger.Error(msg, "  ", shareModePath, err)
			}
		}
	}

	aryCommand = append(aryCommand, "-v /mnt/chenyu-nvme:/mnt/chenyu-nvme:ro")
	aryCommand = append(aryCommand, "-v /mnt/chenyu-nvme:/chenyudata:ro")

	//用户数据目录
	if userDataPath != "" {
		userBasePath := path.Join(config.PrivateStorage, userDataPath)
		if _, err := os.Stat(userBasePath); os.IsNotExist(err) {
			logger.Info("用户存储路径不存在 开始创建存储路径", userBasePath)
			if err := os.MkdirAll(userBasePath, os.ModePerm); err != nil {
				msg := "创建用户文件夹出错"
				logger.Error(msg, err)
			}
		}
		if info, err := os.Stat(userBasePath); err == nil && info.Mode().IsDir() {
			tmp := fmt.Sprintf("-v %s:/usrdata", userBasePath)
			aryCommand = append(aryCommand, tmp)
		}
	} else {
		logger.Info(pre, "userDataPath 为空")
	}

	//App数据目录
	if userDataPath != "" && modelFolder != "" {
		hostPodPath := path.Join(config.PrivateStorage, userDataPath, "container", modelFolder)

		if _, err := os.Stat(hostPodPath); os.IsNotExist(err) {
			logger.Info("Pod存储路径不存在 开始创建存储路径", hostPodPath)
			if err := os.MkdirAll(hostPodPath, os.ModePerm); err != nil {
				msg := "创建Pod文件夹出错"
				logger.Error(msg, err, hostPodPath)
			}
		}
		if info, err := os.Stat(hostPodPath); err == nil && info.Mode().IsDir() {
			tmp := fmt.Sprintf("-v %s:/appdata", hostPodPath)
			aryCommand = append(aryCommand, tmp)
		}
	}

	aryMapPortStr := strings.Join(aryMapPort, "_")

	startupParm := utils.GetJsonFromStruct(parm)
	labelsStr := fmt.Sprintf("--label 'instance_uuid=%s' --label 'startup_mark=%s' --label 'startup_parm=%s' --label 'host=%s' --label 'map_pref=%s' --label 'map_ports=%s' --label 'gpus=%s' --label 'pod_id=%d' --label 'pod_category=%d' --label 'image_id=%d' --label 'image_type=%d'", instanceUuid, startupMark, startupParm, o.Host, gpuFristIndexStr, aryMapPortStr, gpus, pod.ID, pod.Category, podImage.ID, podImage.ImageType)
	aryCommand = append(aryCommand, labelsStr)

	aryCommand = append(aryCommand, fmt.Sprintf("-e InstanceUuid=%s", instanceUuid))

	if podImage.ID == 0 {
		imageStr := fmt.Sprintf("-d hub.suanyun.cn/chenyu/public/%s:%s", pod.PodName, pod.ImageTag)
		//logger.Info("podImage.ID == 0 imageStr:", imageStr, "  ", pod.PodName, "   ", pod.ImageTag)
		if pod.ImageName != "" {
			if strings.Contains(pod.ImageName, ":") {
				imageStr = fmt.Sprintf("-d %s", pod.ImageName)
			} else {
				imageStr = fmt.Sprintf("-d %s:%s", pod.ImageName, pod.ImageTag)
			}
		}
		aryCommand = append(aryCommand, imageStr)
	} else {
		if podImage.ImageType == enums.ImageTypeEnum.CCM && podImage.ImageTag == "" {
			logKey, _ := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, startupMark)
			ctx := context.WithValue(context.Background(), "logkey", logKey)
			if err := waitDockerfileImage(ctx, &podImage); err != nil {
				return "", err
			}
		}

		hubImagePath := GetHubImagePath(podImage.ImageType, podImage.ImageName, podImage.ImageTag)
		if hubImagePath == "" {
			err := errors.New("镜像路径为空")
			logger.Error(err)
			return "", err
		} else {
			imageStr := fmt.Sprintf("-d %s", hubImagePath)
			aryCommand = append(aryCommand, imageStr)
		}
	}

	command := strings.Join(aryCommand, " ")
	command = strings.Replace(command, "hub.suanyun.cn", common.HubServerAddress, -1)
	logger.Info("RunDocker Command:   ", command)
	return command, nil
}

func (o *Virtual) SpellRunDockerProCommand(ctx context.Context, instanceUuid string, startupMark string, pod PodItem, needGpus int, userDataPath string, parm structs.StartupParm) (string, error) {

	pre := fmt.Sprintf("RunDockerPro startMark:%s needGpus:%d userDataPath:%s ", startupMark, needGpus, userDataPath)
	var aryCommand []string
	aryCommand = append(aryCommand, "docker run")
	gpuFristIndexStr := ""
	gpus := ""
	if needGpus == 0 {
		if lockGpuItemAry, err := o.GetLockGpuItem(startupMark); err != nil {
			msg := "未找到锁定的无卡序号"
			logger.Error(msg, err)
			return msg, err
		} else {

			if len(lockGpuItemAry) > 0 {
				gpuFristIndexStr = fmt.Sprintf("%d", lockGpuItemAry[0].Index)
			} else {
				msg := "无卡序号为空"
				err = errors.New(msg)
				logger.Error(pre, err)
				return msg, err
			}
		}
	} else {

		freesGpus := make([]int, 0)
		if lockGpuItemAry, err := o.GetLockGpuItem(startupMark); err != nil {
			msg := "未找到锁定的Gpu"
			logger.Error(pre, msg, err)
			return msg, err
		} else {
			for _, item := range lockGpuItemAry {
				freesGpus = append(freesGpus, item.Index)
			}
			if len(freesGpus) < needGpus {
				msg := "锁定的Gpu不足"
				err = errors.New(msg)
				logger.Error(pre, err)
				return msg, err
			}
			if len(freesGpus) != needGpus {
				msg := "锁定的Gpu不一致"
				err = errors.New(msg)
				logger.Error(pre, err)
				return msg, err
			}
			sort.Ints(freesGpus)
		}

		gpuFristIndexStr = fmt.Sprintf("1%d", freesGpus[0])

		devices := ""
		for _, val := range freesGpus {
			if gpus != "" {
				gpus += "_"
			}
			gpus += utils.Int2String(val)

			if devices != "" {
				devices += ","
			}
			devices += utils.Int2String(val)
		}
		//gpu参数
		gpusStr := fmt.Sprintf(`--gpus '"device=%s"' --shm-size 16g`, devices)
		aryCommand = append(aryCommand, gpusStr)

	}

	{
		n := 1
		if needGpus == 8 {
			n = 0
		} else if slices.Contains([]int{1, 2, 4}, needGpus) {
			n = needGpus
		}
		if n > 0 {
			if n < 2 {
				aryCommand = append(aryCommand, fmt.Sprintf(`--cpus="%d"`, 30*n))
			}
			aryCommand = append(aryCommand, fmt.Sprintf(`--memory="%dg"`, 90*n))
		}
	}

	//端口映射
	aryMapPort := make([]string, 0)
	hasJup := false
	hasWebOs := false
	if pod.PortMaps != "" {
		ary := utils.GetMapAryFromJson(pod.PortMaps)
		if ary == nil || len(ary) == 0 {
			msg := "映射端口解析错误"
			logger.Error(msg, pod.PortMaps)
			return msg, errors.New(msg)
		}

		for i := 0; i < len(ary); i++ {
			if ary[i]["host_port"].(string) == "" {
				continue
			}
			if utils.String2Int(ary[i]["host_port"].(string)) == 0 {
				continue
			}
			if ary[i]["container_port"].(string) == "" {
				continue
			}
			if utils.String2Int(ary[i]["container_port"].(string)) == 0 {
				continue
			}
			if ary[i]["host_port"].(string) == "89" {
				hasJup = true
			}
			if ary[i]["host_port"].(string) == "87" {
				hasWebOs = true
			}
			tmp := fmt.Sprintf("-p %s0%s:%s", gpuFristIndexStr, ary[i]["host_port"].(string), ary[i]["container_port"])
			aryCommand = append(aryCommand, tmp)
			aryMapPort = append(aryMapPort, ary[i]["host_port"].(string))
		}
	}

	if parm.InstanceType == enums.InstanceTypeEnum.Kol && hasJup == false {
		tmp := fmt.Sprintf("-p %s0%s:%s", gpuFristIndexStr, "89", "8888")
		aryCommand = append(aryCommand, tmp)
		aryMapPort = append(aryMapPort, "89")
	}

	if parm.InstanceType == enums.InstanceTypeEnum.Kol && hasWebOs == false {
		tmp := fmt.Sprintf("-p %s0%s:%s", gpuFristIndexStr, "87", "7002")
		aryCommand = append(aryCommand, tmp)
		aryMapPort = append(aryMapPort, "87")
	}

	//logger.Info("SpellRunDockerProCommand Pod:", utils.GetJsonFromStruct(pod))

	podImage := pod.PodImage
	modelFolder := pod.PodName
	if parm.InstanceType == enums.InstanceTypeEnum.Kol {
		modelFolder = pod.Uuid
	} else if podImage.ID > 0 {
		modelFolder = pod.Uuid
	}

	//logger.Info("SpellRunDockerProCommand modelFolder:", modelFolder)

	//模型目录
	if modelFolder != "" {
		//shareModePath := path.Join("/root/suanyun-share/", modelFolder)
		shareModePath := path.Join("/mnt/pod-data", modelFolder)
		logger.Info("shareModePath:", shareModePath)

		if parm.InstanceType == enums.InstanceTypeEnum.Kol {
			if _, err := os.Stat(shareModePath); os.IsNotExist(err) {
				logger.Info("Pod模型目录不存在 开始创建：", shareModePath)
				if err := os.MkdirAll(shareModePath, os.ModePerm); err != nil {
					msg := "创建Pod模型目录失败"
					logger.Error(msg, "  ", shareModePath, err)
					return msg, err
				}
			}
			if info, err := os.Stat(shareModePath); err == nil && info.Mode().IsDir() {
				//tmp := fmt.Sprintf("-v %s:/models", shareModePath)
				//aryCommand = append(aryCommand, tmp)

				tmp1 := fmt.Sprintf("-v %s:/poddata", shareModePath)
				aryCommand = append(aryCommand, tmp1)
			} else {
				msg := "Pod模型目录不存在"
				logger.Error(msg, "  shareModePath：", shareModePath, "  err:", err, "    IsDir:", info.Mode().IsDir())
				return msg, err
			}
		} else {

			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "开始验证目录"+shareModePath, nil)
			if info, err := os.Stat(shareModePath); err == nil && info.Mode().IsDir() {
				//tmp := fmt.Sprintf("-v %s:/models:ro", shareModePath)
				//aryCommand = append(aryCommand, tmp)

				tmp1 := fmt.Sprintf("-v %s:/poddata:ro", shareModePath)
				aryCommand = append(aryCommand, tmp1)
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "完成验证目录"+shareModePath, nil)
			} else {
				msg := "Pod模型路径不存在"
				logger.Error(msg, "  ", shareModePath, err)
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "目录不存在"+shareModePath, nil)
			}
		}
	}

	aryCommand = append(aryCommand, "-v /mnt/chenyu-nvme:/mnt/chenyu-nvme:ro")
	aryCommand = append(aryCommand, "-v /mnt/chenyu-nvme:/chenyudata:ro")

	//目录映射
	//-v /mnt/chenyu-nvme:/mnt/chenyu-nvme:ro
	//-v /mnt/chenyu-nvme:/chenyudata:ro
	//-v /mnt/pod-data/c503abeeefb74af4ab4cb0e5948d4c56:/poddata:ro
	//-v /mnt/user-data/store0/0/4a5c08f09d37:/usrdata
	//-v /mnt/user-data/store0/0/4a5c08f09d37/container/c503abeeefb74af4ab4cb0e5948d4c56:/appdata

	//用户数据目录
	if userDataPath != "" {
		userBasePath := path.Join(config.PrivateStorage, userDataPath)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "开始验证目录"+userBasePath, nil)
		{
			tmp := fmt.Sprintf("-v %s:/usrdata", userBasePath)
			aryCommand = append(aryCommand, tmp)
		}
		//if _, err := os.Stat(userBasePath); os.IsNotExist(err) {
		//	logger.Info("用户存储路径不存在 开始创建存储路径", userBasePath)
		//	if err := os.MkdirAll(userBasePath, os.ModePerm); err != nil {
		//		msg := "创建用户文件夹出错"
		//		logger.Error(msg, err)
		//	}
		//}
		//if info, err := os.Stat(userBasePath); err == nil && info.Mode().IsDir() {
		//	tmp := fmt.Sprintf("-v %s:/usrdata", userBasePath)
		//	aryCommand = append(aryCommand, tmp)
		//}
		//tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "完成验证目录"+userBasePath, nil)

	} else {
		logger.Info(pre, "userDataPath 为空")
	}

	//App数据目录
	if userDataPath != "" && modelFolder != "" {
		hostPodPath := path.Join(config.PrivateStorage, userDataPath, "container", modelFolder)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "开始验证目录"+hostPodPath, nil)
		{
			tmp := fmt.Sprintf("-v %s:/appdata", hostPodPath)
			aryCommand = append(aryCommand, tmp)
		}
		//if _, err := os.Stat(hostPodPath); os.IsNotExist(err) {
		//	logger.Info("Pod存储路径不存在 开始创建存储路径", hostPodPath)
		//	if err := os.MkdirAll(hostPodPath, os.ModePerm); err != nil {
		//		msg := "创建Pod文件夹出错"
		//		logger.Error(msg, err, hostPodPath)
		//	}
		//}
		//if info, err := os.Stat(hostPodPath); err == nil && info.Mode().IsDir() {
		//	tmp := fmt.Sprintf("-v %s:/appdata", hostPodPath)
		//	aryCommand = append(aryCommand, tmp)
		//}
		//tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "完成验证目录"+hostPodPath, nil)
	}

	aryMapPortStr := strings.Join(aryMapPort, "_")

	startupParm := utils.GetJsonFromStruct(parm)
	labelsStr := fmt.Sprintf("--label 'instance_uuid=%s' --label 'startup_mark=%s' --label 'startup_parm=%s' --label 'host=%s' --label 'map_pref=%s' --label 'map_ports=%s' --label 'gpus=%s' --label 'pod_id=%d' --label 'pod_category=%d' --label 'image_id=%d' --label 'image_type=%d'", instanceUuid, startupMark, startupParm, o.Host, gpuFristIndexStr, aryMapPortStr, gpus, pod.ID, pod.Category, podImage.ID, podImage.ImageType)
	aryCommand = append(aryCommand, labelsStr)

	aryCommand = append(aryCommand, fmt.Sprintf("-e InstanceUuid=%s", instanceUuid))

	if podImage.ID == 0 {
		imageStr := fmt.Sprintf("-d hub.suanyun.cn/chenyu/public/%s:%s", pod.PodName, pod.ImageTag)
		//logger.Info("podImage.ID == 0 imageStr:", imageStr, "  ", pod.PodName, "   ", pod.ImageTag)
		if pod.ImageName != "" {
			if strings.Contains(pod.ImageName, ":") {
				imageStr = fmt.Sprintf("-d %s", pod.ImageName)
			} else {
				imageStr = fmt.Sprintf("-d %s:%s", pod.ImageName, pod.ImageTag)
			}
		}
		aryCommand = append(aryCommand, imageStr)
	} else {
		if podImage.ImageType == enums.ImageTypeEnum.CCM && podImage.ImageTag == "" {
			logKey, _ := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, startupMark)
			ctx := context.WithValue(context.Background(), "logkey", logKey)
			if err := waitDockerfileImage(ctx, &podImage); err != nil {
				return "", err
			}
		}

		hubImagePath := GetHubImagePath(podImage.ImageType, podImage.ImageName, podImage.ImageTag)
		if hubImagePath == "" {
			err := errors.New("镜像路径为空")
			logger.Error(err)
			return "", err
		} else {
			imageStr := fmt.Sprintf("-d %s", hubImagePath)
			aryCommand = append(aryCommand, imageStr)
		}
	}

	command := strings.Join(aryCommand, " ")
	command = strings.Replace(command, "hub.suanyun.cn", common.HubServerAddress, -1)
	logger.Info("RunDocker Command:   ", command)
	return command, nil
}

func (o *Virtual) RunDockerUserApi(ctx context.Context, instanceUuid string, startupMark string, pod PodItem, needGpus int, userDataPath string, parm structs.StartupParm) (string, error) {
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: "RunDockerUserApi", StartupMark: startupMark, StartTime: jsontime.Now()}
	//commandKey := utils.GetUUID()
	commandKey := "RunDockerUserApi_" + startupMark
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "开始拼装启动命令", nil)
	command := ""
	if tmpCommand, err := o.SpellRunDockerProCommand(ctx, instanceUuid, startupMark, pod, needGpus, userDataPath, parm); err != nil {
		logger.Error(err)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动失败", "启动命令拼装失败 err: "+err.Error(), nil)
		return err.Error(), err
	} else {
		command = tmpCommand
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "启动命令拼装完成 "+command, nil)
	}

	if err := waitDockerfileImage(ctx, &pod.PodImage); err != nil {
		return err.Error(), err
	}

	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "waitDockerfileImage逻辑结束 ", nil)

	podImage := pod.PodImage
	hubImagePath := GetHubImagePath(podImage.ImageType, podImage.ImageName, podImage.ImageTag)
	if hubImagePath == "" {
		msg := "镜像路径为空"
		logger.Error(msg, "podImage:", utils.GetJsonFromStruct(podImage))
	}
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始检查镜像", pod.Title+" hubImagePath："+hubImagePath, nil)

	if podImage.StorageMode == enums.ImageStorageModeEnum.PrivateDisk {
		if saveImagePath, err := GetPrivateImageSavePath(hubImagePath, userDataPath); err != nil {
			logger.Error(err)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "加载镜像失败", "hubImagePath："+hubImagePath+" userDataPath："+userDataPath+" "+err.Error(), nil)
		} else {
			if err := o.LoadImageUserApi(ctx, saveImagePath); err != nil {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "加载镜像失败", "saveImagePath："+saveImagePath+" userDataPath："+userDataPath+" "+err.Error(), nil)
			} else {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像载入完成", "saveImagePath："+saveImagePath, nil)
			}
		}
	} else {
		imageSizeG := podImage.Size / 1024 / 1024 / 1024
		if imageSizeG >= 500 {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "该镜像版本太大，请选择其他版本", "hubImagePath："+hubImagePath, nil)
			sendMsg := " PodImage:" + utils.GetJsonFromStruct(podImage)
			logKey := "PodImageSizeTooLager_" + utils.Uint2String(podImage.ID)
			EmailService.AddNeedSend(logKey, sendMsg+" 检查时间:"+jsontime.Now().String())
			err := errors.New("该镜像版本太大，请选择其他版本")
			return "该镜像版本太大，请选择其他版本", err
		}
		if err := o.PullImageUserApi(ctx, hubImagePath, podImage); err != nil {
			if strings.Contains(err.Error(), "context canceled") {
				msg := fmt.Sprintf("镜像拉取取消,hubImagePath：%s err:%s", hubImagePath, err.Error())
				logger.Error(err, msg)
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像拉取取消", msg, nil)
				return "镜像拉取取消", err
			} else {
				msg := fmt.Sprintf("镜像拉取失败,hubImagePath：%s err:%s", hubImagePath, err.Error())
				logger.Error(err, msg)
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像拉取失败", msg, nil)
			}
			//return "", err
		} else {
			if virtualP, ok := NodeService.GetVirtualP(o.ID); ok {
				virtualP.SetImageId(podImage.ID)
			}
			taskProgress := tasklog.TaskProgress{
				Status: "ClearProgress", //清除前面的进度
			}

			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像拉取完成", "hubImagePath："+hubImagePath, taskProgress)
		}
	}

	//command = strings.Replace(command, "c9840ca31f8043cdb215eacbab0dafe7", "d9840ca31f8043cdb215eacbab0dafe7", -1)
	logger.Info("RunDocker Command:   ", command)
	//ctx = context.WithValue(ctx, "startup_mark", startupMark)
	if output, err := o.ExecCommandPro(ctx, command); err != nil {
		logger.Error("执行启动命令出错 ", command, err, " output:", output) //Process exited with status 125
		return "执行启动命令出错", err
	} else {
		if len(output) == 64 {
			//status, err := o.DockerStatus(output)
			//logger.Info(command, "dockerStatus:", status, err)
			return output, nil
		} else {
			msg := "执行启动命令失败"
			logger.Error(command, msg, " output:", output)
			return msg, errors.New(msg)
		}
	}
}

func (o *Virtual) Space(ctx context.Context) ([]SpaceItem, error) {

	command := `df -h --output=source,size,used,avail,pcent,target -x tmpfs -x devtmpfs | tail -n +2 | awk '{printf "{\"filesystem\": \"%s\", \"size\": \"%s\", \"used\": \"%s\", \"avail\": \"%s\", \"use\": \"%s\", \"mounted_on\": \"%s\"}\n", $1, $2, $3, $4, $5, $6}'`
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: command, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)

	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()
	arySpace := make([]SpaceItem, 0)
	output, err := o.ExecCommand(command)
	if err != nil {
		logger.Error(commandLog, " err:", err)
		return arySpace, err
	}
	aryLine := strings.Split(output, "\n")
	//"/mnt/nvme" 为Docker磁盘
	for _, line := range aryLine {
		line = strings.TrimSpace(line)
		if !strings.HasPrefix(line, "{") {
			continue
		}
		var space SpaceItem
		if err := utils.GetStructAryFromJson(&space, line); err != nil {
			logger.Error(err, "  output:", line)
		} else {
			if strings.Contains(space.Avail, "T") {
				tmp := strings.Replace(space.Avail, "T", "", -1)
				if f, err := strconv.ParseFloat(tmp, 64); err != nil {
					logger.Error(err, " string:", tmp)
				} else {
					space.AvailG = f * 1024
				}
			} else if strings.Contains(space.Avail, "G") {
				tmp := strings.Replace(space.Avail, "G", "", -1)
				if f, err := strconv.ParseFloat(tmp, 64); err != nil {
					logger.Error(err, " string:", tmp)
				} else {
					space.AvailG = f
				}
			} else if strings.Contains(space.Avail, "M") {
				tmp := strings.Replace(space.Avail, "M", "", -1)
				if f, err := strconv.ParseFloat(tmp, 64); err != nil {
					logger.Error(err, " string:", tmp)
				} else {
					space.AvailG = f / 1024
				}
			}
			arySpace = append(arySpace, space)
		}
	}
	return arySpace, nil
}

func (o *Virtual) DockerSpace(ctx context.Context) (SpaceItem, error) {
	var space SpaceItem
	if spaces, err := o.Space(ctx); err != nil {
		logger.Error(err)
		return space, err
	} else {
		for _, val := range spaces {
			if val.MountedOn == "/mnt/nvme" {
				return val, nil
			}
		}
		for _, val := range spaces {
			if val.MountedOn == "/" {
				return val, nil
			}
		}
		logger.Error("spaces:", utils.GetJsonFromStruct(spaces))
		return space, gorm.ErrRecordNotFound
	}
}

func (o *Virtual) ClearBindPort(ctx context.Context, bindPorts []string) error {

	if len(bindPorts) == 0 {
		return nil
	}
	for i := 0; i < len(bindPorts); i++ {
		if len(bindPorts[i]) != 5 {
			err := errors.New("检测端口不正确")
			logger.Error(err, bindPorts)
			return err
		}
		bindPorts[i] = "-i:" + bindPorts[i]
	}
	iStr := strings.Join(bindPorts, " ")
	command := fmt.Sprintf("lsof %s", iStr) //lsof -i:10088 -i:10089
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: command, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)

	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	trace := commandLog
	listKey := enums.RedisKeyEnum.CpnSchedDockerRemoveLog + time.Now().Format("200601")
	if _, err := common.RedisRPush(listKey, commandLog); err != nil {
		logger.Error(err, "   ", commandLog)
	}

	tasklog.Save(ctx, "", "", "检测端口进程 command:"+command, nil)
	output, err := o.ExecCommand(command)
	if err != nil {
		if err.Error() == "Process exited with status 1" {
			tasklog.Save(ctx, "", "", "未检测到端口进程,直接返回  err:"+err.Error(), nil)
			return nil
		}
		tasklog.Save(ctx, "", "", "检测端口进程出错 command："+command+"  err:"+err.Error(), nil)
		logger.Error(trace, " err:", err)
		return err
	} else {
		tasklog.Save(ctx, "", "", "检测端口进程 output:"+output, nil)
	}
	if strings.Contains(output, "docker-pr") {
		re := regexp.MustCompile(`docker-pr\s+(\d+)`)
		matches := re.FindAllStringSubmatch(output, -1)
		killPorts := make([]string, 0)
		for _, match := range matches {
			if len(match) > 1 {
				//fmt.Println(match[1])
				killPorts = append(killPorts, match[1])
			}
		}
		tasklog.Save(ctx, "", "", "还存在端口进程 killPorts:"+utils.GetJsonFromStruct(killPorts), nil)
		if len(killPorts) > 0 {
			killPortsCommand := fmt.Sprintf("kill -9 %s", strings.Join(killPorts, " ")) //kill -9 pid1 pid2 pid3
			tasklog.Save(ctx, "", "", "结束端口进程 killPortsCommand:"+killPortsCommand, nil)
			if killOutput, err := o.ExecCommand(killPortsCommand); err != nil {
				logger.Error("killPortsCommand:", killPortsCommand, " killOutput:", killOutput, "err:", err)
				tasklog.Save(ctx, "", "", "执行删除命令失败 killPortsCommand:"+killPortsCommand+" err:"+err.Error(), nil)
				return err
			} else {
				logger.Info("killPortsCommand:", killPortsCommand, " killOutput:", killOutput)
				tasklog.Save(ctx, "", "", "执行删除命令完成 killPortsCommand:"+killPortsCommand+" killOutput:"+killOutput, nil)
			}
		}
	}
	return nil
}

func (o *Virtual) RmDocker(ctx context.Context, containerId string) error {
	if err := o.StopDockerUseApi(ctx, containerId); err != nil {
		logger.Error("命令移除Docker前停止Docker失败 containerId:", containerId, "  ", err)
	}

	command := fmt.Sprintf("docker rm -f %s", containerId)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)

	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: command, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)

	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	listKey := enums.RedisKeyEnum.CpnSchedDockerRemoveLog + time.Now().Format("200601")
	if _, err := common.RedisRPush(listKey, commandLog); err != nil {
		logger.Error(err, "   ", commandLog)
	}

	logger.Info(o.Host, " 开始执行容器移除操作 containerId:", containerId)
	output, err := o.ExecCommand(command)
	if err != nil {
		logger.Error(commandLog, " err:", err)
		return err
	} else {
		logger.Info(commandLog, " output:", output)
	}
	if strings.Contains(output, "No such container") { //Error response from daemon: No such container: lsdsd
		return nil
	}
	if strings.HasPrefix(output, containerId) {
		return nil
	}
	return errors.New(output)
}

func (o *Virtual) RmDockerUseApi(ctx context.Context, containerId string) error {
	if err := o.StopDockerUseApi(ctx, containerId); err != nil {
		//if err.Error() == "context deadline exceeded" {
		//	logger.Info("Api 停止docker超时，尝试ssh移除")
		//	if err := o.StopDocker(containerId); err != nil {
		//		logger.Error(err)
		//		return err
		//	}
		//}
		logger.Error("Api移除Docker前停止Docker失败 containerId:", containerId, "  ", err)
	}

	pre := fmt.Sprintf("RmDockerUseApi containerId:%s", containerId)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)

	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: pre, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	listKey := enums.RedisKeyEnum.CpnSchedDockerRemoveLog + time.Now().Format("200601")
	if _, err := common.RedisRPush(listKey, commandLog); err != nil {
		logger.Error(err, "   ", commandLog)
	}

	timeoutCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()
	if err := o.DockerClient.ContainerRemove(timeoutCtx, containerId, container.RemoveOptions{Force: true}); err != nil {
		logger.Error(pre, " ", err)
		return err
	} else {
		return nil
	}
}

func (o *Virtual) PortDocker(ctx context.Context, containerId string) ([]string, error) {

	command := fmt.Sprintf("docker port %s", containerId)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)

	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: command, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	trace := commandLog
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()
	tasklog.Save(ctx, "", "", "提取需要移除的端口:"+trace, nil)
	// 遍历匹配结果并提取端口号

	ports := make([]string, 0)
	output, err := o.ExecCommand(command)
	if err != nil {
		logger.Error(trace, " err:", err)
		tasklog.Save(ctx, "", "", "提取需要移除的端口 err:"+err.Error(), nil)
		return ports, err
	} else {
		tasklog.Save(ctx, "", "", "提取需要移除的端口 output:"+output, nil)
		logger.Info(trace, " output:", output)
	}

	if strings.Contains(output, "No such container") {
		tasklog.Save(ctx, "", "", "提取需要移除的端口 Docker不存在 output:"+output, nil)
		return ports, nil
	}

	// 正则表达式匹配主机端口号
	re := regexp.MustCompile(`:(\d+)\n`)

	// 查找所有匹配的端口号
	matches := re.FindAllStringSubmatch(output, -1)
	checkStr := ""
	for _, match := range matches {
		if strings.Contains(checkStr, match[1]) {
			continue
		}
		checkStr += " " + match[1]
		ports = append(ports, match[1])
	}
	tasklog.Save(ctx, "", "", "提取需要移除的端口 ports:"+utils.GetJsonFromStruct(ports), nil)
	return ports, nil

	//7860/tcp -> 0.0.0.0:10088
	//7860/tcp -> [::]:10088
	//8888/tcp -> 0.0.0.0:10089
	//8888/tcp -> [::]:10089

	//Error response from daemon: No such container: bcdb6ab9a9295
}

func (o *Virtual) PortDockerItem(ctx context.Context, containerId string) ([]string, error) {
	ports := make([]string, 0)
	mapPref := ""
	for _, docker := range o.Dockers {
		if docker.ID == containerId {
			for _, mp := range docker.MapPorts {
				mapPref = docker.MapPref
				tmp := fmt.Sprintf("%s0%s", docker.MapPref, mp)
				ports = append(ports, tmp)
			}
		}
	}

	for _, docker := range o.Dockers {
		if docker.MapPref == mapPref && docker.ID != containerId {
			err := errors.New("端口被其他Docker占用着")
			logger.Error(err, " mapPref:", mapPref, "  docker.ID:", docker.ID, "  virtualId:", docker.VirtualId, "  virtualHost:", docker.VirtualHost)
			return ports, err
		}
	}

	return ports, nil
}

func (o *Virtual) StopDocker(ctx context.Context, containerId string) error {

	command := fmt.Sprintf("docker stop %s", containerId)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)

	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: command, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	trace := commandLog
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	output, err := o.ExecCommand(command)
	if err != nil {
		logger.Error(trace, " err:", err)
		return err
	} else {
		logger.Info(trace, " output:", output)
	}
	//Error response from daemon: No such container: bcdb6ab9a9295
	if strings.Contains(output, "No such container") {
		logger.Error(trace, " 容器停止失败 DockerId不存在:", output)
		return errors.New("Docker不存在")
	}
	if strings.HasPrefix(output, containerId) {
		return nil
	}
	logger.Error(trace, " 容器停止失败 output:", output)
	return errors.New(output)

	//root@zeyun:~# docker stop 86707ce7eb8f
	//86707ce7eb8f
	//root@zeyun:~# docker start e4ccd9fbb8ae
	//e4ccd9fbb8ae
}

func (o *Virtual) RmStopDocker(ctx context.Context, startupMark string, dockerId string) error {
	trace := fmt.Sprintf("VirtualRmStopDocker %d %s %s", o.ID, startupMark, dockerId)
	tasklog.Save(ctx, "", "", trace, nil)
	if startupMark == "" {
		return errors.New("启动标记为空")
	}
	tmpMsg := ""
	unLockGpu := false
	var docker DockerItem
	if container, err := o.ContainerByStartupMarkUseApi(ctx, startupMark); err != nil {
		if err == common.ErrRecordNotFound {
			tmpMsg = "容器不存在，当做已经停止移除处理"
			unLockGpu = true
			tasklog.Save(ctx, "", "", tmpMsg+" err:"+err.Error(), nil)
		} else {
			tmpMsg = "移除停止容器时获取容器信息失败"
			tasklog.Save(ctx, "", "", tmpMsg+" err:"+err.Error(), nil)
			logger.Error(tmpMsg, " ", startupMark, " ", err)
			return err
		}
	} else {
		docker = Container2DockerItem(o.ID, o.Host, container)
		tasklog.Save(ctx, "", "", "Container2DockerItem完成 docker.MapPref："+docker.MapPref, nil)
		if err := o.StopDockerUseApi(ctx, container.ID); err != nil {
			logger.Error(trace, err, "   ")
			tasklog.Save(ctx, "", "", "停止容器失败 err:"+err.Error(), nil)
			return err
		} else {
			time.Sleep(time.Second)
			checkPort := GetCheckPort(docker)
			if checkPort != "" {
				time.Sleep(time.Second)
				if container1, err1 := o.ContainerByPublishUseApi(ctx, checkPort); err1 != nil {
					if err1 == common.ErrRecordNotFound {
						tmpMsg += " 通过端口" + checkPort + "检测 端口已释放"
						logger.Info(tmpMsg, startupMark)
						unLockGpu = true
					} else {
						tmpMsg += " 通过端口" + checkPort + "检测出错"
						logger.Error(tmpMsg, startupMark)
					}
					tasklog.Save(ctx, "", "", tmpMsg+" err:"+err1.Error(), nil)
				} else {
					if container1.State == "running" {
						tmpMsg += " 通过端口" + checkPort + "检测 还被占用中 State:" + container1.State
						logger.Error(tmpMsg, startupMark)
						tasklog.Save(ctx, "", "", tmpMsg, nil)
						return errors.New("容器端口" + checkPort + "未释放")
					} else {
						tmpMsg += " 通过端口" + checkPort + "检测 容器不在运行中 State:" + container1.State
						logger.Info(tmpMsg, startupMark)
						tasklog.Save(ctx, "", "", tmpMsg, nil)
						unLockGpu = true
					}
				}
			} else {
				tasklog.Save(ctx, "", "", "checkPort为空，认为停止成功", nil)
				unLockGpu = true
			}
		}
	}
	if unLockGpu {
		if err := o.UnLockGpus(startupMark, "RmStopDocker"); err != nil {
			logger.Error(err, " host:", docker.VirtualHost, "  startupMark:", docker.StartupMark, "   dockerId:", docker.ID)
			tasklog.Save(ctx, "", "", "UnLockGpus失败 err:"+err.Error(), nil)
		} else {
			tasklog.Save(ctx, "", "", "UnLockGpus完成", nil)
		}

		if docker.ID == "" {
			tasklog.Save(ctx, "", "", "容器未创建 CacheShutdown不做处理", nil)
		} else {
			if err := o.CacheShutdown(ctx, startupMark, docker.ID); err != nil {
				logger.Error("virtual.CacheShutdown err:", err, "  startupMark:", docker.StartupMark)
				tasklog.Save(ctx, "", "", "CacheShutdown失败 err:"+err.Error(), nil)
			} else {
				tasklog.Save(ctx, "", "", "CacheShutdown完成", nil)
			}
		}
		return nil
	} else {
		err := errors.New("未达到移除停止条件")
		tasklog.Save(ctx, "", "", err.Error(), nil)
		return err
	}

}

func (o *Virtual) StopDockerUseApi(ctx context.Context, containerId string) error {
	pre := fmt.Sprintf("StopDockerUseApi containerId:%s", containerId)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)

	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: pre, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	timeoutCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()
	if err := o.DockerClient.ContainerStop(timeoutCtx, containerId, container.StopOptions{}); err != nil {
		logger.Error(pre, " 停止容器失败 err:", err) //context deadline exceeded
		return err
	} else { //需要改造，获取容器状态确认是否真的停止
		time.Sleep(time.Second)
		if container, err := o.ContainerByIdUseApi(ctx, containerId); err != nil {
			if err == common.ErrRecordNotFound {
				logger.Info("停止容器时容器不存在")
				return err
			} else {
				logger.Error(pre, " 停止容器时获取信息失败 err:", err) //context deadline exceeded
				return err
			}
		} else {
			//"State":"running","Status":"Up 8 hours"
			//state:exited status:Exited (137) 1 second ago
			if container.State == "running" {
				//logger.Info(pre, " 容器还在运行中", " state:", container.State, " status:", container.Status)
				err = errors.New("容器还在运行中")
				logger.Error(err, " state:", container.State, " status:", container.Status)
				return err
			} else {
				logger.Info(pre, " 容器已停止", " state:", container.State, " status:", container.Status)
				return nil
			}
		}
	}
}

func (o *Virtual) ContainerInspectUseApi(ctx context.Context, containerId string) error {
	pre := fmt.Sprintf("ContainerInspectUseApi containerId:%s", containerId)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)

	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: pre, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	if containerJSON, err := o.DockerClient.ContainerInspect(timeoutCtx, containerId); err != nil {
		logger.Error(pre, " ", err) //context deadline exceeded
		return err
	} else {
		logger.Info("containerJSON:", utils.GetJsonFromStruct(containerJSON))
		return nil
	}
}

func (o *Virtual) ContainerListUseApi(ctx context.Context, containerId string, startupMark string) ([]types.Container, error) {
	pre := fmt.Sprintf("ContainerListUseApi containerId:%s", containerId, " startupMark:", startupMark)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: pre, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()
	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	if containers, err := o.DockerClient.ContainerList(timeoutCtx, container.ListOptions{
		Size:  true,
		All:   true,
		Since: "container",
		Filters: filters.NewArgs(
			filters.Arg("label", "startup_mark="+startupMark),
			//filters.Arg("id", containerId),
			//filters.Arg("label", "label1"),
			//filters.Arg("label", "label2"),
			//filters.Arg("before", "container"),
		),
	}); err != nil {
		logger.Error(err)
		return nil, err
	} else {
		//logger.Info("ContainerList:", utils.GetJsonFromStruct(containers))
		return containers, nil
	}
	//SizeRw = 10 MB: 这表示容器在其生命周期内的文件系统层（即从基础镜像开始）有 10 MB 的数据更改或添加。
	//SizeRootFs = 34 GB: 这表示容器的整个文件系统，包括基础镜像及其所有层的总大小为 34 GB。这个大小包含了容器创建时的基础镜像以及所有的增量和修改。
}

func (o *Virtual) ContainerByStartupMarkUseApi(ctx context.Context, startupMark string) (types.Container, error) {
	var item types.Container
	if containers, err := o.DockerClient.ContainerList(ctx, container.ListOptions{
		Size:  true,
		All:   true,
		Since: "",
		Filters: filters.NewArgs(
			filters.Arg("label", "startup_mark="+startupMark),
		),
	}); err != nil {
		logger.Error(err)
		return item, err
	} else {
		if len(containers) == 0 {
			return item, common.ErrRecordNotFound
		}
		if len(containers) == 1 {
			return containers[0], nil
		} else {
			return item, errors.New("找到多个容器")
		}
	}
	//SizeRw = 10 MB: 这表示容器在其生命周期内的文件系统层（即从基础镜像开始）有 10 MB 的数据更改或添加。
	//SizeRootFs = 34 GB: 这表示容器的整个文件系统，包括基础镜像及其所有层的总大小为 34 GB。这个大小包含了容器创建时的基础镜像以及所有的增量和修改。
}

func (o *Virtual) ContainerByIdUseApi(ctx context.Context, containerId string) (types.Container, error) {
	var item types.Container
	if containers, err := o.DockerClient.ContainerList(ctx, container.ListOptions{
		Size:  true,
		All:   true,
		Since: "",
		Filters: filters.NewArgs(
			//filters.Arg("label", "startup_mark="+startupMark),
			filters.Arg("id", containerId),
		),
	}); err != nil {
		logger.Error(err)
		return item, err
	} else {
		if len(containers) == 0 {
			return item, common.ErrRecordNotFound
		}
		if len(containers) == 1 {
			return containers[0], nil
		} else {
			return item, errors.New("找到多个容器")
		}
	}
	//SizeRw = 10 MB: 这表示容器在其生命周期内的文件系统层（即从基础镜像开始）有 10 MB 的数据更改或添加。
	//SizeRootFs = 34 GB: 这表示容器的整个文件系统，包括基础镜像及其所有层的总大小为 34 GB。这个大小包含了容器创建时的基础镜像以及所有的增量和修改。
}

func (o *Virtual) ContainerByPublishUseApi(ctx context.Context, publishPort string) (types.Container, error) {
	var item types.Container
	if containers, err := o.DockerClient.ContainerList(ctx, container.ListOptions{
		Size:  true,
		All:   false,
		Since: "",
		Filters: filters.NewArgs(
			//filters.Arg("label", "startup_mark="+startupMark),
			filters.Arg("publish", publishPort),
		),
	}); err != nil {
		logger.Error(err)
		return item, err
	} else {
		if len(containers) == 0 {
			return item, common.ErrRecordNotFound
		}
		if len(containers) == 1 {
			return containers[0], nil
		} else {
			return item, errors.New("找到多个容器")
		}
	}
	//SizeRw = 10 MB: 这表示容器在其生命周期内的文件系统层（即从基础镜像开始）有 10 MB 的数据更改或添加。
	//SizeRootFs = 34 GB: 这表示容器的整个文件系统，包括基础镜像及其所有层的总大小为 34 GB。这个大小包含了容器创建时的基础镜像以及所有的增量和修改。
}

func (o *Virtual) ExportContainer(ctx context.Context, containerId string, path string) (bool, string, error) {
	command := fmt.Sprintf("docker export %s -o %s", containerId, path)
	logger.Info(command)
	output, err := o.ExecCommand(command)
	if err != nil {
		logger.Error(err, containerId)
		return false, "执行开始命令出错", err
	}
	if strings.Contains(output, "No such container") { //Error response from daemon: No such container: lsdsd
		logger.Error("Docker不存在", containerId)
		return false, "Docker不存在", errors.New("Docker不存在")
	}
	return strings.HasPrefix(output, containerId), "", nil
}

func (o *Virtual) ImportContainer(ctx context.Context, path string, imagePath string) (string, error) {
	command := fmt.Sprintf("docker import %s %s", path, imagePath)
	logger.Info(command)
	output, err := o.ExecCommand(command)
	if err != nil {
		logger.Error(err, path, "  ", imagePath)
		return output, err
	}
	return output, nil
}

func (o *Virtual) StartDocker(containerId string) (bool, string, error) {
	command := fmt.Sprintf("docker start %s", containerId)
	logger.Info(command)
	output, err := o.ExecCommand(command)
	if err != nil {
		logger.Error(err, containerId)
		return false, "执行开始命令出错", err
	}
	if strings.Contains(output, "No such container") { //Error response from daemon: No such container: lsdsd
		logger.Error("Docker不存在", containerId)
		return false, "Docker不存在", errors.New("Docker不存在")
	}
	return strings.HasPrefix(output, containerId), "", nil
}

func (o *Virtual) StartDockerUserApi(ctx context.Context, containerId string) error {
	pre := fmt.Sprintf("StartDockerUseApi containerId:%s", containerId)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)

	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: pre, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	if err := o.DockerClient.ContainerStart(ctx, containerId, container.StartOptions{}); err != nil {
		logger.Error(pre, " ", err)
		return err
	} else {
		return nil
	}
}

func (o *Virtual) ReStartDocker(containerId string) (string, error) {
	command := fmt.Sprintf("docker restart %s", containerId)
	logger.Info(command)
	output, err := o.ExecCommand(command)
	if err != nil {
		logger.Error(err, containerId)
		return "执行重启命令出错", err
	}
	if strings.Contains(output, "No such container") { //Error response from daemon: No such container: lsdsd
		logger.Error("Docker不存在", containerId)
		return "Docker不存在", errors.New("Docker不存在")
	}
	if strings.HasPrefix(output, containerId) {
		return "重启成功", nil
	} else {
		return "重启失败", errors.New(output)
	}
}

func (o *Virtual) ReStartDockerUseApi(ctx context.Context, containerId string) error {
	pre := fmt.Sprintf("ReStartDockerUseApi containerId:%s", containerId)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: pre, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()
	if err := o.DockerClient.ContainerRestart(ctx, containerId, container.StopOptions{}); err != nil {
		logger.Error(pre, " ", err)
		return err
	} else {
		return nil
	}
}

func (o *Virtual) LogsDocker(containerId string) (string, error) {
	command := fmt.Sprintf("docker logs %s", containerId)
	logger.Info(command)
	output, err := o.ExecCommand(command)
	if err != nil {
		logger.Error(err, containerId)
		return "执行日志命令出错", err
	}
	if strings.Contains(output, "No such container") { //Error response from daemon: No such container: lsdsd
		logger.Error("Docker不存在", containerId)
		return "Docker不存在", errors.New("Docker不存在")
	}
	return output, nil
}

func (o *Virtual) CommitDocker(ctx context.Context, containerId string, imagePath string) (bool, string, error) {
	command := fmt.Sprintf("docker commit %s %s", containerId, imagePath)
	logger.Info(command)
	output, err := o.ExecCommandPro(ctx, command)
	if err != nil {
		logger.Error(err, containerId)
		return false, "执行docker commit命令出错", err
	}

	if strings.Contains(output, "No such container") { //Error response from daemon: No such container: lsdsd
		logger.Error("Docker不存在", containerId)
		return false, "Docker不存在", errors.New("Docker不存在")
	}
	//return strings.HasPrefix(output, containerId), "", nil
	logger.Info("CommitDocker output:", output)
	return true, output, nil
}

/*
推送案列1
{"msg":"容器更改的大小为************字节 284214.263126MB，容器的总大小为************字节 339.879495GB","state":"progress","log":"hubImagePath:*************/chenyu/private/fa8fc7c7b4f64664bed3b13693568765:C1","time":"2024-11-20 19:54:41"}
{"msg":"开始提交容器","state":"progress","log":"CommitDockerUseApi containerId:0cf04d0a069e imagePath:*************/chenyu/private/fa8fc7c7b4f64664bed3b13693568765:C1","time":"2024-11-20 19:54:41"}
{"msg":"容器提交中","state":"progress","log":"每3秒输出一次日志，当前已耗时1803秒","data":{"status":"ContainerCommit","cover_status":"ContainerCommit","current":150716949354,"total":************,"percent":0.5057272005455298},"percent":0.5057272005455298,"time":"2024-11-20 20:24:42"}
{"msg":"容器提交成功","state":"progress","log":"new_container_id:sha256:d4cedc7a8d2beb344b4174096765e8eae9a07a6f5039ca706f48413a85b32d03","time":"2024-11-20 20:54:10"}
*/
func (o *Virtual) CommitDockerUseApi(ctx context.Context, containerId string, imagePath string, imageId uint, sizeRw int64) error {
	bReturn := false

	imageSizeG := float64(sizeRw) / 1024 / 1024 / 1024
	imageSizeG = math.Round(imageSizeG*100) / 100

	pre := fmt.Sprintf("CommitDockerUseApi containerId:%s imagePath:%s", containerId, imagePath)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)

	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, ImageId: imageId, ImageSizeG: imageSizeG, Command: pre, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		bReturn = true
		logger.Info(pre, "bReturn 置为true")
		//close(doneChan)
		//logger.Info(pre, "doneChan close")
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	// 启动一个 goroutine 来间断性输出日志
	//120768M 需要 681秒 每秒177.33M    126634851340字节 每秒185954260字节
	//改的大小为************字节284214.263126MB 总大小为************字节339.879495GB 需要3596秒   每秒82875488字节
	//82875488
	go func() {

		lastPercentCount := 0
		tickerSecond := int64(0)
		//secondCommitSize := int64(83592318)
		secondCommitSize := int64(82875488)
		needSeconds := sizeRw / secondCommitSize
		needSeconds += 3600
		//timeout := time.After(30 * time.Minute)
		for {
			if bReturn {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "容器提交中 bReturn为ture,退出进度循环", nil)
				break
			}

			tickerSecond += 3
			current := secondCommitSize * tickerSecond
			total := sizeRw
			percent := float64(0)
			if total > 0 {
				percent = float64(current) / float64(total)
			}
			if percent > 0.99 {
				if lastPercentCount == 0 {
					lastPercentCount = 1
				} else {
					lastPercentCount++
				}
				percent = 0.99 + float64(lastPercentCount)/10000
				if percent > 0.9999 {
					percent = 0.9999
				}
			}
			taskProgress := tasklog.TaskProgress{
				Status:      "ContainerCommit",
				CoverStatus: "ContainerCommit",
				Current:     current,
				Total:       total,
				Percent:     percent,
			}
			logTxt := fmt.Sprintf("每3秒输出一次日志，当前已耗时%d秒，总时间%d秒", tickerSecond, needSeconds)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器提交中", logTxt, taskProgress)

			if tickerSecond > needSeconds {
				logger.Info(pre, " 超时，退出")
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器提交中", "超时30分钟退出", nil)
				break
			}
			time.Sleep(time.Second * 3)
		}
		logger.Info(pre, " 循环已退出")
	}()

	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始提交容器", pre, nil)
	if r, err := o.DockerClient.ContainerCommit(ctx, containerId, container.CommitOptions{
		Reference: imagePath,
		Pause:     false,
	}); err != nil {
		logger.Error(pre, err)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器提交失败", err.Error(), nil)
		bReturn = true
		return err
	} else {
		logger.Info(pre, " 容器提交成功 new_container_id:", r.ID) //sha256:8dfd22110dcc868481b29043bc46b88d1d1f9f51a93e726fb9596c796d9c80bc
		//tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器提交成功", "new_container_id:"+r.ID, nil)

		taskProgress := tasklog.TaskProgress{
			Status: "ClearProgress", //清除前面的进度
		}
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器提交成功", "new_container_id:"+r.ID, taskProgress)

		//logger.Info(pre, "容器提交成功 new_container_id:", r.ID)
		bReturn = true
		return nil
	}
}

func (o *Virtual) SaveImage(ctx context.Context, imagePath string, imageSize int64, savePath string) error {
	//command := fmt.Sprintf("docker save -o %s %s", savePath, imagePath)

	command := fmt.Sprintf("docker save %s | pv -s %d > %s", imagePath, imageSize, savePath)
	//docker save hub.suanyun.cn/public/49f09d9ba4c74965a0d743cb6fefbfba:v1.1 | pv -s ************ > /root/suanyun-user/0/4a5c08f09d37/container_images/49f09d9ba4c74965a0d743cb6fefbfba:v1.1.jar

	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: command, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	output, err := o.ExecCommandPro(ctx, command)
	if err != nil {
		logger.Error(err, " command:", commandLog)
		return err
	}
	logger.Info(output)
	return nil
}

func (o *Virtual) SaveImageUseApi(ctx context.Context, imagePath string, savePath string) error {
	extract := false
	logKey := tasklog.GetLogKey(ctx)
	pre := fmt.Sprintf("SaveImageUseApi  imagePath:%s", imagePath)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: pre, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()
	if logKey != "" {
		pre = logKey + " " + pre
	}

	imageId := ""
	imageSize := int64(0)

	options := image.ListOptions{
		Filters: filters.NewArgs(filters.Arg("reference", imagePath)),
	}
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始查找镜像", "imagePath："+imagePath, nil)
	if images, err := o.DockerClient.ImageList(ctx, options); err != nil {
		logger.Error(pre, err)
		return err
	} else {
		logger.Info("reference:", utils.GetJsonFromStruct(images))
		for _, image := range images {
			if image.RepoTags == nil {
				continue
			}
			for _, repo := range image.RepoTags {
				if repo == imagePath {
					logger.Info(pre, "fined:", imagePath)
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "已找到镜像", "imagePath："+imagePath, nil)
					imageId = image.ID
					imageSize = image.Size
				}
			}
		}
	}

	if imageId == "" {
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "未找到镜像", "imagePath："+imagePath, nil)
		err := errors.New("未找到镜像ID")
		logger.Error(pre, err)
		return err
	}

	// 调用ImageSave方法获取镜像数据
	//ImageSave 每秒处理约 76,066,781 bytes/s，或约 76 MB/s
	go func() {
		sec := 0
		attach := float64(0)
		for {
			sec += 2
			chum := 76066781 - utils.GenerateRandomInt(10, 1000)
			current := int64(chum * sec)
			total := imageSize
			percent := float64(0)
			if total > 0 {
				percent = float64(current) / float64(total)
			}
			if current >= total {
				percent = 0.99
				attach += 0.0001
				percent += attach
				if percent >= 1 {
					percent = 0.9999
					extract = true
				}
			}
			taskProgress := tasklog.TaskProgress{
				Status:      "Extracting",
				CoverStatus: "Extracting",
				Current:     current,
				Total:       total,
				Percent:     percent,
			}
			txt := fmt.Sprintf("Progress:%.2f%%  %d/%d", percent*100, current, total)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在提取镜像", txt, taskProgress)
			if extract {
				break
			}
			time.Sleep(time.Second * 2)
		}
	}()

	imageIDs := []string{imageId}
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始保存镜像", "调用ImageSave接口  imagePath："+imagePath, nil)
	saveResponse, err := o.DockerClient.ImageSave(ctx, imageIDs)
	extract = true
	if err != nil {
		logger.Error(pre, err)
		return err
	}
	defer saveResponse.Close()
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始保存镜像", "ImageSave接口调用完成 imagePath："+imagePath, nil)

	// 创建文件保存镜像数据
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始保存镜像", "创建镜像tar文件 savePath："+savePath, nil)
	outFile, err := os.Create(savePath)
	if err != nil {
		logger.Error(pre, err)
		return err
	}
	defer outFile.Close()
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始保存镜像", "镜像tar文件创建完成 savePath："+savePath, nil)

	defaultProgressFunc := func(current, total int64) {
		percent := float64(0)
		if total > 0 {
			percent = float64(current) / float64(total)
		}
		taskProgress := tasklog.TaskProgress{
			Status:      "Saving",
			CoverStatus: "Saving",
			Current:     current,
			Total:       total,
			Percent:     percent,
		}
		txt := fmt.Sprintf("Progress:%.2f%%  %d/%d", percent*100, current, total)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在保存镜像", txt, taskProgress)
	}

	// 使用进度读取器
	progressR := &ProgressReader{
		StartAt:      time.Now().Unix(),
		Reader:       saveResponse,
		Total:        imageSize,
		progressFunc: defaultProgressFunc,
	}
	_, err = io.Copy(outFile, progressR)
	if err != nil {
		logger.Error(pre, err)
		return err
	}
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像保存成功", utils.GetJsonFromStruct(progressR), nil)

	//tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始计算文件MD5", "", nil)
	//if md5, err := utils.FileMd5(savePath); err != nil {
	//	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "计算文件MD5失败", err.Error(), nil)
	//} else {
	//	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "计算文件MD5完成", "md5:"+md5, nil)
	//}

	return nil
}

func (o *Virtual) PushDocker(ctx context.Context, logKey string, imagePath string) (bool, string, error) {
	command := fmt.Sprintf("docker push %s", imagePath)
	logger.Info(command)
	if err := o.LoginHub(); err != nil {
		logger.Error(err)
		StartupLog.Save(logKey, "获取推送权限失败", err.Error())
		return false, "获取推送权限失败", err
	}
	//output, err := o.ExecCommandWithLog(logKey, command)
	output, err := o.ExecCommandPro(ctx, command)
	logger.Info("PushDocker output:", output)
	if err != nil {
		msg := "执行docker push命令出错"
		logger.Error(err, imagePath)
		StartupLog.Save(logKey, "执行Push命令失败", err.Error())
		return false, msg, err
	}
	if strings.Contains(output, "No such container") { //Error response from daemon: No such container: lsdsd
		logger.Error("Docker不存在", imagePath)
		StartupLog.Save(logKey, "Docker不存在", err.Error())
		return false, "Docker不存在", errors.New("Docker不存在")
	}
	sha265 := GetImageSha256(output)
	if sha265 != "" {
		StartupLog.Save(logKey, "", "sha265:"+sha265)
		return true, sha265, nil
	} else {
		StartupLog.Save(logKey, "推送失败", "output："+output+"    sha265："+sha265)
		logger.Info("PushDocker失败 output:", output)
		return false, output, errors.New(output)
	}
}

func (o *Virtual) PushDockerUseApi(ctx context.Context, imagePath string, imageId uint, imageSize int64) error {
	//imageName := strings.Replace(imagePath, "hub.suanyun.cn/", "", -1)
	imageSizeG := float64(imageSize) / 1024 / 1024 / 1024
	imageSizeG = math.Round(imageSizeG*100) / 100
	imageName := imagePath
	command := enums.DockerCommandEnum.Push + " " + imageName
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: command, ImageId: imageId, ImageSizeG: imageSizeG, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info("PushDockerUseApi  commandLog:", commandLog, "  imageId:", imageId)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	showObjName := "镜像"
	if ctx != nil {
		if tmpTask, ok := ctx.Value("task").(string); ok {
			if tmpTask == tasklog.TaskEnum.SaveInstanceImage {
				showObjName = "实例数据"
			}
		}
	}

	logger.Info("开始推送镜像", imageName)
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始推送"+showObjName, imageName, nil)
	var expected = registry.AuthConfig{
		Username:      "admin",
		Password:      "Zeyun1234%^&*",
		ServerAddress: common.HubServerAddress,
	}
	registryToken, err := registry.EncodeAuthConfig(expected)
	if err != nil {
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "生成认证码失败 err:"+err.Error(), nil)
		logger.Error(err)
		return err
	}

	//privilegeFunc := func() (string, error) {
	//	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在推送镜像", "privilegeFunc", nil)
	//	return "IAmValid", nil
	//}
	errorDetail := ""
	if reader, err := o.DockerClient.ImagePush(ctx, imageName, image.PushOptions{ //需要还原
		//All:           true,
		RegistryAuth: registryToken,
		//PrivilegeFunc: privilegeFunc,
	}); err != nil {
		logger.Error(err, "   "+imageName)
		return err
	} else {
		defer reader.Close()
		progressM := make(map[string]jsonmessage.JSONProgress)
		scanner := bufio.NewScanner(reader)
		var taskProgress tasklog.TaskProgress
		for scanner.Scan() {
			select {
			case <-ctx.Done():
				logger.Info("操作被取消")
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, "操作被取消", "用户取消操作", nil)
				return ctx.Err()
			default:
				line := scanner.Text()
				//logger.Info(line)
				if strings.Contains(line, "errorDetail") {
					errorDetail = line
				}
				//{"status":"The push refers to repository [docker.io/public/cfb8d747062e4c329a39249a03fe9e21]"}
				//{"errorDetail":{"message":"An image does not exist locally with the tag: public/cfb8d747062e4c329a39249a03fe9e21"},"error":"An image does not exist locally with the tag: public/cfb8d747062e4c329a39249a03fe9e21"}

				//{"status":"The push refers to repository [hub.suanyun.cn/public/cfb8d747062e4c329a39249a03fe9e21]"}
				//{"status":"Preparing","progressDetail":{},"id":"70d033731499"}
				//{"status":"Preparing","progressDetail":{},"id":"de99a8cd9d8e"}
				//{"status":"Waiting","progressDetail":{},"id":"d5975e4b9f49"}
				//{"status":"Waiting","progressDetail":{},"id":"5bfa328f1d9d"}
				//{"status":"Pushing","progressDetail":{"current":1024,"total":76851},"progress":"[\u003e                                                  ]  1.024kB/76.85kB","id":"65f264a09f79"}
				//{"status":"Layer already exists","progressDetail":{},"id":"688e35443b30"}
				//{"status":"Layer already exists","progressDetail":{},"id":"6f9c37904612"}
				//{"status":"v2.5: digest: sha256:e4118dec13dcde66afe8f53930b8eb6efddd12aff34054bebe05cd737defdc93 size: 4734"}
				//{"progressDetail":{},"aux":{"Tag":"v2.5","Digest":"sha256:e4118dec13dcde66afe8f53930b8eb6efddd12aff34054bebe05cd737defdc93","Size":4734}}
				//{"status":"Pushed","progressDetail":{},"id":"65f264a09f79"}
				//{"status":"v2.5: digest: sha256:92cd557677a992c73e3e8d17424c4ee6507317802c064b9a0f26dec7066b47c5 size: 4734"}
				//{"progressDetail":{},"aux":{"Tag":"v2.5","Digest":"sha256:92cd557677a992c73e3e8d17424c4ee6507317802c064b9a0f26dec7066b47c5","Size":4734}}

				var message jsonmessage.JSONMessage
				if err := utils.GetStructFromJson(&message, line); err != nil {
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在推送"+showObjName, line, nil)
				} else {
					coverStatus := ""
					if message.Status == "Pushing" {
						coverStatus = message.Status
						progressM["Pushing_"+message.ID] = *message.Progress

						current := int64(0)
						total := int64(0)
						for _, progress := range progressM {
							current += progress.Current
							total += progress.Total
						}

						percent := float64(0)
						if total > 0 {
							percent = float64(current) / float64(total)
						}
						taskProgress = tasklog.TaskProgress{
							Status:      message.Status,
							CoverStatus: coverStatus,
							Current:     current,
							Total:       total,
							Percent:     percent,
						}
					}
					//logger.Info("taskProgress:", utils.GetJsonFromStruct(taskProgress))
					if taskProgress.Status != "" {
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在推送"+showObjName, line, taskProgress)
					} else {
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在推送"+showObjName, line, nil)
					}
				}
			}
		}
		if err := scanner.Err(); err != nil {
			logger.Error(err, imageName)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在推送"+showObjName, "scanner.Err："+err.Error(), nil)
			return err
		}
	}
	if errorDetail != "" {
		logger.Error("推送出错:", errorDetail)
		return errors.New("推送出错")
	}
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在推送"+showObjName, "推送函数执行到最后了", nil)
	return nil
}

func (o *Virtual) DockerStatus(dockerNameOrId string) (string, error) {
	command := fmt.Sprintf(`docker inspect --format="{{.State.Status}}" %s`, dockerNameOrId)
	output, err := o.ExecCommand(command)
	//logger.Info("DockerStatus", output)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	return output, nil
}

func (o *Virtual) ContainerInfoUseApi(ctx context.Context, containerId string) (types.Stats, error) {
	var stats types.Stats
	resp, err := o.DockerClient.ContainerStats(ctx, containerId, false)
	if err != nil {
		logger.Error(err)
		return stats, err
	}
	defer resp.Body.Close()
	content, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error(err)
		return stats, err
	}
	//if string(content) != "response" {
	//	err := errors.New("expected response to contain 'response', got %s" + string(content))
	//}

	if err := utils.GetStructFromJson(&stats, string(content)); err != nil {
		logger.Error(err)
		return stats, err
	} else {
		return stats, nil
	}
}

func (o *Virtual) ContainerExecUseApi(ctx context.Context, containerId string, cmd []string) (container.ExecInspect, error) {

	execInspect := container.ExecInspect{
		ContainerID: containerId,
	}
	execOptions := container.ExecOptions{
		AttachStdout: true,
		AttachStderr: true,
		Cmd:          cmd,
		//Cmd:          []string{"bash", scriptPath},
	}
	//bash /chenyudata/scripts/install/migrate_large_files.sh

	// 调用 ContainerExecCreate 来创建执行进程
	if resp, err := o.DockerClient.ContainerExecCreate(ctx, containerId, execOptions); err != nil {
		logger.Error(err)
		return execInspect, err
	} else {
		execInspect.ExecID = resp.ID
	}

	if err := o.DockerClient.ContainerExecStart(ctx, execInspect.ExecID, container.ExecStartOptions{
		Detach: false, // false表示不分离，阻塞直到完成
		Tty:    false, // 不使用 TTY（终端）
	}); err != nil {
		logger.Error(err)
		return execInspect, err
	}

	// 获取执行结果
	if tmpExecInspect, err := o.DockerClient.ContainerExecInspect(ctx, execInspect.ExecID); err != nil {
		logger.Error(err)
		return execInspect, err
	} else {
		execInspect = tmpExecInspect
	}
	return execInspect, nil
}

func (o *Virtual) MigrateLargeFile(ctx context.Context, containerId string) (container.ExecInspect, error) {
	scriptPath := "/chenyudata/scripts/install/migrate_large_files.sh"
	cmd := []string{"sh", "-c", "bash " + scriptPath}
	return o.ContainerExecUseApi(ctx, containerId, cmd)
}

func (o *Virtual) PullImageUserApi(ctx context.Context, hubImagePath string, podImage PodImageItem) error {
	imageSize := podImage.Size
	imageSizeG := imageSize / 1024 / 1024 / 1024
	imageSizeG = math.Round(imageSizeG*100) / 100
	command := enums.DockerCommandEnum.Pull + " " + hubImagePath
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)

	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, ImageId: podImage.ID, ImageSizeG: imageSizeG, Command: command, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()
	if o.DockerClient == nil {
		return errors.New("DockerClient not init")
	}

	var expected = registry.AuthConfig{
		Username:      "admin",
		Password:      "Zeyun1234%^&*",
		ServerAddress: common.HubServerAddress,
	}
	registryToken, err1 := registry.EncodeAuthConfig(expected)
	if err1 != nil {
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始拉取镜像", "生成认证码失败", nil)
		logger.Error(err1)
		return err1
	}

	logger.Info("开始拉取镜像", hubImagePath)
	txt := "镜像大小参数为0  hubImagePath:" + hubImagePath
	if imageSize > 0 {
		txt = fmt.Sprintf("镜像大小为：%fG hubImagePath:%s", imageSizeG, hubImagePath)
	}
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始拉取镜像", txt, nil)
	if reader, err := o.DockerClient.ImagePull(ctx, hubImagePath, image.PullOptions{RegistryAuth: registryToken}); err != nil {
		//if reader, err := o.DockerClient.ImagePull(ctx, imageName, image.PullOptions{}); err != nil {
		logger.Error(err, "   "+hubImagePath)
		return err
	} else {
		defer reader.Close()
		progressM := make(map[string]jsonmessage.JSONProgress)
		scanner := bufio.NewScanner(reader)
		for scanner.Scan() {
			line := scanner.Text()
			//{"status":"Pulling from public/34d7cb39fdc840bfb0af8565333a67c2","id":"1.0"}
			//{"status":"Already exists","progressDetail":{},"id":"43f89b94cd7d"}
			//{"status":"Pulling fs layer","progressDetail":{},"id":"d38c33e9afaf"}
			//{"status":"Downloading","progressDetail":{"current":540036,"total":1612724203},"progress":"[\\u003e                                                  ]    540kB/1.613GB","id":"d38c33e9afaf"}
			//{"status":"Verifying Checksum","progressDetail":{},"id":"d38c33e9afaf"}
			//{"status":"Download complete","progressDetail":{},"id":"d38c33e9afaf"}
			//{"status":"Extracting","progressDetail":{"current":557056,"total":1612724203},"progress":"[\\u003e                                                  ]  557.1kB/1.613GB","id":"d38c33e9afaf"}
			//{"status":"Extracting","progressDetail":{"current":655654912,"total":1612724203},"progress":"[====================\u003e                              ]  655.7MB/1.613GB","id":"d38c33e9afaf"}
			//{"status":"Pull complete","progressDetail":{},"id":"d38c33e9afaf"}
			//{"status":"Digest: sha256:c0908b57284180fe6d0ff9bb07384b9ba99cb3cbd346033a5e12196b1476160b"}
			//{"status":"Status: Downloaded newer image for hub.suanyun.cn/public/34d7cb39fdc840bfb0af8565333a67c2:1.0"}

			//Unable to find image 'hub.suanyun.cn/public/74023ab8ce464be3b9df43a6a9342727:v1.3' locally

			var message jsonmessage.JSONMessage
			if err := utils.GetStructFromJson(&message, line); err != nil {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在拉取镜像", line, nil)
			} else {
				showMsg := "正在拉取镜像"
				coverStatus := ""
				if message.Status == "Downloading" || message.Status == "Download complete" || message.Status == "Pull complete" {
					showMsg = "正在下载镜像"
					//coverStatus = message.Status
					coverStatus = "Downloading,Download complete,Extracting,Verifying Checksum,Pull complete"
					if message.Progress.Current > 0 || message.Progress.Total > 0 {
						progressM["Downloading_"+message.ID] = *message.Progress
					}
				}
				if message.Status == "Extracting" || message.Status == "Verifying Checksum" {
					showMsg = "正在解压镜像"
					//coverStatus = message.Status
					coverStatus = "Downloading,Download complete,Extracting,Verifying Checksum,Pull complete"
					if message.Progress.Current > 0 || message.Progress.Total > 0 {
						progressM["Extracting_"+message.ID] = *message.Progress
					}
				}

				current := int64(0)
				total := int64(0)
				for _, progress := range progressM {
					current += progress.Current
					total += progress.Total
				}

				percent := float64(0)
				if total > 0 {
					percent = float64(current) / float64(total)
				}

				taskProgress := tasklog.TaskProgress{
					Status:      message.Status,
					CoverStatus: coverStatus,
					Current:     current,
					Total:       total,
					Percent:     percent,
				}

				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, showMsg, line, taskProgress)
			}

		}
		if err := scanner.Err(); err != nil {
			logger.Error(err, hubImagePath)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "拉取镜像出错", err.Error(), nil)
			return err
		}
	}

	if images, err := o.DockerImagesUseApi(ctx, hubImagePath); err != nil {
		logger.Error("err:", err, "获取本地镜像失败  virtualId: ", o.ID, "  hubImagePath:", hubImagePath)
	} else {
		if len(images) == 0 {
			logger.Error("查询本地镜像为空 获取本地镜像失败  virtualId: ", o.ID, "  hubImagePath:", hubImagePath)
		} else {
			localImage := DockerImageItem{
				ID:           images[0].ID,
				ImageId:      podImage.ID,
				HubImagePath: hubImagePath,
				SizeG:        images[0].SizeG,
				SizeB:        images[0].SizeB,
				LastUseUnix:  time.Now().Unix(),
			}
			if err := o.SetLocalImage(localImage); err != nil {
				logger.Error("设置本地镜像信息失败 err:", err)
			}

			if imageInspect, err := o.ImageInspectWithRaw(ctx, localImage.ID); err != nil {
				logger.Error("ImageInspectWithRaw err:", err)
			} else {
				go func() {
					layerCount := len(imageInspect.RootFS.Layers)
					if ginH, err := MasterService.ReportInspectImage(o.ID, podImage.ID, layerCount, imageInspect); err != nil {
						logger.Error(err)
					} else {
						logger.Info(utils.GetJsonFromStruct(ginH))
					}

				}()
			}
		}
	}
	return nil
}

func (o *Virtual) LoadImageUserApi(ctx context.Context, imageSavePath string) error {
	command := enums.DockerCommandEnum.Load + " " + imageSavePath
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: command, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()
	if o.DockerClient == nil {
		return errors.New("DockerClient not init")
	}

	logger.Info("开始载入镜像", imageSavePath)
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "在这打开镜像文件", "imageSavePath："+imageSavePath, nil)
	file, err := os.Open(imageSavePath)
	if err != nil {
		logger.Error(err)
		return err
	}
	defer file.Close()
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在载入镜像", "imageSavePath："+imageSavePath, nil)

	response, err := o.DockerClient.ImageLoad(ctx, file, false)
	if err != nil {
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像载入失败", "err："+err.Error(), nil)
		logger.Error(err)
		return err
	}
	defer response.Body.Close()
	// 处理JSON响应
	decoder := json.NewDecoder(response.Body)
	for {
		var event map[string]interface{}
		if err := decoder.Decode(&event); err == io.EOF {
			break
		} else if err != nil {
			logger.Error("Error decoding JSON response: %v", err)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像载入失败", "err："+err.Error(), nil)
			return err
		}
		// 输出加载进度信息
		logger.Info(event)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在载入镜像", "event："+utils.GetJsonFromStruct(event), nil)
	}
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像载入完成", "", nil)
	return nil
}

func (o *Virtual) DockerImages(repository string) ([]DockerImageItem, error) {
	//docker commit [OPTIONS] CONTAINER [REPOSITORY[:TAG]]
	ary := make([]DockerImageItem, 0)
	command := fmt.Sprintf(`docker images --format '{{json .}}'`)
	if repository != "" {
		command = fmt.Sprintf(`docker images --format '{{json .}}' %s`, repository)
	}

	if output, err := o.ExecCommand(command); err != nil {
		logger.Error(err, "  output:", output)
		return ary, err
	} else {
		aryLine := strings.Split(output, "\n")
		for _, line := range aryLine {
			line = strings.TrimSpace(line)
			if !strings.HasPrefix(line, "{") {
				continue
			}
			var dockerImage DockerImageItem
			if err := utils.GetStructAryFromJson(&dockerImage, line); err != nil {
				logger.Error(err, "  output:", line)
				return ary, err
			} else {
				if strings.Contains(dockerImage.Size, "GB") {
					tmp := strings.Replace(dockerImage.Size, "GB", "", -1)
					if f, err := strconv.ParseFloat(tmp, 64); err != nil {
						logger.Error(err, " string:", tmp)
					} else {
						dockerImage.SizeG = f
					}
				}
				ary = append(ary, dockerImage)
			}
		}
		return ary, nil
	}
}

func (o *Virtual) DockerImagesUseApi(ctx context.Context, repository string) ([]DockerImageItem, error) {
	pre := fmt.Sprintf("DockerImagesUseApi repository:%s", repository)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: pre, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	options := image.ListOptions{
		Filters: filters.NewArgs(filters.Arg("reference", repository)),
	}

	if repository == "" {
		options = image.ListOptions{}
	}

	ary := make([]DockerImageItem, 0)
	if images, err := o.DockerClient.ImageList(ctx, options); err != nil {
		logger.Error(err)
		return ary, err
	} else {
		for _, image := range images {

			hubImagePath := ""
			tag := ""
			if len(image.RepoTags) > 0 {
				//if !strings.Contains(image.RepoTags[0], "<none>:") {
				//	hubImagePath = image.RepoTags[0]
				//}
				if strings.HasPrefix(image.RepoTags[0], common.HubServerAddress) {
					hubImagePath = image.RepoTags[0]
					aryTmp := strings.Split(hubImagePath, ":")
					if len(aryTmp) == 2 {
						tag = aryTmp[1]
					}
				}
			}

			size := fmt.Sprintf("%fGB", float64(image.Size)/1024/1024/1024)
			dockerImage := DockerImageItem{
				ID:           image.ID,
				Size:         size,
				SizeG:        float64(image.Size) / 1024 / 1024 / 1024,
				SizeB:        image.Size,
				Created:      image.Created,
				HubImagePath: hubImagePath,
				Tag:          tag,
			}
			ary = append(ary, dockerImage)
		}
		return ary, nil
	}
}

func (o *Virtual) DockerImageRemoveUseApi(ctx context.Context, id string) ([]image.DeleteResponse, error) {
	id = strings.TrimPrefix(id, "sha256:")
	pre := fmt.Sprintf("DockerImageRemoveUseApi id:%s", id)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: pre, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	if imageDeletes, err := o.DockerClient.ImageRemove(ctx, id, image.RemoveOptions{
		Force:         false,
		PruneChildren: false,
	}); err != nil {
		if strings.Contains(err.Error(), "No such image") {
			return imageDeletes, nil
		}
		logger.Error("DockerImageRemoveUseApi id:", id, " err:", err)
		return imageDeletes, err
	} else {
		logger.Info("imageDeletes:", utils.GetJsonFromStruct(imageDeletes))
		return imageDeletes, nil
	}
}

func (o *Virtual) DockerImagesPrune(ctx context.Context, whithA bool) error {

	a := ""
	if whithA {
		a = " -a -f"
	}
	command := fmt.Sprintf("docker image prune%s", a)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)

	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: command, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)

	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	output, err := o.ExecCommand(command)
	if err != nil {
		logger.Error(commandLog, " output:", output, " err:", err)
		return err
	} else {
		logger.Info(commandLog, " output:", output)
		return nil
	}
}

func (o *Virtual) DockerImagesPruneUseApi(ctx context.Context) error {
	pre := fmt.Sprintf("DockerImagesPruneUseApi")
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: pre, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	if imagePrunes, err := o.DockerClient.ImagesPrune(ctx, filters.NewArgs()); err != nil {
		logger.Error("DockerImagesPruneUseApi ", " err:", err)
		return err
	} else {
		logger.Info("imagePrunes:", utils.GetJsonFromStruct(imagePrunes))
		return nil
	}

}

func (o *Virtual) InitLockGpusByDocker(dockers []DockerItem) error {

	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedGpuLock, o.ID)
	if mm, err := common.RedisHGetAll(key); err != nil {
		logger.Error(err, " key:", key)
		return nil
	} else {
		for _, docker := range dockers {
			logger.Info("InitLockGpusByDocker :")
			if docker.MapPref == "" {
				continue
			}

			if num, err := strconv.Atoi(docker.MapPref); err != nil {
				logger.Error(err)
			} else {
				if num <= 17 {
					if docker.Gpus == nil {
						continue
					}
					for _, gpuIndex := range docker.Gpus {
						field := strconv.Itoa(gpuIndex)
						if _, ok := mm[field]; ok {
							continue //存在
						}
						lockGpuItem := LockGpuItem{
							Index:       gpuIndex,
							NeedGpus:    len(docker.Gpus),
							StartupMark: docker.StartupMark,
							LockedAt:    time.Now(),
						}
						json := utils.GetJsonFromStruct(lockGpuItem)
						if _, err := common.RedisHSet(key, field, json); err != nil {
							logger.Error(err)
							return err
						}
					}
				} else {
					field := strconv.Itoa(num)
					if _, ok := mm[field]; ok {
						continue //存在
					}
					lockGpuItem := LockGpuItem{
						Index:       num,
						NeedGpus:    0,
						StartupMark: docker.StartupMark,
						LockedAt:    time.Now(),
					}
					json := utils.GetJsonFromStruct(lockGpuItem)
					if _, err := common.RedisHSet(key, field, json); err != nil {
						logger.Error(err)
						return err
					}
				}
			}
		}
		return nil
	}
}

func (o *Virtual) UnLockGpus(startupMark string, from string) error {
	pre := "释放Gpu startupMark:" + startupMark + " from:" + from + " "
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedGpuLock, o.ID)
	logger.Info(pre)

	ctx := context.Background()
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, startupMark); err != nil {
		logger.Error(err)
	} else {
		ctx = context.WithValue(ctx, "logkey", logKey)
		tasklog.Save(ctx, "", "", fmt.Sprintf("Redis释放Gpu startupMark:%s", startupMark), nil)
	}
	if mm, err := common.RedisHGetAll(key); err != nil {
		logger.Error(pre, err, " key:", key)
		return nil
	} else {
		for gpuIndex, val := range mm {
			var lockGpuItem LockGpuItem
			if err := utils.GetStructFromJson(&lockGpuItem, val); err != nil {
				logger.Error(pre, err)
				return err
			} else {
				if lockGpuItem.StartupMark == startupMark {
					if _, err := common.RedisHDel(key, gpuIndex); err != nil {
						logger.Error(pre, " 已找到释放出错 err:", err)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", pre+"Redis释放Gpu失败:"+val, nil)
						return err
					} else {
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", pre+"Redis释放Gpu成功:"+val, nil)
						logger.Info(pre, " 已找到并释放成功")
					}
				}
			}
		}
		logger.Info(pre, " 结束")
		return nil
	}
}
func (o *Virtual) GetLockGpus() (map[int]LockGpuItem, error) {
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedGpuLock, o.ID)
	gpuMap := make(map[int]LockGpuItem)
	if mm, err := common.RedisHGetAll(key); err != nil {
		logger.Error(err, " key:", key)
		return gpuMap, err
	} else {
		for _, val := range mm {
			var lockGpuItem LockGpuItem
			if err := utils.GetStructFromJson(&lockGpuItem, val); err != nil {
				logger.Error(err)
				return gpuMap, err
			} else {
				gpuMap[lockGpuItem.Index] = lockGpuItem
			}
		}
	}
	return gpuMap, nil
}
func (o *Virtual) GetLockGpuItem(startupMark string) ([]LockGpuItem, error) {
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedGpuLock, o.ID)
	ary := make([]LockGpuItem, 0)

	if mm, err := common.RedisHGetAll(key); err != nil {
		logger.Error(err)
		return ary, err
	} else {
		for _, val := range mm {
			if val == "" {
				continue
			}
			var lockGpuItem LockGpuItem
			if err := utils.GetStructFromJson(&lockGpuItem, val); err != nil {
				logger.Error(err)
				return ary, err
			} else {
				if lockGpuItem.StartupMark == startupMark {
					ary = append(ary, lockGpuItem)
				}
			}
		}
		return ary, nil
	}

}

func (o *Virtual) ManualLockGpu(ctx context.Context, startupMark string) error {
	finded := false
	for _, docker := range o.Dockers {
		if docker.StartupMark == startupMark {
			finded = true
			gpus := docker.Gpus
			needGpus := len(gpus)
			if len(docker.Gpus) == 0 {
				if ii := utils.String2Int(docker.MapPref); ii >= 30 {
					gpus = append(gpus, ii)
				}
			}
			if len(gpus) == 0 {
				return errors.New("可加Gpu数组为空")
			}

			errMsg := ""
			for _, gpuIndex := range gpus {
				if bExists, err := o.ExistsLockGpus(gpuIndex); err != nil {
					logger.Error(err)
					errMsg += err.Error()
					continue
				} else if bExists {
					errMsg += " 已存在"
					continue
				} else if !bExists {
					key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedGpuLock, o.ID)
					lockGpuItem := LockGpuItem{
						Index:       gpuIndex,
						NeedGpus:    needGpus,
						StartupMark: startupMark,
						LockedAt:    time.Now(),
					}
					json := utils.GetJsonFromStruct(lockGpuItem)
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "手动锁定:"+json, nil)
					field := strconv.Itoa(gpuIndex)
					if _, err := common.RedisHSet(key, field, json); err != nil {
						errMsg += " " + err.Error()
						continue
					}
				}
			}
			if errMsg != "" {
				return errors.New(errMsg)
			}
		}
	}
	if finded {
		return nil
	} else {
		return errors.New("未找到相应的容器")
	}
}

func (o *Virtual) ManualUnLockGpu(ctx context.Context, startupMark string) error {
	logger.Info("ManualUnLockGpu startupMark:", startupMark)
	return o.UnLockGpus(startupMark, "ManualUnLockGpu")
}

func (o *Virtual) ExistsLockGpus(gpuIndex int) (bool, error) {
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedGpuLock, o.ID)
	if exists, err := common.RedisHExists(key, utils.Int2String(gpuIndex)); err != nil {
		logger.Error(err)
		return exists, err
	} else {
		return exists, nil
	}
}

func (o *Virtual) LockGpus(ctx context.Context, startupMark string, needGpus int) error {
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedGpuLock, o.ID)
	lockKey := fmt.Sprintf("%svirtual:%d", enums.RedisKeyEnum.LockKey, o.ID)
	if common.RedisLock(lockKey, 1000*10, 1000*20) {
		defer common.RedisUnLock(lockKey)
		if mm, err := common.RedisHGetAll(key); err != nil {
			logger.Error(err)
			return err
		} else {
			if needGpus == 0 {
				for i := 30; i <= 37; i++ {
					field := strconv.Itoa(i)
					if _, ok := mm[field]; !ok {
						lockGpuItem := LockGpuItem{
							Index:       i,
							NeedGpus:    0,
							StartupMark: startupMark,
							LockedAt:    time.Now(),
						}
						json := utils.GetJsonFromStruct(lockGpuItem)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "无卡锁定:"+json, nil)
						if _, err := common.RedisHSet(key, field, json); err != nil {
							logger.Error(err)
							return err
						} else {
							return nil
						}
					}
				}
			} else {
				gpus := make([]int, 0)
				for i := 0; i < len(o.Gpus); i++ {
					field := strconv.Itoa(i)
					if _, ok := mm[field]; !ok {
						gpus = append(gpus, i)
						if len(gpus) == needGpus {
							break
						}
					}
				}
				if len(gpus) < needGpus {
					logger.Error("锁定失败，空余Gpu不足 virtualId：", o.ID, " needGpus:", needGpus)
					return errors.New("锁定失败，空余Gpu不足")
				} else {
					for _, gpuIndex := range gpus {
						field := strconv.Itoa(gpuIndex)
						lockGpuItem := LockGpuItem{
							Index:       gpuIndex,
							NeedGpus:    needGpus,
							StartupMark: startupMark,
							LockedAt:    time.Now(),
						}
						json := utils.GetJsonFromStruct(lockGpuItem)

						if _, err := common.RedisHSet(key, field, json); err != nil {
							tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "Redis锁定Gpu失败:"+json, nil)
							logger.Error(err)
							return err
						} else {
							tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "Redis锁定Gpu成功:"+json, nil)
						}
					}
					return nil
				}
			}
			return errors.New("无卡锁定失败")
		}
	} else {
		logger.Error("Key锁定中 logkey:", key)
		return errors.New("Key锁定中")
	}
}

func (o *Virtual) CacheShutdown(ctx context.Context, startupMark string, containerId string) error {
	if startupMark == "" || containerId == "" {
		return errors.New("参数为空")
	}
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedShutdown, o.ID)
	cacheShutdownItem := CacheShutdownItem{
		StartupMark: startupMark,
		ContainerId: containerId,
		KeepSeconds: common.CacheShutdownKeepSeconds,
		CacheAt:     time.Now().Unix(),
	}
	json := utils.GetJsonFromStruct(cacheShutdownItem)
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "缓存关机:"+json, nil)
	if _, err := common.RedisHSet(key, startupMark, json); err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func (o *Virtual) SetCacheShutdownKeep(ctx context.Context, startupMark string, keepSeconds int64) error {
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedShutdown, o.ID)

	if val, err := common.RedisHGet(key, startupMark); err != nil {
		logger.Error(err)
		return err
	} else {
		if val == "" {
			return errors.New("val未空")
		}
		var cacheShutdownItem CacheShutdownItem
		if err := utils.GetStructFromJson(&cacheShutdownItem, val); err != nil {
			logger.Error(err)
			return err
		} else {
			cacheShutdownItem.KeepSeconds = keepSeconds
			json := utils.GetJsonFromStruct(cacheShutdownItem)
			if _, err := common.RedisHSet(key, startupMark, json); err != nil {
				logger.Error(err)
				return err
			}
		}
	}
	return nil
}

func (o *Virtual) ExistsCacheShutdown(startupMark string) (bool, error) {
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedShutdown, o.ID)
	if exists, err := common.RedisHExists(key, startupMark); err != nil {
		logger.Error(err)
		return exists, err
	} else {
		return exists, nil
	}
}

func (o *Virtual) GetCacheShutdowns() (map[string]CacheShutdownItem, error) {
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedShutdown, o.ID)
	tmpMap := make(map[string]CacheShutdownItem)
	if mm, err := common.RedisHGetAll(key); err != nil {
		logger.Error(err, " key:", key)
		return tmpMap, err
	} else {
		for _, val := range mm {
			var cacheShutdownItem CacheShutdownItem
			if err := utils.GetStructFromJson(&cacheShutdownItem, val); err != nil {
				logger.Error(err)
				return tmpMap, err
			} else {
				tmpMap[cacheShutdownItem.StartupMark] = cacheShutdownItem
			}
		}
	}
	return tmpMap, nil
}

func (o *Virtual) UnCacheShutdown(startupMark string) error {
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedShutdown, o.ID)
	if _, err := common.RedisHDel(key, startupMark); err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func (o *Virtual) OutTimeCacheShutdown(startupMark string) (bool, error) {
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedShutdown, o.ID)
	if val, err := common.RedisHGet(key, startupMark); err != nil {
		logger.Error(err)
		return false, err
	} else {
		if val == "" {
			return true, nil
		}
		var cacheShutdownItem CacheShutdownItem
		if err := utils.GetStructFromJson(&cacheShutdownItem, val); err != nil {
			logger.Error(err)
			return false, err
		} else {
			checkUnix := time.Now().Unix()
			if cacheShutdownItem.CacheAt+cacheShutdownItem.KeepSeconds > checkUnix {
				return false, nil
			}
			return true, nil
		}
	}
}

func (o *Virtual) ClearCacheShutdown(ctx context.Context) error {
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedShutdown, o.ID)
	trace := "ClearCacheShutdown host:" + o.Host + " "
	if mm, err := common.RedisHGetAll(key); err != nil {
		logger.Error(trace, " err:", err)
		return err
	} else {
		for _, val := range mm {
			if val == "" {
				logger.Error(trace, " val为空")
				continue
			}
			var cacheShutdownItem CacheShutdownItem
			if err := utils.GetStructFromJson(&cacheShutdownItem, val); err != nil {
				logger.Error(trace, " err:", err, "  val:", val)
				return err
			} else {
				trace = "ClearCacheShutdown host:" + o.Host + "   startupMark:" + cacheShutdownItem.StartupMark + " dockerId:" + cacheShutdownItem.ContainerId + " "
				if cacheShutdownItem.StartupMark == "" || cacheShutdownItem.ContainerId == "" {
					logger.Error(trace, " 空结构体val:"+val)
				}
				trace = "ClearCacheShutdown host:" + o.Host + "   startupMark:" + cacheShutdownItem.StartupMark + " dockerId:" + cacheShutdownItem.ContainerId + " "
				dockerId := cacheShutdownItem.ContainerId
				checkUnix := time.Now().Unix()
				if cacheShutdownItem.CacheAt+cacheShutdownItem.KeepSeconds > checkUnix {
					//logger.Error(trace, " 时间还未到")
					continue //还未到移除时间
				}

				if err := o.RmDocker(ctx, dockerId); err != nil {
					logger.Error(trace, " err:", err)
					return err
				} else {
					/*
						ports := make([]string, 0)
						if ary, err := o.PortDocker(ctx, dockerId); err != nil {
							logger.Error(trace, " err:", err)
						} else {
							ports = ary
							if len(ports) == 0 {
								if ary1, err := o.PortDockerItem(ctx, dockerId); err != nil {
									logger.Error(trace, " err:", err)
								} else {
									ports = ary1
								}
							}
						}
						if len(ports) > 0 {
							if err := o.ClearBindPort(ctx, ports); err != nil {
								logger.Error(trace, " err:", err, "   ports:", ports)
							} else {
								logger.Info(trace, "清除端口完成 ports:"+utils.GetJsonFromStruct(ports))
							}
						}*/

					if err := o.UnCacheShutdown(cacheShutdownItem.StartupMark); err != nil {
						logger.Error(trace, " err:", err)
					} else {
						logger.Info(trace, " UnCacheShutdown完成")
					}
				}
			}
		}
	}
	return nil
}

func (o *Virtual) LocalImages(ctx context.Context) ([]DockerImageItem, error) {
	pre := fmt.Sprintf("%s(%d) ", o.Host, o.ID)
	mLocalImage, err1 := o.ListLocalImage()
	if err1 != nil {
		logger.Error("mLocalImage err:", err1)
		return nil, err1
	}

	mImage := make(map[string]DockerImageItem)
	if images, err := o.DockerImagesUseApi(ctx, ""); err != nil {
		logger.Error(err)
		return nil, err
	} else {
		for i := 0; i < len(images); i++ {
			image := images[i]
			if image.ID == "" {
				logger.Error(pre, "mage.ID is empty json:", utils.GetJsonFromStruct(image))
				continue
			}

			if localImage, ok := mLocalImage[image.ID]; ok {
				images[i].LastUseUnix = localImage.LastUseUnix
				images[i].ImageId = localImage.ImageId
			} else {
				images[i].LastUseUnix = images[i].Created
			}

			mImage[image.ID] = images[i]
		}

		//按照 LastUseUnix 从小到大排序
		sort.Slice(images, func(i, j int) bool {
			return images[i].LastUseUnix < images[j].LastUseUnix
		})

		for key, _ := range mLocalImage {
			if _, ok := mImage[key]; !ok {
				if _, err2 := common.RedisHDel(key, key); err != nil {
					logger.Error(pre, "SetLocalImage移除失败 err:", err2)
				}
			}
		}
		return images, nil
	}
}

func (o *Virtual) ClearLocalImage(ctx context.Context) error {
	pre := fmt.Sprintf("%s(%d) ", o.Host, o.ID)
	if images, err := o.LocalImages(ctx); err != nil {
		logger.Error(err)
		return err
	} else {
		myMap := make(map[string]DockerImageItem)
		removeAry := make([]DockerImageItem, 0)
		privateRemoveAry := make([]DockerImageItem, 0)
		publicRemoveAry := make([]DockerImageItem, 0)

		//按照 Created 从大到小排序
		sort.Slice(images, func(i, j int) bool {
			return images[i].Created > images[j].Created
		})

		for _, image := range images {
			isPrivate := false
			checkUnix := time.Now().Add(time.Hour * 24 * 6 * -1).Unix() //公共镜像6天没使用才尝试删除
			if strings.Contains(image.HubImagePath, "/private/") {
				checkUnix = time.Now().Add(time.Hour * 24 * 2 * -1).Unix() //个人镜像2天没使用就删除
				isPrivate = true
			}
			if image.HubImagePath == "" {
				checkUnix = time.Now().Unix()
			}
			if image.LastUseUnix < checkUnix {
				if image.ID != "" {
					myMap[image.ID] = image
					removeAry = append(removeAry, image)
					if image.HubImagePath == "" {
						privateRemoveAry = append(privateRemoveAry, image)
						continue
					}
					if isPrivate {
						privateRemoveAry = append(privateRemoveAry, image)
					} else {
						publicRemoveAry = append(publicRemoveAry, image)
					}
				}
			}
		}
		logger.Info(pre, "检索到", len(myMap), "个镜像需要清理，开始清理镜像")
		deleteCount := 0

		for test := 0; test < 10; test++ {
			for i := 0; i < len(privateRemoveAry); i++ {
				image := privateRemoveAry[i]
				if _, ok := myMap[image.ID]; !ok {
					continue
				}
				if _, err := o.DockerImageRemoveUseApi(ctx, image.ID); err != nil {
					logger.Error(pre, " 第", i, "次 移除镜像失败 imageId:", image.ID, " err：", err, "   LastUseUnix:", image.LastUseUnix)
				} else {
					deleteCount++
					delete(myMap, image.ID)
					logger.Info(pre, " 第", i, "次 移除镜像完成 imageId:", image.ID, " LastUseUnix:", image.LastUseUnix)
				}
			}
		}
		logger.Info(pre, "清理了", deleteCount, "个个人镜像")
		{
			time.Sleep(time.Second * 3)
			if space, err := o.DockerSpace(ctx); err != nil {
				logger.Error(pre, "清理个人镜像后获取磁盘空间失败 err:", err)
			} else {
				if space.AvailG >= 500 {
					logger.Info(pre, "清理完个人镜像后，当前空间为", space.AvailG, "已大于500G,退出清理")
					return nil
				} else {
					logger.Info(pre, "清理完个人镜像后，当前空间为", space.AvailG, "未大于500G,需要继续清理")
				}
			}
		}

		if ctx != nil {
			if task, ok := ctx.Value("task").(string); ok {
				if task == "JustClearPrivateImage" {
					logger.Info(pre, "清理了", deleteCount, "个个人镜像,仅清理个人镜像，结束清理 task:JustClearPrivateImage")
					return nil
				}
			}
		}

		//for i := 0; i < len(removeAry); i++ {
		//	image := removeAry[i]
		//	if _, err := o.DockerImageRemoveUseApi(ctx, image.ID); err != nil {
		//		logger.Error(pre, " 第", i, "次 移除镜像失败 imageId:", image.ID, " err：", err, "   LastUseUnix:", image.LastUseUnix)
		//	} else {
		//		deleteCount++
		//		delete(myMap, image.ID)
		//		logger.Info(pre, " 第", i, "次 移除镜像完成 imageId:", image.ID, " LastUseUnix:", image.LastUseUnix)
		//	}
		//}
		for i := 0; i < 10; i++ {
			hasRemove := false
			for key, image := range myMap {
				if _, err := o.DockerImageRemoveUseApi(ctx, image.ID); err != nil {
					logger.Error(pre, " 第", i, "次 移除镜像失败 imageId:", image.ID, " err：", err, "   LastUseUnix:", image.LastUseUnix)
				} else {
					hasRemove = true
					deleteCount++
					delete(myMap, key)
					logger.Info(pre, " 第", i, "次 移除镜像完成 imageId:", image.ID, " LastUseUnix:", image.LastUseUnix)
				}
			}
			if hasRemove == false {
				logger.Info(pre, " 第", i, "次 没有移除成功的镜像 退出循环")
				break
			}

			if space, err := o.DockerSpace(ctx); err != nil {
				logger.Error(pre, "清理镜像后获取磁盘空间失败 err:", err)
			} else {
				if space.AvailG >= 500 {
					logger.Info(pre, " 第", i+1, "次清理完镜像后，当前空间为", space.AvailG, "已大于500G,退出清理")
					logger.Info(pre, "清理完成，清理了", deleteCount, "个镜像，目前剩余", len(myMap), "个镜像")
					return nil
				} else {
					logger.Info(pre, " 第", i+1, "次清理完镜像后，当前空间为", space.AvailG, "未大于500G,需要继续清理")
				}
			}
		}
		logger.Info(pre, "清理完成，清理了", deleteCount, "个镜像，目前剩余", len(myMap), "个镜像")
	}
	return nil
}

func (o *Virtual) SetLocalImage(dockerImage DockerImageItem) error {
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedLocalImage, o.ID)
	if json := utils.GetJsonFromStruct(dockerImage); json == "" {
		err := errors.New("json为空")
		logger.Error(err)
		return err
	} else {
		if dockerImage.ID == "" {
			err := errors.New("镜像ID为空")
			logger.Error(err, " json:", json)
			return err
		}
		if _, err := common.RedisHSet(key, dockerImage.ID, json); err != nil {
			logger.Error(err)
			return err
		} else {
			return nil
		}
	}
}

func (o *Virtual) ListLocalImage() (map[string]DockerImageItem, error) {
	key := fmt.Sprintf("%s%d", enums.RedisKeyEnum.CpnSchedLocalImage, o.ID)
	outMm := make(map[string]DockerImageItem)
	if mm, err := common.RedisHGetAll(key); err != nil {
		logger.Error(err)
		return outMm, err
	} else {
		for _, value := range mm {
			var tmp DockerImageItem
			if err := utils.GetStructFromJson(&tmp, value); err != nil {
				logger.Error(err)
			} else {
				outMm[tmp.ID] = tmp
			}
		}
		// 按照 LastUseUnix 从小到大排序
		//sort.Slice(list, func(i, j int) bool {
		//	return list[i].LastUseUnix < list[j].LastUseUnix
		//})
	}
	return outMm, nil
}

func (o *Virtual) SaveToRedis() error {
	if o.ID == 0 {
		return errors.New("虚拟机ID为0")
	}

	field := fmt.Sprintf("%d", o.ID)
	json := utils.GetJsonFromStruct(o)
	if json == "" {
		return errors.New("序列化为空")
	}

	if _, err := common.RedisHSet(enums.RedisKeyEnum.CpnSchedVirtual, field, json); err != nil {
		logger.Error(err)
		return err
	} else {
		return nil
	}
}

func (o *Virtual) GetFromRedis() (Virtual, error) {
	field := fmt.Sprintf("%d", o.ID)
	var virtual Virtual
	//if json, err := common.RedisHGet(enums.RedisKeyEnum.CpnSchedControlVirtual, o.HostPort); err != nil {
	if json, err := common.RedisHGet(enums.RedisKeyEnum.CpnSchedVirtual, field); err != nil {
		logger.Error(err)
		return virtual, err
	} else {
		if json == "" {
			return virtual, errors.New("Redis is empty")
		}
		if err := utils.GetStructFromJson(&virtual, json); err != nil {
			logger.Error(err)
			return virtual, err
		}
		if virtual.ID == 0 {
			logger.Error("not virtual string virtualID:", o.ID, "   redis string:", json)
			return virtual, errors.New("not virtual string")
		}

		return virtual, nil
	}
}

func (o *Virtual) ExecCommand(command string) (string, error) {
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: command, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()
	// 创建一个新的会话
	if o.SshClient == nil {
		err := errors.New("sshclient is nil")
		logger.Error(err)
		return "", err
	}
	session, err := o.SshClient.NewSession()
	if err != nil {
		logger.Error(err, command) //read tcp *************:59422->***************:22: use of closed network connection
		return "", err
	}
	defer session.Close()

	// 设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	type result struct {
		output []byte
		err    error
	}
	resultChan := make(chan result, 1)

	go func() {
		output, err := session.CombinedOutput(command)
		resultChan <- result{output, err}
	}()

	select {
	case <-ctx.Done():
		session.Close()      // 超时则关闭session
		return "", ctx.Err() // 返回超时错误
	case res := <-resultChan:
		if res.err != nil {
			logger.Error(res.err, " command:", commandLog)
			return "", res.err
		}
		outputStr := string(res.output)
		return outputStr, nil
	}
}

func (o *Virtual) ExecCommandPro(ctx context.Context, command string) (string, error) {
	doneChan := make(chan bool)
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: command, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	bTimeOut := false
	defer func() {
		//logger.Info(commandLog, " doneChan 》")
		if !bTimeOut {
			doneChan <- true
		}
		//logger.Info(commandLog, " doneChan 置为true")
		//close(doneChan)
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()
	runningMsg := ""
	dockerCommand := ""

	//testUser := false
	//if strings.Contains(command, ":2}") {
	//	testUser = true
	//}

	// 创建一个新的会话
	if o.SshClient == nil {
		err := errors.New("sshclient is nil")
		logger.Error(err)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, runningMsg, "err："+err.Error(), nil)
		return "", err
	}
	session, err := o.SshClient.NewSession()
	if err != nil {
		logger.Error(err, command)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, runningMsg, "err："+err.Error(), nil)
		return "", err
	}
	defer func() {
		session.Close()
	}()
	//defer session.Close()
	if strings.HasPrefix(command, "docker run") {
		dockerCommand = enums.DockerCommandEnum.Run
		runningMsg = "容器启动中"
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "Command命令："+command, nil)

		startupMark := ""
		if val, ok := ctx.Value("startup_mark").(string); ok {
			startupMark = val
		}
		// 启动一个 goroutine 来间断性输出日志
		go func() {
			ticker := time.NewTicker(1 * time.Second * 5) // 每一分钟输出一次日志
			defer ticker.Stop()
			//timeout := time.After(30 * time.Minute)
			timeout := time.After(10 * time.Minute)
			for {
				select {
				case <-ticker.C:

					current := int64(0)
					total := int64(100)
					percent := float64(0)
					if total > 0 {
						percent = float64(current) / float64(total)
					}
					taskProgress := tasklog.TaskProgress{
						Status:      "DockerRun",
						CoverStatus: "DockerRun",
						Current:     current,
						Total:       total,
						Percent:     percent,
					}
					//txt := fmt.Sprintf("Progress:%.2f%%  %d/%d", percent*100, current, total)
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "每一分钟输出一次日志", taskProgress)

					if startupMark != "" {
						abortRedisKey := enums.RedisKeyEnum.LockKey + "StartupAbort:" + startupMark
						if abortTime, _ := common.RedisGet(abortRedisKey); abortTime != "" {
							tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在停止启动", abortRedisKey+" context canceled abortTime:"+abortTime, nil)

							bTimeOut = true
							logger.Info("Goroutine 取消，退出")
							if err1 := o.CloseSsh("ExecCommandPro Cancel"); err1 != nil {
								// o.SshClient.Close()
								logger.Error("o.CloseSsh(\"ExecCommandPro\") err:", err1)
								tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在停止启动", "启动取消 结束session失败 err:"+err1.Error(), nil)
							} else {
								tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在停止启动", "启动取消 o.CloseSsh成功", nil)
								return
							}
						}
					}

					//tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "3秒输出一次日志", nil)
				case <-timeout:
					bTimeOut = true
					logger.Info("Goroutine 超时，退出")
					if err1 := o.CloseSsh("ExecCommandPro TimeOut"); err1 != nil {
						// o.SshClient.Close()
						logger.Error("o.CloseSsh(\"ExecCommandPro\") err:", err1)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "超时30分钟退出 结束session失败 err:"+err1.Error(), nil)
					} else {
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "超时30分钟退出 o.CloseSsh成功", nil)
					}
					//if session != nil {
					//	if err1 := o.CloseSsh("ExecCommandPro"); err1 != nil {
					//		// o.SshClient.Close()
					//		logger.Error("session.Signal(ssh.SIGKILL) err:", err1)
					//		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "超时30分钟退出 结束session失败 err:"+err1.Error(), nil)
					//	} else {
					//		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "超时30分钟退出 结束session成功", nil)
					//	}
					//} else {
					//	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "超时30分钟退出 session为空，不处理", nil)
					//}

					return
				case <-doneChan:
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "收到信道退出信号", nil)
					return
				}
			}
		}()
	} else if strings.HasPrefix(command, "docker push") {
		dockerCommand = enums.DockerCommandEnum.Push
		runningMsg = "镜像推送中"
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像推送中", "Command命令："+command, nil)
	} else if strings.HasPrefix(command, "docker commit") {
		dockerCommand = enums.DockerCommandEnum.Commit
		runningMsg = "镜像提交中"
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像提交中", "Command命令："+command, nil)
	} else if strings.HasPrefix(command, "docker save") {
		dockerCommand = enums.DockerCommandEnum.Save
		runningMsg = "镜像保存中"
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像保存中", "Command命令："+command, nil)
	} else {
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "命令执行中", "Command命令："+command, nil)
	}

	commandMd5 := utils.GetMd5(command)
	logger.Info(commandMd5, " run command:", command)
	// 执行远程命令
	stdout, err := session.StdoutPipe()
	if err != nil {
		logger.Error(err)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, runningMsg, "err："+err.Error(), nil)
		return "", err
	}
	scannerStdOut := bufio.NewScanner(stdout)

	stderr, err := session.StderrPipe()
	if err != nil {
		logger.Error(err)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, runningMsg, "err："+err.Error(), nil)
		return "", err
	}

	err = session.Start(command)
	//err = session.Start("sleep infinity")
	if err != nil {
		logger.Error(err)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, runningMsg, "err："+err.Error(), nil)
		return "", err
	}

	aryCheck := []string{"Pulling fs layer", "Already exists", "Waiting", "Verifying Checksum", "Download complete", "Pull complete"}
	//Run： Unable to find image
	//
	//Push： Layer already exists    Waiting   Preparing
	//fa5793fb10c8: Pushed
	//v2.2: digest: sha256:51f263c94545f686eaa0dcf5c8fb5fc729ec13990f75ce55d3f76bc547f3ecc0 size: 3905
	var stdoutLines []string
	var stdLines []string
	go func() {
		//scanner := bufio.NewScanner(stdout)
		for scannerStdOut.Scan() {
			show := runningMsg
			txt := scannerStdOut.Text()
			if runningMsg == "容器启动中" {
				for _, val := range aryCheck {
					if strings.Contains(txt, val) {
						show = "正在获取镜像"
					}
				}
			}
			logger.Info("stdout：" + txt)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, show, "stdout："+txt, nil)
			stdoutLines = append(stdoutLines, txt)
			stdLines = append(stdLines, txt)
			if bTimeOut {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, show, "stdout："+"bTimeOut超时退出", nil)
				return
			}
		}
	}()

	var stderrLines []string
	go func() {
		scanner := bufio.NewScanner(stderr)
		for scanner.Scan() {
			show := runningMsg
			txt := scanner.Text()
			if runningMsg == "容器启动中" {
				for _, val := range aryCheck {
					if strings.Contains(txt, val) {
						show = "正在获取镜像"
					}
				}
			}
			logger.Info("stderr：" + txt)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, show, "stderr："+txt, nil)
			stderrLines = append(stderrLines, txt)
			stdLines = append(stdLines, txt)
			if bTimeOut {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, show, "stderr："+"bTimeOut超时退出", nil)
				return
			}
		}
	}()

	err = session.Wait()
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "session.Wait() 已执行", nil)
	if err != nil {
		logger.Error(err)
		if dockerCommand == enums.DockerCommandEnum.Run {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动失败", err.Error(), nil)
		} else if dockerCommand == enums.DockerCommandEnum.Commit {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像提交失败", err.Error(), nil)
		} else if dockerCommand == enums.DockerCommandEnum.Push {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像推送失败", err.Error(), nil)
		} else {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "命令执行失败", err.Error(), nil)
		}
		logger.Error("after session.Wait() err:", err)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "session.Wait() 已执行，开始返回", nil)
		return "", err
	}

	// 等待命令执行完毕
	outputStr := "Command executed completed"
	if len(stderrLines) > 0 {
		outputStr = stderrLines[len(stderrLines)-1]
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "stderrLines-outputStr："+outputStr, nil)
	}
	if len(stdoutLines) > 0 {
		outputStr = stdoutLines[len(stdoutLines)-1]
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "stdoutLines-outputStr："+outputStr, nil)
	}
	if len(stdLines) > 0 {
		outputStr = stdLines[len(stdLines)-1]
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "stdLines-outputStr："+outputStr, nil)
	}
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "outputStr："+outputStr, nil)
	logger.Info(commandMd5, " run output:   ", outputStr)
	return outputStr, nil
}

func (o *Virtual) ExecCommandWithLog(logKey string, command string) (string, error) {
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	commandRunningValue := structs.RunningCommand{VirtualId: o.ID, VirtualHost: o.Host, Command: command, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	NodeService.CommandRunning.Store(commandKey, commandRunningValue)
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()
	runningMsg := ""
	dockerCommand := ""
	if strings.HasPrefix(command, "docker run") {
		dockerCommand = enums.DockerCommandEnum.Run
		runningMsg = "容器启动中"
		StartupLog.Save(logKey, "容器启动中", "Command命令："+command)
	} else if strings.HasPrefix(command, "docker push") {
		dockerCommand = enums.DockerCommandEnum.Push
		runningMsg = "镜像推送中"
		StartupLog.Save(logKey, "镜像推送中", "Command命令："+command)
	} else if strings.HasPrefix(command, "docker commit") {
		dockerCommand = enums.DockerCommandEnum.Push
		runningMsg = "镜像提交中"
		StartupLog.Save(logKey, "镜像提交中", "Command命令："+command)
	} else {
		StartupLog.Save(logKey, "", "Command命令："+command)
	}

	// 创建一个新的会话
	if o.SshClient == nil {
		err := errors.New("sshclient is nil")
		logger.Error(err)
		StartupLog.Save(logKey, "", "err："+err.Error())
		return "", err
	}
	session, err := o.SshClient.NewSession()
	if err != nil {
		logger.Error(err, command)
		StartupLog.Save(logKey, "", "err："+err.Error())
		return "", err
	}
	defer session.Close()
	commandMd5 := utils.GetMd5(command)
	logger.Info(commandMd5, "logKey:", logKey, " run command:", command)
	// 执行远程命令
	stdout, err := session.StdoutPipe()
	if err != nil {
		logger.Error(err)
		StartupLog.Save(logKey, "", "err："+err.Error())
		return "", err
	}

	stderr, err := session.StderrPipe()
	if err != nil {
		logger.Error(err)
		StartupLog.Save(logKey, "", "err："+err.Error())
		return "", err
	}

	err = session.Start(command)
	if err != nil {
		logger.Error(err)
		StartupLog.Save(logKey, "", "err："+err.Error())
		return "", err
	}

	aryCheck := []string{"Pulling fs layer", "Already exists", "Waiting", "Verifying Checksum", "Download complete", "Pull complete"}
	//Run： Unable to find image
	//
	//Push： Layer already exists    Waiting   Preparing
	//fa5793fb10c8: Pushed
	//v2.2: digest: sha256:51f263c94545f686eaa0dcf5c8fb5fc729ec13990f75ce55d3f76bc547f3ecc0 size: 3905
	var stdoutLines []string
	var stdLines []string
	go func() {
		scanner := bufio.NewScanner(stdout)
		for scanner.Scan() {
			show := runningMsg
			txt := scanner.Text()
			if runningMsg == "容器启动中" {
				for _, val := range aryCheck {
					if strings.Contains(txt, val) {
						show = "正在获取镜像"
					}
				}
			}

			StartupLog.Save(logKey, show, "stdout："+txt)
			stdoutLines = append(stdoutLines, txt)
			stdLines = append(stdLines, txt)
		}
	}()

	var stderrLines []string
	go func() {
		scanner := bufio.NewScanner(stderr)
		for scanner.Scan() {
			show := runningMsg
			txt := scanner.Text()
			if runningMsg == "容器启动中" {
				for _, val := range aryCheck {
					if strings.Contains(txt, val) {
						show = "正在获取镜像"
					}
				}
			}
			StartupLog.Save(logKey, show, "stderr："+txt)
			stderrLines = append(stderrLines, txt)
			stdLines = append(stdLines, txt)
		}
	}()

	err = session.Wait()
	if err != nil {
		logger.Error(err)
		if dockerCommand == enums.DockerCommandEnum.Run {
			StartupLog.Save(logKey, "容器启动失败", err.Error())
		} else if dockerCommand == enums.DockerCommandEnum.Commit {
			StartupLog.Save(logKey, "镜像提交失败", err.Error())
		} else if dockerCommand == enums.DockerCommandEnum.Push {
			StartupLog.Save(logKey, "镜像推送失败", err.Error())
		} else {
			StartupLog.Save(logKey, "命令执行失败", err.Error())
		}

		return "", err
	}

	// 等待命令执行完毕
	outputStr := "Command executed completed"
	if len(stderrLines) > 0 {
		outputStr = stderrLines[len(stderrLines)-1]
		StartupLog.Save(logKey, "", "stderrLines-outputStr："+outputStr)
	}
	if len(stdoutLines) > 0 {
		outputStr = stdoutLines[len(stdoutLines)-1]
		StartupLog.Save(logKey, "", "stdoutLines-outputStr："+outputStr)
	}
	if len(stdLines) > 0 {
		outputStr = stdLines[len(stdLines)-1]
		StartupLog.Save(logKey, "", "stdLines-outputStr："+outputStr)
	}
	StartupLog.Save(logKey, "", "outputStr："+outputStr)
	logger.Info(commandMd5, " run output:   ", outputStr)
	return outputStr, nil
}

func (o *Virtual) getDockerLabels(dockerInfo map[string]interface{}) map[string]interface{} {
	labels := make(map[string]interface{})
	if _, ok := dockerInfo["Labels"]; !ok {
		return labels
	}
	//"Labels": "ssh_port=40030,gpus=3,host_port=*************:2217,maintainer=NVIDIA CORPORATION <<EMAIL>>,org.opencontainers.image.ref.name=ubuntu,org.opencontainers.image.version=20.04,pod_name=Llama2-Chinese-7b-Chat"
	lableStr := dockerInfo["Labels"].(string)

	aryLabel := strings.Split(lableStr, ",")

	for i := len(aryLabel) - 1; i > 0; i-- { //处理值中带,的情况
		if !strings.Contains(aryLabel[i], "=") {
			if i > 0 {
				aryLabel[i-1] = aryLabel[i-1] + "," + aryLabel[i]
			}
		}
	}

	//logger.Info("aryLabel：", aryLabel)

	for _, val := range aryLabel {
		ary := strings.Split(val, "=")
		if len(ary) == 2 {
			switch ary[0] {
			case "instance_id":
				labels["instance_id"] = utils.String2Uint(ary[1])
			case "instance_uuid":
				labels["instance_uuid"] = ary[1]
			case "startup_mark":
				labels["startup_mark"] = ary[1]
			case "startup_parm":
				var tmp structs.StartupParm
				//logger.Info("startup_parm:", ary[1])
				if err := utils.GetStructFromJson(&tmp, ary[1]); err != nil {
					logger.Error(err, ary[1])
				}
				labels["startup_parm"] = tmp
			case "pod_id":
				labels["pod_id"] = utils.String2Uint(ary[1])
			case "pod_category":
				labels["pod_category"] = utils.String2Int(ary[1])
			case "pod_name":
				labels["pod_name"] = ary[1]
			case "image_id":
				labels["image_id"] = utils.String2Uint(ary[1])
			case "image_type":
				labels["image_type"] = utils.String2Int(ary[1])
			case "gpus":
				gpus := make([]int, 0)
				ary1 := strings.Split(ary[1], "_")
				for _, v := range ary1 {
					if v == "" {
						continue
					}
					gpus = append(gpus, utils.String2Int(v))
				}
				labels["gpus"] = gpus
			case "host":
				labels["host"] = ary[1]
			case "map_pref":
				labels["map_pref"] = ary[1]
			case "map_ports":
				labels["map_ports"] = ary[1]
				mapPorts := make([]string, 0)
				ary1 := strings.Split(ary[1], "_")
				for _, v := range ary1 {
					mapPorts = append(mapPorts, v)
				}
				labels["map_ports"] = mapPorts
			case "host_port":
				labels["host_port"] = ary[1]
			case "ssh_port":
				labels["ssh_port"] = ary[1]
			}
		}
	}

	return labels
}
