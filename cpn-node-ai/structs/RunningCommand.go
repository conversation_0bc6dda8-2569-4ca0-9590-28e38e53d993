package structs

import "cpn-ai/common/jsontime"

type RunningCommand struct {
	LogKey      string            `json:"log_key,omitempty"`
	StartupMark string            `json:"startup_mark,omitempty"`
	PodId       uint              `json:"pod_id,omitempty"`
	ImageId     uint              `json:"image_id,omitempty"`
	VirtualId   uint              `json:"virtual_id"`
	VirtualHost string            `json:"virtual_host"`
	UuId        string            `json:"uu_id,omitempty"`
	ImageSizeG  float64           `json:"image_size_g,omitempty"`
	UserId      uint              `json:"user_id,omitempty"`
	Command     string            `json:"command"`
	StartTime   jsontime.JsonTime `json:"start_time"`
}
