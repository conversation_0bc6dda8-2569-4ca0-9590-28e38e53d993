package main

import (
	"bytes"
	"context"
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/service"
	"cpn-ai/service/tasklog"
	"cpn-ai/structs"
	"crypto"
	"crypto/md5"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/hex"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"net/http"
	url2 "net/url"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
)

func GetMapPorts(input string) string {
	//if modelHash == "" {
	//	return ""
	//}

	input = `docker run -p 1{==={GpuIndex}===}022:22 -p 1{==={GpuIndex}===}800:8000 -v /root/aigc-models/llms`
	// 创建正则表达式匹配模式
	//re := regexp.MustCompile(fmt.Sprintf(`0(\d+):\d+,`, input))
	re := regexp.MustCompile(`1\{===\{GpuIndex\}===\}(\d+):\d+`)
	// 查找匹配的字符串
	matches := re.FindAllStringSubmatch(input, -1)
	ary := make([]string, 0)
	for _, match := range matches {
		if len(match) >= 2 {
			ary = append(ary, match[1])
			return match[1]
		}
	}
	return ""
}

type QueueItem struct {
	Action string
	Data   interface{}
}

func getMd5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

func RestartProject13() {
	projectName := "cpn_node_hz04"
	// API接口地址
	//url := "http://192.168.200.25:8888/project/go/restart_project"
	////url := "http://192.168.200.20:2588/project/go/restart_project"
	////url := "http://127.0.0.1:8888/project/go/restart_project"
	//if config.Env == enums.EnvEnum.DEV {
	//	url = "http://117.187.188.4:2588/project/go/restart_project"
	//}
	//url := "http://192.168.200.13:17491/project/go/restart_project"
	url := "http://192.168.200.13:17491/project/go/restart_project"
	//url = "http://192.168.200.4:42130/project/go/restart_project"
	logger.Info("开始重启项目：", projectName, " url  ", url)
	// 获取当前时间戳，单位为毫秒
	timestamp := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	// API接口加密字符串，需要在宝塔面板中生成
	//btSign := "kq5ZJjwaRSO6EYotv0821eckkCoA63sp"
	btSign := "NQpWxe5l3pY3Az83c5cmzSeVUqDN9qCC"
	// 计算MD5加密签名
	md5Sign := getMd5(btSign)
	// 构造请求参数
	requestParams := "request_time=" + timestamp + "&request_token=" + getMd5(timestamp+md5Sign)

	param := fmt.Sprintf(`{"project_name":"%s"}`, projectName)
	requestParams += "&data=" + url2.QueryEscape(param)
	//requestParams += "&data=%7B%22project_name%22%3A%22aigc_api_4005%22%7D"
	// 发送POST请求
	logger.Info("发送重启请求：", projectName, " url  ", url)
	resp, err := http.Post(url, "application/x-www-form-urlencoded", bytes.NewReader([]byte(requestParams)))
	if err != nil {
		logger.Error(err)
		return
	}
	defer resp.Body.Close()
	// 解析响应数据
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Info("重启相应数据：", string(respBody))
	return
}

func main() {
	if 1 == 1 {
		//RestartProject13()
		if err := common.InitRedisClient(); err != nil {
			common.FatalLog("failed to initialize Redis: " + err.Error())
		}

		ctx := context.Background()
		if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(12345)); err != nil {
			logger.Error(err)
		} else {
			ctx = context.WithValue(ctx, "logkey", logKey)
			ctx = context.WithValue(ctx, "task", tasklog.TaskEnum.PullImage)
			tasklog.Delete(logKey)
		}
		taskProgress := tasklog.TaskProgress{
			Status:      "Downloading",
			CoverStatus: "Downloading",
			Current:     100,
			Total:       1000,
		}
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像推送成功", "logTxt", taskProgress)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Success, "镜像保存成功", "logTxt", taskProgress)
		tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Success, "镜像保存成功", "logTxt", taskProgress)
		fmt.Println(1)
	}
	if 1 == 2 {
		//RestartProject13()
		if err := common.InitRedisClient(); err != nil {
			common.FatalLog("failed to initialize Redis: " + err.Error())
		}

		ctx := context.Background()
		if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.PullImage, utils.Uint2String(11)); err != nil {
			logger.Error(err)
		} else {
			ctx = context.WithValue(ctx, "logkey", logKey)
			ctx = context.WithValue(ctx, "task", tasklog.TaskEnum.PullImage)
			tasklog.Delete(logKey)
		}
		taskProgress := tasklog.TaskProgress{
			Status:      "Downloading",
			CoverStatus: "Downloading",
			Current:     100,
			Total:       1000,
		}
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在拉取镜像00000", "logTxt", nil)
		for i := 1; i < 3; i++ {
			logTxt := "测试测是谁的菲拉斯懂法守法"
			taskProgress.Current += int64(i)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在拉取镜像", logTxt, taskProgress)
		}
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在拉取镜像11111", "logTxt", nil)
		for i := 1; i < 10; i++ {
			logTxt := "测试测是谁的菲拉斯懂法守法"
			taskProgress.Current += int64(i)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在拉取镜像", logTxt, taskProgress)

		}

	}
	if 1 == 2 {
		service.VirtualInitQueue = &common.Queue{}
		service.VirtualInitQueue.Cond = sync.NewCond(new(sync.Mutex))
		queueItem := QueueItem{Action: enums.QueueActionEnum.CheckInstanceLive, Data: 1}
		if service.VirtualInitQueue.Contains(queueItem) {
			fmt.Println("contains")
		} else {
			fmt.Println("not contains")
			service.VirtualInitQueue.Enqueue(queueItem)
		}

		queueItem1 := QueueItem{Action: enums.QueueActionEnum.CheckInstanceLive, Data: 1}
		if service.VirtualInitQueue.Contains(queueItem1) {
			fmt.Println("contains1")
		} else {
			fmt.Println("not contains1")
		}

	}
	if 1 == 2 {

		test := `3177609767709	/root/suanyun-user/0/5417o97aa5a1`

		// 正则表达式，匹配开头的数字序列
		re := regexp.MustCompile(`^\d+`)
		// 使用正则表达式查找匹配的部分
		matches := re.FindString(test)

		fmt.Println(matches)

	}

	if 1 == 2 {
		//rewardMonth := time.Now().Truncate(time.Hour * 24)
		now := time.Now()
		rewardMonth := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		fmt.Println(rewardMonth)
		firstOfMonth := rewardMonth.AddDate(0, 0, -rewardMonth.Day()+1)
		lastOfMonth := firstOfMonth.AddDate(0, 1, 0)
		fmt.Println(firstOfMonth)
		fmt.Println(lastOfMonth)
	}

	if 1 == 2 {

		fmt.Println(utils.Get)

		commandRunningValue := structs.RunningCommand{VirtualId: 1, VirtualHost: "sdfsdf", Command: "command", ImageId: 243, StartTime: jsontime.Now()}
		commandLog := utils.GetJsonFromStruct(commandRunningValue)
		fmt.Println(commandLog)

		service.NodeService.CommandRunning.Store("commandKey", commandRunningValue)
		tmpMap := make(map[string]structs.RunningCommand)
		service.NodeService.CommandRunning.Range(func(key, value interface{}) bool {
			tmpMap[key.(string)] = value.(structs.RunningCommand)
			return true // 继续遍历
		})
		fmt.Println(tmpMap["commandKey"])
		result := make(map[string]interface{})
		result["command_running"] = tmpMap
		fmt.Println("result:", utils.GetJsonFromStruct(result))
	}
	if 1 == 1 {

		if service.NodeService.Virtuals == nil {
			if err := service.NodeService.Init(); err != nil {
				logger.Error("初始化调度信息失败", err)
				return
			}
		}

		if err := common.InitRedisClient(); err != nil {
			common.FatalLog("failed to initialize Redis: " + err.Error())
		}

		ctx := context.Background()
		virtual := service.Virtual{
			ID:          55,
			Host:        "***************",
			SshUser:     "root",
			SshPassword: "chenyuworker!@#$",
		}
		//virtual.Host = "***************"
		//virtual.SshPassword = "chenyuworker!@#$"
		if _, err := virtual.SshDial(); err != nil {
			fmt.Println(err)
		}
		defer virtual.CloseSsh("DockerApiTest")
		virtual.NewDockerClient()

		if err := virtual.ClearLocalImage(ctx); err != nil {
			fmt.Println("err:", err)
		}

		if container, err := virtual.ContainerByStartupMarkUseApi(ctx, "5226673cf7384e32bffe1b6a13a1d2d8"); err != nil {
			logger.Error(err)
		} else {
			logger.Info(container)
			fmt.Println("aaa:", container.Labels["sdfsdf"])
		}

		if inspect, err := virtual.MigrateLargeFile(ctx, "2a7622238137"); err != nil {
			fmt.Println(err)
		} else {

			fmt.Println(utils.GetJsonFromStruct(inspect))
		}

		if str, err := virtual.LogsDocker("2a7622238137"); err != nil {
			fmt.Println(err)
		} else {

			fmt.Println(str)
		}

		if container, err := virtual.ContainerByIdUseApi(ctx, "11c380f4a701"); err != nil {
			logger.Error(err)
		} else {
			fmt.Println(container)
		}

		if image, err := virtual.ImageInspectWithRaw(ctx, "f27821d82964"); err != nil {
			fmt.Println(err)
		} else {
			fmt.Println(image)
		}

		if stats, err := virtual.ContainerInfoUseApi(ctx, "0d001204e88c"); err != nil {
			fmt.Println(err)
		} else {
			fmt.Println(stats)
		}

		o := virtual

		o.DockerItemsUseApi()

		hubImagePath := "10.20.103.240/chenyu/public/5f1e699d80924ca5a2c611109534cdfc:v0.2"
		if images, err := o.DockerImagesUseApi(ctx, hubImagePath); err != nil {
			logger.Error("err:", err, "获取本地镜像失败  virtualId: ", o.ID, "  hubImagePath:", hubImagePath)
		} else {
			if len(images) == 0 {
				logger.Error("查询本地镜像为空 获取本地镜像失败  virtualId: ", o.ID, "  hubImagePath:", hubImagePath)
			} else {
				localImage := service.DockerImageItem{
					ID:           images[0].ID,
					ImageId:      0,
					HubImagePath: hubImagePath,
					SizeG:        images[0].SizeG,
					SizeB:        images[0].SizeB,
					LastUseUnix:  time.Now().Unix(),
				}
				if err := o.SetLocalImage(localImage); err != nil {
					logger.Error("设置本地镜像信息失败 err:", err)
				}
			}
		}

		//virtual.Space(ctx)

		if err := virtual.ClearLocalImage(ctx); err != nil {
			fmt.Println("err:", err)
		}

		//options := image.ListOptions{}
		//if images, err := virtual.DockerClient.ImageList(ctx, options); err != nil {
		//	logger.Error(err)
		//} else {
		//	fmt.Println(utils.GetJsonFromStruct(images))
		//
		//}

		//repository := ""
		//options := image.ListOptions{
		//	Filters: filters.NewArgs(filters.Arg("reference", repository)),
		//}

		if _, err := o.DockerImageRemoveUseApi(ctx, "sha256:1119a0e3b10349fee95503bdd1afbe17973137cab5bd515cebe5bb9d8fef97b8"); err != nil {
			logger.Error(err)
		}

		//options := image.ListOptions{}
		//if images, err := o.DockerClient.ImageList(ctx, options); err != nil {
		//	logger.Error(err)
		//} else {
		//	fmt.Println(utils.GetJsonFromStruct(images))
		//
		//}

	}

	sss := `{"Containers":"N/A","CreatedAt":"2024-03-09 00:20:14 +0800 CST","CreatedSince":"5 months ago","Digest":"\u003cnone\u003e","ID":"9adfb6623292","Repository":"*************/public/ootd-iffusion","SharedSize":"N/A","Size":"16.4GB","Tag":"v2","UniqueSize":"N/A","VirtualSize":"16.44GB"}`

	var rawMap map[string]interface{}
	if err := utils.GetStructFromJson(&rawMap, sss); err != nil {
		logger.Error(err)
	} else {
		logger.Info(utils.GetJsonFromStruct(rawMap))
	}

	var dockerImage service.DockerImageItem
	if err := utils.GetStructFromJson(&dockerImage, sss); err != nil {
		logger.Error(err)
	} else {
		logger.Info(utils.GetJsonFromStruct(dockerImage))
	}

	VirtualInitQueue := &common.Queue{}
	VirtualInitQueue.Cond = sync.NewCond(new(sync.Mutex))

	queueItem := QueueItem{Action: enums.QueueActionEnum.CheckInstanceLive, Data: 1}
	VirtualInitQueue.Enqueue(queueItem)

	queueItem = QueueItem{Action: enums.QueueActionEnum.CheckInstanceLive, Data: 2}
	VirtualInitQueue.Enqueue(queueItem)

	queueItem = QueueItem{Action: enums.QueueActionEnum.CheckInstanceLive, Data: 1}
	VirtualInitQueue.Enqueue(queueItem)

	fmt.Println(utils.GetJsonFromStruct(VirtualInitQueue.Items()))

	// 正则表达式匹配主机端口号
	re := regexp.MustCompile(`:(\d+)\n`)

	output2 := `7860/tcp -> 0.0.0.0:10088
7860/tcp -> [::]:10088
8888/tcp -> 0.0.0.0:10089
8888/tcp -> [::]:10089
`
	ports := make([]string, 0)
	// 查找所有匹配的端口号
	matches := re.FindAllStringSubmatch(output2, -1)
	checkStr := ""
	for _, match := range matches {
		if strings.Contains(checkStr, match[1]) {
			continue
		}
		checkStr += " " + match[1]
		ports = append(ports, match[1])
	}
	fmt.Println(ports)

	output1 := `COMMAND      PID USER   FD   TYPE   DEVICE SIZE/OFF NODE NAME
docker-pr 142467 root    4u  IPv4 29549784      0t0  TCP *:11089 (LISTEN)
docker-pr 142475 root    4u  IPv6 29530998      0t0  TCP *:11089 (LISTEN)
docker-pr 142488 root    4u  IPv4 29506381      0t0  TCP *:11088 (LISTEN)
docker-pr 142496 root    4u  IPv6 29510140      0t0  TCP *:11088 (LISTEN)
`
	if strings.Contains(output1, "docker-pr") {
		re := regexp.MustCompile(`docker-pr\s+(\d+)`)
		matches := re.FindAllStringSubmatch(output1, -1)
		killPorts := make([]string, 0)
		for _, match := range matches {
			if len(match) > 1 {
				//fmt.Println(match[1])
				killPorts = append(killPorts, match[1])
			}
		}
		if len(killPorts) > 0 {
			killPortsCommand := fmt.Sprintf("kill -9 %s", strings.Join(killPorts, " ")) //kill -9 pid1 pid2 pid3
			logger.Info(killPortsCommand)
		}
	}

	savePath := `/Users/<USER>/myhd.dmg`
	if md5, err := utils.FileMd5(savePath); err != nil {
		fmt.Println(err)
	} else {
		fmt.Println(md5)
	}

	ttt := `docker run -p 1{==={GpuIndex}===}088:7860 -p 1{==={GpuIndex}===}089:8888 -e DATA_FOLDER=stable-diffusion-webui -v /root/aigc-models:/models:ro --gpus '"device={==={Device}===}"' {==={Labels}===} -d *************/public/stable-diffusion-webui:v1.6`

	fmt.Println(service.GetImagePath(ttt))
	// Initialize Redis
	if err := common.InitRedisClient(); err != nil {
		common.FatalLog("failed to initialize Redis: " + err.Error())
	}

	key := "sdfasdfsd"
	if mm, err := common.RedisHGetAll(key); err != nil {
		logger.Error(err)

	} else {
		logger.Error(mm)

	}

	output := `{"Containers":"N/A","CreatedAt":"2024-04-22 10:06:26 +0800 CST","CreatedSince":"3 weeks ago","Digest":"\u003cnone\u003e","ID":"b0795d733644","Repository":"hub.suanyun.cn/public/gpt-sovits","SharedSize":"N/A","Size":"23GB","Tag":"v1.1","UniqueSize":"N/A","VirtualSize":"23GB"}
{"Containers":"N/A","CreatedAt":"2024-04-20 13:28:43 +0800 CST","CreatedSince":"3 weeks ago","Digest":"\u003cnone\u003e","ID":"3094e6973508","Repository":"hub.suanyun.cn/public/voice-changer","SharedSize":"N/A","Size":"17.2GB","Tag":"v1","UniqueSize":"N/A","VirtualSize":"17.16GB"}
{"Containers":"N/A","CreatedAt":"2024-04-19 18:59:37 +0800 CST","CreatedSince":"3 weeks ago","Digest":"\u003cnone\u003e","ID":"78e8035264b5","Repository":"hub.suanyun.cn/public/so-vits-svc","SharedSize":"N/A","Size":"25.8GB","Tag":"v1","UniqueSize":"N/A","VirtualSize":"25.75GB"}
{"Containers":"N/A","CreatedAt":"2024-04-19 10:55:46 +0800 CST","CreatedSince":"4 weeks ago","Digest":"\u003cnone\u003e","ID":"e715a1b18ad0","Repository":"hub.suanyun.cn/public/open-sora","SharedSize":"N/A","Size":"17.4GB","Tag":"v1.1","UniqueSize":"N/A","VirtualSize":"17.38GB"}
{"Containers":"N/A","CreatedAt":"2024-04-19 09:56:46 +0800 CST","CreatedSince":"4 weeks ago","Digest":"\u003cnone\u003e","ID":"586ba07d6def","Repository":"hub.suanyun.cn/public/text-generation-webui","SharedSize":"N/A","Size":"18.8GB","Tag":"v1.2","UniqueSize":"N/A","VirtualSize":"18.84GB"}
{"Containers":"N/A","CreatedAt":"2024-04-18 19:27:06 +0800 CST","CreatedSince":"4 weeks ago","Digest":"\u003cnone\u003e","ID":"194c5d60b17e","Repository":"hub.suanyun.cn/public/text-generation-webui","SharedSize":"N/A","Size":"18.7GB","Tag":"v1.1","UniqueSize":"N/A","VirtualSize":"18.72GB"}
{"Containers":"N/A","CreatedAt":"2024-04-16 15:57:18 +0800 CST","CreatedSince":"4 weeks ago","Digest":"\u003cnone\u003e","ID":"a6d1fff959e5","Repository":"hub.suanyun.cn/public/photo-marker","SharedSize":"N/A","Size":"23.8GB","Tag":"v1.2","UniqueSize":"N/A","VirtualSize":"23.81GB"}
{"Containers":"N/A","CreatedAt":"2024-04-16 14:33:23 +0800 CST","CreatedSince":"4 weeks ago","Digest":"\u003cnone\u003e","ID":"49db72a28f22","Repository":"hub.suanyun.cn/public/photo-marker","SharedSize":"N/A","Size":"23.6GB","Tag":"v1.1","UniqueSize":"N/A","VirtualSize":"23.59GB"}
{"Containers":"N/A","CreatedAt":"2024-04-15 17:38:15 +0800 CST","CreatedSince":"4 weeks ago","Digest":"\u003cnone\u003e","ID":"a84100c65fbc","Repository":"hub.suanyun.cn/public/dust3r","SharedSize":"N/A","Size":"20.5GB","Tag":"v1.3","UniqueSize":"N/A","VirtualSize":"20.55GB"}
{"Containers":"N/A","CreatedAt":"2024-04-15 17:22:46 +0800 CST","CreatedSince":"4 weeks ago","Digest":"\u003cnone\u003e","ID":"2e0b2afdd7b0","Repository":"hub.suanyun.cn/public/dust3r","SharedSize":"N/A","Size":"20.4GB","Tag":"v1.2","UniqueSize":"N/A","VirtualSize":"20.42GB"}
{"Containers":"N/A","CreatedAt":"2024-04-15 16:57:23 +0800 CST","CreatedSince":"4 weeks ago","Digest":"\u003cnone\u003e","ID":"f7ce96b81351","Repository":"hub.suanyun.cn/public/dust3r","SharedSize":"N/A","Size":"20.4GB","Tag":"v1.1","UniqueSize":"N/A","VirtualSize":"20.42GB"}
{"Containers":"N/A","CreatedAt":"2024-04-15 15:23:13 +0800 CST","CreatedSince":"4 weeks ago","Digest":"\u003cnone\u003e","ID":"88ffaa3f3459","Repository":"hub.suanyun.cn/public/open-webui","SharedSize":"N/A","Size":"21.4GB","Tag":"v1.1","UniqueSize":"N/A","VirtualSize":"21.36GB"}
{"Containers":"N/A","CreatedAt":"2024-04-15 11:06:11 +0800 CST","CreatedSince":"4 weeks ago","Digest":"\u003cnone\u003e","ID":"f972fc8564e3","Repository":"hub.suanyun.cn/public/open-webui","SharedSize":"N/A","Size":"21.4GB","Tag":"v1","UniqueSize":"N/A","VirtualSize":"21.45GB"}
{"Containers":"N/A","CreatedAt":"2024-04-10 13:49:48 +0800 CST","CreatedSince":"5 weeks ago","Digest":"","ID":"5af902536d39","Repository":"hub.suanyun.cn/public/gpt-sovits","SharedSize":"N/A","Size":"22.8GB","Tag":"\u003cnone\u003e","UniqueSize":"N/A","VirtualSize":"22.83GB"}
`

	ary := make([]service.DockerImageItem, 0)
	output = strings.TrimSpace(output)
	aryLine := strings.Split(output, "\n")
	for _, line := range aryLine {
		line = strings.TrimSpace(line)
		if !strings.HasPrefix(line, "{") {
			continue
		}
		var dockerImage service.DockerImageItem
		if err := utils.GetStructAryFromJson(&dockerImage, line); err != nil {
			logger.Error(err, "  output:", line)
		} else {
			ary = append(ary, dockerImage)
		}
	}
	fmt.Println(ary)

	fmt.Println(jsontime.Now())

	output = `v3: digest: sha256:1a9822d498ad0b5b107d2bb2d77a4b6bae8a416b00091571dbb1a0e0432074fe size: 3893`
	output = "v2.2: digest: sha256:cec7049aeb451f03c8df9e93622518c879f32ebe7157e32ff5a1eb0465b4921a size: 3899"
	fmt.Println(service.GetImageSha256(output))

	command := `docker run -p 10088:7860 -p 10089:8888 --gpus '"device=0"' -v /root/suanyun-user/usrtst:/usrdata --label 'instance_uuid=' --label 'startup_mark=08148943758d4defac3aeaec95136151' --label 'host=**************' --label 'map_ports=88_89' --label 'host_port=**************:22' --label 'ssh_port=10022' --label 'gpus=0' --label 'pod_id=34' --label 'pod_category=2' -d hub.suanyun.cn/public/voice-changer:v1`
	service.GetImageTag(command)

	GetMapPorts("")

	if err := common.InitRedisClient(); err != nil {
		common.FatalLog("failed to initialize Redis: " + err.Error())
	}

	if log, err := service.StartupLog.Last("oReq.StartupMark"); err != nil {
		logger.Error(err)
		return
	} else {
		logger.Info(log)
	}

	result := make(map[string]interface{})

	result1 := make(map[string]interface{})
	result["aa"] = result1
	fmt.Println(utils.GetJsonFromStruct(result))

	// 生成RSA密钥对
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		fmt.Println("Error generating private key:", err)
		return
	}
	// 将私钥转换为字符串
	privateKeyPEM := string(pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(privateKey),
	}))

	// 解析私钥字符串
	block, _ := pem.Decode([]byte(privateKeyPEM))
	if block == nil {
		fmt.Println("Failed to parse PEM block containing the private key")
		return
	}
	privateKeyUse, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		fmt.Println("Error parsing private key:", err)
		return
	}

	publicKey := &privateKey.PublicKey

	publicKeyBytes, err := x509.MarshalPKIXPublicKey(publicKey)
	publicKeyPEM := string(pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyBytes,
	}))

	publicKeyPEM = `
-----BEGIN RSA PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAw/92JDPP2/IaQAhcYgco
6ur1fDm4u6U1NBFfeDa0kvZ4Ex89DRXu3FyrDhGLo/m/M5K7GTAfv6lcAzIKmcwk
WX6v0GIA6KkO13ote3U5ooJRZ53/vy/Cr0l6ms17ELsFdMTra9K7XbUwvMgrgLvm
zbFRJp5FqfxUK+y2cfSszBNtJApZ2aMv/ewjFI4uurTyfHe6fMNFNyHcKBLkTSTe
BYEyOcLDQea8sZRy0SJ1QQG1EMRWrNiEYDVXATMlMiyiuTa8p4Yxt0cL/xjyyaYo
vOgV3WZx16qVU/9vzDJoq6j1v1dbSf7OrXt8Z5bwqKWJnRBB0VnHowQb8PTEdO10
3QIDAQAB
-----END RSA PUBLIC KEY-----
`
	// 解析公钥字符串
	publicKeyBlock, _ := pem.Decode([]byte(publicKeyPEM))
	if publicKeyBlock == nil {
		fmt.Println("Failed to parse PEM block containing the public key")
		return
	}
	publicKeyInterface, err := x509.ParsePKIXPublicKey(publicKeyBlock.Bytes)
	if err != nil {
		fmt.Println("Error parsing public key:", err)
		return
	}
	publicKeyUse, ok := publicKeyInterface.(*rsa.PublicKey)
	if !ok {
		fmt.Println("Failed to cast public key to RSA public key")
		return
	}

	// 要签名的消息
	message := []byte("Hello, World!")

	// 计算消息的哈希值
	hashed := sha256.Sum256(message)

	// 使用私钥对消息进行数字签名
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKeyUse, crypto.SHA256, hashed[:])
	if err != nil {
		fmt.Println("Error signing message:", err)
		return
	}

	// 使用公钥验证签名
	err = rsa.VerifyPKCS1v15(publicKeyUse, crypto.SHA256, hashed[:], signature)
	if err != nil {
		fmt.Println("Verification failed:", err)
		return
	}

	fmt.Println("Signature verified successfully.")

	// 输出私钥和公钥的字符串形式
	fmt.Println("Private Key (PEM):")
	fmt.Println(privateKeyPEM)
	fmt.Println("\nPublic Key (PEM):")
	fmt.Println(publicKeyPEM)
}
