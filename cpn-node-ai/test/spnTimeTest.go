package main

import (
	"cpn-ai/common/logger"
	"cpn-ai/common/shortid"
	"cpn-ai/common/utils"
	"fmt"
	"github.com/google/uuid"
	"math/big"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"time"
)

func walkDir(root string) error {
	// 使用 Walk 函数遍历文件夹下的所有文件和目录
	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			logger.Error(err)
			return err
		}
		// 打印文件或目录的路径
		fmt.Println(path)
		return nil
	})
	if err != nil {
		return fmt.Errorf("walkDir: %v", err)
	}
	return nil
}

// 自定义的Base64字符集
// var customBase64 = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_"
var customBase64 = "abcdefghijklmnopqrstuvwxyz0123456789"

const base36Digits = "abcdefghijklmnopqrstuvwxyz0123456789"

func decimalToBase36(n int64) string {
	if n == 0 {
		return string(base36Digits[0])
	}
	base := big.NewInt(36)
	result := ""
	zero := big.NewInt(0)
	bigN := big.NewInt(n)

	for bigN.Cmp(zero) > 0 {
		quo := new(big.Int)
		quo.Mod(bigN, base)
		result = string(base36Digits[quo.Int64()]) + result
		bigN.Quo(bigN, base)
	}
	return result
}

func base36ToDecimal(s string) int64 {
	n := int64(0)
	base := big.NewInt(36)
	exp := new(big.Int)

	for i := len(s) - 1; i >= 0; i-- {
		exp.SetInt64(int64(len(s) - 1 - i))
		digitValue := int64(strings.IndexByte(base36Digits, s[i]))
		tmp := new(big.Int).Exp(base, exp, nil)
		tmp.Mul(tmp, big.NewInt(digitValue))
		n += tmp.Int64()
	}
	return n
}

func gen(num int64) string {
	numStr := decimalToBase36(num)
	numStrLen := int64(len(numStr))
	firstNum := int64(rand.Intn(36))
	secondNum := int64(rand.Intn(36))
	randNum := firstNum + secondNum
	randNum = randNum % 36
	thirdNum := randNum + numStrLen
	thirdNum = thirdNum % 36
	firstNumStr := decimalToBase36(firstNum)
	secondNumStr := decimalToBase36(secondNum)
	thirdNumStr := decimalToBase36(thirdNum)

	firstCode := firstNumStr + secondNumStr + thirdNumStr + numStr
	code := uuid.New().String()
	code = strings.Replace(code, "-", "", -1)
	needLen := 18 - len(firstCode)
	lastCode := code[:needLen]
	lastCode = firstCode + lastCode
	return lastCode
}

func decode(str string) int64 {
	firstNumStr := string(str[0])
	secondNumStr := string(str[1])
	thirdNumStr := string(str[2])

	firstNum := base36ToDecimal(firstNumStr)
	secondNum := base36ToDecimal(secondNumStr)
	thirdNum := base36ToDecimal(thirdNumStr)

	randNum := firstNum + secondNum
	randNum = randNum % 36
	numStrLen := int64(0)
	if thirdNum > randNum {
		numStrLen = thirdNum - randNum
	} else {
		numStrLen = 36 - randNum + thirdNum
	}
	numStr := str[3 : 3+numStrLen]
	num := base36ToDecimal(numStr)
	return num
}

// 生成随机数的函数
func randByte() byte {
	return customBase64[rand.Intn(len(customBase64))]
}

func main123() {
	fmt.Println(shortid.EncodeAdvance(int64(1000000000), 12))
	//9223372036854775807
	//18446744073709551615
	//var maxUint64 uint64 = 18446744073709551615
	var maxint64 int64 = 9223372036854775807
	fmt.Println(decimalToBase36(maxint64))
	//fmt.Println(decimalToBase36(maxUint64))
	for i := 0; i < 100; i++ {
		oo := int64(rand.Intn(100000000))
		oo = int64(i)
		aa := shortid.EncodeAdvance(oo, 12)
		bb := shortid.Decode(aa)
		fmt.Println(aa, "   ", oo, "      ", bb)
		if oo != bb {
			fmt.Println("error")
		}
	}
	gen(2)

	walkDir("/Users/<USER>/zcloud/aigc-api/stray-dog")

	msg := ""
	mPrice := utils.GetMapFromJson(`{"hour":5,"day":38.65,"week":245.43,"month":847.80}`)
	if _, ok := mPrice["hour"].(float64); !ok {
		msg = "价格格式错误"

		return
	}
	if _, ok := mPrice["day"].(float64); !ok {
		msg = "价格格式错误"
		return
	}
	if _, ok := mPrice["week"].(float64); !ok {
		msg = "价格格式错误"
		return
	}
	if _, ok := mPrice["month"].(float64); !ok {
		msg = "价格格式错误"
		return
	}
	logger.Error(msg)

	t := time.Unix(int64(1708252096), 0)
	fmt.Println(t)

	//duration := time.Second * time.Duration(3605)
	//hours := int(duration.Hours())
	//fmt.Println(hours)
	//remainingSeconds := int(duration.Seconds()) % 3600
	//fmt.Println(remainingSeconds)

	now := time.Now()
	fmt.Println(now.Format(time.DateTime))
	timeFormat := "2006-01-02 15:00:00"
	timeString := now.Format(timeFormat)
	fmt.Println(timeString)

	// 使用 time.Parse 将字符串转换为 time.Time 类型
	parsedTime, err := time.ParseInLocation(timeFormat, timeString, time.Local)
	if err != nil {
		fmt.Println("Error parsing time:", err)
		return
	}
	fmt.Println(parsedTime.Format(timeFormat))

	time1 := time.Date(2014, time.January, 31, 14, 1, 1, 880, time.Local)
	//time1 = time1.Truncate(time.Second)
	fmt.Println(time1.Format(time.DateTime))
	time2 := time.Date(2014, time.January, 31, 14, 1, 2, 5, time.Local)
	//time2 = time2.Truncate(time.Second)
	fmt.Println(time2.Format(time.DateTime))
	duration := time2.Sub(time1)
	fmt.Println(int(duration.Seconds()))
	fmt.Println(int(duration.Minutes()))
	fmt.Println(int(duration.Hours()))
}
