
1.新建项目文件夹(文件夹名称为项目名称)
2.用goland编辑器打开文件夹(跳出窗口强制转化)
3.设置包管理为国内镜像   Preferences->GoModules->勾选(Enable Go modules integration)->
    Environment（输入或右边按钮选择内容：GOPROXY=https://goproxy.io,direct）
4.设置Go Build  在编辑器右上角点击(Add Configuration)
   跳出的配置窗口左边点击 Add new，然后选择Go Build
   在窗口右边Run kind选择Directory
5.添加 main.go文件 修改包名为main
6.打开编辑器下面终端窗口 输入
    go mod init 项目名称(一般是项目根文件夹名)
    导入项目需要的go包

7.安装gin框架   go get -u github.com/gin-gonic/gin     -u是更新到最新版本
8.搭建目录框架



CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o cpn_node.linux
chmod 777 ./cpn_node.linux
涉及到扣费的地方

需要的接口
SchedService.RunDocker(instance.Uuid)
SchedService.RmDocker(instance.DockerId)


AllGpus
nvidia-smi --query-gpu=index,uuid,name,memory.total,utilization.gpu --format=csv,noheader

RunningGpus
nvidia-smi --query-compute-apps=gpu_uuid,pid,process_name --format=csv,noheader

192.168.200.170
192.168.200.171
192.168.200.173
192.168.200.91




