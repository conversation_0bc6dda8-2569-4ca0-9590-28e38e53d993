import os
from PIL import Image
import sys
import torch
import numpy as np

# 添加 ComfyUI 的父目录到 sys.path
comfyui_path = '/root/ComfyUI'
if comfyui_path not in sys.path:
    sys.path.append(comfyui_path)

try:
    import comfy.model_management as mm
    print("comfy.model_management 导入成功！")
except ImportError as e:
    print(f"导入错误: {e}")

# 直接导入 joycaption_alpha_2 中的功能
from nodes import Florence2Run, DownloadAndLoadFlorence2Model


def pil_to_comfyui_image(pil_image: Image.Image) -> torch.Tensor:
    """将 PIL Image 转换为 ComfyUI 图像批次张量。"""
    image = pil_image.convert("RGB")
    img_array = np.array(image).astype(np.float32) / 255.0
    img_tensor = torch.from_numpy(img_array).unsqueeze(0)  # 添加批次维度
    # ComfyUI 图像批次通常是 (batch_size, height, width, channels)
    img_tensor = img_tensor.permute(0, 2, 1, 3)  # 将通道维度移到最后
    return img_tensor

def load_image_to_comfy(image_path):
    """
    从图片路径加载图片并转换为 ComfyUI 的 IMAGE 张量格式.

    Args:
        image_path (str): 图片文件的路径.

    Returns:
        torch.Tensor: 形状为 (1, height, width, 3) 的 ComfyUI IMAGE 张量.
    """
    try:
        img_pil = Image.open(image_path).convert("RGB")
        img_np = np.array(img_pil).astype(np.float32) / 255.0
        img_tensor = torch.from_numpy(img_np).unsqueeze(0) # 添加批次维度 (batch_size=1)
        # PIL/NumPy 是 (height, width, channels)，ComfyUI 是 (batch_size, height, width, channels)
        return img_tensor
    except Exception as e:
        print(f"加载图片失败: {e}")
        return None

class Florence2Tagger:
    def __init__(self):
        print("开始加载 Florence2Tagger")
        self.florence2_instance = Florence2Run()

        load_model = DownloadAndLoadFlorence2Model()
        model = "microsoft/Florence-2-base"
        model = "CogFlorence-2.1-Large"
        precision = "fp16"
        attention = "sdpa"
        lora = None
        result = load_model.loadmodel(model, precision, attention, lora)
        self.florence2_model = result[0]
        # # self.device = "cuda" if torch.cuda.is_available() else "cpu"
        # self.device = "cuda"
        #
        # # 设置默认参数
        # self.llm_model = "/chenyudata/ComfyUI/models/LLM/Orenguteng--Llama-3.1-8B-Lexi-Uncensored-V2"  # 根据实际路径修改
        # self.llm_model = "/chenyudata/ComfyUI/models/LLM/unsloth--Meta-Llama-3.1-8B-Instruct"
        # self.dtype = "bf16"  # bf16 或 "nf4"
        # self.vlm_lora = "text_model"
        #
        # # 加载模型
        # try:
        #     self.model = load_models(
        #         model_path=self.llm_model,
        #         dtype=self.dtype
        #     )
        #     print("JoyCaption2Tagger 模型加载完成")
        #     print("model.clip_model:", self.model.clip_model)
        # except Exception as e:
        #     print(f"模型加载失败: {e}")
        #     self.model = None



    def tag(self, image_path, concept_sentence, *captions):
        """
        对图像进行标注，生成描述文本

        Args:
            image_path: 图像路径或PIL图像对象
            concept_sentence: 概念句子，如果非空，将添加到生成的描述末尾
            *captions: 额外的描述参数（未使用）

        Returns:
            生成的描述文本
        """
        print("开始打标 ", image_path)
        # 加载图像
        if isinstance(image_path, str):
            image = load_image_to_comfy(image_path)
        elif isinstance(image_path, Image.Image):
            image = image_path
        else:
            raise ValueError("image_path 必须是字符串路径或PIL图像对象")

        llm_model = "unsloth/Meta-Llama-3.1-8B-Instruct"
        dtype = "nf4"
        caption_type = "Descriptive"
        caption_length = "any"  # 使用固定的字符串值
        user_prompt = ""
        max_new_tokens = 260  # 获取默认值
        top_p = 0.8  # 获取默认值0.9
        temperature = 0.6  # 获取默认值0.6
        cache_model = True
        device = "cuda:0"
        extra_options_node = None

        batch_size = 1  # 批处理大小
        character_name = ""

        text_input = ""
        task = "more_detailed_caption"
        fill_mask = True
        keep_model_loaded = True
        num_beams = 3
        max_new_tokens = 1024
        do_sample = True
        output_mask_select = ""
        seed = None

        florence2_model = self.florence2_model
        # 生成描述
        try:
            result = self.florence2_instance.encode(image, text_input, florence2_model, task, fill_mask, keep_model_loaded,
            num_beams, max_new_tokens, do_sample, output_mask_select, seed)
            print("result:", result)
            return result[2]

        except Exception as e:
            print(f"生成描述失败: {e}")
            return f"生成描述失败: {e}"


if __name__ == "__main__":
    tagger = Florence2Tagger()
    print(" tagger = Florence2Tagger()")
    tag1 = tagger.tag(
        "/usrdata/team-data/872de996f7ab954bc0c18dff2a6e08f4/train/tags/0c42f21f45354fe4834588e3dcfb131b/96498c3bc5bf4d93b989c8fb89e5d3af.jpeg",
        "a beautiful scene")
    tag2 = tagger.tag(
        "/usrdata/team-data/872de996f7ab954bc0c18dff2a6e08f4/train/tags/0c42f21f45354fe4834588e3dcfb131b/3ce09996d5f84ca8a86eb5a878f6e92a.png",
        "")
    print("Tag 1:", tag1)
    print("Tag 2:", tag2)