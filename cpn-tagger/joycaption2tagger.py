import os
import torch
from PIL import Image
import sys
from typing import List, Union


# 添加 ComfyUI 的父目录到 sys.path
comfyui_path = '/root/ComfyUI'
if comfyui_path not in sys.path:
    sys.path.append(comfyui_path)

try:
    import comfy.model_management as mm
    print("comfy.model_management 导入成功！")
except ImportError as e:
    print(f"导入错误: {e}")


comfyui_node_path = '/root/ComfyUI/custom_nodes/ComfyUI_LayerStyle/py/'
if comfyui_node_path not in sys.path:
    sys.path.append(comfyui_node_path)

try:
    from imagefunc import download_hg_model, log, tensor2pil, clear_memory
    print("imagefunc 导入成功！")
except ImportError as e:
    print(f"imagefunc 导入错误: {e}")

try:
    from joycaption_alpha_2 import LS_JoyCaption2
    print("joycaption_alpha_2 导入成功！")
except ImportError as e:
    print(f"joycaption_alpha_2 导入错误: {e}")

# 直接导入 joycaption_alpha_2 中的功能
# from joycaption_alpha_2 import LS_JoyCaption2

class JoyCaption2Tagger:
    def __init__(self):
        print("开始加载 JoyCaption2Tagger")
        self.joy_caption_instance = LS_JoyCaption2()

    def tag(self, image_path, concept_sentence, *captions):
        """
        对图像进行标注，生成描述文本

        Args:
            image_path: 图像路径或PIL图像对象
            concept_sentence: 概念句子，如果非空，将添加到生成的描述末尾
            *captions: 额外的描述参数（未使用）

        Returns:
            生成的描述文本
        """
        print("开始打标 ", image_path)
        # 加载图像
        if isinstance(image_path, str):
            image = Image.open(image_path)  # 确保图像是 RGB 格式
            # 将 PIL Image 对象转换为 PyTorch 张量
        elif isinstance(image_path, Image.Image):
            image = image_path
        else:
            raise ValueError("image_path 必须是字符串路径或PIL图像对象")

        images = [image]
        llm_model = "unsloth/Meta-Llama-3.1-8B-Instruct"
        dtype = "nf4"
        caption_type = "Descriptive"
        vlm_lora = "text_model"
        caption_length = "any"  # 使用固定的字符串值
        user_prompt = ""
        max_new_tokens = 300  # 获取默认值
        top_p = 0.9  # 获取默认值0.9
        temperature = 0.6  # 获取默认值0.6
        cache_model = True
        device = "cuda"
        extra_options = None

        batch_size = 1  # 批处理大小
        character_name = ""

        # 生成描述
        try:
            print("jc2tagger llm_model:", llm_model)
            result = self.joy_caption_instance.joycaption2(images, llm_model, device, dtype, vlm_lora, caption_type, caption_length, user_prompt, max_new_tokens, top_p, temperature, cache_model, extra_options)
            print("result:", result)

            return "caption_text"

        except Exception as e:
            print(f"生成描述失败: {e}")
            return f"生成描述失败: {e}"

if __name__ == "__main__":
    tagger = JoyCaption2Tagger()
    tag1 = tagger.tag("/usrdata/team-data/872de996f7ab954bc0c18dff2a6e08f4/train/tags/0c42f21f45354fe4834588e3dcfb131b/96498c3bc5bf4d93b989c8fb89e5d3af.jpeg", "a beautiful scene")
    tag2 = tagger.tag("/usrdata/team-data/872de996f7ab954bc0c18dff2a6e08f4/train/tags/0c42f21f45354fe4834588e3dcfb131b/3ce09996d5f84ca8a86eb5a878f6e92a.png", "")
    print("Tag 1:", tag1)
    print("Tag 2:", tag2)