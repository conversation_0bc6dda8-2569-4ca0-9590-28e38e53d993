import gradio as gr
from PIL import Image
from fastapi import FastAPI
from pydantic import BaseModel
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import torch
from transformers import AutoProcessor, AutoModelForCausalLM
import os
import csv
import numpy as np
import asyncio
import redisqueue
import mycrop
import json
import time
from datetime import datetime
from typing import Dict
from florence2tagger import Florence2<PERSON>agger
from jc2tagger import JC2Tagger
from wd14tagger_pro import Wd14Tagger


TAG_ALGs = ["WD14", "JoyCaption2", "Florence2", "Qwen"]

def get_queue_name(tag_alg: str) -> Dict[str, str]:
    return {
        "input": "tagger:" + tag_alg,
        "output": "tagger",
        "zset": "tagger:" + tag_alg,
        "hset": "tagger"
    }

class TaskRunStatusRequest(BaseModel):
    run_status: int
class CropRequest(BaseModel):
    image_path: str
    crop_method: str
    crop_size: str


class CropTagRequest(BaseModel):
    q_task_id: str
    tag_alg: str
    image_folder: str
    crop_method: str
    crop_target: str
    crop_size: str
    crop_image_folder: str
    crop_param: dict


TrainStorage1 = "/Users/<USER>/mnt/team-data/872de996f7ab954bc0c18dff2a6e08f4/train"
TrainStorage2 = "/usrdata/team-data/872de996f7ab954bc0c18dff2a6e08f4/train"
TrainStorage = "/team-data/train"

# 获取当前执行文件的绝对路径
file_path = os.path.abspath(__file__)
# 获取所在文件夹路径
dir_path = os.path.dirname(file_path)

models_dir = os.path.join(dir_path, "models")

florence2_tagger = Florence2Tagger()
joycaption2_tagger = JC2Tagger()
wd14_tagger = Wd14Tagger()
task_run_status = 1
async def task_run():
    global task_run_status
    while True:
        try:
            if task_run_status != 1:
                print(f"\rrun status is stop, sleep 10 sec - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", end='', flush=True)
                await asyncio.sleep(10)
                continue
            task = redisqueue.task_pop_auto(TAG_ALGs, get_queue_name)
            if task is None:
                print(f"\rpop_auto：{task} task is none sleep 3 sec - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", end='', flush=True)
                await asyncio.sleep(3)
                continue
            try:
                print("pop_auto:", task)
                q_task_input = task["q_task_input"]
                images_uuid = q_task_input["images_uuid"]
                tags_uuid = q_task_input["tags_uuid"]
                images_folder = os.path.join(TrainStorage, "images", images_uuid)
                tags_folder = os.path.join(TrainStorage, "tags", tags_uuid)
                my_crop_request = CropTagRequest(
                    q_task_id=task["q_task_id"],
                    tag_alg=q_task_input["tag_alg"],
                    image_folder=images_folder,
                    crop_method=q_task_input["crop_method"],
                    crop_target=q_task_input["crop_traget"],
                    crop_size=q_task_input["crop_size"],
                    crop_image_folder=tags_folder,
                    crop_param=q_task_input
                )
                print("my_crop_request", my_crop_request)
                crop_tag(my_crop_request)
            except Exception as e:
                print("处理任务出错 err:", e)
        except Exception as e:
            print("出错了，延迟5秒 err:", e)
            await asyncio.sleep(5)


def run_tag(image,param):
    try:
        tag_alg = param["tag_alg"]
        if tag_alg == "WD14":
            caption_text = wd14_tagger.tag(image,None,None)
        if tag_alg == "Florence2":
            caption_text = florence2_tagger.tag(image,None,None)
        if tag_alg == "JoyCaption2":
            caption_text = joycaption2_tagger.tag(image,None,None)
    except Exception as e:
        print(f"打标失败: {e}")
    finally:
            return caption_text

def crop_img(image, crop_method, crop_size):
    try:
        if crop_method == "center":
            crop_image = mycrop.center_crop(image, crop_size[0], crop_size[1])
        elif crop_method == "focus":
            crop_image = mycrop.focus_crop(image, crop_size[0], crop_size[1])
        elif crop_method == "none":
            crop_image = image
        else:
            print("裁剪失败，未指定裁剪方式")
            return None
        return crop_image
    except Exception as e:
        print(f"裁剪失败: {e}")
        return None

def filter_images(images_folder: str, crop_target: str, crop_images_folder: str):
    """
    遍历 image_folder 中的所有图片，并根据 crop_target 的值过滤图片文件名。

    Args:
        image_folder (str): 包含原始图片的文件夹路径。
        crop_target (str): 过滤目标，可以是 "all" 或 "leave"。
                           如果为 "all"，则返回所有图片文件名。
                           如果为 "leave"，则移除在 crop_image_folder 中已存在的同名图片文件名。
        crop_image_folder (str): 包含已裁剪图片的文件夹路径 (仅当 crop_target=="leave" 时使用)。

    Returns:
        list: 包含过滤后的图片文件名列表（不包含扩展名）。
    """
    images = []
    allowed_extensions = ['.jpg', '.jpeg', '.png']  # 你可以根据需要添加更多图片扩展名

    if not os.path.isdir(images_folder):
        print(f"错误: 原始图片文件夹 '{images_folder}' 不存在。")
        return images

    for filename in os.listdir(images_folder):
        images.append(filename)
        # name, ext = os.path.splitext(filename)
        # if ext.lower() in allowed_extensions:
        #     images.append(name)


    if not os.path.isdir(crop_images_folder):
        print(f"警告: 裁剪图片文件夹 '{crop_images_folder}' 不存在，将创建该目录。")
        os.makedirs(crop_images_folder, exist_ok=True)
        return images

    if crop_target == "all":
        return images
    elif crop_target == "leave":
        if not os.path.isdir(crop_images_folder):
            return images

        cropped_images = set()
        for filename in os.listdir(crop_images_folder):
            cropped_images.add(filename)
            # name, ext = os.path.splitext(filename)
            # if ext.lower() in allowed_extensions:  # 假设裁剪后的图片也是这些格式
            #     cropped_images.add(name)

        filtered_images = [img for img in images if img not in cropped_images]
        return filtered_images
    else:
        print(f"错误: 未知的 crop_target 值 '{crop_target}'。请使用 'all' 或 'leave'。")
        return images

def crop_tag(req: CropTagRequest):
    try:
        print("crop_tag req:", req)
        images = filter_images(req.image_folder, req.crop_target, req.crop_image_folder)
        print("crop_tag images:", images)
        count = 0
        for filename in images:
            image_path = os.path.join(req.image_folder, filename)
            crop_image_path = os.path.join(req.crop_image_folder, filename)
            image = Image.open(image_path)
            result_str = req.crop_size.split("*")
            crop_size = [int(x) for x in result_str]
            crop_image = crop_img(image,req.crop_method,crop_size)

            if crop_image is None:
                return {"code": 1, "msg": "裁剪失败，裁剪图片" + filename + "时失败"}
            crop_image.save(crop_image_path)

            image_files = [crop_image_path]
            print("开始执行打标run_tag:", image_files)
            caption_text = run_tag(crop_image_path, req.crop_param)
            print("打标完成run_tag:", image_files)
            base, ext = os.path.splitext(crop_image_path)
            txt_path = base + ".txt"
            print("txt_path", txt_path)
            try:
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(caption_text)
                print(f"文本已成功写入到: {txt_path}")
            except Exception as e:
                print(f"写入文本到 {txt_path} 时发生错误: {e}")

            count += 1
            progress = count/len(images)
            redisqueue.progress(req.tag_alg, get_queue_name, req.q_task_id, progress)

        images = filter_images(req.image_folder, "all", req.crop_image_folder)
        destination_folder = req.crop_image_folder
        jsonl_file_path = os.path.join(destination_folder, "metadata.jsonl")
        with open(jsonl_file_path, "a") as jsonl_file:
            for filename in images:
                crop_image_path = os.path.join(req.crop_image_folder, filename)
                base, ext = os.path.splitext(crop_image_path)
                txt_file_path = base + ".txt"
                try:
                    with open(txt_file_path, "r") as txt_file:
                        txt_content = txt_file.read().strip()
                        data = {"file_name": filename, "prompt": txt_content}
                        jsonl_file.write(json.dumps(data) + "\n")
                except Exception as e:
                    print(f"读取文本文件 {txt_file_path} 时发生错误: {e}")
        return {"code": 0, "msg": "裁剪完成，共处理了" + str(count) + "张图片"}
    except Exception as e:
        print(e)
        redisqueue.completed(req.tag_alg, get_queue_name, req.q_task_id, {})
        return {"code": 1, "msg": "执行失败 err:" + str(e)}
    finally:
        redisqueue.completed(req.tag_alg, get_queue_name, req.q_task_id, {})

# Gradio 专用的处理函数，用于在 Gradio 界 面中显示裁剪后的图片
def center_crop_gradio(image, crop_method, crop_size_str):
    result_str = crop_size_str.split("*")
    crop_size = [int(x) for x in result_str]
    crop_image = crop_img(image, crop_method, crop_size)
    return crop_image

cropMethod_dict = {"聚焦裁剪": "focus", "中央裁剪": "center", "无需裁剪": "none"}
cropSize_dict = {"512*512": "512*512", "1024*1024": "1024*1024", "1536*1536": "1536*1536"}


async def switch_task_status():
    try:
        global task_run_status
        if task_run_status == 1:
            task_run_status = 0
        else:
            task_run_status = 1
        print(f"操作成功: {task_run_status}")
        tmp = "当前状态：开启"
        if task_run_status == 0:
            tmp = "当前状态：暂停"
        return gr.Button(value=tmp)
        # return f"操作成功: {task_run_status}"
    except Exception as e:
        print(f"操作失败: {task_run_status} {e}")
        #return f"启动失败: {e}"

async def refresh_task_status():
    print("refresh_task_status===============")
    try:
        global task_run_status
        tmp = "当前状态：开启"
        if task_run_status == 0:
            tmp = "当前状态：暂停"
        return gr.Button(value=tmp)
    except Exception as e:
        print(f"操作失败: {task_run_status} {e}")


# 创建 FastAPI 应用
app = FastAPI()

# 添加 CORS 中间件，允许跨域请求 (根据你的需求配置)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 定义 FastAPI 接口

@app.on_event("startup")
async def startup_event():
    asyncio.create_task(task_run())
    print("FastAPI 应用启动完成，后台任务已启动。")

# mac终端调用测试
# curl -X POST \
#   -H "Content-Type: application/json" \
#   -d '{"image_path": "/Users/<USER>/Documents/测试材料/IMG_4780.PNG", "cropMethod": "center", "cropSize": "512*512"}' \
#   http://127.0.0.1:7860/api/center_crop
@app.post("/api/center_crop")
async def api_center_crop(req: CropRequest):
    """通过接口进行中央裁剪 (接收 JSON 请求体)"""
    result_str = req.crop_size.split("*")
    crop_size = [int(x) for x in result_str]
    image = Image.open(req.image_path)
    crop_image = crop_img(image, req.crop_method, crop_size)
    return crop_image

# curl -X POST \
#   -H "Content-Type: application/json" \
#   -d '{"task_id": "tsettasktask", "image_folder": "/Users/<USER>/mnt/smb/train/images/c84f1034225d4ff1b16464921fbf6c6c", "crop_image_folder": "/Users/<USER>/mnt/smb/train/tags/0fd5698a7dfe450094a2a26c902591de", "crop_target": "all", "crop_method": "center", "crop_size": "512*512"}' \

@app.post("/api/crop_tag")
async def api_crop_tag(request_body: CropTagRequest):
    """通过接口进行中央裁剪 (接收 JSON 请求体)"""
    return crop_tag(request_body)

with gr.Blocks(theme=gr.themes.Soft()) as demo:
    gr.Markdown("## 后台任务控制")
    with gr.Row():
        tmp = "当前状态：开启"
        if task_run_status == 0:
            tmp = "当前状态：暂停"
        btn_switch_task = gr.Button(tmp, variant="primary")
    with gr.Row():
        with gr.Accordion(open=True, label="原始图片"):
            fileImage = gr.Image(type="filepath")
        with gr.Accordion(open=True, label="裁剪图片"):
            fileCropImage = gr.Image(type="filepath")
    with gr.Row():
        with gr.Column():
            ddCropMethod = gr.Dropdown(choices=cropMethod_dict, label="裁剪方式", interactive=True)
        with gr.Column():
            ddCropSize = gr.Dropdown(choices=cropSize_dict, label="裁剪尺寸", interactive=True)
        with gr.Column():
            btnAnimate = gr.Button("🚀 Animate", variant="primary")
    btnAnimate.click(
        fn=center_crop_gradio, # 使用 Gradio 专用的处理函数
        inputs=[
            fileImage,
            ddCropMethod,
            ddCropSize,
        ],
        outputs=[fileCropImage],
        show_progress=True
    )
    btn_switch_task.click(
        fn=switch_task_status,
        inputs=[],
        outputs=[btn_switch_task]  # 可以将操作结果也显示在状态文本框
    )
    demo.load(
        fn=refresh_task_status,
        inputs=[],
        outputs=[btn_switch_task]
    )

# 将 Gradio 应用挂载到 FastAPI
app = gr.mount_gradio_app(app, demo, path="/")

if __name__ == "__main__":
    #task_run()
    uvicorn.run(app, host="0.0.0.0", port=7860)