
       真实存在的路径           不存在的路径
ln -s /usrdata/models/tagger /root/tagger/models

pip install --upgrade pip
pip install transformers
pip install redis
pip install onnxruntime

清空回收站
rm -rf ~/.local/share/Trash/files/*

清空临时文件夹
sudo rm -rf /tmp/*
rm -rf ~/.cache/tmp/*

查找进程
ps aux | grep test.py
ps aux | grep tagger.py
ps aux | grep flux_train_ui.py
ps aux | grep train.py
ps aux | grep train_job.py

端口占用
sudo lsof -i :7860

find /models -name "*FLUX.1-dev*"

打标Pod: a3e22dd3b18942a0b8ed9edd4b210efb（1183）   ImageId:119085
训练Pod: 0c9676f9a46847e897d27b2f784f282a（1194）   ImageId:120508
测试虚拟机ID:251


/chenyudata/ComfyUI/models/diffusers/FLUX.1-dev
/poddata/models/flux.1-dev

cd /root/ai-toolkit
python3 flux_train_ui.py 2>&1 | tee -a /root/train_log.txt