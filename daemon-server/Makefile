# 项目名称
PROJECT_NAME := daemon-server

GIT_COMMIT ?= $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_TIME ?= $(shell date -u '+%Y-%m-%dT%H:%M:%SZ')

# 可执行文件输出路径
OUTPUT_DIR := ./bin
SERVER_OUTPUT_BINARY := $(OUTPUT_DIR)/$(PROJECT_NAME)
LAYER_EXPORT_OUTPUT_BINARY := $(OUTPUT_DIR)/layer-export
LAYER_IMPORT_OUTPUT_BINARY := $(OUTPUT_DIR)/layer-import
CGO=0

# 链接标志，包括版本信息
LDFLAGS = -s -w \
	-X daemon-server/pkg/version.GitCommit=$(GIT_COMMIT) \
	-X daemon-server/pkg/version.BuildTime=$(BUILD_TIME)


# 自动检测操作系统
ifeq ($(OS),Windows_NT)
    TARGET_OS := windows
else
    UNAME_S := $(shell uname -s)
    ifeq ($(UNAME_S),Linux)
        TARGET_OS := linux
    else ifeq ($(UNAME_S),Darwin)
        TARGET_OS := darwin
    else
        $(error Unsupported operating system: $(UNAME_S))
    endif
endif

# 自动检测架构
ifeq ($(OS),Windows_NT)
    ifeq ($(PROCESSOR_ARCHITECTURE),AMD64)
        TARGET_ARCH := amd64
    else ifeq ($(PROCESSOR_ARCHITECTURE),ARM64)
        TARGET_ARCH := arm64
    else
        $(error Unsupported architecture on Windows: $(PROCESSOR_ARCHITECTURE))
    endif
else
    UNAME_M := $(shell uname -m)
    ifeq ($(UNAME_M),x86_64)
        TARGET_ARCH := amd64
    else ifeq ($(UNAME_M),aarch64)
        TARGET_ARCH := arm64
    else
        $(error Unsupported architecture: $(UNAME_M))
    endif
endif

# 构建server
server:
	@mkdir -p $(OUTPUT_DIR)
	@echo "Building $(PROJECT_NAME) version $(VERSION) ($(GIT_COMMIT)) built on $(BUILD_TIME)"
	go mod tidy
	CGO_ENABLED=$(CGO) GOOS=$(TARGET_OS) GOARCH=$(TARGET_ARCH) go build -ldflags "$(LDFLAGS)" -o $(SERVER_OUTPUT_BINARY) ./cmd/server/main.go

# layer export
layer-tools:
	@mkdir -p $(OUTPUT_DIR)
	@echo "Building layer-export version $(VERSION) ($(GIT_COMMIT)) built on $(BUILD_TIME)"
	go mod tidy
	CGO_ENABLED=$(CGO) GOOS=$(TARGET_OS) GOARCH=$(TARGET_ARCH) go build -ldflags "$(LDFLAGS)" -o $(LAYER_EXPORT_OUTPUT_BINARY) ./cmd/layer-export/layer-export.go
	CGO_ENABLED=$(CGO) GOOS=$(TARGET_OS) GOARCH=$(TARGET_ARCH) go build -ldflags "$(LDFLAGS)" -o $(LAYER_IMPORT_OUTPUT_BINARY) ./cmd/layer-import/layer-import.go

# 清理生成的文件
clean:
	rm -rf $(OUTPUT_DIR)

# 构建所有目标
all: clean server layer-tools

# 定义默认目标
.DEFAULT_GOAL := all
