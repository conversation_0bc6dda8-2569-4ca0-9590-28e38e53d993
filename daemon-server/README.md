# Daemon Server

A Go HTTP server using the Gin framework.

## Project Structure

```
.
├── api/            # API protocol definitions
├── cmd/            # Main applications
│   └── server/     # Server application
├── configs/        # Configuration files
├── docs/           # Documentation
├── internal/       # Private application code
│   ├── app/        # Application core
│   ├── docker/     # Docker operations
│   ├── handlers/   # HTTP handlers
│   ├── logger/     # Logging system
│   └── middleware/ # HTTP middleware
├── pkg/            # Public libraries
│   └── utils/      # Utility functions
└── test/           # Additional tests
```

## Getting Started

### Prerequisites

- Go 1.24 or higher

### Installation

1. Clone the repository
2. Install dependencies:

```bash
go mod download
```

### Running the Server

```bash
go run cmd/server/main.go
```

## API Endpoints

### Basic Endpoints
- `GET /ping` - Health check endpoint
- `GET /info` - Server information endpoint

### Docker API Endpoints
The server provides a comprehensive Docker API with the following endpoints:

#### Container Operations
- `GET /api/v1/containers` - List containers
- `POST /api/v1/containers` - Create a container
- `GET /api/v1/containers/:id` - Inspect a container
- `DELETE /api/v1/containers/:id` - Remove a container
- `POST /api/v1/containers/:id/start` - Start a container
- `POST /api/v1/containers/:id/stop` - Stop a container
- `POST /api/v1/containers/:id/restart` - Restart a container
- `GET /api/v1/containers/:id/logs` - Get container logs
- `POST /api/v1/containers/:id/commit` - Commit a container to an image
- `GET /api/v1/containers/:id/export` - Export a container as a tar file
- `GET /api/v1/containers/:id/ports` - Get container port mappings

#### Image Operations
- `GET /api/v1/images` - List images
- `POST /api/v1/images/pull` - Pull an image
- `GET /api/v1/images/:id` - Inspect an image
- `DELETE /api/v1/images/:id` - Remove an image
- `POST /api/v1/images/push` - Push an image
- `GET /api/v1/images/save` - Save images as a tar file
- `POST /api/v1/images/prune` - Prune unused images

#### System Operations
- `GET /api/v1/system/disk-usage` - Get Docker disk usage information

The API uses simplified request formats for container creation and image operations, making it easier to use with intuitive parameters.

Example of container creation:
```json
{
  "name": "my-container",
  "image": "nginx:latest",
  "ports": ["8080:80/tcp"],
  "volumes": ["/host/data:/data"],
  "restart": "always"
}
```

For detailed API documentation, see:
- [Docker API Documentation](docs/docker-api.md)
- [Docker API Extension Documentation](docs/docker-api-extension.md)

## Logging

The application uses a structured logging system with the following features:

- Multiple log levels (debug, info, warn, error, fatal)
- JSON or text log formats
- Log rotation based on file size
- Automatic log file compression
- Log retention policies

To use the logger in your code:

```go
import "daemon-server/internal/logger"

// Simple logging
logger.Debug("Debug message")
logger.Info("Info message")
logger.Warn("Warning message")
logger.Error("Error message")
logger.Fatal("Fatal message") // This will exit the application

// Structured logging with fields
logger.Info("User logged in",
    logger.String("user_id", "123"),
    logger.Int("login_count", 5),
    logger.Bool("admin", false),
)

// Logging with error
if err != nil {
    logger.Error("Operation failed", logger.Err(err))
}

// Create a logger with context
ctxLogger := logger.With(
    logger.String("request_id", "abc-123"),
    logger.String("user_id", "user-456"),
)
ctxLogger.Info("Processing request")
```

## Configuration

Configuration is stored in `configs/config.yaml`. The following options are available:

```yaml
server:
  host: "0.0.0.0"  # Server host
  port: 8080       # Server port

logger:
  level: "info"      # Log level (debug, info, warn, error, fatal)
  file: "logs/server.log"  # Log file path
  format: "json"     # Log format (json or text)
  maxSize: 10        # Maximum size of log file in MB before rotation
  maxBackups: 5      # Maximum number of old log files to retain
  maxAge: 30         # Maximum number of days to retain old log files
  compress: true     # Compress rotated files

docker:
  host: "unix:///var/run/docker.sock"  # Docker host (e.g., unix:///var/run/docker.sock)
  apiVersion: ""                       # Docker API version (empty for auto-negotiation)
  tlsVerify: false                     # Whether to verify TLS
  certPath: ""                         # Path to TLS certificates
  registryAuth: false                  # Whether to use registry authentication
  registryConfig: "~/.docker/config.json"  # Path to registry config file
```
