package main

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"

	"daemon-server/pkg/container"
)

func main() {
	// 定义命令行参数
	markID := flag.String("mark_id", "", "容器标记ID")
	containerID := flag.String("container_id", "", "容器ID")
	backupDir := flag.String("backup_dir", "", "备份目录路径")
	dockerHost := flag.String("docker_host", "", "Docker主机地址")
	verbose := flag.Bool("verbose", false, "输出详细日志")

	// 解析命令行参数
	flag.Parse()

	// 验证必要参数
	if *containerID == "" || *backupDir == "" {
		fmt.Println("错误：容器ID和备份目录是必需的")
		flag.Usage()
		os.Exit(1)
	}
	// 调用备份函数
	info, err := container.BackupContainer(container.BackupOptions{
		ContainerMarkID: *markID,
		ContainerID:     *containerID,
		BackupDir:       *backupDir,
		DockerHost:      *dockerHost,
		Verbose:         *verbose,
	})

	if err != nil {
		fmt.Printf("备份容器失败：%v\n", err)
		os.Exit(1)
	}

	fmt.Printf("备份完成，容器信息：\n")
	fmt.Printf("容器标记ID：%s\n", info.ContainerMarkID)
	fmt.Printf("容器ID：%s\n", info.ContainerID)
	fmt.Printf("容器名称：%s\n", info.ContainerName)
	fmt.Printf("镜像名称：%s\n", info.ImageName)
	fmt.Printf("镜像ID：%s\n", info.ImageID)
	fmt.Printf("MountID：%s\n", info.MountID)
	fmt.Printf("Upper目录：%s\n", info.UpperDir)
	fmt.Printf("备份文件[size:%.2fMB]：%s\n", float64(info.Size)/(1024*1024), filepath.Join(*backupDir, info.ContainerMarkID+container.ContainerLayerFileSuffix))
	fmt.Printf("备份Docker元数据文件：%s\n", filepath.Join(*backupDir, info.ContainerMarkID+container.DockerMetadataFileSuffix))
	fmt.Printf("备份容器元数据文件：%s\n", filepath.Join(*backupDir, info.ContainerMarkID, container.ContainerMetadataFileSuffix))
}
