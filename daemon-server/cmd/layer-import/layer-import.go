package main

import (
	"flag"
	"fmt"
	"os"

	"daemon-server/pkg/container"
)

func main() {
	// 定义命令行参数
	containerMarkID := flag.String("mark_id", "", "容器标记ID")
	containerID := flag.String("container_id", "", "容器ID")
	backupDir := flag.String("backup_dir", "", "备份目录路径")
	tempDir := flag.String("temp_dir", "", "临时目录路径,存临时存放解压出来的文件")
	newContainerName := flag.String("container_name", "", "新容器名称（可选）")
	dockerHost := flag.String("docker_host", "", "Docker主机地址，值为空时使用unix:///var/run/docker.sock;值不为空时则使用tcp连接，自动检测是否为tcp开头")
	verbose := flag.Bool("verbose", false, "输出详细日志")
	checkLiveRestoreConfig := flag.Bool("check_live_restore_config", true, "检查LiveRestore配置")
	//skipPull := flag.Bool("skip-pull", false, "跳过拉取镜像步骤")
	skipDockerSystemCompare := flag.Bool("skip_docker_system_compare", false, "跳过Docker系统(OS)比较步骤")
	skipDockerVersionCompare := flag.Bool("skip_docker_version_compare", false, "跳过Docker版本比较步骤")

	// 解析命令行参数
	flag.Parse()

	// 验证必要参数
	if *backupDir == "" {
		fmt.Println("must have backup_dir argument")
		flag.Usage()
		os.Exit(1)
	}

	if *containerMarkID == "" {
		fmt.Println("must have container_id argument")
		flag.Usage()
		os.Exit(1)
	}
	if *containerID == "" {
		fmt.Println("must have container_id argument")
		flag.Usage()
		os.Exit(1)
	}
	if *containerMarkID == "" {
		fmt.Println("must have container_mark_id argument")
		flag.Usage()
		os.Exit(1)
	}

	// 调用恢复函数
	err := container.LoadContainerInfo(container.RestoreOptions{
		ContainerMarkID:          *containerMarkID,
		BackupDir:                *backupDir,
		ContainerName:            *newContainerName,
		ContainerID:              *containerID,
		DockerHost:               *dockerHost,
		Verbose:                  *verbose,
		SkipPull:                 false,
		CheckLiveRestoreConfig:   *checkLiveRestoreConfig,
		SkipCreate:               false,
		SkipStart:                false,
		TempDir:                  *tempDir,
		SkipDockerSystemCompare:  *skipDockerSystemCompare,
		SkipDockerVersionCompare: *skipDockerVersionCompare,
	})

	if err != nil {
		fmt.Printf("恢复容器失败：%v\n", err)
		os.Exit(1)
	} else {
		fmt.Println("恢复容器[", *containerID, "]成功")
	}
}
