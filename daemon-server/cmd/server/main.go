package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"daemon-server/configs"
	"daemon-server/internal/app"
	"daemon-server/internal/logger"
	"daemon-server/pkg/version"
)

func main() {
	// Load configuration
	config, err := configs.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	if err := logger.Init(config); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}

	// Log startup information
	vInfo := version.GetVersionInfo()
	logger.Info("Starting server",
		logger.String("host", config.Server.Host),
		logger.Int("port", config.Server.Port),
		logger.String("log_level", config.Logger.Level),
		logger.String("version", vInfo.Version),
		logger.String("git_commit", vInfo.GitCommit),
		logger.String("build_time", vInfo.BuildTime),
	)

	// Create server
	server := app.NewServer(config)

	// Setup graceful shutdown
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// Start server in a goroutine
	go func() {
		if err := server.Run(); err != nil {
			logger.Fatal("Server failed to start", logger.Err(err))
		}
	}()

	// Wait for termination signal
	sig := <-sigCh
	logger.Info("Received signal, shutting down", logger.String("signal", sig.String()))

	// Close resources
	if err := server.Close(); err != nil {
		logger.Error("Error during shutdown", logger.Err(err))
	}

	logger.Info("Server shutdown complete")
}
