package configs

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// Config represents the application configuration
type Config struct {
	Server    ServerConfig    `yaml:"server"`
	Logger    LoggerConfig    `yaml:"logger"`
	Docker    DockerConfig    `yaml:"docker"`
	Scheduler SchedulerConfig `yaml:"scheduler"`
}

// ServerConfig represents the server configuration
type ServerConfig struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

// LoggerConfig represents the logger configuration
type LoggerConfig struct {
	Level      string `yaml:"level"`      // Log level (debug, info, warn, error, fatal)
	File       string `yaml:"file"`       // Log file path
	Format     string `yaml:"format"`     // Log format (json or text)
	MaxSize    int    `yaml:"maxSize"`    // Maximum size of log file in MB before rotation
	MaxBackups int    `yaml:"maxBackups"` // Maximum number of old log files to retain
	MaxAge     int    `yaml:"maxAge"`     // Maximum number of days to retain old log files
	Compress   bool   `yaml:"compress"`   // Compress rotated files
}

// DockerConfig represents the Docker configuration
type DockerConfig struct {
	Host           string `yaml:"host"`           // Docker host (e.g., unix:///var/run/docker.sock)
	APIVersion     string `yaml:"apiVersion"`     // Docker API version
	TLSVerify      bool   `yaml:"tlsVerify"`      // Whether to verify TLS
	CertPath       string `yaml:"certPath"`       // Path to TLS certificates
	RegistryAuth   bool   `yaml:"registryAuth"`   // Whether to use registry authentication
	RegistryConfig string `yaml:"registryConfig"` // Path to registry config file
}

// SchedulerConfig represents the scheduler configuration
type SchedulerConfig struct {
	Enabled     bool `yaml:"enabled"`     // Whether the scheduler is enabled
	Concurrency int  `yaml:"concurrency"` // Maximum number of concurrent scheduled tasks
}

// LoadConfig loads the configuration from the config file
func LoadConfig() (*Config, error) {
	// Default configuration
	config := &Config{
		Server: ServerConfig{
			Host: "0.0.0.0",
			Port: 8080,
		},
		Logger: LoggerConfig{
			Level:      "info",
			File:       "logs/server.log",
			Format:     "json",
			MaxSize:    10,   // 10 MB
			MaxBackups: 5,    // 5 backups
			MaxAge:     30,   // 30 days
			Compress:   true, // Compress rotated files
		},
		Docker: DockerConfig{
			Host:           "unix:///var/run/docker.sock", // Default Docker socket
			APIVersion:     "",                            // Empty for auto-negotiation
			TLSVerify:      false,
			CertPath:       "",
			RegistryAuth:   false,
			RegistryConfig: "~/.docker/config.json",
		},
		Scheduler: SchedulerConfig{
			Enabled:     true,
			Concurrency: 5,
		},
	}

	// Try to load from file
	configFile := "configs/config.yaml"
	if _, err := os.Stat(configFile); err == nil {
		file, err := os.Open(configFile)
		if err != nil {
			return nil, fmt.Errorf("failed to open config file: %w", err)
		}
		defer file.Close()

		decoder := yaml.NewDecoder(file)
		if err := decoder.Decode(config); err != nil {
			return nil, fmt.Errorf("failed to decode config file: %w", err)
		}
	}

	return config, nil
}
