server:
  host: "0.0.0.0"
  port: 6789

logger:
  level: "info"      # Log level (debug, info, warn, error, fatal)
  file: "logs/server.log"  # Log file path
  format: "text"     # Log format (json or text)
  maxSize: 10        # Maximum size of log file in MB before rotation
  maxBackups: 5      # Maximum number of old log files to retain
  maxAge: 30         # Maximum number of days to retain old log files
  compress: true     # Compress rotated files

docker:
  host: "unix:///var/run/docker.sock"  # Docker host (e.g., unix:///var/run/docker.sock)
  apiVersion: ""                       # Docker API version (empty for auto-negotiation)
  tlsVerify: false                     # Whether to verify TLS
  certPath: ""                         # Path to TLS certificates
  registryAuth: false                  # Whether to use registry authentication
  registryConfig: "~/.docker/config.json"  # Path to registry config file

scheduler:
  enabled: true                        # Whether the scheduler is enabled
  concurrency: 5                       # Maximum number of concurrent scheduled tasks
