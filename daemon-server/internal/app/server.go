package app

import (
	"context"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"

	"daemon-server/configs"
	"daemon-server/internal/docker"
	"daemon-server/internal/handlers"
	"daemon-server/internal/logger"
	"daemon-server/internal/middleware"
	"daemon-server/internal/scheduler"
	"daemon-server/internal/system"
	"daemon-server/internal/taskflow"
)

// Server represents the HTTP server
type Server struct {
	router      *gin.Engine
	config      *configs.Config
	dockerSvc   *docker.Service
	systemSvc   *system.CommandService
	taskManager *taskflow.Manager
	scheduler   *scheduler.Scheduler
}

// NewServer creates a new server instance
func NewServer(config *configs.Config) *Server {
	// Set Gin mode based on log level
	if config.Logger.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// Create router without default middleware
	router := gin.New()

	// Apply global middleware
	router.Use(middleware.Logger(config))
	router.Use(middleware.Recovery())

	// Initialize Docker service
	dockerSvc, err := docker.NewService(config)
	if err != nil {
		logger.Error("Failed to initialize Docker service", logger.Err(err))
		// Continue without Docker service
	}

	// Initialize system command service
	systemSvc := system.NewCommandService()

	// Initialize taskflow manager
	taskManager := taskflow.NewManager(config)

	// Initialize scheduler
	scheduler := scheduler.NewScheduler(config, taskManager)

	// Initialize server
	server := &Server{
		router:      router,
		config:      config,
		dockerSvc:   dockerSvc,
		systemSvc:   systemSvc,
		taskManager: taskManager,
		scheduler:   scheduler,
	}

	// Setup routes
	server.setupRoutes()

	logger.Info("Server initialized", logger.String("mode", gin.Mode()))

	return server
}

// setupRoutes configures the routes for the server
func (s *Server) setupRoutes() {
	// Basic routes
	s.router.GET("/ping", handlers.Ping)
	s.router.GET("/info", handlers.GetServerInfo)

	// Register Docker API routes
	handlers.RegisterDockerAPIRoutes(s.router, s.dockerSvc, s.taskManager)

	// Register System API routes
	handlers.RegisterSystemAPIRoutes(s.router, s.systemSvc, s.taskManager)

	// Register Taskflow API routes
	handlers.RegisterTaskflowAPIRoutes(s.router, s.taskManager)

	logger.Info("Routes configured")
}

// Run starts the HTTP server
func (s *Server) Run() error {
	addr := fmt.Sprintf("%s:%d", s.config.Server.Host, s.config.Server.Port)
	logger.Info("Starting HTTP server", logger.String("address", addr))

	// Ping Docker daemon if Docker service is available
	if s.dockerSvc != nil {
		ctx := context.Background()
		if err := s.dockerSvc.Ping(ctx); err != nil {
			logger.Warn("Docker daemon is not available", logger.Err(err))
		}
	}

	// Start scheduler if enabled
	if s.config.Scheduler.Enabled && s.scheduler != nil {
		// Register default tasks
		s.scheduler.RegisterDefaultTasks()

		// Start the scheduler
		s.scheduler.Start()
		logger.Info("Scheduler started")
	}

	return s.router.Run(addr)
}

// Close closes the server and its resources
func (s *Server) Close() error {
	// Create a context with timeout for graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown scheduler
	if s.scheduler != nil {
		s.scheduler.Stop()
		logger.Info("Scheduler shut down")
	}

	// Shutdown taskflow manager
	if s.taskManager != nil {
		s.taskManager.Shutdown(ctx)
		logger.Info("Taskflow manager shut down")
	}

	// Close Docker service if available
	if s.dockerSvc != nil {
		if err := s.dockerSvc.Close(); err != nil {
			logger.Error("Failed to close Docker service", logger.Err(err))
		}
	}

	return nil
}
