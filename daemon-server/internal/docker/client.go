package docker

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/events"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/docker/api/types/volume"

	"daemon-server/configs"
	"daemon-server/internal/logger"
)

// Client is the interface for Docker operations
type Client interface {
	// Container operations
	ContainerList(ctx context.Context, all bool) ([]types.Container, error)
	ContainerListWithOptions(ctx context.Context, options container.ListOptions) ([]types.Container, error)
	ContainerCreate(ctx context.Context, config *container.Config, hostConfig *container.HostConfig, networkConfig *network.NetworkingConfig, name string) (container.CreateResponse, error)
	ContainerStart(ctx context.Context, containerID string) error
	ContainerStop(ctx context.Context, containerID string, timeout *int) error
	ContainerRestart(ctx context.Context, containerID string, timeout *int) error
	ContainerRemove(ctx context.Context, containerID string, force, removeVolumes bool) error
	ContainerInspect(ctx context.Context, containerID string) (container.InspectResponse, error)
	ContainerLogs(ctx context.Context, containerID string, follow bool, tail string) (io.ReadCloser, error)
	ContainerStats(ctx context.Context, containerID string, stream bool) (Stats, error)
	ContainerCommit(ctx context.Context, containerID string, options container.CommitOptions) (types.IDResponse, error)
	ContainerExport(ctx context.Context, containerID string) (io.ReadCloser, error)

	// Image operations
	ImageList(ctx context.Context, option image.ListOptions) ([]image.Summary, error)
	ImagePull(ctx context.Context, ref string, options ImagePullOptions) (io.ReadCloser, error)
	ImagePush(ctx context.Context, ref string, options ImagePushOptions) (io.ReadCloser, error)
	ImageRemove(ctx context.Context, imageID string, force, pruneChildren bool) ([]image.DeleteResponse, error)
	ImageInspect(ctx context.Context, imageID string) (types.ImageInspect, error)
	ImageHistory(ctx context.Context, imageID string) ([]image.HistoryResponseItem, error)
	ImageLoad(ctx context.Context, filePath string) (io.ReadCloser, error)
	ImageSave(ctx context.Context, imageIDs []string) (io.ReadCloser, error)
	ImagePrune(ctx context.Context, all bool) (image.PruneReport, error)

	// Volume operations
	VolumeList(ctx context.Context) (volume.ListResponse, error)
	VolumeCreate(ctx context.Context, options volume.CreateOptions) (volume.Volume, error)
	VolumeRemove(ctx context.Context, volumeID string, force bool) error
	VolumeInspect(ctx context.Context, volumeID string) (volume.Volume, error)

	// Network operations
	NetworkList(ctx context.Context) ([]NetworkResource, error)
	NetworkCreate(ctx context.Context, name string, options NetworkCreate) (NetworkCreateResponse, error)
	NetworkRemove(ctx context.Context, networkID string) error
	NetworkInspect(ctx context.Context, networkID string, verbose bool) (NetworkResource, error)

	// System operations
	SystemInfo(ctx context.Context) (Info, error)
	SystemVersion(ctx context.Context) (Version, error)
	SystemDiskUsage(ctx context.Context) (DiskUsage, error)
	SystemEvents(ctx context.Context, options EventsOptions) (<-chan events.Message, <-chan error)

	ContainerExecCreate(ctx context.Context, containerID string, options container.ExecOptions) (container.ExecCreateResponse, error)
	ContainerExecStart(ctx context.Context, execID string, config container.ExecStartOptions) (types.HijackedResponse, error)
	ContainerExecInspect(ctx context.Context, execID string) (container.ExecInspect, error)

	// Close the client
	Close() error
}

// dockerClient implements the Client interface
type dockerClient struct {
	cli *DockerClient
}

// NewClient creates a new Docker client
func NewClient(config *configs.Config) (Client, error) {
	// Expand home directory in registry config path
	registryConfig := config.Docker.RegistryConfig
	if strings.HasPrefix(registryConfig, "~/") {
		home, err := os.UserHomeDir()
		if err != nil {
			return nil, fmt.Errorf("failed to get user home directory: %w", err)
		}
		registryConfig = filepath.Join(home, registryConfig[2:])
	}

	// Create Docker client
	cli, err := NewDockerClient(config.Docker.Host, config.Docker.APIVersion)
	if err != nil {
		return nil, fmt.Errorf("failed to create Docker client: %w", err)
	}

	logger.Info("Docker client created",
		logger.String("host", config.Docker.Host),
		logger.String("api_version", config.Docker.APIVersion),
	)

	return &dockerClient{cli: cli}, nil
}

// Close closes the Docker client
func (c *dockerClient) Close() error {
	if c.cli != nil {
		return c.cli.Close()
	}
	return nil
}

// ContainerList lists containers
func (c *dockerClient) ContainerList(ctx context.Context, all bool) ([]types.Container, error) {
	return c.cli.ContainerList(ctx, all)
}

// ContainerListWithOptions lists containers with filter options
func (c *dockerClient) ContainerListWithOptions(ctx context.Context, options container.ListOptions) ([]types.Container, error) {
	return c.cli.ContainerListWithOptions(ctx, options)
}

// ContainerCreate creates a new container
func (c *dockerClient) ContainerCreate(ctx context.Context, config *container.Config, hostConfig *container.HostConfig, networkConfig *network.NetworkingConfig, name string) (container.CreateResponse, error) {
	return c.cli.ContainerCreate(ctx, config, hostConfig, networkConfig, name)
}

// ContainerStart starts a container
func (c *dockerClient) ContainerStart(ctx context.Context, containerID string) error {
	return c.cli.ContainerStart(ctx, containerID)
}

// ContainerStop stops a container
func (c *dockerClient) ContainerStop(ctx context.Context, containerID string, timeout *int) error {
	return c.cli.ContainerStop(ctx, containerID, timeout)
}

// ContainerRestart restarts a container
func (c *dockerClient) ContainerRestart(ctx context.Context, containerID string, timeout *int) error {
	return c.cli.ContainerRestart(ctx, containerID, timeout)
}

// ContainerRemove removes a container
func (c *dockerClient) ContainerRemove(ctx context.Context, containerID string, force, removeVolumes bool) error {
	return c.cli.ContainerRemove(ctx, containerID, force, removeVolumes)
}

// ContainerInspect inspects a container
func (c *dockerClient) ContainerInspect(ctx context.Context, containerID string) (types.ContainerJSON, error) {
	return c.cli.ContainerInspect(ctx, containerID)
}

// ContainerLogs gets container logs
func (c *dockerClient) ContainerLogs(ctx context.Context, containerID string, follow bool, tail string) (io.ReadCloser, error) {
	return c.cli.ContainerLogs(ctx, containerID, follow, tail)
}

// ContainerStats gets container stats
func (c *dockerClient) ContainerStats(ctx context.Context, containerID string, stream bool) (Stats, error) {
	return c.cli.ContainerStats(ctx, containerID, stream)
}

// ImageList lists images
func (c *dockerClient) ImageList(ctx context.Context, option image.ListOptions) ([]image.Summary, error) {
	return c.cli.ImageList(ctx, option)
}

// ImagePull pulls an image
func (c *dockerClient) ImagePull(ctx context.Context, ref string, options ImagePullOptions) (io.ReadCloser, error) {
	return c.cli.ImagePull(ctx, ref, options)
}

// ImagePush pushes an image
func (c *dockerClient) ImagePush(ctx context.Context, ref string, options ImagePushOptions) (io.ReadCloser, error) {
	return c.cli.ImagePush(ctx, ref, options)
}

// ImageRemove removes an image
func (c *dockerClient) ImageRemove(ctx context.Context, imageID string, force, pruneChildren bool) ([]image.DeleteResponse, error) {
	return c.cli.ImageRemove(ctx, imageID, force, pruneChildren)
}

// ImageInspect inspects an image
func (c *dockerClient) ImageInspect(ctx context.Context, imageID string) (types.ImageInspect, error) {
	return c.cli.ImageInspect(ctx, imageID)
}

// ImageHistory gets the history of an image
func (c *dockerClient) ImageHistory(ctx context.Context, imageID string) ([]image.HistoryResponseItem, error) {
	return c.cli.ImageHistory(ctx, imageID)
}

// ImageLoad loads an image from a file
func (c *dockerClient) ImageLoad(ctx context.Context, filePath string) (io.ReadCloser, error) {
	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file not found: %s", filePath)
	}

	// Mock implementation for now
	return io.NopCloser(strings.NewReader(`{"status":"Loading image from file: ` + filePath + `"}
{"status":"Load complete"}
`)), nil
}

// ImageSave saves images to a tar file
func (c *dockerClient) ImageSave(ctx context.Context, imageIDs []string) (io.ReadCloser, error) {
	return c.cli.ImageSave(ctx, imageIDs)
}

// ImagePrune prunes unused images
func (c *dockerClient) ImagePrune(ctx context.Context, all bool) (image.PruneReport, error) {
	return c.cli.ImagePrune(ctx, all)
}

// VolumeList lists volumes
func (c *dockerClient) VolumeList(ctx context.Context) (volume.ListResponse, error) {
	return c.cli.VolumeList(ctx)
}

// VolumeCreate creates a volume
func (c *dockerClient) VolumeCreate(ctx context.Context, options volume.CreateOptions) (volume.Volume, error) {
	return c.cli.VolumeCreate(ctx, options)
}

// VolumeRemove removes a volume
func (c *dockerClient) VolumeRemove(ctx context.Context, volumeID string, force bool) error {
	return c.cli.VolumeRemove(ctx, volumeID, force)
}

// VolumeInspect inspects a volume
func (c *dockerClient) VolumeInspect(ctx context.Context, volumeID string) (volume.Volume, error) {
	return c.cli.VolumeInspect(ctx, volumeID)
}

// NetworkList lists networks
func (c *dockerClient) NetworkList(ctx context.Context) ([]NetworkResource, error) {
	return c.cli.NetworkList(ctx)
}

// NetworkCreate creates a network
func (c *dockerClient) NetworkCreate(ctx context.Context, name string, options NetworkCreate) (NetworkCreateResponse, error) {
	return c.cli.NetworkCreate(ctx, name, options)
}

// NetworkRemove removes a network
func (c *dockerClient) NetworkRemove(ctx context.Context, networkID string) error {
	return c.cli.NetworkRemove(ctx, networkID)
}

// NetworkInspect inspects a network
func (c *dockerClient) NetworkInspect(ctx context.Context, networkID string, verbose bool) (NetworkResource, error) {
	return c.cli.NetworkInspect(ctx, networkID, verbose)
}

// SystemInfo gets system information
func (c *dockerClient) SystemInfo(ctx context.Context) (Info, error) {
	info, err := c.cli.SystemInfo(ctx)
	if err != nil {
		// Log the error for debugging
		logger.Error("Error getting system info", logger.Err(err))
	}
	return info, err
}

// SystemVersion gets system version
func (c *dockerClient) SystemVersion(ctx context.Context) (Version, error) {
	return c.cli.SystemVersion(ctx)
}

// SystemDiskUsage gets system disk usage
func (c *dockerClient) SystemDiskUsage(ctx context.Context) (DiskUsage, error) {
	return c.cli.SystemDiskUsage(ctx)
}

// SystemEvents gets system events
func (c *dockerClient) SystemEvents(ctx context.Context, options EventsOptions) (<-chan events.Message, <-chan error) {
	return c.cli.SystemEvents(ctx, options)
}

// ContainerCommit commits a container to an image
func (c *dockerClient) ContainerCommit(ctx context.Context, containerID string, options container.CommitOptions) (types.IDResponse, error) {
	return c.cli.ContainerCommit(ctx, containerID, options)
}

// ContainerExport exports a container to a tar file
func (c *dockerClient) ContainerExport(ctx context.Context, containerID string) (io.ReadCloser, error) {
	return c.cli.ContainerExport(ctx, containerID)
}

func (c *dockerClient) ContainerExecCreate(ctx context.Context, containerID string, options container.ExecOptions) (container.ExecCreateResponse, error) {
	return c.cli.ContainerExecCreate(ctx, containerID, options)
}

func (c *dockerClient) ContainerExecStart(ctx context.Context, execID string, config container.ExecStartOptions) (types.HijackedResponse, error) {
	return c.cli.ContainerExecStart(ctx, execID, config)
}

func (c *dockerClient) ContainerExecInspect(ctx context.Context, execID string) (container.ExecInspect, error) {
	return c.cli.ContainerExecInspect(ctx, execID)
}
