package docker

import (
	"context"
	"daemon-server/internal/taskflow"
	"daemon-server/pkg/command"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/go-connections/nat"

	"daemon-server/internal/logger"
)

// ContainerService provides container management operations
type ContainerService struct {
	client Client
}

// NewContainerService creates a new container service
func NewContainerService(client Client) *ContainerService {
	return &ContainerService{
		client: client,
	}
}

// ContainerInfo represents container information
type ContainerInfo struct {
	ID         string            `json:"id"`
	Name       string            `json:"name"`
	Image      string            `json:"image"`
	Command    string            `json:"command"`
	Created    time.Time         `json:"created"`
	Status     string            `json:"status"`
	State      string            `json:"state"`
	Ports      []types.Port      `json:"ports"`
	Labels     map[string]string `json:"labels"`
	SizeRw     int64             `json:"size_rw"`
	SizeRootFs int64             `json:"size_root_fs"`
	Networks   []string          `json:"networks"`
}

// ContainerCreateRequest represents a request to create a container
type ContainerCreateRequest struct {
	Name          string                    `json:"name"`
	Image         string                    `json:"image"`
	Command       []string                  `json:"command"`
	Entrypoint    []string                  `json:"entrypoint"`
	Env           []string                  `json:"env"`
	Labels        map[string]string         `json:"labels"`
	ExposedPorts  nat.PortSet               `json:"exposed_ports"`
	HostConfig    *container.HostConfig     `json:"host_config"`
	NetworkConfig *network.NetworkingConfig `json:"network_config"`
}

// ContainerCreateResponse represents a response from creating a container
type ContainerCreateResponse struct {
	ID       string   `json:"id"`
	Warnings []string `json:"warnings"`
}

// ListContainers lists all containers
func (s *ContainerService) ListContainers(ctx context.Context, all bool) ([]types.Container, error) {
	containers, err := s.client.ContainerList(ctx, all)
	if err != nil {
		logger.Error("Failed to list containers", logger.Err(err))
		return nil, fmt.Errorf("failed to list containers: %w", err)
	}

	return containers, nil
}

// ListContainersWithOptions lists containers with filter options
func (s *ContainerService) ListContainersWithOptions(ctx context.Context, options container.ListOptions) ([]types.Container, error) {
	containers, err := s.client.ContainerListWithOptions(ctx, options)
	if err != nil {
		logger.Error("Failed to list containers with options",
			logger.Err(err),
			logger.Bool("all", options.All),
		)
		return nil, fmt.Errorf("failed to list containers with options: %w", err)
	}

	return containers, nil
}

// CreateContainer creates a new container
func (s *ContainerService) CreateContainer(ctx context.Context, req ContainerCreateRequest) (*ContainerCreateResponse, error) {
	config := &container.Config{
		Image:        req.Image,
		Cmd:          req.Command,
		Entrypoint:   req.Entrypoint,
		Env:          req.Env,
		Labels:       req.Labels,
		ExposedPorts: req.ExposedPorts,
	}

	resp, err := s.client.ContainerCreate(ctx, config, req.HostConfig, req.NetworkConfig, req.Name)
	if err != nil {
		logger.Error("Failed to create container",
			logger.Err(err),
			logger.String("image", req.Image),
			logger.String("name", req.Name),
		)
		return nil, fmt.Errorf("failed to create container: %w", err)
	}

	logger.Info("Container created",
		logger.String("id", resp.ID),
		logger.String("name", req.Name),
		logger.String("image", req.Image),
	)

	return &ContainerCreateResponse{
		ID:       resp.ID,
		Warnings: resp.Warnings,
	}, nil
}

// StartContainer starts a container
func (s *ContainerService) StartContainer(ctx context.Context, containerID string) error {
	if err := s.client.ContainerStart(ctx, containerID); err != nil {
		logger.Error("Failed to start container",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		return fmt.Errorf("failed to start container: %w", err)
	}

	logger.Info("Container started", logger.String("container_id", containerID))
	return nil
}

// StopContainer stops a container
func (s *ContainerService) StopContainer(ctx context.Context, containerID string, timeout *int) error {
	if err := s.client.ContainerStop(ctx, containerID, timeout); err != nil {
		logger.Error("Failed to stop container",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		return fmt.Errorf("failed to stop container: %w", err)
	}

	logger.Info("Container stopped", logger.String("container_id", containerID))
	return nil
}

// RestartContainer restarts a container
func (s *ContainerService) RestartContainer(ctx context.Context, containerID string, timeout *int) error {
	if err := s.client.ContainerRestart(ctx, containerID, timeout); err != nil {
		logger.Error("Failed to restart container",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		return fmt.Errorf("failed to restart container: %w", err)
	}

	logger.Info("Container restarted", logger.String("container_id", containerID))
	return nil
}

// RemoveContainer removes a container
func (s *ContainerService) RemoveContainer(ctx context.Context, containerID string, force, removeVolumes bool) error {
	if err := s.client.ContainerRemove(ctx, containerID, force, removeVolumes); err != nil {
		logger.Error("Failed to remove container",
			logger.Err(err),
			logger.String("container_id", containerID),
			logger.Bool("force", force),
			logger.Bool("remove_volumes", removeVolumes),
		)
		return fmt.Errorf("failed to remove container: %w", err)
	}

	logger.Info("Container removed",
		logger.String("container_id", containerID),
		logger.Bool("force", force),
		logger.Bool("remove_volumes", removeVolumes),
	)
	return nil
}

// InspectContainer inspects a container
func (s *ContainerService) InspectContainer(ctx context.Context, containerID string) (*types.ContainerJSON, error) {
	containerJSON, err := s.client.ContainerInspect(ctx, containerID)
	if err != nil {
		logger.Error("Failed to inspect container",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		return nil, fmt.Errorf("failed to inspect container: %w", err)
	}

	logger.Info("Container inspected", logger.String("container_id", containerID))
	return &containerJSON, nil
}

// GetEnhancedContainerDetails gets enhanced container details
func (s *ContainerService) GetEnhancedContainerDetails(ctx context.Context, containerID string) (*EnhancedContainerDetails, error) {
	containerJSON, err := s.client.ContainerInspect(ctx, containerID)
	if err != nil {
		logger.Error("Failed to get enhanced container details",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		return nil, fmt.Errorf("failed to get enhanced container details: %w", err)
	}

	// Create enhanced container details
	details := &EnhancedContainerDetails{
		ID:              containerJSON.ID,
		Name:            containerJSON.Name,
		Image:           containerJSON.Config.Image,
		ImageID:         containerJSON.Image,
		Command:         strings.Join(containerJSON.Config.Cmd, " "),
		Created:         func() time.Time { t, _ := time.Parse(time.RFC3339, containerJSON.Created); return t }(),
		State:           containerJSON.State.Status,
		Status:          containerJSON.State.Status,
		ExitCode:        containerJSON.State.ExitCode,
		Error:           containerJSON.State.Error,
		RestartCount:    containerJSON.RestartCount,
		Ports:           containerJSON.NetworkSettings.Ports,
		NetworkMode:     string(containerJSON.HostConfig.NetworkMode),
		NetworkSettings: containerJSON.NetworkSettings,
		HostConfig:      containerJSON.HostConfig,
		Config:          containerJSON.Config,
		Health:          containerJSON.State.Health,
		Mounts:          containerJSON.Mounts,
		Labels:          containerJSON.Config.Labels,
	}

	// Add IP address information if available
	if containerJSON.NetworkSettings != nil {
		if containerJSON.NetworkSettings.IPAddress != "" {
			details.IPAddress = containerJSON.NetworkSettings.IPAddress
			details.IPPrefixLen = containerJSON.NetworkSettings.IPPrefixLen
			details.Gateway = containerJSON.NetworkSettings.Gateway
			details.MacAddress = containerJSON.NetworkSettings.MacAddress
		}
	}

	// Add size information if available
	if containerJSON.SizeRw != nil {
		details.SizeRw = *containerJSON.SizeRw
	}
	if containerJSON.SizeRootFs != nil {
		details.SizeRootFs = *containerJSON.SizeRootFs
	}

	// Add timestamps if available
	if containerJSON.State.StartedAt != "" && containerJSON.State.StartedAt != "0001-01-01T00:00:00Z" {
		startedAt, err := time.Parse(time.RFC3339, containerJSON.State.StartedAt)
		if err == nil {
			details.Started = startedAt
		}
	}
	if containerJSON.State.FinishedAt != "" && containerJSON.State.FinishedAt != "0001-01-01T00:00:00Z" {
		finishedAt, err := time.Parse(time.RFC3339, containerJSON.State.FinishedAt)
		if err == nil {
			details.Finished = finishedAt
		}
	}

	logger.Info("Enhanced container details retrieved", logger.String("container_id", containerID))
	return details, nil
}

// GetContainerLogs gets container logs
func (s *ContainerService) GetContainerLogs(ctx context.Context, containerID string, follow bool, tail string) (io.ReadCloser, error) {
	logs, err := s.client.ContainerLogs(ctx, containerID, follow, tail)
	if err != nil {
		logger.Error("Failed to get container logs",
			logger.Err(err),
			logger.String("container_id", containerID),
			logger.Bool("follow", follow),
			logger.String("tail", tail),
		)
		return nil, fmt.Errorf("failed to get container logs: %w", err)
	}

	logger.Info("Container logs retrieved",
		logger.String("container_id", containerID),
		logger.Bool("follow", follow),
		logger.String("tail", tail),
	)
	return logs, nil
}

// GetContainerStats gets container stats
func (s *ContainerService) GetContainerStats(ctx context.Context, containerID string, stream bool) (Stats, error) {
	stats, err := s.client.ContainerStats(ctx, containerID, stream)
	if err != nil {
		logger.Error("Failed to get container stats",
			logger.Err(err),
			logger.String("container_id", containerID),
			logger.Bool("stream", stream),
		)
		return Stats{}, fmt.Errorf("failed to get container stats: %w", err)
	}

	logger.Info("Container stats retrieved",
		logger.String("container_id", containerID),
		logger.Bool("stream", stream),
	)
	return stats, nil
}

func (s *ContainerService) ContainerExecCreate(ctx context.Context, containerID string, options container.ExecOptions) (container.ExecCreateResponse, error) {
	return s.client.ContainerExecCreate(ctx, containerID, options)
}
func (s *ContainerService) ContainerExecStart(ctx context.Context, execID string, config container.ExecStartOptions) (types.HijackedResponse, error) {
	return s.client.ContainerExecStart(ctx, execID, config)
}

func (s *ContainerService) ContainerExecInspect(ctx context.Context, execID string) (container.ExecInspect, error) {
	return s.client.ContainerExecInspect(ctx, execID)
}

// SimpleContainerCommitRequest 表示简化的容器提交请求
type SimpleContainerCommitRequest struct {
	ContainerID string                  `json:"container_id"`
	Options     container.CommitOptions `json:"options,omitempty"`
}

// CommitContainer 将容器提交为镜像
func (s *ContainerService) CommitContainer(ctx context.Context, req SimpleContainerCommitRequest) (string, error) {

	// 调用Docker SDK的ContainerCommit方法
	resp, err := s.client.ContainerCommit(ctx, req.ContainerID, req.Options)
	if err != nil {
		logger.Error("Failed to commit container",
			logger.Err(err),
			logger.String("container_id", req.ContainerID),
			logger.String("reference", req.Options.Reference),
			logger.Bool("pause", req.Options.Pause),
		)
		return "", fmt.Errorf("failed to commit container: %w", err)
	}

	logger.Info("Container committed successfully",
		logger.String("image_id", resp.ID),
		logger.String("container_id", req.ContainerID),
		logger.String("reference", req.Options.Reference),
		logger.Bool("pause", req.Options.Pause),
	)
	return resp.ID, nil
}

// ExportContainer 导出容器为tar文件
func (s *ContainerService) ExportContainer(ctx context.Context, containerID string) (io.ReadCloser, error) {
	if containerID == "" {
		logger.Error("Failed to export container: container ID is empty")
		return nil, fmt.Errorf("container ID is required")
	}

	// 使用Docker SDK的ContainerExport方法
	reader, err := s.client.ContainerExport(ctx, containerID)
	if err != nil {
		logger.Error("Failed to export container",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		return nil, fmt.Errorf("failed to export container: %w", err)
	}

	logger.Info("Container exported successfully", logger.String("container_id", containerID))
	return reader, nil
}

// GetContainerPorts 获取容器端口映射
func (s *ContainerService) GetContainerPorts(ctx context.Context, containerID string) (nat.PortMap, error) {
	// 直接使用ContainerInspect方法获取容器信息
	containerJSON, err := s.client.ContainerInspect(ctx, containerID)
	if err != nil {
		logger.Error("Failed to get container ports",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		return nil, fmt.Errorf("failed to get container ports: %w", err)
	}

	logger.Info("Container ports retrieved", logger.String("container_id", containerID))
	return containerJSON.NetworkSettings.Ports, nil
}

// ExecContainer 在容器中执行命令
func (s *ContainerService) ExecContainer(ctx context.Context, containerID string, options container.ExecOptions) (*container.ExecInspect, string, error) {
	// 验证输入
	if containerID == "" {
		logger.Error("Failed to execute command: container ID is empty")
		return nil, "", fmt.Errorf("container ID is required")
	}

	if len(options.Cmd) == 0 {
		logger.Error("Failed to execute command: command is empty")
		return nil, "", fmt.Errorf("command is required")
	}

	// 创建执行实例
	resp, err := s.client.ContainerExecCreate(ctx, containerID, options)
	if err != nil {
		logger.Error("Failed to create exec instance",
			logger.Err(err),
			logger.String("container_id", containerID),
			logger.String("command", strings.Join(options.Cmd, " ")),
		)
		return nil, "", fmt.Errorf("failed to create exec instance: %w", err)
	}

	// 附加到执行实例
	hijackedResp, err := s.client.ContainerExecStart(ctx, resp.ID, container.ExecStartOptions{
		Detach: options.Detach,
		Tty:    options.Tty,
	})
	if err != nil {
		logger.Error("Failed to attach to exec instance",
			logger.Err(err),
			logger.String("container_id", containerID),
			logger.String("exec_id", resp.ID),
		)
		return nil, "", fmt.Errorf("failed to attach to exec instance: %w", err)
	}
	defer hijackedResp.Close()

	// 读取输出
	var output strings.Builder
	if !options.Detach {
		// 从连接中读取输出
		buf := make([]byte, 1024)
		for {
			n, err := hijackedResp.Reader.Read(buf)
			if err != nil && err != io.EOF {
				logger.Error("Failed to read exec output",
					logger.Err(err),
					logger.String("container_id", containerID),
					logger.String("exec_id", resp.ID),
				)
				return nil, "", fmt.Errorf("failed to read exec output: %w", err)
			}
			if n == 0 {
				break
			}
			output.Write(buf[:n])
		}
	}

	// 检查执行状态
	inspect, err := s.client.ContainerExecInspect(ctx, resp.ID)
	if err != nil {
		logger.Error("Failed to inspect exec instance",
			logger.Err(err),
			logger.String("container_id", containerID),
			logger.String("exec_id", resp.ID),
		)
		return nil, "", fmt.Errorf("failed to inspect exec instance: %w", err)
	}

	logger.Info("Command executed in container",
		logger.String("container_id", containerID),
		logger.String("exec_id", resp.ID),
		logger.Int("exit_code", inspect.ExitCode),
		logger.Bool("running", inspect.Running),
	)

	return &inspect, output.String(), nil
}

// EnhancedCreateContainerTask creates an enhanced task function for container creation with better error handling and logging
func EnhancedCreateContainerTask(dockerSvc *Service) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting container creation task",
			logger.Any("input", input),
		)

		// Convert input to container create request
		req, ok := input.(ContainerCreateRequest)
		if !ok {
			err := fmt.Errorf("invalid input type for container creation task: expected ContainerCreateRequest, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Validate input
		if req.Image == "" {
			err := fmt.Errorf("image is required for container creation")
			logger.Error("Task validation failed", logger.Err(err))
			return nil, err
		}

		logger.Info("Creating container",
			logger.String("image", req.Image),
			logger.String("name", req.Name),
		)

		// Create container
		resp, err := dockerSvc.Container().CreateContainer(ctx, req)
		if err != nil {
			logger.Error("Task: Failed to create container",
				logger.Err(err),
				logger.String("image", req.Image),
				logger.String("name", req.Name),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to create container: %w", err)
		}

		logger.Info("Container created successfully",
			logger.String("container_id", resp.ID),
			logger.String("image", req.Image),
			logger.String("name", req.Name),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"container_id": resp.ID,
			"warnings":     resp.Warnings,
			"message":      "Container created successfully",
		}, nil
	}
}

// EnhancedStartContainerTask creates an enhanced task function for container starting with better error handling and logging
func EnhancedStartContainerTask(dockerSvc *Service) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting container start task",
			logger.Any("input", input),
		)

		// Convert input to container ID
		containerID, ok := input.(string)
		if !ok {
			err := fmt.Errorf("invalid input type for container start task: expected string, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Validate input
		if containerID == "" {
			err := fmt.Errorf("container ID is required for container start")
			logger.Error("Task validation failed", logger.Err(err))
			return nil, err
		}

		logger.Info("Starting container",
			logger.String("container_id", containerID),
		)

		// Start container
		err := dockerSvc.Container().StartContainer(ctx, containerID)
		if err != nil {
			logger.Error("Task: Failed to start container",
				logger.Err(err),
				logger.String("container_id", containerID),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to start container: %w", err)
		}

		logger.Info("Container started successfully",
			logger.String("container_id", containerID),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"message":      "Container started successfully",
			"container_id": containerID,
		}, nil
	}
}

// EnhancedStopContainerTask creates an enhanced task function for container stopping with better error handling and logging
func EnhancedStopContainerTask(dockerSvc *Service) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting container stop task",
			logger.Any("input", input),
		)

		// Convert input to map containing container ID and timeout
		inputMap, ok := input.(map[string]interface{})
		if !ok {
			err := fmt.Errorf("invalid input type for container stop task: expected map, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract container ID
		containerID, ok := inputMap["container_id"].(string)
		if !ok {
			err := fmt.Errorf("container_id is required and must be a string")
			logger.Error("Task validation failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract timeout if provided
		var timeout *int
		if timeoutVal, ok := inputMap["timeout"].(float64); ok {
			timeoutInt := int(timeoutVal)
			timeout = &timeoutInt
			logger.Info("Using custom timeout for container stop",
				logger.Int("timeout", timeoutInt),
			)
		}

		logger.Info("Stopping container",
			logger.String("container_id", containerID),
		)

		// Stop container
		err := dockerSvc.Container().StopContainer(ctx, containerID, timeout)
		if err == nil {
			logger.Info("Container stopped successfully",
				logger.String("container_id", containerID),
				logger.Duration("duration", time.Since(startTime)),
			)

			return map[string]interface{}{
				"message":      "Container stopped successfully",
				"container_id": containerID,
			}, nil
		}
		logger.Error("Task: Failed to stop container",
			logger.Err(err),
			logger.String("container_id", containerID),
			logger.Duration("duration", time.Since(startTime)),
		)

		containerInfo, err := dockerSvc.Container().InspectContainer(ctx, containerID)
		if err != nil {
			logger.Info("Container not found, skipping stop",
				logger.String("container_id", containerID),
			)

			return nil, fmt.Errorf("container[%s] not found: %w", containerID, err)
		}
		if containerInfo.State.Running {
			logger.Info("Container is running, force killing container process", logger.String("container_id", containerID))
			// force kill container process
			pid := containerInfo.State.Pid
			if pid < 300 {
				logger.Info("Container process not found, skipping force kill",
					logger.String("container_id", containerID),
					logger.Int("pid", pid),
				)
				return nil, fmt.Errorf("container[%s] process not found", containerID)
			}
			cmd := fmt.Sprintf("kill -9 %d", pid)
			resp, err := command.Execute(command.Request{Command: cmd, Timeout: 5})
			if err != nil {
				logger.Error("Failed to force kill container process",
					logger.Err(err),
					logger.String("container_id", containerID),
					logger.String("command", cmd),
					logger.Any("resp", resp),
				)
				return nil, fmt.Errorf("failed to force kill container process: %w", err)
			}
			logger.Info("Container process killed successfully",
				logger.String("container_id", containerID),
				logger.String("command", cmd),
				logger.Any("resp", resp),
			)

			return map[string]interface{}{
				"message":      "Container force killed",
				"container_id": containerID,
			}, nil
		}
		logger.Info("Container stopped before force kill, skipping stop",
			logger.String("container_id", containerID),
		)

		return map[string]interface{}{
			"message":      "Container already stopped before force kill",
			"container_id": containerID,
		}, nil
	}
}

// EnhancedRestartContainerTask creates an enhanced task function for container restarting with better error handling and logging
func EnhancedRestartContainerTask(dockerSvc *Service) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting container restart task",
			logger.Any("input", input),
		)

		// Convert input to map containing container ID and timeout
		inputMap, ok := input.(map[string]interface{})
		if !ok {
			err := fmt.Errorf("invalid input type for container restart task: expected map, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract container ID
		containerID, ok := inputMap["container_id"].(string)
		if !ok {
			err := fmt.Errorf("container_id is required and must be a string")
			logger.Error("Task validation failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract timeout if provided
		var timeout *int
		if timeoutVal, ok := inputMap["timeout"].(float64); ok {
			timeoutInt := int(timeoutVal)
			timeout = &timeoutInt
			logger.Info("Using custom timeout for container restart",
				logger.Int("timeout", timeoutInt),
			)
		}

		logger.Info("Restarting container",
			logger.String("container_id", containerID),
		)

		// Restart container
		err := dockerSvc.Container().RestartContainer(ctx, containerID, timeout)
		if err != nil {
			logger.Error("Task: Failed to restart container",
				logger.Err(err),
				logger.String("container_id", containerID),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to restart container: %w", err)
		}

		logger.Info("Container restarted successfully",
			logger.String("container_id", containerID),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"message":      "Container restarted successfully",
			"container_id": containerID,
		}, nil
	}
}

// EnhancedRemoveContainerTask creates an enhanced task function for container removal with better error handling and logging
func EnhancedRemoveContainerTask(dockerSvc *Service) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting container remove task",
			logger.Any("input", input),
		)

		// Convert input to map containing container ID, force, and removeVolumes
		inputMap, ok := input.(map[string]interface{})
		if !ok {
			err := fmt.Errorf("invalid input type for container remove task: expected map, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract container ID
		containerID, ok := inputMap["container_id"].(string)
		if !ok {
			err := fmt.Errorf("container_id is required and must be a string")
			logger.Error("Task validation failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract force and removeVolumes flags
		force, _ := inputMap["force"].(bool)
		removeVolumes, _ := inputMap["remove_volumes"].(bool)

		logger.Info("Removing container",
			logger.String("container_id", containerID),
			logger.Bool("force", force),
			logger.Bool("remove_volumes", removeVolumes),
		)

		// Remove container
		err := dockerSvc.Container().RemoveContainer(ctx, containerID, force, removeVolumes)
		if err != nil {
			logger.Error("Task: Failed to remove container",
				logger.Err(err),
				logger.String("container_id", containerID),
				logger.Bool("force", force),
				logger.Bool("remove_volumes", removeVolumes),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to remove container: %w", err)
		}

		logger.Info("Container removed successfully",
			logger.String("container_id", containerID),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"message":      "Container removed successfully",
			"container_id": containerID,
		}, nil
	}
}

// EnhancedCommitContainerTask creates an enhanced task function for container committing with better error handling and logging
func EnhancedCommitContainerTask(dockerSvc *Service) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting container commit task",
			logger.Any("input", input),
		)

		// Convert input to container commit request
		req, ok := input.(SimpleContainerCommitRequest)
		if !ok {
			err := fmt.Errorf("invalid input type for container commit task: expected SimpleContainerCommitRequest, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Validate input
		if req.ContainerID == "" {
			err := fmt.Errorf("container ID is required for container commit")
			logger.Error("Task validation failed", logger.Err(err))
			return nil, err
		}

		logger.Info("Committing container",
			logger.String("container_id", req.ContainerID),
			logger.String("reference", req.Options.Reference),
			logger.Bool("pause", req.Options.Pause),
		)

		// Commit container
		imageID, err := dockerSvc.Container().CommitContainer(ctx, req)
		if err != nil {
			logger.Error("Task: Failed to commit container",
				logger.Err(err),
				logger.String("container_id", req.ContainerID),
				logger.String("reference", req.Options.Reference),
				logger.Bool("pause", req.Options.Pause),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to commit container: %w", err)
		}

		logger.Info("Container committed successfully",
			logger.String("container_id", req.ContainerID),
			logger.String("image_id", imageID),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"message":      "Container committed successfully",
			"container_id": req.ContainerID,
			"image_id":     imageID,
		}, nil
	}
}

// EnhancedExportContainerTask creates an enhanced task function for container exporting with better error handling and logging
func EnhancedExportContainerTask(dockerSvc *Service) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting container export task",
			logger.Any("input", input),
		)

		// Convert input to container ID
		containerID, ok := input.(string)
		if !ok {
			err := fmt.Errorf("invalid input type for container export task: expected string, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Validate input
		if containerID == "" {
			err := fmt.Errorf("container ID is required for container export")
			logger.Error("Task validation failed", logger.Err(err))
			return nil, err
		}

		logger.Info("Exporting container",
			logger.String("container_id", containerID),
		)

		// Export container
		reader, err := dockerSvc.Container().ExportContainer(ctx, containerID)
		if err != nil {
			logger.Error("Task: Failed to export container",
				logger.Err(err),
				logger.String("container_id", containerID),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to export container: %w", err)
		}
		defer reader.Close()

		// For demonstration purposes, we'll just read the first few bytes
		// In a real implementation, you would save this to a file and return the file path
		buf := make([]byte, 1024)
		n, _ := reader.Read(buf)
		preview := string(buf[:n])

		logger.Info("Container exported successfully",
			logger.String("container_id", containerID),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"message":      "Container exported successfully",
			"container_id": containerID,
			"preview":      preview,
			// In a real implementation: "file_path": "/path/to/export.tar"
		}, nil
	}
}

// EnhancedExecContainerTask creates an enhanced task function for container command execution with better error handling and logging
func EnhancedExecContainerTask(dockerSvc *Service) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting container exec task",
			logger.Any("input", input),
		)

		// Convert input to map containing container ID and exec request
		inputMap, ok := input.(map[string]interface{})
		if !ok {
			err := fmt.Errorf("invalid input type for container exec task: expected map, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract container ID
		containerID, ok := inputMap["container_id"].(string)
		if !ok {
			err := fmt.Errorf("container_id is required and must be a string")
			logger.Error("Task validation failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract exec config
		options, ok := inputMap["exec_config"].(container.ExecOptions)
		if !ok {
			err := fmt.Errorf("exec_config is required and must be an object")
			logger.Error("Task validation failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}
		// Execute command in container
		resp, out, err := dockerSvc.Container().ExecContainer(ctx, containerID, options)
		if err != nil {
			logger.Error("Task: Failed to execute command in container",
				logger.Err(err),
				logger.String("container_id", containerID),
				logger.String("command", strings.Join(options.Cmd, " ")),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to execute command in container: %w", err)
		}

		logger.Info("Command executed successfully",
			logger.String("container_id", containerID),
			logger.String("command", strings.Join(options.Cmd, " ")),
			logger.Int("exit_code", resp.ExitCode),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"message":      "Command executed successfully",
			"container_id": containerID,
			"command":      strings.Join(options.Cmd, " "),
			"output":       out,
			"resp":         resp,
		}, nil
	}
}
