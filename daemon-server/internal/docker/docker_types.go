package docker

import (
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/filters"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/docker/api/types/volume"
)

// ImagePullOptions represents options for pulling images
type ImagePullOptions struct {
	RegistryAuth string
}

// ImagePushOptions represents options for pushing images
type ImagePushOptions struct {
	RegistryAuth string
}

// NetworkCreate represents options for creating a network
type NetworkCreate struct {
	Driver     string
	Internal   bool
	Options    map[string]string
	Labels     map[string]string
	EnableIPv6 bool
}

// NetworkCreateResponse represents the response from network creation
type NetworkCreateResponse struct {
	ID      string
	Warning string
}

// NetworkResource represents a Docker network resource
type NetworkResource struct {
	ID         string
	Name       string
	Driver     string
	Scope      string
	Internal   bool
	EnableIPv6 bool
	IPAM       network.IPAM
	Containers map[string]network.EndpointResource
	Options    map[string]string
	Labels     map[string]string
}

// Stats represents container stats
type Stats struct {
	Read        string                      `json:"read"`
	PreRead     string                      `json:"preread"`
	CPUStats    CPUStats                    `json:"cpu_stats,omitempty"`
	PreCPUStats CPUStats                    `json:"precpu_stats,omitempty"`
	MemoryStats MemoryStats                 `json:"memory_stats,omitempty"`
	BlkioStats  BlkioStats                  `json:"blkio_stats,omitempty"`
	PidsStats   PidsStats                   `json:"pids_stats,omitempty"`
	Networks    map[string]NetworkStats     `json:"networks,omitempty"`
}

// CPUStats aggregates and wraps all CPU related info of container
type CPUStats struct {
	CPUUsage       CPUUsage       `json:"cpu_usage"`
	SystemUsage    uint64         `json:"system_cpu_usage"`
	ThrottlingData ThrottlingData `json:"throttling_data,omitempty"`
}

// CPUUsage stores All CPU stats aggregated since container inception
type CPUUsage struct {
	TotalUsage        uint64   `json:"total_usage"`
	PercpuUsage       []uint64 `json:"percpu_usage"`
	UsageInKernelmode uint64   `json:"usage_in_kernelmode"`
	UsageInUsermode   uint64   `json:"usage_in_usermode"`
}

// ThrottlingData stores CPU throttling stats of one running container
type ThrottlingData struct {
	Periods          uint64 `json:"periods"`
	ThrottledPeriods uint64 `json:"throttled_periods"`
	ThrottledTime    uint64 `json:"throttled_time"`
}

// MemoryStats aggregates all memory stats since container inception
type MemoryStats struct {
	Usage    uint64            `json:"usage"`
	MaxUsage uint64            `json:"max_usage"`
	Stats    map[string]uint64 `json:"stats"`
	Failcnt  uint64            `json:"failcnt"`
	Limit    uint64            `json:"limit"`
}

// BlkioStats stores All IO service stats for data read and write
type BlkioStats struct {
	IoServiceBytesRecursive []BlkioStatEntry `json:"io_service_bytes_recursive"`
	IoServicedRecursive     []BlkioStatEntry `json:"io_serviced_recursive"`
	IoQueuedRecursive       []BlkioStatEntry `json:"io_queue_recursive"`
	IoServiceTimeRecursive  []BlkioStatEntry `json:"io_service_time_recursive"`
	IoWaitTimeRecursive     []BlkioStatEntry `json:"io_wait_time_recursive"`
	IoMergedRecursive       []BlkioStatEntry `json:"io_merged_recursive"`
	IoTimeRecursive         []BlkioStatEntry `json:"io_time_recursive"`
	SectorsRecursive        []BlkioStatEntry `json:"sectors_recursive"`
}

// BlkioStatEntry is one small entity to store a piece of Blkio stats
type BlkioStatEntry struct {
	Major uint64 `json:"major"`
	Minor uint64 `json:"minor"`
	Op    string `json:"op"`
	Value uint64 `json:"value"`
}

// NetworkStats aggregates the network stats of one container
type NetworkStats struct {
	RxBytes   uint64 `json:"rx_bytes"`
	RxPackets uint64 `json:"rx_packets"`
	RxErrors  uint64 `json:"rx_errors"`
	RxDropped uint64 `json:"rx_dropped"`
	TxBytes   uint64 `json:"tx_bytes"`
	TxPackets uint64 `json:"tx_packets"`
	TxErrors  uint64 `json:"tx_errors"`
	TxDropped uint64 `json:"tx_dropped"`
}

// PidsStats contains the stats of a container's pids
type PidsStats struct {
	Current uint64 `json:"current,omitempty"`
	Limit   uint64 `json:"limit,omitempty"`
}

// Info represents system information
type Info struct {
	ID                string
	Containers        int
	ContainersRunning int
	ContainersPaused  int
	ContainersStopped int
	Images            int
	Driver            string
	DriverStatus      [][2]string
	SystemTime        string
	LoggingDriver     string
	CgroupDriver      string
	KernelVersion     string
	OperatingSystem   string
	OSType            string
	Architecture      string
	NCPU              int
	MemTotal          int64
	DockerRootDir     string
	ServerVersion     string
}

// Version represents version information
type Version struct {
	Version       string
	APIVersion    string
	MinAPIVersion string
	GitCommit     string
	GoVersion     string
	Os            string
	Arch          string
	BuildTime     string
}

// DiskUsage represents disk usage information
type DiskUsage struct {
	LayersSize int64
	Images     []image.Summary
	Containers []types.Container
	Volumes    []volume.Volume
	BuildCache []types.BuildCache
}

// EventsOptions represents options for getting events
type EventsOptions struct {
	Since   string
	Until   string
	Filters filters.Args
}
