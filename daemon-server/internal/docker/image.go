package docker

import (
	"context"
	"fmt"
	"io"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/image"

	"daemon-server/internal/logger"
)

// ImageService provides image management operations
type ImageService struct {
	client Client
}

// NewImageService creates a new image service
func NewImageService(client Client) *ImageService {
	return &ImageService{
		client: client,
	}
}

// ImageInfo represents image information
type ImageInfo struct {
	ID          string            `json:"id"`
	RepoTags    []string          `json:"repo_tags"`
	RepoDigests []string          `json:"repo_digests"`
	Created     time.Time         `json:"created"`
	Size        int64             `json:"size"`
	Labels      map[string]string `json:"labels"`
}

// ListImages lists all images
func (s *ImageService) ListImages(ctx context.Context, option image.ListOptions) ([]image.Summary, error) {
	images, err := s.client.ImageList(ctx, option)
	if err != nil {
		logger.Error("Failed to list images", logger.Err(err))
		return nil, fmt.Errorf("failed to list images: %w", err)
	}

	logger.Info("Listed images",
		logger.Int("count", len(images)),
		logger.Any("option", option),
	)

	return images, nil
}

// PullImage pulls an image
func (s *ImageService) PullImage(ctx context.Context, ref string, auth string) (io.ReadCloser, error) {
	options := ImagePullOptions{
		RegistryAuth: auth,
	}

	reader, err := s.client.ImagePull(ctx, ref, options)
	if err != nil {
		logger.Error("Failed to pull image",
			logger.Err(err),
			logger.String("ref", ref),
		)
		return nil, fmt.Errorf("failed to pull image: %w", err)
	}

	logger.Info("Image pull started", logger.String("ref", ref))
	return reader, nil
}

// RemoveImage removes an image
func (s *ImageService) RemoveImage(ctx context.Context, imageID string, force, pruneChildren bool) ([]image.DeleteResponse, error) {
	responses, err := s.client.ImageRemove(ctx, imageID, force, pruneChildren)
	if err != nil {
		logger.Error("Failed to remove image",
			logger.Err(err),
			logger.String("image_id", imageID),
			logger.Bool("force", force),
			logger.Bool("prune_children", pruneChildren),
		)
		return nil, fmt.Errorf("failed to remove image: %w", err)
	}

	logger.Info("Image removed",
		logger.String("image_id", imageID),
		logger.Int("responses", len(responses)),
	)
	return responses, nil
}

// InspectImage inspects an image
func (s *ImageService) InspectImage(ctx context.Context, imageID string) (*types.ImageInspect, error) {
	inspect, err := s.client.ImageInspect(ctx, imageID)
	if err != nil {
		logger.Error("Failed to inspect image",
			logger.Err(err),
			logger.String("image_id", imageID),
		)
		return nil, fmt.Errorf("failed to inspect image: %w", err)
	}

	logger.Info("Image inspected", logger.String("image_id", imageID))
	return &inspect, nil
}

// GetImageHistory gets the history of an image
func (s *ImageService) GetImageHistory(ctx context.Context, imageID string) ([]image.HistoryResponseItem, error) {
	history, err := s.client.ImageHistory(ctx, imageID)
	if err != nil {
		logger.Error("Failed to get image history",
			logger.Err(err),
			logger.String("image_id", imageID),
		)
		return nil, fmt.Errorf("failed to get image history: %w", err)
	}

	logger.Info("Image history retrieved",
		logger.String("image_id", imageID),
		logger.Int("layers", len(history)),
	)
	return history, nil
}

// SimplePushImageRequest 表示简化的镜像推送请求
type SimplePushImageRequest struct {
	TaskID string `json:"task_id"`        // 任务ID，用于实时输出
	Image  string `json:"image"`          // 镜像名称，格式为 "repository:tag"
	Auth   string `json:"auth,omitempty"` // Base64 编码的认证字符串
}

// PushImage 推送镜像
func (s *ImageService) PushImage(ctx context.Context, req SimplePushImageRequest) (io.ReadCloser, error) {
	// 验证镜像名称
	if req.Image == "" {
		logger.Error("Failed to push image: image name is required")
		return nil, fmt.Errorf("image name is required")
	}

	// 创建镜像推送选项
	options := ImagePushOptions{
		RegistryAuth: req.Auth,
	}

	// 调用Docker客户端推送镜像
	reader, err := s.client.ImagePush(ctx, req.Image, options)
	if err != nil {
		logger.Error("Failed to push image",
			logger.Err(err),
			logger.String("image", req.Image),
			logger.Bool("with_auth", req.Auth != ""),
		)
		return nil, fmt.Errorf("failed to push image: %w", err)
	}

	logger.Info("Image push started",
		logger.String("image", req.Image),
		logger.Bool("with_auth", req.Auth != ""),
	)
	return reader, nil
}

// SaveImage 保存镜像为tar文件
func (s *ImageService) SaveImage(ctx context.Context, imageIDs []string) (io.ReadCloser, error) {
	// 验证镜像ID列表
	if len(imageIDs) == 0 {
		logger.Error("Failed to save images: at least one image ID is required")
		return nil, fmt.Errorf("at least one image ID is required")
	}

	// 调用Docker客户端保存镜像
	reader, err := s.client.ImageSave(ctx, imageIDs)
	if err != nil {
		logger.Error("Failed to save images",
			logger.Err(err),
			logger.Any("image_ids", imageIDs),
		)
		return nil, fmt.Errorf("failed to save images: %w", err)
	}

	logger.Info("Images saved successfully",
		logger.Any("image_ids", imageIDs),
	)
	return reader, nil
}

// LoadImage 从本地文件加载镜像
func (s *ImageService) LoadImage(ctx context.Context, filePath string) (io.ReadCloser, error) {
	// 验证文件路径
	if filePath == "" {
		logger.Error("Failed to load image: file path is required")
		return nil, fmt.Errorf("file path is required")
	}

	// 调用Docker客户端加载镜像
	reader, err := s.client.ImageLoad(ctx, filePath)
	if err != nil {
		logger.Error("Failed to load image",
			logger.Err(err),
			logger.String("file_path", filePath),
		)
		return nil, fmt.Errorf("failed to load image: %w", err)
	}

	logger.Info("Image load started",
		logger.String("file_path", filePath),
	)
	return reader, nil
}

// PruneImages 清理未使用的镜像
func (s *ImageService) PruneImages(ctx context.Context, all bool) (image.PruneReport, error) {
	// 使用Docker客户端的ImagePrune方法清理未使用的镜像
	response, err := s.client.ImagePrune(ctx, all)
	if err != nil {
		logger.Error("Failed to prune images",
			logger.Err(err),
			logger.Bool("all", all),
		)
		return image.PruneReport{}, fmt.Errorf("failed to prune images: %w", err)
	}

	logger.Info("Images pruned successfully",
		logger.Int("deleted_count", len(response.ImagesDeleted)),
		logger.Int64("space_reclaimed", int64(response.SpaceReclaimed)),
		logger.Bool("all", all),
	)
	return response, nil
}
