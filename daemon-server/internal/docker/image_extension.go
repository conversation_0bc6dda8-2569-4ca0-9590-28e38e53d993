package docker

import (
	"bufio"
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"daemon-server/internal/logger"
	"daemon-server/internal/taskflow"
)

// EnhancedPushImageTask 创建增强的镜像推送任务函数，具有更好的错误处理和日志记录
func EnhancedPushImageTask(dockerSvc *Service, updateOutput func(string, bool, int, string)) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting image push task",
			logger.Any("input", input),
		)

		// 将输入转换为镜像推送请求
		req, ok := input.(SimplePushImageRequest)
		if !ok {
			err := fmt.Errorf("invalid input type for image push task: expected SimplePushImageRequest, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// 验证镜像名称
		if req.Image == "" {
			err := fmt.Errorf("image name is required")
			logger.Error("Task validation failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		logger.Info("Pushing image",
			logger.String("image", req.Image),
			logger.Bool("with_auth", req.Auth != ""),
		)

		// 推送镜像
		reader, err := dockerSvc.Image().PushImage(ctx, req)
		if err != nil {
			logger.Error("Task: Failed to push image",
				logger.Err(err),
				logger.String("image", req.Image),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to push image: %w", err)
		}
		defer reader.Close()

		output := strings.Builder{}
		scan := bufio.NewScanner(reader)
		for scan.Scan() {
			line := scan.Text()
			output.WriteString(line + "\n")
			updateOutput(req.TaskID, true, 50, line)
		}

		logger.Info("Image pushed successfully",
			logger.String("image", req.Image),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"message": "Image pushed successfully",
			"image":   req.Image,
			"output":  output.String(),
		}, nil
	}
}

// EnhancedSaveImageTask 创建增强的镜像保存任务函数，具有更好的错误处理和日志记录
func EnhancedSaveImageTask(dockerSvc *Service) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting image save task",
			logger.Any("input", input),
		)

		param := input.(ImageSaveParam)
		// 保存镜像

		reader, err := dockerSvc.Image().SaveImage(ctx, []string{param.ImageId})
		if err != nil {
			logger.Error("Task: Failed to save images",
				logger.Err(err),
				logger.Any("image_id", param.ImageId),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to save images: %w", err)
		}
		defer reader.Close()

		_, err = os.Stat(param.SavePath)
		if err == nil {
			msg := fmt.Sprintf("file already exists: %s", param.SavePath)
			logger.Error(msg)
			return nil, errors.New(msg)
		}
		if !os.IsNotExist(err) {
			msg := fmt.Sprintf("get file[%s] stats err: %v", param.SavePath, err)
			logger.Error(msg)
			return nil, errors.New(msg)
		}
		file, err := os.Create(param.SavePath)
		if err != nil {
			msg := fmt.Sprintf("create file[%s] err: %v", param.SavePath, err)
			logger.Error(msg)
			return nil, errors.New(msg)
		}
		defer file.Close()

		_, err = io.Copy(file, reader)
		if err != nil {
			msg := fmt.Sprintf("copy image to file[%s] err: %v", param.SavePath, err)
			logger.Error(msg)
			return nil, errors.New(msg)
		}

		logger.Info("Images saved successfully",
			logger.String("image_id", param.ImageId),
			logger.String("save_path", param.SavePath),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"message":   "Images saved successfully",
			"image_id":  param.ImageId,
			"save_path": param.SavePath,
		}, nil
	}
}

// EnhancedLoadImageTask 创建增强的镜像加载任务函数，具有更好的错误处理和日志记录
func EnhancedLoadImageTask(dockerSvc *Service) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting image load task",
			logger.Any("input", input),
		)

		// 将输入转换为文件路径
		var filePath string
		switch v := input.(type) {
		case string:
			filePath = v
		case SimpleLoadImageRequest:
			filePath = v.FilePath
		case map[string]interface{}:
			if path, ok := v["file_path"].(string); ok {
				filePath = path
			} else {
				err := fmt.Errorf("file_path is required and must be a string")
				logger.Error("Task validation failed",
					logger.Err(err),
					logger.Any("input", input),
				)
				return nil, err
			}
		default:
			err := fmt.Errorf("invalid input type for image load task: expected string, SimpleLoadImageRequest or map, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// 验证文件路径
		if filePath == "" {
			err := fmt.Errorf("file path is required")
			logger.Error("Task validation failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		logger.Info("Loading image from file",
			logger.String("file_path", filePath),
		)

		// 加载镜像
		reader, err := dockerSvc.Image().LoadImage(ctx, filePath)
		if err != nil {
			logger.Error("Task: Failed to load image",
				logger.Err(err),
				logger.String("file_path", filePath),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to load image: %w", err)
		}
		defer reader.Close()

		// 读取输出
		buf := make([]byte, 1024)
		n, _ := reader.Read(buf)
		preview := string(buf[:n])

		logger.Info("Image loaded successfully",
			logger.String("file_path", filePath),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"message":   "Image loaded successfully",
			"file_path": filePath,
			"preview":   preview,
		}, nil
	}
}

// EnhancedPruneImagesTask 创建增强的镜像清理任务函数，具有更好的错误处理和日志记录
func EnhancedPruneImagesTask(dockerSvc *Service) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting image prune task",
			logger.Any("input", input),
		)

		// 将输入转换为all标志
		var all bool
		switch v := input.(type) {
		case bool:
			all = v
		case map[string]interface{}:
			if value, ok := v["all"].(bool); ok {
				all = value
			}
		default:
			// 默认不清理所有镜像
			all = false
		}

		logger.Info("Pruning images",
			logger.Bool("all", all),
		)

		// 清理镜像
		response, err := dockerSvc.Image().PruneImages(ctx, all)
		if err != nil {
			logger.Error("Task: Failed to prune images",
				logger.Err(err),
				logger.Bool("all", all),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to prune images: %w", err)
		}

		logger.Info("Images pruned successfully",
			logger.Int("deleted_count", len(response.ImagesDeleted)),
			logger.Int64("space_reclaimed", int64(response.SpaceReclaimed)),
			logger.Bool("all", all),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"message":         "Images pruned successfully",
			"deleted_count":   len(response.ImagesDeleted),
			"space_reclaimed": response.SpaceReclaimed,
			"all":             all,
			"deleted_images":  response.ImagesDeleted,
		}, nil
	}
}

// EnhancedPullImageTask creates an enhanced task function for image pulling with better error handling and logging
func EnhancedPullImageTask(dockerSvc *Service, updateOutput func(string, bool, int, string)) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting image pull task",
			logger.Any("input", input),
		)

		// Convert input to image pull request
		inputMap, ok := input.(map[string]interface{})
		if !ok {
			err := fmt.Errorf("invalid input type for image pull task: expected map, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract image reference
		image, ok := inputMap["image"].(string)
		if !ok {
			err := fmt.Errorf("image is required and must be a string")
			logger.Error("Task validation failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract auth if provided
		auth, _ := inputMap["auth"].(string)
		taskID := inputMap["task_id"].(string)

		logger.Info("Pulling image",
			logger.String("image", image),
			logger.Bool("with_auth", auth != ""),
			logger.String("task_id", taskID),
		)

		// Pull image
		reader, err := dockerSvc.Image().PullImage(ctx, image, auth)
		if err != nil {
			logger.Error("Task: Failed to pull image",
				logger.Err(err),
				logger.String("image", image),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to pull image: %w", err)
		}
		defer reader.Close()

		// Read the pull output
		output := strings.Builder{}
		scan := bufio.NewScanner(reader)
		for scan.Scan() {
			line := scan.Text()
			output.WriteString(line + "\n")
			updateOutput(taskID, true, 50, line)
		}

		logger.Info("Image pulled successfully",
			logger.String("image", image),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"message": "Image pulled successfully",
			"image":   image,
			"output":  output.String(),
		}, nil
	}
}

// EnhancedRemoveImageTask creates an enhanced task function for image removal with better error handling and logging
func EnhancedRemoveImageTask(dockerSvc *Service) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()
		logger.Info("Starting image remove task",
			logger.Any("input", input),
		)

		// Convert input to map
		inputMap, ok := input.(map[string]interface{})
		if !ok {
			err := fmt.Errorf("invalid input type for image removal task: expected map, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract parameters
		imageID, ok := inputMap["image_id"].(string)
		if !ok {
			err := fmt.Errorf("image_id is required and must be a string")
			logger.Error("Task validation failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		force, _ := inputMap["force"].(bool)
		pruneChildren, _ := inputMap["prune_children"].(bool)

		logger.Info("Removing image",
			logger.String("image_id", imageID),
			logger.Bool("force", force),
			logger.Bool("prune_children", pruneChildren),
		)

		// Remove image
		responses, err := dockerSvc.Image().RemoveImage(ctx, imageID, force, pruneChildren)
		if err != nil {
			logger.Error("Task: Failed to remove image",
				logger.Err(err),
				logger.String("image_id", imageID),
				logger.Bool("force", force),
				logger.Bool("prune_children", pruneChildren),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to remove image: %w", err)
		}

		logger.Info("Image removed successfully",
			logger.String("image_id", imageID),
			logger.Int("removed_count", len(responses)),
			logger.Duration("duration", time.Since(startTime)),
		)

		return map[string]interface{}{
			"message":   "Image removed successfully",
			"id":        imageID,
			"responses": responses,
		}, nil
	}
}
