package docker

import (
	"context"
	"io"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"daemon-server/configs"
	"daemon-server/internal/logger"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/image"
)

// TestNewImageService tests the NewImageService function
func TestNewImageService(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()

	// Create a new image service
	service := NewImageService(mockClient)

	// Check that the service is not nil
	if service == nil {
		t.Fatal("Service is nil")
	}

	// Check that the service has the client
	if service.client != mockClient {
		t.Error("Service does not have the client")
	}
}

// TestListImages tests the ListImages method
func TestListImages(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImageListFunc = func(ctx context.Context, option image.ListOptions) ([]image.Summary, error) {
		return []image.Summary{
			{
				ID:          "image1",
				RepoTags:    []string{"image1:latest"},
				RepoDigests: []string{"image1@sha256:123"},
				Created:     time.Now().Unix(),
				Size:        1000,
				Labels:      map[string]string{"test": "true"},
			},
			{
				ID:          "image2",
				RepoTags:    []string{"image2:latest"},
				RepoDigests: []string{"image2@sha256:456"},
				Created:     time.Now().Unix(),
				Size:        2000,
				Labels:      map[string]string{"test": "false"},
			},
		}, nil
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// List images
	images, err := service.ListImages(context.Background(), image.ListOptions{All: true})
	if err != nil {
		t.Fatalf("Failed to list images: %v", err)
	}

	// Check the images
	if len(images) != 2 {
		t.Fatalf("Expected 2 images, got %d", len(images))
	}
	if images[0].ID != "image1" {
		t.Errorf("Expected image ID 'image1', got '%s'", images[0].ID)
	}
	if len(images[0].RepoTags) != 1 || images[0].RepoTags[0] != "image1:latest" {
		t.Errorf("Expected image repo tags ['image1:latest'], got %v", images[0].RepoTags)
	}
	if len(images[0].RepoDigests) != 1 || images[0].RepoDigests[0] != "image1@sha256:123" {
		t.Errorf("Expected image repo digests ['image1@sha256:123'], got %v", images[0].RepoDigests)
	}
	if images[0].Size != 1000 {
		t.Errorf("Expected image size 1000, got %d", images[0].Size)
	}
	if len(images[0].Labels) != 1 || images[0].Labels["test"] != "true" {
		t.Errorf("Expected image labels {'test': 'true'}, got %v", images[0].Labels)
	}
}

// TestListImagesError tests the ListImages method with an error
func TestListImagesError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImageListFunc = func(ctx context.Context, option image.ListOptions) ([]image.Summary, error) {
		return nil, ErrMock
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// List images
	images, err := service.ListImages(context.Background(), image.ListOptions{All: true})
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if images != nil {
		t.Fatal("Expected images to be nil")
	}
}

// TestPullImage tests the PullImage method
func TestPullImage(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImagePullFunc = func(ctx context.Context, ref string, options ImagePullOptions) (io.ReadCloser, error) {
		// Check the parameters
		if ref != "test-image:latest" {
			t.Errorf("Expected ref 'test-image:latest', got '%s'", ref)
		}
		if options.RegistryAuth != "test-auth" {
			t.Errorf("Expected registry auth 'test-auth', got '%s'", options.RegistryAuth)
		}
		return io.NopCloser(strings.NewReader(`{"status":"Pulling from test-image:latest"}
{"status":"Pull complete"}`)), nil
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Pull an image
	reader, err := service.PullImage(context.Background(), "test-image:latest", "test-auth")
	if err != nil {
		t.Fatalf("Failed to pull image: %v", err)
	}

	// Check the reader
	bytes, err := io.ReadAll(reader)
	if err != nil {
		t.Fatalf("Failed to read from reader: %v", err)
	}
	if !strings.Contains(string(bytes), "Pulling from test-image:latest") {
		t.Errorf("Expected output to contain pulling message, got '%s'", string(bytes))
	}
	if !strings.Contains(string(bytes), "Pull complete") {
		t.Errorf("Expected output to contain completion message, got '%s'", string(bytes))
	}
}

// TestPullImageError tests the PullImage method with an error
func TestPullImageError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImagePullFunc = func(ctx context.Context, ref string, options ImagePullOptions) (io.ReadCloser, error) {
		return nil, ErrMock
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Pull an image
	reader, err := service.PullImage(context.Background(), "test-image:latest", "test-auth")
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if reader != nil {
		t.Fatal("Expected reader to be nil")
	}
}

// TestRemoveImage tests the RemoveImage method
func TestRemoveImage(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImageRemoveFunc = func(ctx context.Context, imageID string, force, pruneChildren bool) ([]image.DeleteResponse, error) {
		// Check the parameters
		if imageID != "test-image-id" {
			t.Errorf("Expected image ID 'test-image-id', got '%s'", imageID)
		}
		if !force {
			t.Error("Expected force to be true")
		}
		if !pruneChildren {
			t.Error("Expected pruneChildren to be true")
		}
		return []image.DeleteResponse{
			{
				Untagged: "test-image:latest",
				Deleted:  "test-image-id",
			},
		}, nil
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Remove an image
	responses, err := service.RemoveImage(context.Background(), "test-image-id", true, true)
	if err != nil {
		t.Fatalf("Failed to remove image: %v", err)
	}

	// Check the responses
	if len(responses) != 1 {
		t.Fatalf("Expected 1 response, got %d", len(responses))
	}
	if responses[0].Untagged != "test-image:latest" {
		t.Errorf("Expected untagged 'test-image:latest', got '%s'", responses[0].Untagged)
	}
	if responses[0].Deleted != "test-image-id" {
		t.Errorf("Expected deleted 'test-image-id', got '%s'", responses[0].Deleted)
	}
}

// TestRemoveImageError tests the RemoveImage method with an error
func TestRemoveImageError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImageRemoveFunc = func(ctx context.Context, imageID string, force, pruneChildren bool) ([]image.DeleteResponse, error) {
		return nil, ErrMock
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Remove an image
	responses, err := service.RemoveImage(context.Background(), "test-image-id", false, false)
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if responses != nil {
		t.Fatal("Expected responses to be nil")
	}
}

// TestInspectImage tests the InspectImage method
func TestInspectImage(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImageInspectFunc = func(ctx context.Context, imageID string) (types.ImageInspect, error) {
		// Check the parameters
		if imageID != "test-image-id" {
			t.Errorf("Expected image ID 'test-image-id', got '%s'", imageID)
		}
		return types.ImageInspect{
			ID:       "test-image-id",
			RepoTags: []string{"test-image:latest"},
			Created:  "2023-01-01T00:00:00Z",
			Size:     1000,
			Config: &container.Config{
				Labels: map[string]string{"test": "true"},
			},
		}, nil
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Inspect an image
	imageInspect, err := service.InspectImage(context.Background(), "test-image-id")
	if err != nil {
		t.Fatalf("Failed to inspect image: %v", err)
	}

	// Check the image inspect
	if imageInspect.ID != "test-image-id" {
		t.Errorf("Expected image ID 'test-image-id', got '%s'", imageInspect.ID)
	}
	if len(imageInspect.RepoTags) != 1 || imageInspect.RepoTags[0] != "test-image:latest" {
		t.Errorf("Expected image repo tags ['test-image:latest'], got %v", imageInspect.RepoTags)
	}
	if imageInspect.Created != "2023-01-01T00:00:00Z" {
		t.Errorf("Expected image created '2023-01-01T00:00:00Z', got '%s'", imageInspect.Created)
	}
	if imageInspect.Size != 1000 {
		t.Errorf("Expected image size 1000, got %d", imageInspect.Size)
	}
	if imageInspect.Config == nil || len(imageInspect.Config.Labels) != 1 || imageInspect.Config.Labels["test"] != "true" {
		t.Errorf("Expected image labels {'test': 'true'}, got %v", imageInspect.Config.Labels)
	}
}

// TestInspectImageError tests the InspectImage method with an error
func TestInspectImageError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImageInspectFunc = func(ctx context.Context, imageID string) (types.ImageInspect, error) {
		return types.ImageInspect{}, ErrMock
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Inspect an image
	imageInspect, err := service.InspectImage(context.Background(), "test-image-id")
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if imageInspect != nil {
		t.Fatal("Expected image inspect to be nil")
	}
}

// TestLoadImage tests the LoadImage method
func TestLoadImage(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImageLoadFunc = func(ctx context.Context, filePath string) (io.ReadCloser, error) {
		// Check the parameters
		if filePath != "/path/to/image.tar" {
			t.Errorf("Expected file path '/path/to/image.tar', got '%s'", filePath)
		}
		return io.NopCloser(strings.NewReader(`{"status":"Loading image from file: /path/to/image.tar"}
{"status":"Load complete"}`)), nil
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Load an image
	reader, err := service.LoadImage(context.Background(), "/path/to/image.tar")
	if err != nil {
		t.Fatalf("Failed to load image: %v", err)
	}

	// Check the reader
	bytes, err := io.ReadAll(reader)
	if err != nil {
		t.Fatalf("Failed to read from reader: %v", err)
	}
	if !strings.Contains(string(bytes), "Loading image from file: /path/to/image.tar") {
		t.Errorf("Expected output to contain loading message, got '%s'", string(bytes))
	}
	if !strings.Contains(string(bytes), "Load complete") {
		t.Errorf("Expected output to contain completion message, got '%s'", string(bytes))
	}
}

// TestLoadImageError tests the LoadImage method with an error
func TestLoadImageError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImageLoadFunc = func(ctx context.Context, filePath string) (io.ReadCloser, error) {
		return nil, ErrMock
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Load an image
	reader, err := service.LoadImage(context.Background(), "/path/to/image.tar")
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if reader != nil {
		t.Fatal("Expected reader to be nil")
	}
}

// TestPruneImages tests the PruneImages method
func TestPruneImages(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImagePruneFunc = func(ctx context.Context, all bool) (image.PruneReport, error) {
		// Check the parameters
		if !all {
			t.Errorf("Expected all to be true, got false")
		}
		return image.PruneReport{
			ImagesDeleted: []image.DeleteResponse{
				{
					Untagged: "test-image:latest",
					Deleted:  "test-image-id",
				},
			},
			SpaceReclaimed: 1024 * 1024 * 100, // 100MB
		}, nil
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Prune images
	result, err := service.PruneImages(context.Background(), true)
	if err != nil {
		t.Fatalf("Failed to prune images: %v", err)
	}

	// Check the result
	if len(result.ImagesDeleted) != 1 {
		t.Fatalf("Expected 1 deleted image, got %d", len(result.ImagesDeleted))
	}
	if result.ImagesDeleted[0].Untagged != "test-image:latest" {
		t.Errorf("Expected untagged 'test-image:latest', got '%s'", result.ImagesDeleted[0].Untagged)
	}
	if result.ImagesDeleted[0].Deleted != "test-image-id" {
		t.Errorf("Expected deleted 'test-image-id', got '%s'", result.ImagesDeleted[0].Deleted)
	}
	if result.SpaceReclaimed != 1024*1024*100 {
		t.Errorf("Expected space reclaimed 104857600, got %d", result.SpaceReclaimed)
	}
}

// TestPruneImagesError tests the PruneImages method with an error
func TestPruneImagesError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImagePruneFunc = func(ctx context.Context, all bool) (image.PruneReport, error) {
		return image.PruneReport{}, ErrMock
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Prune images
	_, err := service.PruneImages(context.Background(), false)
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
}

// TestPushImage tests the PushImage method
func TestPushImage(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImagePushFunc = func(ctx context.Context, ref string, options ImagePushOptions) (io.ReadCloser, error) {
		// Check the parameters
		if ref != "test-image:latest" {
			t.Errorf("Expected ref 'test-image:latest', got '%s'", ref)
		}
		if options.RegistryAuth != "test-auth" {
			t.Errorf("Expected registry auth 'test-auth', got '%s'", options.RegistryAuth)
		}
		return io.NopCloser(strings.NewReader(`{"status":"Pushing image test-image:latest"}
{"status":"Push complete"}`)), nil
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Push an image
	req := SimplePushImageRequest{
		Image: "test-image:latest",
		Auth:  "test-auth",
	}
	reader, err := service.PushImage(context.Background(), req)
	if err != nil {
		t.Fatalf("Failed to push image: %v", err)
	}

	// Check the reader
	bytes, err := io.ReadAll(reader)
	if err != nil {
		t.Fatalf("Failed to read from reader: %v", err)
	}
	if !strings.Contains(string(bytes), "Pushing image test-image:latest") {
		t.Errorf("Expected output to contain pushing message, got '%s'", string(bytes))
	}
	if !strings.Contains(string(bytes), "Push complete") {
		t.Errorf("Expected output to contain completion message, got '%s'", string(bytes))
	}
}

// TestPushImageError tests the PushImage method with an error
func TestPushImageError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImagePushFunc = func(ctx context.Context, ref string, options ImagePushOptions) (io.ReadCloser, error) {
		return nil, ErrMock
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Push an image
	req := SimplePushImageRequest{
		Image: "test-image:latest",
		Auth:  "test-auth",
	}
	reader, err := service.PushImage(context.Background(), req)
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if reader != nil {
		t.Fatal("Expected reader to be nil")
	}
}

// TestSaveImage tests the SaveImage method
func TestSaveImage(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImageSaveFunc = func(ctx context.Context, imageIDs []string) (io.ReadCloser, error) {
		// Check the parameters
		if len(imageIDs) != 2 {
			t.Errorf("Expected 2 image IDs, got %d", len(imageIDs))
		}
		if imageIDs[0] != "image1" || imageIDs[1] != "image2" {
			t.Errorf("Expected image IDs ['image1', 'image2'], got %v", imageIDs)
		}
		return io.NopCloser(strings.NewReader(`{"status":"Saving images to tar file"}
{"status":"Save complete"}`)), nil
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Save images
	imageIDs := []string{"image1", "image2"}
	reader, err := service.SaveImage(context.Background(), imageIDs)
	if err != nil {
		t.Fatalf("Failed to save images: %v", err)
	}

	// Check the reader
	bytes, err := io.ReadAll(reader)
	if err != nil {
		t.Fatalf("Failed to read from reader: %v", err)
	}
	if !strings.Contains(string(bytes), "Saving images to tar file") {
		t.Errorf("Expected output to contain saving message, got '%s'", string(bytes))
	}
	if !strings.Contains(string(bytes), "Save complete") {
		t.Errorf("Expected output to contain completion message, got '%s'", string(bytes))
	}
}

// TestSaveImageError tests the SaveImage method with an error
func TestSaveImageError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImageSaveFunc = func(ctx context.Context, imageIDs []string) (io.ReadCloser, error) {
		return nil, ErrMock
	}

	// Create an image service with the mock client
	service := NewImageService(mockClient)

	// Save images
	imageIDs := []string{"image1", "image2"}
	reader, err := service.SaveImage(context.Background(), imageIDs)
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if reader != nil {
		t.Fatal("Expected reader to be nil")
	}
}

// TestEnhancedPullImageTask tests the EnhancedPullImageTask function
func TestEnhancedPullImageTask(t *testing.T) {
	// Initialize logger for tests
	config := &configs.Config{
		Logger: configs.LoggerConfig{
			Level: "info",
			File:  "/tmp/test.log",
		},
	}
	logger.Init(config)

	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImagePullFunc = func(ctx context.Context, ref string, options ImagePullOptions) (io.ReadCloser, error) {
		// Check the parameters
		if ref != "test-image:latest" {
			t.Errorf("Expected ref 'test-image:latest', got '%s'", ref)
		}
		if options.RegistryAuth != "test-auth" {
			t.Errorf("Expected registry auth 'test-auth', got '%s'", options.RegistryAuth)
		}
		return io.NopCloser(strings.NewReader(`{"status":"Pulling from test-image:latest"}
{"status":"Pull complete"}`)), nil
	}

	// Create a Docker service with the mock client
	dockerSvc := &Service{
		client:   mockClient,
		imageSvc: NewImageService(mockClient),
	}

	// Create a mock update output function
	updateOutputCalled := false
	updateOutput := func(taskID string, isStdout bool, progress int, message string) {
		updateOutputCalled = true
		if taskID != "task1" {
			t.Errorf("Expected task ID 'task1', got '%s'", taskID)
		}
		if !isStdout {
			t.Error("Expected isStdout to be true")
		}
		if progress != 50 {
			t.Errorf("Expected progress 50, got %d", progress)
		}
	}

	// Create the task function
	taskFunc := EnhancedPullImageTask(dockerSvc, updateOutput)

	// Execute the task function
	input := map[string]interface{}{
		"image":   "test-image:latest",
		"auth":    "test-auth",
		"task_id": "task1",
	}
	result, err := taskFunc(context.Background(), input)
	if err != nil {
		t.Fatalf("Failed to execute task function: %v", err)
	}

	// Check the result
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		t.Fatalf("Expected result to be a map, got %T", result)
	}
	if resultMap["message"] != "Image pulled successfully" {
		t.Errorf("Expected message 'Image pulled successfully', got '%s'", resultMap["message"])
	}
	if resultMap["image"] != "test-image:latest" {
		t.Errorf("Expected image 'test-image:latest', got '%s'", resultMap["image"])
	}
	if !updateOutputCalled {
		t.Error("Expected updateOutput to be called")
	}
}

// TestEnhancedPushImageTask tests the EnhancedPushImageTask function
func TestEnhancedPushImageTask(t *testing.T) {
	// Initialize logger for tests
	config := &configs.Config{
		Logger: configs.LoggerConfig{
			Level: "info",
			File:  "/tmp/test.log",
		},
	}
	logger.Init(config)

	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImagePushFunc = func(ctx context.Context, ref string, options ImagePushOptions) (io.ReadCloser, error) {
		// Check the parameters
		if ref != "test-image:latest" {
			t.Errorf("Expected ref 'test-image:latest', got '%s'", ref)
		}
		if options.RegistryAuth != "test-auth" {
			t.Errorf("Expected registry auth 'test-auth', got '%s'", options.RegistryAuth)
		}
		return io.NopCloser(strings.NewReader(`{"status":"Pushing image test-image:latest"}
{"status":"Push complete"}`)), nil
	}

	// Create a Docker service with the mock client
	dockerSvc := &Service{
		client:   mockClient,
		imageSvc: NewImageService(mockClient),
	}

	// Create a mock update output function
	updateOutputCalled := false
	updateOutput := func(taskID string, isStdout bool, progress int, message string) {
		updateOutputCalled = true
		if taskID != "task1" {
			t.Errorf("Expected task ID 'task1', got '%s'", taskID)
		}
		if !isStdout {
			t.Error("Expected isStdout to be true")
		}
		if progress != 50 {
			t.Errorf("Expected progress 50, got %d", progress)
		}
	}

	// Create the task function
	taskFunc := EnhancedPushImageTask(dockerSvc, updateOutput)

	// Execute the task function
	input := SimplePushImageRequest{
		TaskID: "task1",
		Image:  "test-image:latest",
		Auth:   "test-auth",
	}
	result, err := taskFunc(context.Background(), input)
	if err != nil {
		t.Fatalf("Failed to execute task function: %v", err)
	}

	// Check the result
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		t.Fatalf("Expected result to be a map, got %T", result)
	}
	if resultMap["message"] != "Image pushed successfully" {
		t.Errorf("Expected message 'Image pushed successfully', got '%s'", resultMap["message"])
	}
	if resultMap["image"] != "test-image:latest" {
		t.Errorf("Expected image 'test-image:latest', got '%s'", resultMap["image"])
	}
	if !updateOutputCalled {
		t.Error("Expected updateOutput to be called")
	}
}

// TestEnhancedSaveImageTask tests the EnhancedSaveImageTask function
func TestEnhancedSaveImageTask(t *testing.T) {
	// Initialize logger for tests
	config := &configs.Config{
		Logger: configs.LoggerConfig{
			Level: "info",
			File:  "/tmp/test.log",
		},
	}
	logger.Init(config)

	// Create a temporary directory for the test
	tempDir := t.TempDir()
	savePath := filepath.Join(tempDir, "test-image.tar")

	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImageSaveFunc = func(ctx context.Context, imageIDs []string) (io.ReadCloser, error) {
		// Check the parameters
		if len(imageIDs) != 1 {
			t.Errorf("Expected 1 image ID, got %d", len(imageIDs))
		}
		if imageIDs[0] != "test-image-id" {
			t.Errorf("Expected image ID 'test-image-id', got '%s'", imageIDs[0])
		}
		return io.NopCloser(strings.NewReader(`test image content`)), nil
	}

	// Create a Docker service with the mock client
	dockerSvc := &Service{
		client:   mockClient,
		imageSvc: NewImageService(mockClient),
	}

	// Create the task function
	taskFunc := EnhancedSaveImageTask(dockerSvc)

	// Execute the task function
	input := ImageSaveParam{
		ImageId:  "test-image-id",
		SavePath: savePath,
	}
	result, err := taskFunc(context.Background(), input)
	if err != nil {
		t.Fatalf("Failed to execute task function: %v", err)
	}

	// Check the result
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		t.Fatalf("Expected result to be a map, got %T", result)
	}
	if resultMap["message"] != "Images saved successfully" {
		t.Errorf("Expected message 'Images saved successfully', got '%s'", resultMap["message"])
	}
	if resultMap["image_id"] != "test-image-id" {
		t.Errorf("Expected image_id 'test-image-id', got '%s'", resultMap["image_id"])
	}
	if resultMap["save_path"] != savePath {
		t.Errorf("Expected save_path '%s', got '%s'", savePath, resultMap["save_path"])
	}

	// Check that the file was created
	if _, err := os.Stat(savePath); os.IsNotExist(err) {
		t.Errorf("Expected file '%s' to exist", savePath)
	}

	// Check the file content
	content, err := os.ReadFile(savePath)
	if err != nil {
		t.Fatalf("Failed to read file: %v", err)
	}
	if string(content) != "test image content" {
		t.Errorf("Expected file content 'test image content', got '%s'", string(content))
	}
}

// TestEnhancedLoadImageTask tests the EnhancedLoadImageTask function
func TestEnhancedLoadImageTask(t *testing.T) {
	// Initialize logger for tests
	config := &configs.Config{
		Logger: configs.LoggerConfig{
			Level: "info",
			File:  "/tmp/test.log",
		},
	}
	logger.Init(config)

	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImageLoadFunc = func(ctx context.Context, filePath string) (io.ReadCloser, error) {
		// Check the parameters
		if filePath != "/path/to/image.tar" {
			t.Errorf("Expected file path '/path/to/image.tar', got '%s'", filePath)
		}
		return io.NopCloser(strings.NewReader(`{"status":"Loading image from file: /path/to/image.tar"}
{"status":"Load complete"}`)), nil
	}

	// Create a Docker service with the mock client
	dockerSvc := &Service{
		client:   mockClient,
		imageSvc: NewImageService(mockClient),
	}

	// Create the task function
	taskFunc := EnhancedLoadImageTask(dockerSvc)

	// Execute the task function with string input
	result1, err := taskFunc(context.Background(), "/path/to/image.tar")
	if err != nil {
		t.Fatalf("Failed to execute task function with string input: %v", err)
	}

	// Check the result
	resultMap1, ok := result1.(map[string]interface{})
	if !ok {
		t.Fatalf("Expected result to be a map, got %T", result1)
	}
	if resultMap1["message"] != "Image loaded successfully" {
		t.Errorf("Expected message 'Image loaded successfully', got '%s'", resultMap1["message"])
	}
	if resultMap1["file_path"] != "/path/to/image.tar" {
		t.Errorf("Expected file_path '/path/to/image.tar', got '%s'", resultMap1["file_path"])
	}

	// Execute the task function with SimpleLoadImageRequest input
	result2, err := taskFunc(context.Background(), SimpleLoadImageRequest{FilePath: "/path/to/image.tar"})
	if err != nil {
		t.Fatalf("Failed to execute task function with SimpleLoadImageRequest input: %v", err)
	}

	// Check the result
	resultMap2, ok := result2.(map[string]interface{})
	if !ok {
		t.Fatalf("Expected result to be a map, got %T", result2)
	}
	if resultMap2["message"] != "Image loaded successfully" {
		t.Errorf("Expected message 'Image loaded successfully', got '%s'", resultMap2["message"])
	}
	if resultMap2["file_path"] != "/path/to/image.tar" {
		t.Errorf("Expected file_path '/path/to/image.tar', got '%s'", resultMap2["file_path"])
	}

	// Execute the task function with map input
	result3, err := taskFunc(context.Background(), map[string]interface{}{
		"file_path": "/path/to/image.tar",
	})
	if err != nil {
		t.Fatalf("Failed to execute task function with map input: %v", err)
	}

	// Check the result
	resultMap3, ok := result3.(map[string]interface{})
	if !ok {
		t.Fatalf("Expected result to be a map, got %T", result3)
	}
	if resultMap3["message"] != "Image loaded successfully" {
		t.Errorf("Expected message 'Image loaded successfully', got '%s'", resultMap3["message"])
	}
	if resultMap3["file_path"] != "/path/to/image.tar" {
		t.Errorf("Expected file_path '/path/to/image.tar', got '%s'", resultMap3["file_path"])
	}
}

// TestEnhancedPruneImagesTask tests the EnhancedPruneImagesTask function
func TestEnhancedPruneImagesTask(t *testing.T) {
	// Initialize logger for tests
	config := &configs.Config{
		Logger: configs.LoggerConfig{
			Level: "info",
			File:  "/tmp/test.log",
		},
	}
	logger.Init(config)

	// Create a mock client
	mockClient := NewMockClient()
	mockClient.ImagePruneFunc = func(ctx context.Context, all bool) (image.PruneReport, error) {
		// Check the parameters
		if !all {
			t.Errorf("Expected all to be true, got false")
		}
		return image.PruneReport{
			ImagesDeleted: []image.DeleteResponse{
				{
					Untagged: "test-image:latest",
					Deleted:  "test-image-id",
				},
			},
			SpaceReclaimed: 1024 * 1024 * 100, // 100MB
		}, nil
	}

	// Create a Docker service with the mock client
	dockerSvc := &Service{
		client:   mockClient,
		imageSvc: NewImageService(mockClient),
	}

	// Create the task function
	taskFunc := EnhancedPruneImagesTask(dockerSvc)

	// Execute the task function with bool input
	result1, err := taskFunc(context.Background(), true)
	if err != nil {
		t.Fatalf("Failed to execute task function with bool input: %v", err)
	}

	// Check the result
	resultMap1, ok := result1.(map[string]interface{})
	if !ok {
		t.Fatalf("Expected result to be a map, got %T", result1)
	}
	if resultMap1["message"] != "Images pruned successfully" {
		t.Errorf("Expected message 'Images pruned successfully', got '%s'", resultMap1["message"])
	}
	if resultMap1["deleted_count"] != 1 {
		t.Errorf("Expected deleted_count 1, got %d", resultMap1["deleted_count"])
	}
	// Check that space_reclaimed is a number and has the expected value
	spaceReclaimed, ok := resultMap1["space_reclaimed"].(uint64)
	if !ok {
		t.Errorf("Expected space_reclaimed to be a uint64, got %T", resultMap1["space_reclaimed"])
	} else if spaceReclaimed != uint64(1024*1024*100) {
		t.Errorf("Expected space_reclaimed %d, got %d", uint64(1024*1024*100), spaceReclaimed)
	}
	if resultMap1["all"] != true {
		t.Errorf("Expected all true, got %v", resultMap1["all"])
	}

	// Execute the task function with map input
	result2, err := taskFunc(context.Background(), map[string]interface{}{
		"all": true,
	})
	if err != nil {
		t.Fatalf("Failed to execute task function with map input: %v", err)
	}

	// Check the result
	resultMap2, ok := result2.(map[string]interface{})
	if !ok {
		t.Fatalf("Expected result to be a map, got %T", result2)
	}
	if resultMap2["message"] != "Images pruned successfully" {
		t.Errorf("Expected message 'Images pruned successfully', got '%s'", resultMap2["message"])
	}
	if resultMap2["deleted_count"] != 1 {
		t.Errorf("Expected deleted_count 1, got %d", resultMap2["deleted_count"])
	}
	// Check that space_reclaimed is a number and has the expected value
	spaceReclaimed2, ok := resultMap2["space_reclaimed"].(uint64)
	if !ok {
		t.Errorf("Expected space_reclaimed to be a uint64, got %T", resultMap2["space_reclaimed"])
	} else if spaceReclaimed2 != uint64(1024*1024*100) {
		t.Errorf("Expected space_reclaimed %d, got %d", uint64(1024*1024*100), spaceReclaimed2)
	}
	if resultMap2["all"] != true {
		t.Errorf("Expected all true, got %v", resultMap2["all"])
	}
}
