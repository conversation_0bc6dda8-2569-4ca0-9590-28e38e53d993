package docker

import (
	"bufio"
	"context"
	"errors"
	"io"
	"strings"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/events"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/docker/api/types/volume"
)

// MockClient implements the Client interface for testing
type MockClient struct {
	// Container operations
	ContainerListFunc            func(ctx context.Context, all bool) ([]types.Container, error)
	ContainerListWithOptionsFunc func(ctx context.Context, options container.ListOptions) ([]types.Container, error)
	ContainerCreateFunc          func(ctx context.Context, config *container.Config, hostConfig *container.HostConfig, networkConfig *network.NetworkingConfig, name string) (container.CreateResponse, error)
	ContainerStartFunc           func(ctx context.Context, containerID string) error
	ContainerStopFunc            func(ctx context.Context, containerID string, timeout *int) error
	ContainerRestartFunc         func(ctx context.Context, containerID string, timeout *int) error
	ContainerRemoveFunc          func(ctx context.Context, containerID string, force, removeVolumes bool) error
	ContainerInspectFunc         func(ctx context.Context, containerID string) (types.ContainerJSON, error)
	ContainerLogsFunc            func(ctx context.Context, containerID string, follow bool, tail string) (io.ReadCloser, error)
	ContainerStatsFunc           func(ctx context.Context, containerID string, stream bool) (Stats, error)
	ContainerCommitFunc          func(ctx context.Context, containerID string, options container.CommitOptions) (types.IDResponse, error)
	ContainerExportFunc          func(ctx context.Context, containerID string) (io.ReadCloser, error)

	// Image operations
	ImageListFunc    func(ctx context.Context, option image.ListOptions) ([]image.Summary, error)
	ImagePullFunc    func(ctx context.Context, ref string, options ImagePullOptions) (io.ReadCloser, error)
	ImagePushFunc    func(ctx context.Context, ref string, options ImagePushOptions) (io.ReadCloser, error)
	ImageRemoveFunc  func(ctx context.Context, imageID string, force, pruneChildren bool) ([]image.DeleteResponse, error)
	ImageInspectFunc func(ctx context.Context, imageID string) (types.ImageInspect, error)
	ImageHistoryFunc func(ctx context.Context, imageID string) ([]image.HistoryResponseItem, error)
	ImageLoadFunc    func(ctx context.Context, filePath string) (io.ReadCloser, error)
	ImageSaveFunc    func(ctx context.Context, imageIDs []string) (io.ReadCloser, error)
	ImagePruneFunc   func(ctx context.Context, all bool) (image.PruneReport, error)

	// Volume operations
	VolumeListFunc    func(ctx context.Context) (volume.ListResponse, error)
	VolumeCreateFunc  func(ctx context.Context, options volume.CreateOptions) (volume.Volume, error)
	VolumeRemoveFunc  func(ctx context.Context, volumeID string, force bool) error
	VolumeInspectFunc func(ctx context.Context, volumeID string) (volume.Volume, error)

	// Network operations
	NetworkListFunc    func(ctx context.Context) ([]NetworkResource, error)
	NetworkCreateFunc  func(ctx context.Context, name string, options NetworkCreate) (NetworkCreateResponse, error)
	NetworkRemoveFunc  func(ctx context.Context, networkID string) error
	NetworkInspectFunc func(ctx context.Context, networkID string, verbose bool) (NetworkResource, error)

	// Exec operations
	ContainerExecCreateFunc  func(ctx context.Context, containerID string, options container.ExecOptions) (types.IDResponse, error)
	ContainerExecStartFunc   func(ctx context.Context, execID string, config container.ExecStartOptions) (types.HijackedResponse, error)
	ContainerExecInspectFunc func(ctx context.Context, execID string) (container.ExecInspect, error)

	// System operations
	SystemInfoFunc      func(ctx context.Context) (Info, error)
	SystemVersionFunc   func(ctx context.Context) (Version, error)
	SystemDiskUsageFunc func(ctx context.Context) (DiskUsage, error)
	SystemEventsFunc    func(ctx context.Context, options EventsOptions) (<-chan events.Message, <-chan error)

	// Close operation
	CloseFunc func() error
}

// ContainerList implements the Client interface
func (m *MockClient) ContainerList(ctx context.Context, all bool) ([]types.Container, error) {
	if m.ContainerListFunc != nil {
		return m.ContainerListFunc(ctx, all)
	}
	return []types.Container{}, nil
}

// ContainerListWithOptions implements the Client interface
func (m *MockClient) ContainerListWithOptions(ctx context.Context, options container.ListOptions) ([]types.Container, error) {
	if m.ContainerListWithOptionsFunc != nil {
		return m.ContainerListWithOptionsFunc(ctx, options)
	}
	return []types.Container{}, nil
}

// ContainerCreate implements the Client interface
func (m *MockClient) ContainerCreate(ctx context.Context, config *container.Config, hostConfig *container.HostConfig, networkConfig *network.NetworkingConfig, name string) (container.CreateResponse, error) {
	if m.ContainerCreateFunc != nil {
		return m.ContainerCreateFunc(ctx, config, hostConfig, networkConfig, name)
	}
	return container.CreateResponse{ID: "mock-container-id"}, nil
}

// ContainerStart implements the Client interface
func (m *MockClient) ContainerStart(ctx context.Context, containerID string) error {
	if m.ContainerStartFunc != nil {
		return m.ContainerStartFunc(ctx, containerID)
	}
	return nil
}

// ContainerStop implements the Client interface
func (m *MockClient) ContainerStop(ctx context.Context, containerID string, timeout *int) error {
	if m.ContainerStopFunc != nil {
		return m.ContainerStopFunc(ctx, containerID, timeout)
	}
	return nil
}

// ContainerRestart implements the Client interface
func (m *MockClient) ContainerRestart(ctx context.Context, containerID string, timeout *int) error {
	if m.ContainerRestartFunc != nil {
		return m.ContainerRestartFunc(ctx, containerID, timeout)
	}
	return nil
}

// ContainerRemove implements the Client interface
func (m *MockClient) ContainerRemove(ctx context.Context, containerID string, force, removeVolumes bool) error {
	if m.ContainerRemoveFunc != nil {
		return m.ContainerRemoveFunc(ctx, containerID, force, removeVolumes)
	}
	return nil
}

// ContainerInspect implements the Client interface
func (m *MockClient) ContainerInspect(ctx context.Context, containerID string) (types.ContainerJSON, error) {
	if m.ContainerInspectFunc != nil {
		return m.ContainerInspectFunc(ctx, containerID)
	}
	return types.ContainerJSON{}, nil
}

// ContainerLogs implements the Client interface
func (m *MockClient) ContainerLogs(ctx context.Context, containerID string, follow bool, tail string) (io.ReadCloser, error) {
	if m.ContainerLogsFunc != nil {
		return m.ContainerLogsFunc(ctx, containerID, follow, tail)
	}
	return io.NopCloser(strings.NewReader("mock logs")), nil
}

// ContainerStats implements the Client interface
func (m *MockClient) ContainerStats(ctx context.Context, containerID string, stream bool) (Stats, error) {
	if m.ContainerStatsFunc != nil {
		return m.ContainerStatsFunc(ctx, containerID, stream)
	}
	return Stats{
		Read:    time.Now().Format(time.RFC3339),
		PreRead: time.Now().Add(-1 * time.Second).Format(time.RFC3339),
	}, nil
}

// ContainerCommit implements the Client interface
func (m *MockClient) ContainerCommit(ctx context.Context, containerID string, options container.CommitOptions) (types.IDResponse, error) {
	if m.ContainerCommitFunc != nil {
		return m.ContainerCommitFunc(ctx, containerID, options)
	}
	return types.IDResponse{ID: "mock-image-id"}, nil
}

// ContainerExport implements the Client interface
func (m *MockClient) ContainerExport(ctx context.Context, containerID string) (io.ReadCloser, error) {
	if m.ContainerExportFunc != nil {
		return m.ContainerExportFunc(ctx, containerID)
	}
	return io.NopCloser(strings.NewReader("mock container export data")), nil
}

// ImageList implements the Client interface
func (m *MockClient) ImageList(ctx context.Context, option image.ListOptions) ([]image.Summary, error) {
	if m.ImageListFunc != nil {
		return m.ImageListFunc(ctx, option)
	}
	return []image.Summary{}, nil
}

// ImagePull implements the Client interface
func (m *MockClient) ImagePull(ctx context.Context, ref string, options ImagePullOptions) (io.ReadCloser, error) {
	if m.ImagePullFunc != nil {
		return m.ImagePullFunc(ctx, ref, options)
	}
	return io.NopCloser(strings.NewReader("mock pull")), nil
}

// ImagePush implements the Client interface
func (m *MockClient) ImagePush(ctx context.Context, ref string, options ImagePushOptions) (io.ReadCloser, error) {
	if m.ImagePushFunc != nil {
		return m.ImagePushFunc(ctx, ref, options)
	}
	return io.NopCloser(strings.NewReader("mock push")), nil
}

// ImageRemove implements the Client interface
func (m *MockClient) ImageRemove(ctx context.Context, imageID string, force, pruneChildren bool) ([]image.DeleteResponse, error) {
	if m.ImageRemoveFunc != nil {
		return m.ImageRemoveFunc(ctx, imageID, force, pruneChildren)
	}
	return []image.DeleteResponse{}, nil
}

// ImageInspect implements the Client interface
func (m *MockClient) ImageInspect(ctx context.Context, imageID string) (types.ImageInspect, error) {
	if m.ImageInspectFunc != nil {
		return m.ImageInspectFunc(ctx, imageID)
	}
	return types.ImageInspect{}, nil
}

// ImageLoad implements the Client interface
func (m *MockClient) ImageLoad(ctx context.Context, filePath string) (io.ReadCloser, error) {
	if m.ImageLoadFunc != nil {
		return m.ImageLoadFunc(ctx, filePath)
	}
	return io.NopCloser(strings.NewReader(`{"status":"Loading image from file: ` + filePath + `"}
{"status":"Load complete"}
`)), nil
}

// ImageSave implements the Client interface
func (m *MockClient) ImageSave(ctx context.Context, imageIDs []string) (io.ReadCloser, error) {
	if m.ImageSaveFunc != nil {
		return m.ImageSaveFunc(ctx, imageIDs)
	}
	return io.NopCloser(strings.NewReader(`{"status":"Saving images to tar file"}
{"status":"Save complete"}
`)), nil
}

// ImagePrune implements the Client interface
func (m *MockClient) ImagePrune(ctx context.Context, all bool) (image.PruneReport, error) {
	if m.ImagePruneFunc != nil {
		return m.ImagePruneFunc(ctx, all)
	}
	return image.PruneReport{
		ImagesDeleted: []image.DeleteResponse{
			{
				Untagged: "mock/image:latest",
				Deleted:  "sha256:mock123456789",
			},
		},
		SpaceReclaimed: 1024 * 1024 * 100, // 100MB
	}, nil
}

// ImageHistory implements the Client interface
func (m *MockClient) ImageHistory(ctx context.Context, imageID string) ([]image.HistoryResponseItem, error) {
	if m.ImageHistoryFunc != nil {
		return m.ImageHistoryFunc(ctx, imageID)
	}
	return []image.HistoryResponseItem{
		{
			ID:        "sha256:mock123456789",
			Created:   time.Now().Unix(),
			CreatedBy: "/bin/sh -c #(nop) CMD [\"/bin/bash\"]",
			Size:      1024 * 1024 * 10, // 10MB
			Comment:   "Mock image history",
		},
	}, nil
}

// VolumeList implements the Client interface
func (m *MockClient) VolumeList(ctx context.Context) (volume.ListResponse, error) {
	if m.VolumeListFunc != nil {
		return m.VolumeListFunc(ctx)
	}
	return volume.ListResponse{}, nil
}

// VolumeCreate implements the Client interface
func (m *MockClient) VolumeCreate(ctx context.Context, options volume.CreateOptions) (volume.Volume, error) {
	if m.VolumeCreateFunc != nil {
		return m.VolumeCreateFunc(ctx, options)
	}
	return volume.Volume{Name: "mock-volume"}, nil
}

// VolumeRemove implements the Client interface
func (m *MockClient) VolumeRemove(ctx context.Context, volumeID string, force bool) error {
	if m.VolumeRemoveFunc != nil {
		return m.VolumeRemoveFunc(ctx, volumeID, force)
	}
	return nil
}

// VolumeInspect implements the Client interface
func (m *MockClient) VolumeInspect(ctx context.Context, volumeID string) (volume.Volume, error) {
	if m.VolumeInspectFunc != nil {
		return m.VolumeInspectFunc(ctx, volumeID)
	}
	return volume.Volume{Name: "mock-volume"}, nil
}

// NetworkList implements the Client interface
func (m *MockClient) NetworkList(ctx context.Context) ([]NetworkResource, error) {
	if m.NetworkListFunc != nil {
		return m.NetworkListFunc(ctx)
	}
	return []NetworkResource{}, nil
}

// NetworkCreate implements the Client interface
func (m *MockClient) NetworkCreate(ctx context.Context, name string, options NetworkCreate) (NetworkCreateResponse, error) {
	if m.NetworkCreateFunc != nil {
		return m.NetworkCreateFunc(ctx, name, options)
	}
	return NetworkCreateResponse{ID: "mock-network-id"}, nil
}

// NetworkRemove implements the Client interface
func (m *MockClient) NetworkRemove(ctx context.Context, networkID string) error {
	if m.NetworkRemoveFunc != nil {
		return m.NetworkRemoveFunc(ctx, networkID)
	}
	return nil
}

// NetworkInspect implements the Client interface
func (m *MockClient) NetworkInspect(ctx context.Context, networkID string, verbose bool) (NetworkResource, error) {
	if m.NetworkInspectFunc != nil {
		return m.NetworkInspectFunc(ctx, networkID, verbose)
	}
	return NetworkResource{ID: "mock-network-id", Name: "mock-network"}, nil
}

// SystemInfo implements the Client interface
func (m *MockClient) SystemInfo(ctx context.Context) (Info, error) {
	if m.SystemInfoFunc != nil {
		return m.SystemInfoFunc(ctx)
	}
	return Info{
		ID:            "mock-id",
		ServerVersion: "mock-version",
		Containers:    1,
		Images:        2,
	}, nil
}

// SystemVersion implements the Client interface
func (m *MockClient) SystemVersion(ctx context.Context) (Version, error) {
	if m.SystemVersionFunc != nil {
		return m.SystemVersionFunc(ctx)
	}
	return Version{Version: "mock-version"}, nil
}

// SystemDiskUsage implements the Client interface
func (m *MockClient) SystemDiskUsage(ctx context.Context) (DiskUsage, error) {
	if m.SystemDiskUsageFunc != nil {
		return m.SystemDiskUsageFunc(ctx)
	}
	return DiskUsage{LayersSize: 1000}, nil
}

// SystemEvents implements the Client interface
func (m *MockClient) SystemEvents(ctx context.Context, options EventsOptions) (<-chan events.Message, <-chan error) {
	if m.SystemEventsFunc != nil {
		return m.SystemEventsFunc(ctx, options)
	}

	msgCh := make(chan events.Message)
	errCh := make(chan error, 1)

	go func() {
		defer close(msgCh)
		defer close(errCh)

		// Send a mock event
		select {
		case <-ctx.Done():
			errCh <- ctx.Err()
			return
		case msgCh <- events.Message{Type: "container", Action: "create"}:
		}

		// Wait for context to be done
		<-ctx.Done()
		errCh <- ctx.Err()
	}()

	return msgCh, errCh
}

// ContainerExecCreate implements the Client interface
func (m *MockClient) ContainerExecCreate(ctx context.Context, containerID string, options container.ExecOptions) (types.IDResponse, error) {
	if m.ContainerExecCreateFunc != nil {
		return m.ContainerExecCreateFunc(ctx, containerID, options)
	}
	return types.IDResponse{ID: "mock-exec-id"}, nil
}

// ContainerExecStart implements the Client interface
func (m *MockClient) ContainerExecStart(ctx context.Context, execID string, config container.ExecStartOptions) (types.HijackedResponse, error) {
	if m.ContainerExecStartFunc != nil {
		return m.ContainerExecStartFunc(ctx, execID, config)
	}
	// Create a reader for the HijackedResponse
	reader := bufio.NewReader(strings.NewReader("mock output"))
	return types.HijackedResponse{
		Reader: reader,
		Conn:   nil, // We don't need a connection for the test
	}, nil
}

// ContainerExecInspect implements the Client interface
func (m *MockClient) ContainerExecInspect(ctx context.Context, execID string) (container.ExecInspect, error) {
	if m.ContainerExecInspectFunc != nil {
		return m.ContainerExecInspectFunc(ctx, execID)
	}
	return container.ExecInspect{
		Running:  false,
		ExitCode: 0,
		Pid:      12345,
	}, nil
}

// Close implements the Client interface
func (m *MockClient) Close() error {
	if m.CloseFunc != nil {
		return m.CloseFunc()
	}
	return nil
}

// NewMockClient creates a new mock client
func NewMockClient() *MockClient {
	return &MockClient{}
}

// MockReadCloser is a mock implementation of io.ReadCloser
type MockReadCloser struct {
	Reader    io.Reader
	CloseFunc func() error
}

func (m *MockReadCloser) Read(p []byte) (n int, err error) {
	return m.Reader.Read(p)
}

func (m *MockReadCloser) Close() error {
	if m.CloseFunc != nil {
		return m.CloseFunc()
	}
	return nil
}

// NewMockReadCloser creates a new mock read closer
func NewMockReadCloser(s string) *MockReadCloser {
	return &MockReadCloser{
		Reader: strings.NewReader(s),
	}
}

// ErrMock is a mock error
var ErrMock = errors.New("mock error")
