package docker

// This file contains mock implementations for testing the Docker service

// NewMockService creates a new mock service
func NewMockService() *Service {
	// Create a mock client
	client := NewMockClient()

	// Create a service with the mock client directly
	return &Service{
		client:       client,
		containerSvc: NewContainerService(client),
		imageSvc:     NewImageService(client),
		volumeSvc:    NewVolumeService(client),
		networkSvc:   NewNetworkService(client),
		systemSvc:    NewSystemService(client),
	}
}


