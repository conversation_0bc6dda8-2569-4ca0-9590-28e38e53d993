package docker

import (
	"context"
	"fmt"

	"github.com/docker/docker/api/types/network"

	"daemon-server/internal/logger"
)

// NetworkService provides network management operations
type NetworkService struct {
	client Client
}

// NewNetworkService creates a new network service
func NewNetworkService(client Client) *NetworkService {
	return &NetworkService{
		client: client,
	}
}

// NetworkInfo represents network information
type NetworkInfo struct {
	ID         string                              `json:"id"`
	Name       string                              `json:"name"`
	Driver     string                              `json:"driver"`
	Scope      string                              `json:"scope"`
	Internal   bool                                `json:"internal"`
	EnableIPv6 bool                                `json:"enable_ipv6"`
	IPAM       network.IPAM                        `json:"ipam"`
	Containers map[string]network.EndpointResource `json:"containers"`
	Options    map[string]string                   `json:"options"`
	Labels     map[string]string                   `json:"labels"`
}

// ListNetworks lists all networks
func (s *NetworkService) ListNetworks(ctx context.Context) ([]NetworkInfo, error) {
	networks, err := s.client.NetworkList(ctx)
	if err != nil {
		logger.Error("Failed to list networks", logger.Err(err))
		return nil, fmt.Errorf("failed to list networks: %w", err)
	}

	var networkInfos []NetworkInfo
	for _, net := range networks {
		// Convert IPAM and Containers to our types
		ipam := network.IPAM{
			Driver:  net.IPAM.Driver,
			Options: net.IPAM.Options,
			Config:  net.IPAM.Config,
		}

		containers := make(map[string]network.EndpointResource)
		for k, v := range net.Containers {
			containers[k] = network.EndpointResource{
				Name:        v.Name,
				EndpointID:  v.EndpointID,
				MacAddress:  v.MacAddress,
				IPv4Address: v.IPv4Address,
				IPv6Address: v.IPv6Address,
			}
		}

		networkInfos = append(networkInfos, NetworkInfo{
			ID:         net.ID,
			Name:       net.Name,
			Driver:     net.Driver,
			Scope:      net.Scope,
			Internal:   net.Internal,
			EnableIPv6: net.EnableIPv6,
			IPAM:       ipam,
			Containers: containers,
			Options:    net.Options,
			Labels:     net.Labels,
		})
	}

	logger.Info("Listed networks", logger.Int("count", len(networkInfos)))
	return networkInfos, nil
}

// CreateNetwork creates a network
func (s *NetworkService) CreateNetwork(ctx context.Context, name string, driver string, internal bool, labels map[string]string, options map[string]string) (*NetworkInfo, error) {
	createOptions := NetworkCreate{
		Driver:   driver,
		Options:  options,
		Labels:   labels,
		Internal: internal,
	}

	resp, err := s.client.NetworkCreate(ctx, name, createOptions)
	if err != nil {
		logger.Error("Failed to create network",
			logger.Err(err),
			logger.String("name", name),
			logger.String("driver", driver),
		)
		return nil, fmt.Errorf("failed to create network: %w", err)
	}

	logger.Info("Network created",
		logger.String("id", resp.ID),
		logger.String("name", name),
		logger.String("driver", driver),
	)

	// Inspect the created network to get full details
	network, err := s.InspectNetwork(ctx, resp.ID)
	if err != nil {
		return nil, err
	}

	return network, nil
}

// RemoveNetwork removes a network
func (s *NetworkService) RemoveNetwork(ctx context.Context, networkID string) error {
	if err := s.client.NetworkRemove(ctx, networkID); err != nil {
		logger.Error("Failed to remove network",
			logger.Err(err),
			logger.String("network_id", networkID),
		)
		return fmt.Errorf("failed to remove network: %w", err)
	}

	logger.Info("Network removed", logger.String("network_id", networkID))
	return nil
}

// InspectNetwork inspects a network
func (s *NetworkService) InspectNetwork(ctx context.Context, networkID string) (*NetworkInfo, error) {
	net, err := s.client.NetworkInspect(ctx, networkID, true)
	if err != nil {
		logger.Error("Failed to inspect network",
			logger.Err(err),
			logger.String("network_id", networkID),
		)
		return nil, fmt.Errorf("failed to inspect network: %w", err)
	}

	// Convert IPAM and Containers to our types
	ipam := network.IPAM{
		Driver:  net.IPAM.Driver,
		Options: net.IPAM.Options,
		Config:  net.IPAM.Config,
	}

	containers := make(map[string]network.EndpointResource)
	for k, v := range net.Containers {
		containers[k] = network.EndpointResource{
			Name:        v.Name,
			EndpointID:  v.EndpointID,
			MacAddress:  v.MacAddress,
			IPv4Address: v.IPv4Address,
			IPv6Address: v.IPv6Address,
		}
	}

	logger.Info("Network inspected", logger.String("network_id", networkID))
	return &NetworkInfo{
		ID:         net.ID,
		Name:       net.Name,
		Driver:     net.Driver,
		Scope:      net.Scope,
		Internal:   net.Internal,
		EnableIPv6: net.EnableIPv6,
		IPAM:       ipam,
		Containers: containers,
		Options:    net.Options,
		Labels:     net.Labels,
	}, nil
}
