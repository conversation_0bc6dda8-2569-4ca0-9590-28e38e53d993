package docker

import (
	"context"
	"testing"

	"github.com/docker/docker/api/types/network"
)

// TestNewNetworkService tests the NewNetworkService function
func TestNewNetworkService(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()

	// Create a new network service
	service := NewNetworkService(mockClient)

	// Check that the service is not nil
	if service == nil {
		t.Fatal("Service is nil")
	}

	// Check that the service has the client
	if service.client != mockClient {
		t.Error("Service does not have the client")
	}
}

// TestListNetworks tests the ListNetworks method
func TestListNetworks(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.NetworkListFunc = func(ctx context.Context) ([]NetworkResource, error) {
		return []NetworkResource{
			{
				ID:         "network1",
				Name:       "bridge",
				Driver:     "bridge",
				Scope:      "local",
				Internal:   false,
				EnableIPv6: false,
				IPAM: network.IPAM{
					Driver: "default",
					Config: []network.IPAMConfig{
						{
							Subnet:  "**********/16",
							Gateway: "**********",
						},
					},
				},
				Containers: map[string]network.EndpointResource{
					"container1": {
						Name:        "container1",
						EndpointID:  "endpoint1",
						MacAddress:  "02:42:ac:11:00:02",
						IPv4Address: "**********/16",
					},
				},
				Options: map[string]string{
					"com.docker.network.bridge.default_bridge": "true",
				},
				Labels: map[string]string{
					"test": "true",
				},
			},
			{
				ID:         "network2",
				Name:       "host",
				Driver:     "host",
				Scope:      "local",
				Internal:   false,
				EnableIPv6: false,
				IPAM: network.IPAM{
					Driver: "default",
				},
				Containers: map[string]network.EndpointResource{},
				Options:    map[string]string{},
				Labels:     map[string]string{},
			},
		}, nil
	}

	// Create a network service with the mock client
	service := NewNetworkService(mockClient)

	// List networks
	networks, err := service.ListNetworks(context.Background())
	if err != nil {
		t.Fatalf("Failed to list networks: %v", err)
	}

	// Check the networks
	if len(networks) != 2 {
		t.Fatalf("Expected 2 networks, got %d", len(networks))
	}
	if networks[0].ID != "network1" {
		t.Errorf("Expected network ID 'network1', got '%s'", networks[0].ID)
	}
	if networks[0].Name != "bridge" {
		t.Errorf("Expected network name 'bridge', got '%s'", networks[0].Name)
	}
	if networks[0].Driver != "bridge" {
		t.Errorf("Expected network driver 'bridge', got '%s'", networks[0].Driver)
	}
	if networks[0].Scope != "local" {
		t.Errorf("Expected network scope 'local', got '%s'", networks[0].Scope)
	}
	if networks[0].Internal {
		t.Error("Expected network internal to be false")
	}
	if networks[0].EnableIPv6 {
		t.Error("Expected network enableIPv6 to be false")
	}
	if networks[0].IPAM.Driver != "default" {
		t.Errorf("Expected network IPAM driver 'default', got '%s'", networks[0].IPAM.Driver)
	}
	if len(networks[0].IPAM.Config) != 1 {
		t.Fatalf("Expected 1 IPAM config, got %d", len(networks[0].IPAM.Config))
	}
	if networks[0].IPAM.Config[0].Subnet != "**********/16" {
		t.Errorf("Expected IPAM config subnet '**********/16', got '%s'", networks[0].IPAM.Config[0].Subnet)
	}
	if networks[0].IPAM.Config[0].Gateway != "**********" {
		t.Errorf("Expected IPAM config gateway '**********', got '%s'", networks[0].IPAM.Config[0].Gateway)
	}
	if len(networks[0].Containers) != 1 {
		t.Fatalf("Expected 1 container, got %d", len(networks[0].Containers))
	}
	if _, ok := networks[0].Containers["container1"]; !ok {
		t.Error("Expected container 'container1' to exist")
	}
	if networks[0].Containers["container1"].Name != "container1" {
		t.Errorf("Expected container name 'container1', got '%s'", networks[0].Containers["container1"].Name)
	}
	if networks[0].Containers["container1"].EndpointID != "endpoint1" {
		t.Errorf("Expected container endpoint ID 'endpoint1', got '%s'", networks[0].Containers["container1"].EndpointID)
	}
	if networks[0].Containers["container1"].MacAddress != "02:42:ac:11:00:02" {
		t.Errorf("Expected container MAC address '02:42:ac:11:00:02', got '%s'", networks[0].Containers["container1"].MacAddress)
	}
	if networks[0].Containers["container1"].IPv4Address != "**********/16" {
		t.Errorf("Expected container IPv4 address '**********/16', got '%s'", networks[0].Containers["container1"].IPv4Address)
	}
	if len(networks[0].Options) != 1 {
		t.Fatalf("Expected 1 option, got %d", len(networks[0].Options))
	}
	if networks[0].Options["com.docker.network.bridge.default_bridge"] != "true" {
		t.Errorf("Expected option 'com.docker.network.bridge.default_bridge' to be 'true', got '%s'", networks[0].Options["com.docker.network.bridge.default_bridge"])
	}
	if len(networks[0].Labels) != 1 {
		t.Fatalf("Expected 1 label, got %d", len(networks[0].Labels))
	}
	if networks[0].Labels["test"] != "true" {
		t.Errorf("Expected label 'test' to be 'true', got '%s'", networks[0].Labels["test"])
	}
}

// TestListNetworksError tests the ListNetworks method with an error
func TestListNetworksError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.NetworkListFunc = func(ctx context.Context) ([]NetworkResource, error) {
		return nil, ErrMock
	}

	// Create a network service with the mock client
	service := NewNetworkService(mockClient)

	// List networks
	networks, err := service.ListNetworks(context.Background())
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if networks != nil {
		t.Fatal("Expected networks to be nil")
	}
}

// TestCreateNetwork tests the CreateNetwork method
func TestCreateNetwork(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.NetworkCreateFunc = func(ctx context.Context, name string, options NetworkCreate) (NetworkCreateResponse, error) {
		// Check the parameters
		if name != "test-network" {
			t.Errorf("Expected network name 'test-network', got '%s'", name)
		}
		if options.Driver != "bridge" {
			t.Errorf("Expected network driver 'bridge', got '%s'", options.Driver)
		}
		if !options.Internal {
			t.Error("Expected network internal to be true")
		}
		if len(options.Options) != 1 || options.Options["com.docker.network.bridge.name"] != "test-bridge" {
			t.Errorf("Expected network options {'com.docker.network.bridge.name': 'test-bridge'}, got %v", options.Options)
		}
		if len(options.Labels) != 1 || options.Labels["test"] != "true" {
			t.Errorf("Expected network labels {'test': 'true'}, got %v", options.Labels)
		}

		return NetworkCreateResponse{
			ID:      "test-network-id",
			Warning: "test warning",
		}, nil
	}

	// Mock the network inspect function to return a network
	mockClient.NetworkInspectFunc = func(ctx context.Context, networkID string, verbose bool) (NetworkResource, error) {
		// Check the parameters
		if networkID != "test-network-id" {
			t.Errorf("Expected network ID 'test-network-id', got '%s'", networkID)
		}
		if !verbose {
			t.Error("Expected verbose to be true")
		}

		return NetworkResource{
			ID:         "test-network-id",
			Name:       "test-network",
			Driver:     "bridge",
			Scope:      "local",
			Internal:   true,
			EnableIPv6: false,
			IPAM: network.IPAM{
				Driver: "default",
				Config: []network.IPAMConfig{
					{
						Subnet:  "**********/16",
						Gateway: "**********",
					},
				},
			},
			Containers: map[string]network.EndpointResource{},
			Options: map[string]string{
				"com.docker.network.bridge.name": "test-bridge",
			},
			Labels: map[string]string{
				"test": "true",
			},
		}, nil
	}

	// Create a network service with the mock client
	service := NewNetworkService(mockClient)

	// Create a network
	network, err := service.CreateNetwork(context.Background(), "test-network", "bridge", true, map[string]string{"test": "true"}, map[string]string{"com.docker.network.bridge.name": "test-bridge"})
	if err != nil {
		t.Fatalf("Failed to create network: %v", err)
	}

	// Check the network
	if network.ID != "test-network-id" {
		t.Errorf("Expected network ID 'test-network-id', got '%s'", network.ID)
	}
	if network.Name != "test-network" {
		t.Errorf("Expected network name 'test-network', got '%s'", network.Name)
	}
	if network.Driver != "bridge" {
		t.Errorf("Expected network driver 'bridge', got '%s'", network.Driver)
	}
	if network.Scope != "local" {
		t.Errorf("Expected network scope 'local', got '%s'", network.Scope)
	}
	if !network.Internal {
		t.Error("Expected network internal to be true")
	}
	if network.EnableIPv6 {
		t.Error("Expected network enableIPv6 to be false")
	}
	if network.IPAM.Driver != "default" {
		t.Errorf("Expected network IPAM driver 'default', got '%s'", network.IPAM.Driver)
	}
	if len(network.IPAM.Config) != 1 {
		t.Fatalf("Expected 1 IPAM config, got %d", len(network.IPAM.Config))
	}
	if network.IPAM.Config[0].Subnet != "**********/16" {
		t.Errorf("Expected IPAM config subnet '**********/16', got '%s'", network.IPAM.Config[0].Subnet)
	}
	if network.IPAM.Config[0].Gateway != "**********" {
		t.Errorf("Expected IPAM config gateway '**********', got '%s'", network.IPAM.Config[0].Gateway)
	}
	if len(network.Options) != 1 {
		t.Fatalf("Expected 1 option, got %d", len(network.Options))
	}
	if network.Options["com.docker.network.bridge.name"] != "test-bridge" {
		t.Errorf("Expected option 'com.docker.network.bridge.name' to be 'test-bridge', got '%s'", network.Options["com.docker.network.bridge.name"])
	}
	if len(network.Labels) != 1 {
		t.Fatalf("Expected 1 label, got %d", len(network.Labels))
	}
	if network.Labels["test"] != "true" {
		t.Errorf("Expected label 'test' to be 'true', got '%s'", network.Labels["test"])
	}
}

// TestCreateNetworkError tests the CreateNetwork method with an error
func TestCreateNetworkError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.NetworkCreateFunc = func(ctx context.Context, name string, options NetworkCreate) (NetworkCreateResponse, error) {
		return NetworkCreateResponse{}, ErrMock
	}

	// Create a network service with the mock client
	service := NewNetworkService(mockClient)

	// Create a network
	network, err := service.CreateNetwork(context.Background(), "test-network", "bridge", false, nil, nil)
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if network != nil {
		t.Fatal("Expected network to be nil")
	}
}

// TestRemoveNetwork tests the RemoveNetwork method
func TestRemoveNetwork(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.NetworkRemoveFunc = func(ctx context.Context, networkID string) error {
		// Check the parameters
		if networkID != "test-network-id" {
			t.Errorf("Expected network ID 'test-network-id', got '%s'", networkID)
		}
		return nil
	}

	// Create a network service with the mock client
	service := NewNetworkService(mockClient)

	// Remove a network
	err := service.RemoveNetwork(context.Background(), "test-network-id")
	if err != nil {
		t.Fatalf("Failed to remove network: %v", err)
	}
}

// TestRemoveNetworkError tests the RemoveNetwork method with an error
func TestRemoveNetworkError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.NetworkRemoveFunc = func(ctx context.Context, networkID string) error {
		return ErrMock
	}

	// Create a network service with the mock client
	service := NewNetworkService(mockClient)

	// Remove a network
	err := service.RemoveNetwork(context.Background(), "test-network-id")
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
}

// TestInspectNetwork tests the InspectNetwork method
func TestInspectNetwork(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.NetworkInspectFunc = func(ctx context.Context, networkID string, verbose bool) (NetworkResource, error) {
		// Check the parameters
		if networkID != "test-network-id" {
			t.Errorf("Expected network ID 'test-network-id', got '%s'", networkID)
		}
		if !verbose {
			t.Error("Expected verbose to be true")
		}

		return NetworkResource{
			ID:         "test-network-id",
			Name:       "test-network",
			Driver:     "bridge",
			Scope:      "local",
			Internal:   false,
			EnableIPv6: false,
			IPAM: network.IPAM{
				Driver: "default",
				Config: []network.IPAMConfig{
					{
						Subnet:  "**********/16",
						Gateway: "**********",
					},
				},
			},
			Containers: map[string]network.EndpointResource{
				"container1": {
					Name:        "container1",
					EndpointID:  "endpoint1",
					MacAddress:  "02:42:ac:12:00:02",
					IPv4Address: "**********/16",
				},
			},
			Options: map[string]string{
				"com.docker.network.bridge.name": "test-bridge",
			},
			Labels: map[string]string{
				"test": "true",
			},
		}, nil
	}

	// Create a network service with the mock client
	service := NewNetworkService(mockClient)

	// Inspect a network
	network, err := service.InspectNetwork(context.Background(), "test-network-id")
	if err != nil {
		t.Fatalf("Failed to inspect network: %v", err)
	}

	// Check the network
	if network.ID != "test-network-id" {
		t.Errorf("Expected network ID 'test-network-id', got '%s'", network.ID)
	}
	if network.Name != "test-network" {
		t.Errorf("Expected network name 'test-network', got '%s'", network.Name)
	}
	if network.Driver != "bridge" {
		t.Errorf("Expected network driver 'bridge', got '%s'", network.Driver)
	}
	if network.Scope != "local" {
		t.Errorf("Expected network scope 'local', got '%s'", network.Scope)
	}
	if network.Internal {
		t.Error("Expected network internal to be false")
	}
	if network.EnableIPv6 {
		t.Error("Expected network enableIPv6 to be false")
	}
	if network.IPAM.Driver != "default" {
		t.Errorf("Expected network IPAM driver 'default', got '%s'", network.IPAM.Driver)
	}
	if len(network.IPAM.Config) != 1 {
		t.Fatalf("Expected 1 IPAM config, got %d", len(network.IPAM.Config))
	}
	if network.IPAM.Config[0].Subnet != "**********/16" {
		t.Errorf("Expected IPAM config subnet '**********/16', got '%s'", network.IPAM.Config[0].Subnet)
	}
	if network.IPAM.Config[0].Gateway != "**********" {
		t.Errorf("Expected IPAM config gateway '**********', got '%s'", network.IPAM.Config[0].Gateway)
	}
	if len(network.Containers) != 1 {
		t.Fatalf("Expected 1 container, got %d", len(network.Containers))
	}
	if _, ok := network.Containers["container1"]; !ok {
		t.Error("Expected container 'container1' to exist")
	}
	if network.Containers["container1"].Name != "container1" {
		t.Errorf("Expected container name 'container1', got '%s'", network.Containers["container1"].Name)
	}
	if network.Containers["container1"].EndpointID != "endpoint1" {
		t.Errorf("Expected container endpoint ID 'endpoint1', got '%s'", network.Containers["container1"].EndpointID)
	}
	if network.Containers["container1"].MacAddress != "02:42:ac:12:00:02" {
		t.Errorf("Expected container MAC address '02:42:ac:12:00:02', got '%s'", network.Containers["container1"].MacAddress)
	}
	if network.Containers["container1"].IPv4Address != "**********/16" {
		t.Errorf("Expected container IPv4 address '**********/16', got '%s'", network.Containers["container1"].IPv4Address)
	}
	if len(network.Options) != 1 {
		t.Fatalf("Expected 1 option, got %d", len(network.Options))
	}
	if network.Options["com.docker.network.bridge.name"] != "test-bridge" {
		t.Errorf("Expected option 'com.docker.network.bridge.name' to be 'test-bridge', got '%s'", network.Options["com.docker.network.bridge.name"])
	}
	if len(network.Labels) != 1 {
		t.Fatalf("Expected 1 label, got %d", len(network.Labels))
	}
	if network.Labels["test"] != "true" {
		t.Errorf("Expected label 'test' to be 'true', got '%s'", network.Labels["test"])
	}
}

// TestInspectNetworkError tests the InspectNetwork method with an error
func TestInspectNetworkError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.NetworkInspectFunc = func(ctx context.Context, networkID string, verbose bool) (NetworkResource, error) {
		return NetworkResource{}, ErrMock
	}

	// Create a network service with the mock client
	service := NewNetworkService(mockClient)

	// Inspect a network
	network, err := service.InspectNetwork(context.Background(), "test-network-id")
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if network != nil {
		t.Fatal("Expected network to be nil")
	}
}
