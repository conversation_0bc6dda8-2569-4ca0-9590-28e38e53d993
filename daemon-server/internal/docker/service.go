package docker

import (
	"context"

	"daemon-server/configs"
	"daemon-server/internal/logger"
)

// Service provides access to all Docker services
type Service struct {
	client       Client
	containerSvc *ContainerService
	imageSvc     *ImageService
	volumeSvc    *VolumeService
	networkSvc   *NetworkService
	systemSvc    *SystemService
}

// NewService creates a new Docker service
func NewService(config *configs.Config) (*Service, error) {
	client, err := NewClient(config)
	if err != nil {
		return nil, err
	}

	return &Service{
		client:       client,
		containerSvc: NewContainerService(client),
		imageSvc:     NewImageService(client),
		volumeSvc:    NewVolumeService(client),
		networkSvc:   NewNetworkService(client),
		systemSvc:    NewSystemService(client),
	}, nil
}

// Close closes the Docker service
func (s *Service) Close() error {
	if s.client != nil {
		return s.client.Close()
	}
	return nil
}

// Container returns the container service
func (s *Service) Container() *ContainerService {
	return s.containerSvc
}

// Image returns the image service
func (s *Service) Image() *ImageService {
	return s.imageSvc
}

// Volume returns the volume service
func (s *Service) Volume() *VolumeService {
	return s.volumeSvc
}

// Network returns the network service
func (s *Service) Network() *NetworkService {
	return s.networkSvc
}

// System returns the system service
func (s *Service) System() *SystemService {
	return s.systemSvc
}

// Ping checks if the Docker daemon is running
func (s *Service) Ping(ctx context.Context) error {
	info, err := s.systemSvc.GetSystemInfo(ctx)
	if err != nil {
		logger.Error("Failed to ping Docker daemon", logger.Err(err))
		return err
	}

	logger.Info("Docker daemon is running",
		logger.String("version", info.ServerVersion),
		logger.Int("containers", info.Containers),
		logger.Int("images", info.Images),
	)
	return nil
}

// RefreshClient refreshes the Docker client connection
// This should be called when the Docker daemon restarts
func (s *Service) RefreshClient(config *configs.Config) error {
	logger.Info("Refreshing Docker client connection")

	// Close existing client
	if err := s.Close(); err != nil {
		logger.Warn("Error closing existing Docker client", logger.Err(err))
		// Continue anyway
	}

	// Create new client
	client, err := NewClient(config)
	if err != nil {
		logger.Error("Failed to create new Docker client", logger.Err(err))
		return err
	}

	// Update service with new client
	s.client = client
	s.containerSvc = NewContainerService(client)
	s.imageSvc = NewImageService(client)
	s.volumeSvc = NewVolumeService(client)
	s.networkSvc = NewNetworkService(client)
	s.systemSvc = NewSystemService(client)

	logger.Info("Docker client refreshed successfully")
	return nil
}
