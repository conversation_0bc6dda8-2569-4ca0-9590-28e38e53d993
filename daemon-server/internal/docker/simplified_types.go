package docker

import (
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/go-connections/nat"
)

// SimpleContainerCreateRequest represents a simplified request to create a container
type SimpleContainerCreateRequest struct {
	Name        string            `json:"name"`
	Image       string            `json:"image"`
	Command     []string          `json:"command,omitempty"`
	Entrypoint  []string          `json:"entrypoint,omitempty"`
	Env         []string          `json:"env,omitempty"`
	Labels      map[string]string `json:"labels,omitempty"`
	Ports       []string          `json:"ports,omitempty"`        // Format: "host_port:container_port/protocol", e.g. "8080:80/tcp"
	Volumes     []string          `json:"volumes,omitempty"`      // Format: "host_path:container_path", e.g. "/host:/container"
	NetworkMode string            `json:"network_mode,omitempty"` // e.g. "bridge", "host", "none", "container:<name|id>"
	Restart     string            `json:"restart,omitempty"`      // e.g. "no", "always", "on-failure", "unless-stopped"
	Privileged  bool              `json:"privileged,omitempty"`
	AutoRemove  bool              `json:"auto_remove,omitempty"`
}

// ToContainerCreateRequest converts a SimpleContainerCreateRequest to a ContainerCreateRequest
func (s *SimpleContainerCreateRequest) ToContainerCreateRequest() ContainerCreateRequest {
	// Create exposed ports
	exposedPorts := nat.PortSet{}
	portBindings := nat.PortMap{}

	for _, port := range s.Ports {
		portObj, binding, _ := nat.ParsePortSpecs([]string{port})
		for p := range portObj {
			exposedPorts[p] = struct{}{}
		}
		for p, b := range binding {
			portBindings[p] = b
		}
	}

	// Create host config
	hostConfig := &container.HostConfig{
		PortBindings: portBindings,
		Privileged:   s.Privileged,
		AutoRemove:   s.AutoRemove,
	}

	// Set restart policy
	if s.Restart != "" {
		hostConfig.RestartPolicy = container.RestartPolicy{
			Name: container.RestartPolicyMode(s.Restart),
		}
	}

	// Set network mode
	if s.NetworkMode != "" {
		hostConfig.NetworkMode = container.NetworkMode(s.NetworkMode)
	}

	// Set volumes
	if len(s.Volumes) > 0 {
		hostConfig.Binds = s.Volumes
	}

	// Create network config
	networkConfig := &network.NetworkingConfig{}

	return ContainerCreateRequest{
		Name:          s.Name,
		Image:         s.Image,
		Command:       s.Command,
		Entrypoint:    s.Entrypoint,
		Env:           s.Env,
		Labels:        s.Labels,
		ExposedPorts:  exposedPorts,
		HostConfig:    hostConfig,
		NetworkConfig: networkConfig,
	}
}

// SimplePullImageRequest represents a simplified request to pull an image
type SimplePullImageRequest struct {
	Image string `json:"image"`          // Image name with optional tag, e.g. "nginx:latest"
	Auth  string `json:"auth,omitempty"` // Base64 encoded auth string
}

type ImageSaveParam struct {
	ImageId  string `json:"image_id"`
	SavePath string `json:"save_path"`
}

// SimpleLoadImageRequest 表示简化的镜像加载请求
type SimpleLoadImageRequest struct {
	FilePath string `json:"file_path"` // 本地文件路径
}
