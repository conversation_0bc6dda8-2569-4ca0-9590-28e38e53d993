package docker

import (
	"context"
	"fmt"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/events"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/api/types/volume"

	"daemon-server/internal/logger"
)

// SystemService provides system management operations
type SystemService struct {
	client Client
}

// NewSystemService creates a new system service
func NewSystemService(client Client) *SystemService {
	return &SystemService{
		client: client,
	}
}

// SystemInfo represents system information
type SystemInfo struct {
	ID                string      `json:"id"`
	Containers        int         `json:"containers"`
	ContainersRunning int         `json:"containers_running"`
	ContainersPaused  int         `json:"containers_paused"`
	ContainersStopped int         `json:"containers_stopped"`
	Images            int         `json:"images"`
	Driver            string      `json:"driver"`
	DriverStatus      [][2]string `json:"driver_status"`
	SystemTime        string      `json:"system_time"`
	LoggingDriver     string      `json:"logging_driver"`
	CgroupDriver      string      `json:"cgroup_driver"`
	KernelVersion     string      `json:"kernel_version"`
	OperatingSystem   string      `json:"operating_system"`
	OSType            string      `json:"os_type"`
	Architecture      string      `json:"architecture"`
	NCPU              int         `json:"n_cpu"`
	MemTotal          int64       `json:"mem_total"`
	DockerRootDir     string      `json:"docker_root_dir"`
	ServerVersion     string      `json:"server_version"`
}

// GetSystemInfo gets system information
func (s *SystemService) GetSystemInfo(ctx context.Context) (*SystemInfo, error) {
	info, err := s.client.SystemInfo(ctx)
	if err != nil {
		logger.Error("Failed to get system info", logger.Err(err))
		return nil, fmt.Errorf("failed to get system info: %w", err)
	}

	logger.Info("System info retrieved")
	return &SystemInfo{
		ID:                info.ID,
		Containers:        info.Containers,
		ContainersRunning: info.ContainersRunning,
		ContainersPaused:  info.ContainersPaused,
		ContainersStopped: info.ContainersStopped,
		Images:            info.Images,
		Driver:            info.Driver,
		DriverStatus:      info.DriverStatus,
		SystemTime:        info.SystemTime,
		LoggingDriver:     info.LoggingDriver,
		CgroupDriver:      info.CgroupDriver,
		KernelVersion:     info.KernelVersion,
		OperatingSystem:   info.OperatingSystem,
		OSType:            info.OSType,
		Architecture:      info.Architecture,
		NCPU:              info.NCPU,
		MemTotal:          info.MemTotal,
		DockerRootDir:     info.DockerRootDir,
		ServerVersion:     info.ServerVersion,
	}, nil
}

// VersionInfo represents version information
type VersionInfo struct {
	Version       string `json:"version"`
	APIVersion    string `json:"api_version"`
	MinAPIVersion string `json:"min_api_version"`
	GitCommit     string `json:"git_commit"`
	GoVersion     string `json:"go_version"`
	Os            string `json:"os"`
	Arch          string `json:"arch"`
	BuildTime     string `json:"build_time"`
}

// GetVersionInfo gets version information
func (s *SystemService) GetVersionInfo(ctx context.Context) (*VersionInfo, error) {
	version, err := s.client.SystemVersion(ctx)
	if err != nil {
		logger.Error("Failed to get version info", logger.Err(err))
		return nil, fmt.Errorf("failed to get version info: %w", err)
	}

	logger.Info("Version info retrieved")
	return &VersionInfo{
		Version:       version.Version,
		APIVersion:    version.APIVersion,
		MinAPIVersion: version.MinAPIVersion,
		GitCommit:     version.GitCommit,
		GoVersion:     version.GoVersion,
		Os:            version.Os,
		Arch:          version.Arch,
		BuildTime:     version.BuildTime,
	}, nil
}

// DiskUsageInfo represents disk usage information
type DiskUsageInfo struct {
	LayersSize int64              `json:"layers_size"`
	Images     []image.Summary    `json:"images"`
	Containers []types.Container  `json:"containers"`
	Volumes    []volume.Volume    `json:"volumes"`
	BuildCache []types.BuildCache `json:"build_cache"`
}

// GetDiskUsage gets disk usage information
func (s *SystemService) GetDiskUsage(ctx context.Context) (*DiskUsageInfo, error) {
	usage, err := s.client.SystemDiskUsage(ctx)
	if err != nil {
		logger.Error("Failed to get disk usage", logger.Err(err))
		return nil, fmt.Errorf("failed to get disk usage: %w", err)
	}

	logger.Info("Disk usage retrieved")
	return &DiskUsageInfo{
		LayersSize: usage.LayersSize,
		Images:     usage.Images,
		Containers: usage.Containers,
		Volumes:    usage.Volumes,
		BuildCache: usage.BuildCache,
	}, nil
}

// Using EventsOptions from types.go

// MonitorEvents monitors Docker events
func (s *SystemService) MonitorEvents(ctx context.Context, options EventsOptions) (<-chan events.Message, <-chan error) {
	// Pass the options directly
	eventsOptions := options

	logger.Info("Monitoring Docker events")
	return s.client.SystemEvents(ctx, eventsOptions)
}
