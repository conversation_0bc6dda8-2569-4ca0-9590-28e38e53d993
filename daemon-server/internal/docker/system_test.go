package docker

import (
	"context"
	"testing"
	"time"

	"github.com/docker/docker/api/types/events"
	"github.com/docker/docker/api/types/filters"
)

// TestNewSystemService tests the NewSystemService function
func TestNewSystemService(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()

	// Create a new system service
	service := NewSystemService(mockClient)

	// Check that the service is not nil
	if service == nil {
		t.Fatal("Service is nil")
	}

	// Check that the service has the client
	if service.client != mockClient {
		t.Error("Service does not have the client")
	}
}

// TestGetSystemInfo tests the GetSystemInfo method
func TestGetSystemInfo(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.SystemInfoFunc = func(ctx context.Context) (Info, error) {
		return Info{
			ID:                "test-id",
			Containers:        10,
			ContainersRunning: 5,
			ContainersPaused:  1,
			ContainersStopped: 4,
			Images:            20,
			Driver:            "overlay2",
			DriverStatus:      [][2]string{{"Pool Name", "docker-pool"}},
			SystemTime:        "2023-01-01T00:00:00Z",
			LoggingDriver:     "json-file",
			CgroupDriver:      "cgroupfs",
			KernelVersion:     "5.10.0",
			OperatingSystem:   "Ubuntu 20.04.3 LTS",
			OSType:            "linux",
			Architecture:      "x86_64",
			NCPU:              4,
			MemTotal:          8589934592,
			DockerRootDir:     "/var/lib/docker",
			ServerVersion:     "20.10.12",
		}, nil
	}

	// Create a system service with the mock client
	service := NewSystemService(mockClient)

	// Get system info
	info, err := service.GetSystemInfo(context.Background())
	if err != nil {
		t.Fatalf("Failed to get system info: %v", err)
	}

	// Check the info
	if info.ID != "test-id" {
		t.Errorf("Expected ID 'test-id', got '%s'", info.ID)
	}
	if info.Containers != 10 {
		t.Errorf("Expected 10 containers, got %d", info.Containers)
	}
	if info.ContainersRunning != 5 {
		t.Errorf("Expected 5 running containers, got %d", info.ContainersRunning)
	}
	if info.ContainersPaused != 1 {
		t.Errorf("Expected 1 paused container, got %d", info.ContainersPaused)
	}
	if info.ContainersStopped != 4 {
		t.Errorf("Expected 4 stopped containers, got %d", info.ContainersStopped)
	}
	if info.Images != 20 {
		t.Errorf("Expected 20 images, got %d", info.Images)
	}
	if info.Driver != "overlay2" {
		t.Errorf("Expected driver 'overlay2', got '%s'", info.Driver)
	}
	if len(info.DriverStatus) != 1 || info.DriverStatus[0][0] != "Pool Name" || info.DriverStatus[0][1] != "docker-pool" {
		t.Errorf("Expected driver status [['Pool Name', 'docker-pool']], got %v", info.DriverStatus)
	}
	if info.SystemTime != "2023-01-01T00:00:00Z" {
		t.Errorf("Expected system time '2023-01-01T00:00:00Z', got '%s'", info.SystemTime)
	}
	if info.LoggingDriver != "json-file" {
		t.Errorf("Expected logging driver 'json-file', got '%s'", info.LoggingDriver)
	}
	if info.CgroupDriver != "cgroupfs" {
		t.Errorf("Expected cgroup driver 'cgroupfs', got '%s'", info.CgroupDriver)
	}
	if info.KernelVersion != "5.10.0" {
		t.Errorf("Expected kernel version '5.10.0', got '%s'", info.KernelVersion)
	}
	if info.OperatingSystem != "Ubuntu 20.04.3 LTS" {
		t.Errorf("Expected operating system 'Ubuntu 20.04.3 LTS', got '%s'", info.OperatingSystem)
	}
	if info.OSType != "linux" {
		t.Errorf("Expected OS type 'linux', got '%s'", info.OSType)
	}
	if info.Architecture != "x86_64" {
		t.Errorf("Expected architecture 'x86_64', got '%s'", info.Architecture)
	}
	if info.NCPU != 4 {
		t.Errorf("Expected 4 CPUs, got %d", info.NCPU)
	}
	if info.MemTotal != 8589934592 {
		t.Errorf("Expected 8589934592 bytes of memory, got %d", info.MemTotal)
	}
	if info.DockerRootDir != "/var/lib/docker" {
		t.Errorf("Expected Docker root dir '/var/lib/docker', got '%s'", info.DockerRootDir)
	}
	if info.ServerVersion != "20.10.12" {
		t.Errorf("Expected server version '20.10.12', got '%s'", info.ServerVersion)
	}
}

// TestGetSystemInfoError tests the GetSystemInfo method with an error
func TestGetSystemInfoError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.SystemInfoFunc = func(ctx context.Context) (Info, error) {
		return Info{}, ErrMock
	}

	// Create a system service with the mock client
	service := NewSystemService(mockClient)

	// Get system info
	info, err := service.GetSystemInfo(context.Background())
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if info != nil {
		t.Fatal("Expected info to be nil")
	}
}

// TestGetVersionInfo tests the GetVersionInfo method
func TestGetVersionInfo(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.SystemVersionFunc = func(ctx context.Context) (Version, error) {
		return Version{
			Version:       "20.10.12",
			APIVersion:    "1.41",
			MinAPIVersion: "1.12",
			GitCommit:     "459d0df",
			GoVersion:     "go1.16.12",
			Os:            "linux",
			Arch:          "amd64",
			BuildTime:     "2021-12-13T11:46:56.000000000+00:00",
		}, nil
	}

	// Create a system service with the mock client
	service := NewSystemService(mockClient)

	// Get version info
	version, err := service.GetVersionInfo(context.Background())
	if err != nil {
		t.Fatalf("Failed to get version info: %v", err)
	}

	// Check the version
	if version.Version != "20.10.12" {
		t.Errorf("Expected version '20.10.12', got '%s'", version.Version)
	}
	if version.APIVersion != "1.41" {
		t.Errorf("Expected API version '1.41', got '%s'", version.APIVersion)
	}
	if version.MinAPIVersion != "1.12" {
		t.Errorf("Expected min API version '1.12', got '%s'", version.MinAPIVersion)
	}
	if version.GitCommit != "459d0df" {
		t.Errorf("Expected git commit '459d0df', got '%s'", version.GitCommit)
	}
	if version.GoVersion != "go1.16.12" {
		t.Errorf("Expected Go version 'go1.16.12', got '%s'", version.GoVersion)
	}
	if version.Os != "linux" {
		t.Errorf("Expected OS 'linux', got '%s'", version.Os)
	}
	if version.Arch != "amd64" {
		t.Errorf("Expected architecture 'amd64', got '%s'", version.Arch)
	}
	if version.BuildTime != "2021-12-13T11:46:56.000000000+00:00" {
		t.Errorf("Expected build time '2021-12-13T11:46:56.000000000+00:00', got '%s'", version.BuildTime)
	}
}

// TestGetVersionInfoError tests the GetVersionInfo method with an error
func TestGetVersionInfoError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.SystemVersionFunc = func(ctx context.Context) (Version, error) {
		return Version{}, ErrMock
	}

	// Create a system service with the mock client
	service := NewSystemService(mockClient)

	// Get version info
	version, err := service.GetVersionInfo(context.Background())
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if version != nil {
		t.Fatal("Expected version to be nil")
	}
}

// TestGetDiskUsage tests the GetDiskUsage method
func TestGetDiskUsage(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.SystemDiskUsageFunc = func(ctx context.Context) (DiskUsage, error) {
		return DiskUsage{
			LayersSize: 1000000000,
		}, nil
	}

	// Create a system service with the mock client
	service := NewSystemService(mockClient)

	// Get disk usage
	usage, err := service.GetDiskUsage(context.Background())
	if err != nil {
		t.Fatalf("Failed to get disk usage: %v", err)
	}

	// Check the usage
	if usage.LayersSize != 1000000000 {
		t.Errorf("Expected layers size 1000000000, got %d", usage.LayersSize)
	}
}

// TestGetDiskUsageError tests the GetDiskUsage method with an error
func TestGetDiskUsageError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.SystemDiskUsageFunc = func(ctx context.Context) (DiskUsage, error) {
		return DiskUsage{}, ErrMock
	}

	// Create a system service with the mock client
	service := NewSystemService(mockClient)

	// Get disk usage
	usage, err := service.GetDiskUsage(context.Background())
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if usage != nil {
		t.Fatal("Expected usage to be nil")
	}
}

// TestMonitorEvents tests the MonitorEvents method
func TestMonitorEvents(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.SystemEventsFunc = func(ctx context.Context, options EventsOptions) (<-chan events.Message, <-chan error) {
		// Check the parameters
		if options.Since != "2023-01-01T00:00:00Z" {
			t.Errorf("Expected since '2023-01-01T00:00:00Z', got '%s'", options.Since)
		}
		if options.Until != "2023-01-02T00:00:00Z" {
			t.Errorf("Expected until '2023-01-02T00:00:00Z', got '%s'", options.Until)
		}

		// Create channels
		msgCh := make(chan events.Message, 1)
		errCh := make(chan error, 1)

		// Send a message
		go func() {
			msgCh <- events.Message{
				Type:   "container",
				Action: "create",
				Actor: events.Actor{
					ID: "container1",
					Attributes: map[string]string{
						"name": "container1",
					},
				},
				Time:     time.Now().Unix(),
				TimeNano: time.Now().UnixNano(),
			}
			close(msgCh)
			close(errCh)
		}()

		return msgCh, errCh
	}

	// Create a system service with the mock client
	service := NewSystemService(mockClient)

	// Create filters
	f := filters.NewArgs()
	f.Add("type", "container")

	// Monitor events
	msgCh, errCh := service.MonitorEvents(context.Background(), EventsOptions{
		Since:   "2023-01-01T00:00:00Z",
		Until:   "2023-01-02T00:00:00Z",
		Filters: f,
	})

	// Check the messages
	msg := <-msgCh
	if msg.Type != "container" {
		t.Errorf("Expected message type 'container', got '%s'", msg.Type)
	}
	if msg.Action != "create" {
		t.Errorf("Expected message action 'create', got '%s'", msg.Action)
	}
	if msg.Actor.ID != "container1" {
		t.Errorf("Expected actor ID 'container1', got '%s'", msg.Actor.ID)
	}
	if msg.Actor.Attributes["name"] != "container1" {
		t.Errorf("Expected actor attribute 'name' to be 'container1', got '%s'", msg.Actor.Attributes["name"])
	}

	// Check for errors
	select {
	case err := <-errCh:
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}
	default:
		// No error, which is expected
	}
}
