package docker

import (
	"context"
	"fmt"

	"github.com/docker/docker/api/types/volume"

	"daemon-server/internal/logger"
)

// VolumeService provides volume management operations
type VolumeService struct {
	client Client
}

// NewVolumeService creates a new volume service
func NewVolumeService(client Client) *VolumeService {
	return &VolumeService{
		client: client,
	}
}

// VolumeInfo represents volume information
type VolumeInfo struct {
	Name       string            `json:"name"`
	Driver     string            `json:"driver"`
	Mountpoint string            `json:"mountpoint"`
	CreatedAt  string            `json:"created_at"`
	Labels     map[string]string `json:"labels"`
	Scope      string            `json:"scope"`
	Options    map[string]string `json:"options"`
}

// ListVolumes lists all volumes
func (s *VolumeService) ListVolumes(ctx context.Context) ([]VolumeInfo, error) {
	resp, err := s.client.VolumeList(ctx)
	if err != nil {
		logger.Error("Failed to list volumes", logger.Err(err))
		return nil, fmt.<PERSON><PERSON><PERSON>("failed to list volumes: %w", err)
	}

	var volumeInfos []VolumeInfo
	for _, vol := range resp.Volumes {
		volumeInfos = append(volumeInfos, VolumeInfo{
			Name:       vol.Name,
			Driver:     vol.Driver,
			Mountpoint: vol.Mountpoint,
			CreatedAt:  vol.CreatedAt,
			Labels:     vol.Labels,
			Scope:      vol.Scope,
			Options:    vol.Options,
		})
	}

	logger.Info("Listed volumes", logger.Int("count", len(volumeInfos)))
	return volumeInfos, nil
}

// CreateVolume creates a volume
func (s *VolumeService) CreateVolume(ctx context.Context, name string, driver string, labels map[string]string, options map[string]string) (*VolumeInfo, error) {
	createOptions := volume.CreateOptions{
		Name:       name,
		Driver:     driver,
		Labels:     labels,
		DriverOpts: options,
	}

	vol, err := s.client.VolumeCreate(ctx, createOptions)
	if err != nil {
		logger.Error("Failed to create volume",
			logger.Err(err),
			logger.String("name", name),
			logger.String("driver", driver),
		)
		return nil, fmt.Errorf("failed to create volume: %w", err)
	}

	logger.Info("Volume created",
		logger.String("name", vol.Name),
		logger.String("driver", vol.Driver),
	)

	return &VolumeInfo{
		Name:       vol.Name,
		Driver:     vol.Driver,
		Mountpoint: vol.Mountpoint,
		CreatedAt:  vol.CreatedAt,
		Labels:     vol.Labels,
		Scope:      vol.Scope,
		Options:    vol.Options,
	}, nil
}

// RemoveVolume removes a volume
func (s *VolumeService) RemoveVolume(ctx context.Context, volumeID string, force bool) error {
	if err := s.client.VolumeRemove(ctx, volumeID, force); err != nil {
		logger.Error("Failed to remove volume",
			logger.Err(err),
			logger.String("volume_id", volumeID),
			logger.Bool("force", force),
		)
		return fmt.Errorf("failed to remove volume: %w", err)
	}

	logger.Info("Volume removed",
		logger.String("volume_id", volumeID),
		logger.Bool("force", force),
	)
	return nil
}

// InspectVolume inspects a volume
func (s *VolumeService) InspectVolume(ctx context.Context, volumeID string) (*VolumeInfo, error) {
	vol, err := s.client.VolumeInspect(ctx, volumeID)
	if err != nil {
		logger.Error("Failed to inspect volume",
			logger.Err(err),
			logger.String("volume_id", volumeID),
		)
		return nil, fmt.Errorf("failed to inspect volume: %w", err)
	}

	logger.Info("Volume inspected", logger.String("volume_id", volumeID))
	return &VolumeInfo{
		Name:       vol.Name,
		Driver:     vol.Driver,
		Mountpoint: vol.Mountpoint,
		CreatedAt:  vol.CreatedAt,
		Labels:     vol.Labels,
		Scope:      vol.Scope,
		Options:    vol.Options,
	}, nil
}
