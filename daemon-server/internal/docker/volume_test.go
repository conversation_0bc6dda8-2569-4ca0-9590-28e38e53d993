package docker

import (
	"context"
	"testing"

	"github.com/docker/docker/api/types/volume"
)

// TestNewVolumeService tests the NewVolumeService function
func TestNewVolumeService(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()

	// Create a new volume service
	service := NewVolumeService(mockClient)

	// Check that the service is not nil
	if service == nil {
		t.Fatal("Service is nil")
	}

	// Check that the service has the client
	if service.client != mockClient {
		t.Error("Service does not have the client")
	}
}

// TestListVolumes tests the ListVolumes method
func TestListVolumes(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.VolumeListFunc = func(ctx context.Context) (volume.ListResponse, error) {
		return volume.ListResponse{
			Volumes: []*volume.Volume{
				{
					Name:       "volume1",
					Driver:     "local",
					Mountpoint: "/var/lib/docker/volumes/volume1/_data",
					CreatedAt:  "2023-01-01T00:00:00Z",
					Labels:     map[string]string{"test": "true"},
					Scope:      "local",
					Options:    map[string]string{"type": "tmpfs"},
				},
				{
					Name:       "volume2",
					Driver:     "local",
					Mountpoint: "/var/lib/docker/volumes/volume2/_data",
					CreatedAt:  "2023-01-02T00:00:00Z",
					Labels:     map[string]string{"test": "false"},
					Scope:      "local",
					Options:    map[string]string{"type": "tmpfs"},
				},
			},
		}, nil
	}

	// Create a volume service with the mock client
	service := NewVolumeService(mockClient)

	// List volumes
	volumes, err := service.ListVolumes(context.Background())
	if err != nil {
		t.Fatalf("Failed to list volumes: %v", err)
	}

	// Check the volumes
	if len(volumes) != 2 {
		t.Fatalf("Expected 2 volumes, got %d", len(volumes))
	}
	if volumes[0].Name != "volume1" {
		t.Errorf("Expected volume name 'volume1', got '%s'", volumes[0].Name)
	}
	if volumes[0].Driver != "local" {
		t.Errorf("Expected volume driver 'local', got '%s'", volumes[0].Driver)
	}
	if volumes[0].Mountpoint != "/var/lib/docker/volumes/volume1/_data" {
		t.Errorf("Expected volume mountpoint '/var/lib/docker/volumes/volume1/_data', got '%s'", volumes[0].Mountpoint)
	}
	if volumes[0].CreatedAt != "2023-01-01T00:00:00Z" {
		t.Errorf("Expected volume created at '2023-01-01T00:00:00Z', got '%s'", volumes[0].CreatedAt)
	}
	if len(volumes[0].Labels) != 1 || volumes[0].Labels["test"] != "true" {
		t.Errorf("Expected volume labels {'test': 'true'}, got %v", volumes[0].Labels)
	}
	if volumes[0].Scope != "local" {
		t.Errorf("Expected volume scope 'local', got '%s'", volumes[0].Scope)
	}
	if len(volumes[0].Options) != 1 || volumes[0].Options["type"] != "tmpfs" {
		t.Errorf("Expected volume options {'type': 'tmpfs'}, got %v", volumes[0].Options)
	}
}

// TestListVolumesError tests the ListVolumes method with an error
func TestListVolumesError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.VolumeListFunc = func(ctx context.Context) (volume.ListResponse, error) {
		return volume.ListResponse{}, ErrMock
	}

	// Create a volume service with the mock client
	service := NewVolumeService(mockClient)

	// List volumes
	volumes, err := service.ListVolumes(context.Background())
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if volumes != nil {
		t.Fatal("Expected volumes to be nil")
	}
}

// TestCreateVolume tests the CreateVolume method
func TestCreateVolume(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.VolumeCreateFunc = func(ctx context.Context, options volume.CreateOptions) (volume.Volume, error) {
		// Check the parameters
		if options.Name != "test-volume" {
			t.Errorf("Expected volume name 'test-volume', got '%s'", options.Name)
		}
		if options.Driver != "local" {
			t.Errorf("Expected volume driver 'local', got '%s'", options.Driver)
		}
		if len(options.Labels) != 1 || options.Labels["test"] != "true" {
			t.Errorf("Expected volume labels {'test': 'true'}, got %v", options.Labels)
		}
		if len(options.DriverOpts) != 1 || options.DriverOpts["type"] != "tmpfs" {
			t.Errorf("Expected volume driver options {'type': 'tmpfs'}, got %v", options.DriverOpts)
		}

		return volume.Volume{
			Name:       "test-volume",
			Driver:     "local",
			Mountpoint: "/var/lib/docker/volumes/test-volume/_data",
			CreatedAt:  "2023-01-01T00:00:00Z",
			Labels:     map[string]string{"test": "true"},
			Scope:      "local",
			Options:    map[string]string{"type": "tmpfs"},
		}, nil
	}

	// Create a volume service with the mock client
	service := NewVolumeService(mockClient)

	// Create a volume
	vol, err := service.CreateVolume(context.Background(), "test-volume", "local", map[string]string{"test": "true"}, map[string]string{"type": "tmpfs"})
	if err != nil {
		t.Fatalf("Failed to create volume: %v", err)
	}

	// Check the volume
	if vol.Name != "test-volume" {
		t.Errorf("Expected volume name 'test-volume', got '%s'", vol.Name)
	}
	if vol.Driver != "local" {
		t.Errorf("Expected volume driver 'local', got '%s'", vol.Driver)
	}
	if vol.Mountpoint != "/var/lib/docker/volumes/test-volume/_data" {
		t.Errorf("Expected volume mountpoint '/var/lib/docker/volumes/test-volume/_data', got '%s'", vol.Mountpoint)
	}
	if vol.CreatedAt != "2023-01-01T00:00:00Z" {
		t.Errorf("Expected volume created at '2023-01-01T00:00:00Z', got '%s'", vol.CreatedAt)
	}
	if len(vol.Labels) != 1 || vol.Labels["test"] != "true" {
		t.Errorf("Expected volume labels {'test': 'true'}, got %v", vol.Labels)
	}
	if vol.Scope != "local" {
		t.Errorf("Expected volume scope 'local', got '%s'", vol.Scope)
	}
	if len(vol.Options) != 1 || vol.Options["type"] != "tmpfs" {
		t.Errorf("Expected volume options {'type': 'tmpfs'}, got %v", vol.Options)
	}
}

// TestCreateVolumeError tests the CreateVolume method with an error
func TestCreateVolumeError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.VolumeCreateFunc = func(ctx context.Context, options volume.CreateOptions) (volume.Volume, error) {
		return volume.Volume{}, ErrMock
	}

	// Create a volume service with the mock client
	service := NewVolumeService(mockClient)

	// Create a volume
	vol, err := service.CreateVolume(context.Background(), "test-volume", "local", nil, nil)
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if vol != nil {
		t.Fatal("Expected volume to be nil")
	}
}

// TestRemoveVolume tests the RemoveVolume method
func TestRemoveVolume(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.VolumeRemoveFunc = func(ctx context.Context, volumeID string, force bool) error {
		// Check the parameters
		if volumeID != "test-volume" {
			t.Errorf("Expected volume ID 'test-volume', got '%s'", volumeID)
		}
		if !force {
			t.Error("Expected force to be true")
		}
		return nil
	}

	// Create a volume service with the mock client
	service := NewVolumeService(mockClient)

	// Remove a volume
	err := service.RemoveVolume(context.Background(), "test-volume", true)
	if err != nil {
		t.Fatalf("Failed to remove volume: %v", err)
	}
}

// TestRemoveVolumeError tests the RemoveVolume method with an error
func TestRemoveVolumeError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.VolumeRemoveFunc = func(ctx context.Context, volumeID string, force bool) error {
		return ErrMock
	}

	// Create a volume service with the mock client
	service := NewVolumeService(mockClient)

	// Remove a volume
	err := service.RemoveVolume(context.Background(), "test-volume", false)
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
}

// TestInspectVolume tests the InspectVolume method
func TestInspectVolume(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.VolumeInspectFunc = func(ctx context.Context, volumeID string) (volume.Volume, error) {
		// Check the parameters
		if volumeID != "test-volume" {
			t.Errorf("Expected volume ID 'test-volume', got '%s'", volumeID)
		}
		return volume.Volume{
			Name:       "test-volume",
			Driver:     "local",
			Mountpoint: "/var/lib/docker/volumes/test-volume/_data",
			CreatedAt:  "2023-01-01T00:00:00Z",
			Labels:     map[string]string{"test": "true"},
			Scope:      "local",
			Options:    map[string]string{"type": "tmpfs"},
		}, nil
	}

	// Create a volume service with the mock client
	service := NewVolumeService(mockClient)

	// Inspect a volume
	vol, err := service.InspectVolume(context.Background(), "test-volume")
	if err != nil {
		t.Fatalf("Failed to inspect volume: %v", err)
	}

	// Check the volume
	if vol.Name != "test-volume" {
		t.Errorf("Expected volume name 'test-volume', got '%s'", vol.Name)
	}
	if vol.Driver != "local" {
		t.Errorf("Expected volume driver 'local', got '%s'", vol.Driver)
	}
	if vol.Mountpoint != "/var/lib/docker/volumes/test-volume/_data" {
		t.Errorf("Expected volume mountpoint '/var/lib/docker/volumes/test-volume/_data', got '%s'", vol.Mountpoint)
	}
	if vol.CreatedAt != "2023-01-01T00:00:00Z" {
		t.Errorf("Expected volume created at '2023-01-01T00:00:00Z', got '%s'", vol.CreatedAt)
	}
	if len(vol.Labels) != 1 || vol.Labels["test"] != "true" {
		t.Errorf("Expected volume labels {'test': 'true'}, got %v", vol.Labels)
	}
	if vol.Scope != "local" {
		t.Errorf("Expected volume scope 'local', got '%s'", vol.Scope)
	}
	if len(vol.Options) != 1 || vol.Options["type"] != "tmpfs" {
		t.Errorf("Expected volume options {'type': 'tmpfs'}, got %v", vol.Options)
	}
}

// TestInspectVolumeError tests the InspectVolume method with an error
func TestInspectVolumeError(t *testing.T) {
	// Create a mock client
	mockClient := NewMockClient()
	mockClient.VolumeInspectFunc = func(ctx context.Context, volumeID string) (volume.Volume, error) {
		return volume.Volume{}, ErrMock
	}

	// Create a volume service with the mock client
	service := NewVolumeService(mockClient)

	// Inspect a volume
	vol, err := service.InspectVolume(context.Background(), "test-volume")
	if err == nil {
		t.Fatal("Expected an error, but got nil")
	}
	if vol != nil {
		t.Fatal("Expected volume to be nil")
	}
}
