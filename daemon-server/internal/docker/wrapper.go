package docker

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/events"
	"github.com/docker/docker/api/types/filters"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/docker/api/types/volume"
	"github.com/docker/docker/client"
)

// DockerClient is a wrapper around the Docker client
type DockerClient struct {
	cli *client.Client
}

// NewDockerClient creates a new Docker client
func NewDockerClient(host string, version string) (*DockerClient, error) {
	options := []client.Opt{
		client.WithHost(host),
	}

	if version != "" {
		options = append(options, client.WithVersion(version))
	} else {
		options = append(options, client.WithAPIVersionNegotiation())
	}

	cli, err := client.NewClientWithOpts(options...)
	if err != nil {
		return nil, err
	}

	return &DockerClient{cli: cli}, nil
}

// RefreshClient recreates the Docker client with the same settings
func (c *DockerClient) RefreshClient(host string, version string) error {
	// Close the existing client if it exists
	if c.cli != nil {
		c.cli.Close()
	}

	// Create options for the new client
	options := []client.Opt{
		client.WithHost(host),
	}

	if version != "" {
		options = append(options, client.WithVersion(version))
	} else {
		options = append(options, client.WithAPIVersionNegotiation())
	}

	// Create a new client
	cli, err := client.NewClientWithOpts(options...)
	if err != nil {
		return err
	}

	// Update the client
	c.cli = cli
	return nil
}

// IsDockerAvailable checks if the Docker daemon is available
func (c *DockerClient) IsDockerAvailable(ctx context.Context) bool {
	_, err := c.cli.Ping(ctx)
	return err == nil
}

// HandlePossibleDockerRestart attempts to refresh the client if Docker seems unavailable
func (c *DockerClient) HandlePossibleDockerRestart(ctx context.Context, host string, version string) error {
	if !c.IsDockerAvailable(ctx) {
		return c.RefreshClient(host, version)
	}
	return nil
}

// Close closes the Docker client
func (c *DockerClient) Close() error {
	return c.cli.Close()
}

// ContainerList lists containers
func (c *DockerClient) ContainerList(ctx context.Context, all bool) ([]types.Container, error) {
	return c.cli.ContainerList(ctx, container.ListOptions{All: all})
}

// ContainerListWithOptions lists containers with filter options
func (c *DockerClient) ContainerListWithOptions(ctx context.Context, options container.ListOptions) ([]types.Container, error) {
	return c.cli.ContainerList(ctx, options)
}

// ContainerCreate creates a container
func (c *DockerClient) ContainerCreate(ctx context.Context, config *container.Config, hostConfig *container.HostConfig, networkConfig *network.NetworkingConfig, name string) (container.CreateResponse, error) {
	return c.cli.ContainerCreate(ctx, config, hostConfig, networkConfig, nil, name)
}

// ContainerStart starts a container
func (c *DockerClient) ContainerStart(ctx context.Context, containerID string) error {
	return c.cli.ContainerStart(ctx, containerID, container.StartOptions{})
}

// ContainerStop stops a container
func (c *DockerClient) ContainerStop(ctx context.Context, containerID string, timeout *int) error {
	options := container.StopOptions{}
	if timeout != nil {
		options.Timeout = timeout
	}
	return c.cli.ContainerStop(ctx, containerID, options)
}

// ContainerRestart restarts a container
func (c *DockerClient) ContainerRestart(ctx context.Context, containerID string, timeout *int) error {
	// First stop the container
	options := container.StopOptions{}
	if timeout != nil {
		options.Timeout = timeout
	}
	err := c.cli.ContainerStop(ctx, containerID, options)
	if err != nil {
		return err
	}
	// Then start it again
	return c.cli.ContainerRestart(ctx, containerID, options)
}

// ContainerRemove removes a container
func (c *DockerClient) ContainerRemove(ctx context.Context, containerID string, force, removeVolumes bool) error {
	return c.cli.ContainerRemove(ctx, containerID, container.RemoveOptions{
		Force:         force,
		RemoveVolumes: removeVolumes,
	})
}

// ContainerInspect inspects a container
func (c *DockerClient) ContainerInspect(ctx context.Context, containerID string) (container.InspectResponse, error) {
	return c.cli.ContainerInspect(ctx, containerID)
}

// ContainerLogs gets container logs
func (c *DockerClient) ContainerLogs(ctx context.Context, containerID string, follow bool, tail string) (io.ReadCloser, error) {
	return c.cli.ContainerLogs(ctx, containerID, container.LogsOptions{
		ShowStdout: true,
		ShowStderr: true,
		Follow:     follow,
		Tail:       tail,
	})
}

// ContainerStats gets container stats
func (c *DockerClient) ContainerStats(ctx context.Context, containerID string, stream bool) (Stats, error) {
	stats, err := c.cli.ContainerStats(ctx, containerID, stream)
	if err != nil {
		return Stats{}, err
	}

	if !stream {
		defer stats.Body.Close()
	}

	// 解析容器统计信息
	var statsJSON struct {
		Read     time.Time
		PreRead  time.Time
		CPUStats struct {
			CPUUsage struct {
				TotalUsage        uint64
				PercpuUsage       []uint64
				UsageInKernelmode uint64
				UsageInUsermode   uint64
			}
			SystemUsage    uint64
			ThrottlingData struct {
				Periods          uint64
				ThrottledPeriods uint64
				ThrottledTime    uint64
			}
		}
		PreCPUStats struct {
			CPUUsage struct {
				TotalUsage        uint64
				PercpuUsage       []uint64
				UsageInKernelmode uint64
				UsageInUsermode   uint64
			}
			SystemUsage    uint64
			ThrottlingData struct {
				Periods          uint64
				ThrottledPeriods uint64
				ThrottledTime    uint64
			}
		}
		MemoryStats struct {
			Usage    uint64
			MaxUsage uint64
			Stats    map[string]uint64
			Failcnt  uint64
			Limit    uint64
		}
		BlkioStats struct {
			IoServiceBytesRecursive []struct {
				Major uint64
				Minor uint64
				Op    string
				Value uint64
			}
			IoServicedRecursive []struct {
				Major uint64
				Minor uint64
				Op    string
				Value uint64
			}
			IoQueuedRecursive []struct {
				Major uint64
				Minor uint64
				Op    string
				Value uint64
			}
			IoServiceTimeRecursive []struct {
				Major uint64
				Minor uint64
				Op    string
				Value uint64
			}
			IoWaitTimeRecursive []struct {
				Major uint64
				Minor uint64
				Op    string
				Value uint64
			}
			IoMergedRecursive []struct {
				Major uint64
				Minor uint64
				Op    string
				Value uint64
			}
			IoTimeRecursive []struct {
				Major uint64
				Minor uint64
				Op    string
				Value uint64
			}
			SectorsRecursive []struct {
				Major uint64
				Minor uint64
				Op    string
				Value uint64
			}
		}
		PidsStats struct {
			Current uint64
			Limit   uint64
		}
		Networks map[string]struct {
			RxBytes   uint64
			RxPackets uint64
			RxErrors  uint64
			RxDropped uint64
			TxBytes   uint64
			TxPackets uint64
			TxErrors  uint64
			TxDropped uint64
		}
	}
	if err := json.NewDecoder(stats.Body).Decode(&statsJSON); err != nil {
		return Stats{}, fmt.Errorf("failed to decode container stats: %w", err)
	}

	// 填充Stats结构体
	return Stats{
		Read:    statsJSON.Read.Format(time.RFC3339),
		PreRead: statsJSON.PreRead.Format(time.RFC3339),
		CPUStats: CPUStats{
			CPUUsage: CPUUsage{
				TotalUsage:        statsJSON.CPUStats.CPUUsage.TotalUsage,
				PercpuUsage:       statsJSON.CPUStats.CPUUsage.PercpuUsage,
				UsageInKernelmode: statsJSON.CPUStats.CPUUsage.UsageInKernelmode,
				UsageInUsermode:   statsJSON.CPUStats.CPUUsage.UsageInUsermode,
			},
			SystemUsage: statsJSON.CPUStats.SystemUsage,
			ThrottlingData: ThrottlingData{
				Periods:          statsJSON.CPUStats.ThrottlingData.Periods,
				ThrottledPeriods: statsJSON.CPUStats.ThrottlingData.ThrottledPeriods,
				ThrottledTime:    statsJSON.CPUStats.ThrottlingData.ThrottledTime,
			},
		},
		PreCPUStats: CPUStats{
			CPUUsage: CPUUsage{
				TotalUsage:        statsJSON.PreCPUStats.CPUUsage.TotalUsage,
				PercpuUsage:       statsJSON.PreCPUStats.CPUUsage.PercpuUsage,
				UsageInKernelmode: statsJSON.PreCPUStats.CPUUsage.UsageInKernelmode,
				UsageInUsermode:   statsJSON.PreCPUStats.CPUUsage.UsageInUsermode,
			},
			SystemUsage: statsJSON.PreCPUStats.SystemUsage,
			ThrottlingData: ThrottlingData{
				Periods:          statsJSON.PreCPUStats.ThrottlingData.Periods,
				ThrottledPeriods: statsJSON.PreCPUStats.ThrottlingData.ThrottledPeriods,
				ThrottledTime:    statsJSON.PreCPUStats.ThrottlingData.ThrottledTime,
			},
		},
		MemoryStats: MemoryStats{
			Usage:    statsJSON.MemoryStats.Usage,
			MaxUsage: statsJSON.MemoryStats.MaxUsage,
			Stats:    statsJSON.MemoryStats.Stats,
			Failcnt:  statsJSON.MemoryStats.Failcnt,
			Limit:    statsJSON.MemoryStats.Limit,
		},
		BlkioStats: BlkioStats{
			IoServiceBytesRecursive: convertBlkioEntries(statsJSON.BlkioStats.IoServiceBytesRecursive),
			IoServicedRecursive:     convertBlkioEntries(statsJSON.BlkioStats.IoServicedRecursive),
			IoQueuedRecursive:       convertBlkioEntries(statsJSON.BlkioStats.IoQueuedRecursive),
			IoServiceTimeRecursive:  convertBlkioEntries(statsJSON.BlkioStats.IoServiceTimeRecursive),
			IoWaitTimeRecursive:     convertBlkioEntries(statsJSON.BlkioStats.IoWaitTimeRecursive),
			IoMergedRecursive:       convertBlkioEntries(statsJSON.BlkioStats.IoMergedRecursive),
			IoTimeRecursive:         convertBlkioEntries(statsJSON.BlkioStats.IoTimeRecursive),
			SectorsRecursive:        convertBlkioEntries(statsJSON.BlkioStats.SectorsRecursive),
		},
		PidsStats: PidsStats{
			Current: statsJSON.PidsStats.Current,
			Limit:   statsJSON.PidsStats.Limit,
		},
		Networks: convertNetworkStats(statsJSON.Networks),
	}, nil
}

// convertBlkioEntries 转换BlkioStatEntry
func convertBlkioEntries(entries []struct {
	Major uint64
	Minor uint64
	Op    string
	Value uint64
}) []BlkioStatEntry {
	result := make([]BlkioStatEntry, len(entries))
	for i, entry := range entries {
		result[i] = BlkioStatEntry{
			Major: entry.Major,
			Minor: entry.Minor,
			Op:    entry.Op,
			Value: entry.Value,
		}
	}
	return result
}

// convertNetworkStats 转换NetworkStats
func convertNetworkStats(networks map[string]struct {
	RxBytes   uint64
	RxPackets uint64
	RxErrors  uint64
	RxDropped uint64
	TxBytes   uint64
	TxPackets uint64
	TxErrors  uint64
	TxDropped uint64
}) map[string]NetworkStats {
	result := make(map[string]NetworkStats)
	for name, stats := range networks {
		result[name] = NetworkStats{
			RxBytes:   stats.RxBytes,
			RxPackets: stats.RxPackets,
			RxErrors:  stats.RxErrors,
			RxDropped: stats.RxDropped,
			TxBytes:   stats.TxBytes,
			TxPackets: stats.TxPackets,
			TxErrors:  stats.TxErrors,
			TxDropped: stats.TxDropped,
		}
	}
	return result
}

// ImageList lists images
func (c *DockerClient) ImageList(ctx context.Context, option image.ListOptions) ([]image.Summary, error) {
	return c.cli.ImageList(ctx, option)
}

// ImagePull pulls an image
func (c *DockerClient) ImagePull(ctx context.Context, ref string, options ImagePullOptions) (io.ReadCloser, error) {
	// Create Docker's options
	opts := image.PullOptions{
		RegistryAuth: options.RegistryAuth,
	}
	return c.cli.ImagePull(ctx, ref, opts)
}

// ImagePush pushes an image
func (c *DockerClient) ImagePush(ctx context.Context, ref string, options ImagePushOptions) (io.ReadCloser, error) {
	// Create Docker's options
	opts := image.PushOptions{
		RegistryAuth: options.RegistryAuth,
	}
	return c.cli.ImagePush(ctx, ref, opts)
}

// ImageRemove removes an image
func (c *DockerClient) ImageRemove(ctx context.Context, imageID string, force, pruneChildren bool) ([]image.DeleteResponse, error) {
	return c.cli.ImageRemove(ctx, imageID, image.RemoveOptions{
		Force:         force,
		PruneChildren: pruneChildren,
	})
}

// ImageInspect inspects an image
func (c *DockerClient) ImageInspect(ctx context.Context, imageID string) (types.ImageInspect, error) {
	inspect, _, err := c.cli.ImageInspectWithRaw(ctx, imageID)
	return inspect, err
}

// ImageHistory gets the history of an image
func (c *DockerClient) ImageHistory(ctx context.Context, imageID string) ([]image.HistoryResponseItem, error) {
	return c.cli.ImageHistory(ctx, imageID)
}

// ImageSave saves images to a tar file
func (c *DockerClient) ImageSave(ctx context.Context, imageIDs []string) (io.ReadCloser, error) {
	return c.cli.ImageSave(ctx, imageIDs)
}

// ImagePrune prunes unused images
func (c *DockerClient) ImagePrune(ctx context.Context, all bool) (image.PruneReport, error) {
	// Create Docker's options
	filtersArgs := filters.NewArgs()
	if all {
		filtersArgs.Add("all", "true")
	}

	// Call Docker API
	report, err := c.cli.ImagesPrune(ctx, filtersArgs)
	if err != nil {
		return image.PruneReport{}, err
	}

	return report, nil
}

// ContainerCommit commits a container to an image
func (c *DockerClient) ContainerCommit(ctx context.Context, containerID string, options container.CommitOptions) (types.IDResponse, error) {
	return c.cli.ContainerCommit(ctx, containerID, options)
}

// ContainerExport exports a container to a tar file
func (c *DockerClient) ContainerExport(ctx context.Context, containerID string) (io.ReadCloser, error) {
	return c.cli.ContainerExport(ctx, containerID)
}

// VolumeList lists volumes
func (c *DockerClient) VolumeList(ctx context.Context) (volume.ListResponse, error) {
	return c.cli.VolumeList(ctx, volume.ListOptions{})
}

// VolumeCreate creates a volume
func (c *DockerClient) VolumeCreate(ctx context.Context, options volume.CreateOptions) (volume.Volume, error) {
	return c.cli.VolumeCreate(ctx, options)
}

// VolumeRemove removes a volume
func (c *DockerClient) VolumeRemove(ctx context.Context, volumeID string, force bool) error {
	return c.cli.VolumeRemove(ctx, volumeID, force)
}

// VolumeInspect inspects a volume
func (c *DockerClient) VolumeInspect(ctx context.Context, volumeID string) (volume.Volume, error) {
	return c.cli.VolumeInspect(ctx, volumeID)
}

// NetworkList lists networks
func (c *DockerClient) NetworkList(ctx context.Context) ([]NetworkResource, error) {
	networks, err := c.cli.NetworkList(ctx, network.ListOptions{})
	if err != nil {
		return nil, err
	}

	var result []NetworkResource
	for _, network := range networks {
		result = append(result, NetworkResource{
			ID:         network.ID,
			Name:       network.Name,
			Driver:     network.Driver,
			Scope:      network.Scope,
			Internal:   network.Internal,
			EnableIPv6: network.EnableIPv6,
			IPAM:       network.IPAM,
			Containers: network.Containers,
			Options:    network.Options,
			Labels:     network.Labels,
		})
	}

	return result, nil
}

// NetworkCreate creates a network
func (c *DockerClient) NetworkCreate(ctx context.Context, name string, options NetworkCreate) (NetworkCreateResponse, error) {
	enableIPv6 := options.EnableIPv6
	resp, err := c.cli.NetworkCreate(ctx, name, network.CreateOptions{
		Driver:     options.Driver,
		Internal:   options.Internal,
		Options:    options.Options,
		Labels:     options.Labels,
		EnableIPv6: &enableIPv6,
	})
	if err != nil {
		return NetworkCreateResponse{}, err
	}

	return NetworkCreateResponse{
		ID:      resp.ID,
		Warning: resp.Warning,
	}, nil
}

// NetworkRemove removes a network
func (c *DockerClient) NetworkRemove(ctx context.Context, networkID string) error {
	return c.cli.NetworkRemove(ctx, networkID)
}

// NetworkInspect inspects a network
func (c *DockerClient) NetworkInspect(ctx context.Context, networkID string, verbose bool) (NetworkResource, error) {
	network, err := c.cli.NetworkInspect(ctx, networkID, network.InspectOptions{Verbose: verbose})
	if err != nil {
		return NetworkResource{}, err
	}

	return NetworkResource{
		ID:         network.ID,
		Name:       network.Name,
		Driver:     network.Driver,
		Scope:      network.Scope,
		Internal:   network.Internal,
		EnableIPv6: network.EnableIPv6,
		IPAM:       network.IPAM,
		Containers: network.Containers,
		Options:    network.Options,
		Labels:     network.Labels,
	}, nil
}

// SystemInfo gets system information
func (c *DockerClient) SystemInfo(ctx context.Context) (Info, error) {
	info, err := c.cli.Info(ctx)
	if err != nil {
		return Info{}, err
	}

	return Info{
		ID:                info.ID,
		Containers:        info.Containers,
		ContainersRunning: info.ContainersRunning,
		ContainersPaused:  info.ContainersPaused,
		ContainersStopped: info.ContainersStopped,
		Images:            info.Images,
		Driver:            info.Driver,
		DriverStatus:      info.DriverStatus,
		SystemTime:        info.SystemTime,
		LoggingDriver:     info.LoggingDriver,
		CgroupDriver:      info.CgroupDriver,
		KernelVersion:     info.KernelVersion,
		OperatingSystem:   info.OperatingSystem,
		OSType:            info.OSType,
		Architecture:      info.Architecture,
		NCPU:              info.NCPU,
		MemTotal:          info.MemTotal,
		DockerRootDir:     info.DockerRootDir,
		ServerVersion:     info.ServerVersion,
	}, nil
}

// SystemVersion gets system version
func (c *DockerClient) SystemVersion(ctx context.Context) (Version, error) {
	version, err := c.cli.ServerVersion(ctx)
	if err != nil {
		return Version{}, err
	}

	return Version{
		Version:       version.Version,
		APIVersion:    version.APIVersion,
		MinAPIVersion: version.MinAPIVersion,
		GitCommit:     version.GitCommit,
		GoVersion:     version.GoVersion,
		Os:            version.Os,
		Arch:          version.Arch,
		BuildTime:     version.BuildTime,
	}, nil
}

// SystemDiskUsage gets system disk usage
func (c *DockerClient) SystemDiskUsage(ctx context.Context) (DiskUsage, error) {
	usage, err := c.cli.DiskUsage(ctx, types.DiskUsageOptions{})
	if err != nil {
		return DiskUsage{}, err
	}

	// Create empty slices for the collections
	images := []image.Summary{}
	containers := []types.Container{}
	volumes := []volume.Volume{}
	buildCache := []types.BuildCache{}

	return DiskUsage{
		LayersSize: usage.LayersSize,
		Images:     images,
		Containers: containers,
		Volumes:    volumes,
		BuildCache: buildCache,
	}, nil
}

// SystemEvents gets system events
func (c *DockerClient) SystemEvents(ctx context.Context, options EventsOptions) (<-chan events.Message, <-chan error) {
	// Call Docker API
	return c.cli.Events(ctx, events.ListOptions{
		Since:   options.Since,
		Until:   options.Until,
		Filters: options.Filters,
	})
}

// ContainerExecCreate creates an exec instance in a container
func (c *DockerClient) ContainerExecCreate(ctx context.Context, containerID string, options container.ExecOptions) (types.IDResponse, error) {
	return c.cli.ContainerExecCreate(ctx, containerID, options)
}

// ContainerExecStart attaches to an exec instance
func (c *DockerClient) ContainerExecStart(ctx context.Context, execID string, config container.ExecStartOptions) (types.HijackedResponse, error) {
	return c.cli.ContainerExecAttach(ctx, execID, config)
}

// ContainerExecInspect inspects an exec instance
func (c *DockerClient) ContainerExecInspect(ctx context.Context, execID string) (container.ExecInspect, error) {
	return c.cli.ContainerExecInspect(ctx, execID)
}
