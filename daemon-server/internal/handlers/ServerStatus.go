package handlers

import (
	"daemon-server/pkg/version"
	"time"
)

const (
	ServerStatusOnline  = "online"
	ServerStatusOffline = "offline"
	ServerStatusPaused  = "paused"
)

type ServerInfo struct {
	Status    string `json:"status"`
	StartedAt string `json:"time"`
	Version   string `json:"version"`
	Name      string `json:"name"`
}

var serverInfo *ServerInfo

func init() {
	serverInfo = &ServerInfo{
		Name:      "Chenyu Daemon Server",
		Version:   "0.0.1",
		Status:    ServerStatusOnline,
		StartedAt: time.Now().Format(time.RFC3339),
	}
}

func (s *ServerInfo) UpdateStatus(status string) {
	s.Status = status
}

func GetServerStatus() *ServerInfo {
	vInfo := version.GetVersionInfo()
	serverInfo.Version = vInfo.Version
	return serverInfo
}
