package handlers

import (
	"errors"
	"fmt"
	"github.com/docker/docker/api/types/filters"
	"net/url"
)

func ConvertQueryString2Filter(queryString string) (*filters.Args, error) {
	filterStr, err := url.QueryUnescape(queryString)
	if err != nil {
		msg := fmt.Sprintf("invalid query string to filter: %s, err: %v", queryString, err)
		return nil, errors.New(msg)
	}
	args, err := filters.FromJSON(filterStr)
	if err != nil {
		msg := fmt.Sprintf("invalid filter: %s, err: %v", filterStr, err)
		return nil, errors.New(msg)
	}
	return &args, nil
}
