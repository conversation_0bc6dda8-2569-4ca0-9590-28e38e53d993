package handlers

import (
	"github.com/gin-gonic/gin"

	"daemon-server/internal/docker"
	"daemon-server/internal/taskflow"
)

// RegisterDockerAPIRoutes registers Docker API routes
func RegisterDockerAPIRoutes(router *gin.Engine, dockerSvc *docker.Service, taskManager *taskflow.Manager) {
	// Skip if Docker service is not available
	if dockerSvc == nil {
		return
	}

	// Create API group
	apiV1 := router.Group("/api/v1")

	// Create handlers
	containerHandler := NewDockerContainerHandler(dockerSvc, taskManager)
	imageHandler := NewDockerImageHandler(dockerSvc, taskManager)
	systemHandler := NewDockerSystemHandler(dockerSvc)

	// Register container routes
	containers := apiV1.Group("/containers")
	{
		containers.GET("", containerHandler.ListContainers)
		containers.POST("", containerHandler.CreateContainer)
		containers.GET("/:id", containerHandler.InspectContainer)
		containers.DELETE("/:id", containerHandler.RemoveContainer)
		containers.POST("/:id/start", containerHandler.StartContainer)
		containers.POST("/:id/stop", containerHandler.StopContainer)
		containers.POST("/:id/restart", containerHandler.RestartContainer)
		containers.GET("/:id/logs", containerHandler.GetContainerLogs)
		// 新增容器API
		containers.POST("/:id/commit", containerHandler.CommitContainer)
		containers.GET("/:id/export", containerHandler.ExportContainer)
		containers.GET("/:id/ports", containerHandler.GetContainerPorts)
		containers.POST("/:id/exec", containerHandler.ExecContainer)
		containers.GET("/:id/stats", containerHandler.GetContainerStats)
	}

	// Register image routes
	images := apiV1.Group("/images")
	{
		images.GET("", imageHandler.ListImages)
		images.POST("/pull", imageHandler.PullImage)
		images.GET("/:id", imageHandler.InspectImage)
		images.DELETE("/:id", imageHandler.RemoveImage)
		// 新增镜像API
		images.POST("/push", imageHandler.PushImage)
		images.POST("/save", imageHandler.SaveImage)
		images.POST("/prune", imageHandler.PruneImages)
		images.POST("/load", imageHandler.LoadImage)
		images.GET("/:id/history", imageHandler.GetImageHistory)
	}

	// Register system routes
	system := apiV1.Group("/system")
	{
		system.GET("/disk-usage", systemHandler.GetDiskUsage)
	}
}
