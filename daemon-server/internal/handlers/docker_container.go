package handlers

import (
	"io"
	"net/http"
	"strconv"

	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/filters"
	"github.com/gin-gonic/gin"

	"daemon-server/internal/docker"
	"daemon-server/internal/logger"
	"daemon-server/internal/taskflow"
)

// DockerContainerHandler handles Docker container operations
type DockerContainerHandler struct {
	dockerSvc   *docker.Service
	taskManager *taskflow.Manager
}

// NewDockerContainerHandler creates a new Docker container handler
func NewDockerContainerHandler(dockerSvc *docker.Service, taskManager *taskflow.Manager) *DockerContainerHandler {
	return &DockerContainerHandler{
		dockerSvc:   dockerSvc,
		taskManager: taskManager,
	}
}

// ListContainers lists all containers
// GET /api/v1/containers
func (h *DockerContainerHandler) ListContainers(c *gin.Context) {
	// Get query parameters
	all := c.<PERSON>("all", "false") == "true"
	size := c.<PERSON>("size", "false") == "true"
	args := filters.NewArgs()
	// Check if filters are provided
	filtersParam := c.Query("filters")
	if filtersParam != "" {
		filterArgs, err := ConvertQueryString2Filter(filtersParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": err.Error(),
			})
			return
		}
		args = *filterArgs
	}

	// Create list options
	options := container.ListOptions{
		All:     all,
		Filters: args,
	}
	if size {
		options.Size = size
	}

	// List containers with options
	containers, err := h.dockerSvc.Container().ListContainersWithOptions(c.Request.Context(), options)
	if err != nil {
		logger.Error("Failed to list containers",
			logger.Err(err),
			logger.Bool("all", all),
			logger.String("filters", filtersParam),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to list containers: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"containers": containers,
	})
}

// CreateContainer creates a new container asynchronously
// POST /api/v1/containers
func (h *DockerContainerHandler) CreateContainer(c *gin.Context) {
	// Parse simplified request body
	var simpleReq docker.SimpleContainerCreateRequest
	if err := c.ShouldBindJSON(&simpleReq); err != nil {
		logger.Error("Failed to parse request body", logger.Err(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// Convert to full request
	req := simpleReq.ToContainerCreateRequest()

	// Create a task for container creation
	steps := []taskflow.Step{
		{
			Name:        "Create Container",
			Description: "Creating container from image " + simpleReq.Image,
			Input:       req,
			Func:        docker.EnhancedCreateContainerTask(h.dockerSvc),
		},
	}

	// Create task
	task, err := h.taskManager.CreateTask("", "Create Container", "Creating container from image "+simpleReq.Image, steps, req)
	if err != nil {
		logger.Error("Failed to create task for container creation",
			logger.Err(err),
			logger.String("image", simpleReq.Image),
			logger.String("name", simpleReq.Name),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for container creation: " + err.Error(),
		})
		return
	}

	// Execute task
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute container creation task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute container creation task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Container creation task started",
		"task_id": task.ID,
	})
}

// StartContainer starts a container asynchronously
// POST /api/v1/containers/:id/start
func (h *DockerContainerHandler) StartContainer(c *gin.Context) {
	// Get container ID from path
	containerID := c.Param("id")
	if containerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Container ID is required",
		})
		return
	}

	// Create a task for container starting
	steps := []taskflow.Step{
		{
			Name:        "Start Container",
			Description: "Starting container " + containerID,
			Input:       containerID,
			Func:        docker.EnhancedStartContainerTask(h.dockerSvc),
		},
	}

	// Create task
	task, err := h.taskManager.CreateTask("", "Start Container", "Starting container "+containerID, steps, containerID)
	if err != nil {
		logger.Error("Failed to create task for container starting",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for container starting: " + err.Error(),
		})
		return
	}

	// Execute task
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute container starting task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute container starting task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Container starting task started",
		"task_id": task.ID,
	})
}

// StopContainer stops a container asynchronously
// POST /api/v1/containers/:id/stop
func (h *DockerContainerHandler) StopContainer(c *gin.Context) {
	// Get container ID from path
	containerID := c.Param("id")
	if containerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Container ID is required",
		})
		return
	}

	// Get timeout from query parameters
	var timeout *int
	if timeoutStr := c.Query("timeout"); timeoutStr != "" {
		timeoutVal, err := strconv.Atoi(timeoutStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid timeout value: " + err.Error(),
			})
			return
		}
		timeout = &timeoutVal
	}

	// Prepare input for the task
	input := map[string]interface{}{
		"container_id": containerID,
	}
	if timeout != nil {
		input["timeout"] = float64(*timeout)
	}

	// Create a task for container stopping
	steps := []taskflow.Step{
		{
			Name:        "Stop Container",
			Description: "Stopping container " + containerID,
			Input:       input,
			Func:        docker.EnhancedStopContainerTask(h.dockerSvc),
		},
	}

	// Create task
	task, err := h.taskManager.CreateTask("", "Stop Container", "Stopping container "+containerID, steps, input)
	if err != nil {
		logger.Error("Failed to create task for container stopping",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for container stopping: " + err.Error(),
		})
		return
	}

	// Execute task
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute container stopping task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute container stopping task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Container stopping task started",
		"task_id": task.ID,
	})
}

// RestartContainer restarts a container asynchronously
// POST /api/v1/containers/:id/restart
func (h *DockerContainerHandler) RestartContainer(c *gin.Context) {
	// Get container ID from path
	containerID := c.Param("id")
	if containerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Container ID is required",
		})
		return
	}

	// Get timeout from query parameters
	var timeout *int
	if timeoutStr := c.Query("timeout"); timeoutStr != "" {
		timeoutVal, err := strconv.Atoi(timeoutStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid timeout value: " + err.Error(),
			})
			return
		}
		timeout = &timeoutVal
	}

	// Prepare input for the task
	input := map[string]interface{}{
		"container_id": containerID,
	}
	if timeout != nil {
		input["timeout"] = float64(*timeout)
	}

	// Create a task for container restarting
	steps := []taskflow.Step{
		{
			Name:        "Restart Container",
			Description: "Restarting container " + containerID,
			Input:       input,
			Func:        docker.EnhancedRestartContainerTask(h.dockerSvc),
		},
	}

	// Create task
	task, err := h.taskManager.CreateTask("", "Restart Container", "Restarting container "+containerID, steps, input)
	if err != nil {
		logger.Error("Failed to create task for container restarting",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for container restarting: " + err.Error(),
		})
		return
	}

	// Execute task
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute container restarting task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute container restarting task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Container restarting task started",
		"task_id": task.ID,
	})
}

// RemoveContainer removes a container asynchronously
// DELETE /api/v1/containers/:id
func (h *DockerContainerHandler) RemoveContainer(c *gin.Context) {
	// Get container ID from path
	containerID := c.Param("id")
	if containerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Container ID is required",
		})
		return
	}

	// Get force and remove volumes from query parameters
	force := c.DefaultQuery("force", "false") == "true"
	removeVolumes := c.DefaultQuery("volumes", "false") == "true"

	// Prepare input for the task
	input := map[string]interface{}{
		"container_id":   containerID,
		"force":          force,
		"remove_volumes": removeVolumes,
	}

	// Create a task for container removal
	steps := []taskflow.Step{
		{
			Name:        "Remove Container",
			Description: "Removing container " + containerID,
			Input:       input,
			Func:        docker.EnhancedRemoveContainerTask(h.dockerSvc),
		},
	}

	// Create task
	task, err := h.taskManager.CreateTask("", "Remove Container", "Removing container "+containerID, steps, input)
	if err != nil {
		logger.Error("Failed to create task for container removal",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for container removal: " + err.Error(),
		})
		return
	}

	// Execute task
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute container removal task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute container removal task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Container removal task started",
		"task_id": task.ID,
	})
}

// InspectContainer inspects a container
// GET /api/v1/containers/:id
func (h *DockerContainerHandler) InspectContainer(c *gin.Context) {
	// Get container ID from path
	containerID := c.Param("id")
	if containerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Container ID is required",
		})
		return
	}

	// Check if detailed view is requested
	detailed := c.DefaultQuery("detailed", "false") == "true"

	if detailed {
		// Get enhanced container details
		details, err := h.dockerSvc.Container().GetEnhancedContainerDetails(c.Request.Context(), containerID)
		if err != nil {
			logger.Error("Failed to get enhanced container details",
				logger.Err(err),
				logger.String("container_id", containerID),
			)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to get enhanced container details: " + err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, details)
	} else {
		// Inspect container (original behavior)
		containerJSON, err := h.dockerSvc.Container().InspectContainer(c.Request.Context(), containerID)
		if err != nil {
			logger.Error("Failed to inspect container",
				logger.Err(err),
				logger.String("container_id", containerID),
			)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to inspect container: " + err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, containerJSON)
	}
}

// GetContainerLogs gets container logs
// GET /api/v1/containers/:id/logs
func (h *DockerContainerHandler) GetContainerLogs(c *gin.Context) {
	// Get container ID from path
	containerID := c.Param("id")
	if containerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Container ID is required",
		})
		return
	}

	// Get follow and tail from query parameters
	follow := c.DefaultQuery("follow", "false") == "true"
	tail := c.DefaultQuery("tail", "all")

	// Get container logs
	logs, err := h.dockerSvc.Container().GetContainerLogs(c.Request.Context(), containerID, follow, tail)
	if err != nil {
		logger.Error("Failed to get container logs",
			logger.Err(err),
			logger.String("container_id", containerID),
			logger.Bool("follow", follow),
			logger.String("tail", tail),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get container logs: " + err.Error(),
		})
		return
	}
	defer logs.Close()

	// Set content type
	c.Header("Content-Type", "text/plain")

	// Stream logs to response
	c.Status(http.StatusOK)
	c.Stream(func(w io.Writer) bool {
		_, err := io.Copy(w, logs)
		return err == nil
	})
}

// CommitContainer 将容器提交为镜像（异步）
// POST /api/v1/containers/:id/commit
func (h *DockerContainerHandler) CommitContainer(c *gin.Context) {
	// 获取容器ID
	containerID := c.Param("id")
	if containerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Container ID is required",
		})
		return
	}

	// 解析请求体
	var req docker.SimpleContainerCommitRequest
	var options container.CommitOptions
	if err := c.ShouldBindJSON(&options); err != nil {
		logger.Error("Failed to parse request body", logger.Err(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// 设置容器ID
	req.ContainerID = containerID
	req.Options = options

	// 创建容器提交任务
	steps := []taskflow.Step{
		{
			Name:        "Commit Container",
			Description: "Committing container " + containerID + " to image",
			Input:       req,
			Func:        docker.EnhancedCommitContainerTask(h.dockerSvc),
		},
	}

	// 创建任务
	task, err := h.taskManager.CreateTask("", "Commit Container", "Committing container "+containerID+" to image", steps, req)
	if err != nil {
		logger.Error("Failed to create task for container commit",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for container commit: " + err.Error(),
		})
		return
	}

	// 执行任务
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute container commit task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute container commit task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Container commit task started",
		"task_id": task.ID,
	})
}

// ExportContainer 导出容器为tar文件（异步）
// GET /api/v1/containers/:id/export
func (h *DockerContainerHandler) ExportContainer(c *gin.Context) {
	// 获取容器ID
	containerID := c.Param("id")
	if containerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Container ID is required",
		})
		return
	}

	// 创建容器导出任务
	steps := []taskflow.Step{
		{
			Name:        "Export Container",
			Description: "Exporting container " + containerID + " to tar file",
			Input:       containerID,
			Func:        docker.EnhancedExportContainerTask(h.dockerSvc),
		},
	}

	// 创建任务
	task, err := h.taskManager.CreateTask("", "Export Container", "Exporting container "+containerID+" to tar file", steps, containerID)
	if err != nil {
		logger.Error("Failed to create task for container export",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for container export: " + err.Error(),
		})
		return
	}

	// 执行任务
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute container export task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute container export task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Container export task started",
		"task_id": task.ID,
		"note":    "Once the task is complete, you can download the exported file using the task result",
	})
}

// GetContainerPorts 获取容器端口映射
// GET /api/v1/containers/:id/ports
func (h *DockerContainerHandler) GetContainerPorts(c *gin.Context) {
	// 获取容器ID
	containerID := c.Param("id")
	if containerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Container ID is required",
		})
		return
	}

	// 获取容器端口映射
	ports, err := h.dockerSvc.Container().GetContainerPorts(c.Request.Context(), containerID)
	if err != nil {
		logger.Error("Failed to get container ports",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get container ports: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"ports": ports,
	})
}

// ExecContainer 在容器中执行命令（异步）
// POST /api/v1/containers/:id/exec
func (h *DockerContainerHandler) ExecContainer(c *gin.Context) {
	// 获取容器ID
	containerID := c.Param("id")
	if containerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Container ID is required",
		})
		return
	}

	// 解析请求体
	var req container.ExecOptions
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to parse request body", logger.Err(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// 验证请求
	if len(req.Cmd) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Command is required",
		})
		return
	}

	// 创建任务输入
	input := map[string]interface{}{
		"container_id": containerID,
		"exec_config":  req,
	}

	// 创建容器执行命令任务
	steps := []taskflow.Step{
		{
			Name:        "Execute Command in Container",
			Description: "Executing command in container " + containerID,
			Input:       input,
			Func:        docker.EnhancedExecContainerTask(h.dockerSvc),
		},
	}

	// 创建任务
	task, err := h.taskManager.CreateTask("", "Execute Command in Container", "Executing command in container "+containerID, steps, input)
	if err != nil {
		logger.Error("Failed to create task for container exec",
			logger.Err(err),
			logger.String("container_id", containerID),
			logger.Any("command", req.Cmd),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for container exec: " + err.Error(),
		})
		return
	}

	// 执行任务
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute container exec task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute container exec task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Container exec task started",
		"task_id": task.ID,
	})
}

// GetContainerStats 获取容器资源使用统计信息
// GET /api/v1/containers/:id/stats
func (h *DockerContainerHandler) GetContainerStats(c *gin.Context) {
	// 获取容器ID
	containerID := c.Param("id")
	if containerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Container ID is required",
		})
		return
	}

	// 获取stream参数，默认为false
	stream := c.DefaultQuery("stream", "false") == "true"

	// 获取容器统计信息
	stats, err := h.dockerSvc.Container().GetContainerStats(c.Request.Context(), containerID, stream)
	if err != nil {
		logger.Error("Failed to get container stats",
			logger.Err(err),
			logger.String("container_id", containerID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get container stats: " + err.Error(),
		})
		return
	}

	// 返回容器统计信息
	c.JSON(http.StatusOK, stats)
}
