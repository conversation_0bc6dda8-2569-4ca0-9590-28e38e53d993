package handlers

import (
	"daemon-server/pkg/utils"
	"github.com/docker/docker/api/types/filters"
	"github.com/docker/docker/api/types/image"
	"github.com/gin-gonic/gin"
	"io"
	"net/http"

	"daemon-server/internal/docker"
	"daemon-server/internal/logger"
	"daemon-server/internal/taskflow"
)

// DockerImageHandler handles Docker image operations
type DockerImageHandler struct {
	dockerSvc   *docker.Service
	taskManager *taskflow.Manager
}

// NewDockerImageHandler creates a new Docker image handler
func NewDockerImageHandler(dockerSvc *docker.Service, taskManager *taskflow.Manager) *DockerImageHandler {
	return &DockerImageHandler{
		dockerSvc:   dockerSvc,
		taskManager: taskManager,
	}
}

// ListImages lists all images
// GET /api/v1/images
func (h *DockerImageHandler) ListImages(c *gin.Context) {
	// Get query parameters
	all := c.<PERSON><PERSON><PERSON>("all", "false") == "true"
	// Create default empty filters
	filtersArgs := filters.NewArgs()

	// Check if filters are provided
	filterString := c.Query("filters")
	if filterString != "" {
		logger.Debug("source filters", logger.String("filters", filterString))
		args, err := ConvertQueryString2Filter(filterString)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": err.Error(),
			})
			return
		}
		filtersArgs = *args
	}
	option := image.ListOptions{
		All:     all,
		Filters: filtersArgs,
	}

	// List images
	images, err := h.dockerSvc.Image().ListImages(c.Request.Context(), option)
	if err != nil {
		logger.Error("Failed to list images",
			logger.Err(err),
			logger.Bool("all", all),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to list images: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"images": images,
	})
}

// PullImage pulls an image asynchronously
// POST /api/v1/images/pull
func (h *DockerImageHandler) PullImage(c *gin.Context) {
	// Get image reference and auth from request body
	var req docker.SimplePullImageRequest
	if err := c.ShouldBindJSON(&req); err != nil && err != io.EOF {
		logger.Error("Failed to parse request body", logger.Err(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate image reference
	if req.Image == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Image reference is required",
		})
		return
	}
	taskID := utils.GetUuidString()
	// Prepare input for the task
	input := map[string]interface{}{
		"image":   req.Image,
		"task_id": taskID,
	}
	if req.Auth != "" {
		input["auth"] = req.Auth
	}

	updateOutputFunc := func(taskID string, isStdout bool, progress int, message string) {
		if err := h.taskManager.UpdateTaskProgress(taskID, progress, message, isStdout); err != nil {
			logger.Error("Failed to update task progress",
				logger.String("task_id", taskID),
				logger.Int("progress", progress),
				logger.String("message", message),
				logger.Err(err),
			)
		}
	}

	// Create a task for image pulling
	steps := []taskflow.Step{
		{
			Name:        "Pull Image",
			Description: "Pulling image " + req.Image,
			Input:       input,
			Func:        docker.EnhancedPullImageTask(h.dockerSvc, updateOutputFunc),
		},
	}

	// Create task
	task, err := h.taskManager.CreateTask(taskID, "Pull Image", "Pulling image "+req.Image, steps, input)
	if err != nil {
		logger.Error("Failed to create task for image pulling",
			logger.Err(err),
			logger.String("image", req.Image),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for image pulling: " + err.Error(),
		})
		return
	}

	// Execute task
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute image pulling task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute image pulling task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Image pulling task started",
		"task_id": task.ID,
	})
}

// RemoveImage removes an image asynchronously
// DELETE /api/v1/images/:id
func (h *DockerImageHandler) RemoveImage(c *gin.Context) {
	// Get image ID from path
	imageID := c.Param("id")
	if imageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Image ID is required",
		})
		return
	}

	// Get force and prune children from query parameters
	force := c.DefaultQuery("force", "false") == "true"
	pruneChildren := c.DefaultQuery("prune", "false") == "true"

	// Prepare input for the task
	input := map[string]interface{}{
		"image_id":       imageID,
		"force":          force,
		"prune_children": pruneChildren,
	}

	// Create a task for image removal
	steps := []taskflow.Step{
		{
			Name:        "Remove Image",
			Description: "Removing image " + imageID,
			Input:       input,
			Func:        docker.EnhancedRemoveImageTask(h.dockerSvc),
		},
	}

	// Create task
	task, err := h.taskManager.CreateTask("", "Remove Image", "Removing image "+imageID, steps, input)
	if err != nil {
		logger.Error("Failed to create task for image removal",
			logger.Err(err),
			logger.String("image_id", imageID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for image removal: " + err.Error(),
		})
		return
	}

	// Execute task
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute image removal task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute image removal task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Image removal task started",
		"task_id": task.ID,
	})
}

// LoadImage loads an image from a local file asynchronously
// POST /api/v1/images/load
func (h *DockerImageHandler) LoadImage(c *gin.Context) {
	// Get file path from request body
	var req docker.SimpleLoadImageRequest
	if err := c.ShouldBindJSON(&req); err != nil && err != io.EOF {
		logger.Error("Failed to parse request body", logger.Err(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate file path
	if req.FilePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "File path is required",
		})
		return
	}

	// Prepare input for the task
	input := map[string]interface{}{
		"file_path": req.FilePath,
	}

	// Create a task for image loading
	steps := []taskflow.Step{
		{
			Name:        "Load Image",
			Description: "Loading image from file " + req.FilePath,
			Input:       input,
			Func:        docker.EnhancedLoadImageTask(h.dockerSvc),
		},
	}

	// Create task
	task, err := h.taskManager.CreateTask("", "Load Image", "Loading image from file "+req.FilePath, steps, input)
	if err != nil {
		logger.Error("Failed to create task for image loading",
			logger.Err(err),
			logger.String("file_path", req.FilePath),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for image loading: " + err.Error(),
		})
		return
	}

	// Execute task
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute image loading task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute image loading task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Image loading task started",
		"task_id": task.ID,
	})
}

// InspectImage inspects an image
// GET /api/v1/images/:id
func (h *DockerImageHandler) InspectImage(c *gin.Context) {
	// Get image ID from path
	imageID := c.Param("id")
	if imageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Image ID is required",
		})
		return
	}
	// Inspect image (original behavior)
	imageInspect, err := h.dockerSvc.Image().InspectImage(c.Request.Context(), imageID)
	if err != nil {
		logger.Error("Failed to inspect image",
			logger.Err(err),
			logger.String("image_id", imageID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to inspect image: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, imageInspect)
}

// GetImageHistory gets the history of an image
// GET /api/v1/images/:id/history
func (h *DockerImageHandler) GetImageHistory(c *gin.Context) {
	// Get image ID from path
	imageID := c.Param("id")
	if imageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Image ID is required",
		})
		return
	}

	// Get image history
	history, err := h.dockerSvc.Image().GetImageHistory(c.Request.Context(), imageID)
	if err != nil {
		logger.Error("Failed to get image history",
			logger.Err(err),
			logger.String("image_id", imageID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get image history: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"history": history,
	})
}

// PushImage 推送镜像（异步）
// POST /api/v1/images/push
func (h *DockerImageHandler) PushImage(c *gin.Context) {
	// 解析请求体
	var req docker.SimplePushImageRequest
	if err := c.ShouldBindJSON(&req); err != nil && err != io.EOF {
		logger.Error("Failed to parse request body", logger.Err(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// 验证镜像名称
	if req.Image == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Image name is required",
		})
		return
	}

	if req.TaskID == "" {
		req.TaskID = utils.GetUuidString()
	}
	updateOutputFunc := func(taskID string, isStdout bool, progress int, message string) {
		if err := h.taskManager.UpdateTaskProgress(taskID, progress, message, isStdout); err != nil {
			logger.Error("Failed to update task progress",
				logger.String("task_id", taskID),
				logger.Int("progress", progress),
				logger.String("message", message),
				logger.Err(err),
			)
		}
	}

	// 创建镜像推送任务
	steps := []taskflow.Step{
		{
			Name:        "Push Image",
			Description: "Pushing image " + req.Image,
			Input:       req,
			Func:        docker.EnhancedPushImageTask(h.dockerSvc, updateOutputFunc),
		},
	}

	// 创建任务
	task, err := h.taskManager.CreateTask(req.TaskID, "Push Image", "Pushing image "+req.Image, steps, req)
	if err != nil {
		logger.Error("Failed to create task for image pushing",
			logger.Err(err),
			logger.String("image", req.Image),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for image pushing: " + err.Error(),
		})
		return
	}

	// 执行任务
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute image pushing task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute image pushing task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Image pushing task started",
		"task_id": task.ID,
	})
}

// SaveImage 保存镜像为tar文件（异步）
// GET /api/v1/images/save
func (h *DockerImageHandler) SaveImage(c *gin.Context) {
	taskID := utils.GetUuidString()
	// 获取镜像ID列表
	var param docker.ImageSaveParam
	if err := c.ShouldBindJSON(&param); err != nil && err != io.EOF {
		logger.Error("Failed to parse request body", logger.Err(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	if param.ImageId == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "image id is required",
		})
		return
	}
	if param.SavePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "image save path is required",
		})
		return
	}

	// 创建镜像保存任务
	steps := []taskflow.Step{
		{
			Name:        "Save Image",
			Description: "Saving images to tar file",
			Input:       param,
			Func:        docker.EnhancedSaveImageTask(h.dockerSvc),
		},
	}

	// 创建任务
	task, err := h.taskManager.CreateTask(taskID, "Save Image", "Saving images to tar file", steps, param)
	if err != nil {
		logger.Error("Failed to create task for image saving",
			logger.Err(err),
			logger.Any("req", param),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for image saving: " + err.Error(),
		})
		return
	}

	// 执行任务
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute image saving task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute image saving task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Image saving task started",
		"task_id": task.ID,
		"note":    "Once the task is complete, you can download the saved file using the task result",
	})
}

// PruneImages 清理未使用的镜像（异步）
// POST /api/v1/images/prune
func (h *DockerImageHandler) PruneImages(c *gin.Context) {
	// 获取查询参数
	all := c.DefaultQuery("all", "false") == "true"

	// 准备任务输入
	input := map[string]interface{}{
		"all": all,
	}

	// 创建镜像清理任务
	steps := []taskflow.Step{
		{
			Name:        "Prune Images",
			Description: "Pruning unused images",
			Input:       input,
			Func:        docker.EnhancedPruneImagesTask(h.dockerSvc),
		},
	}

	// 创建任务
	task, err := h.taskManager.CreateTask("", "Prune Images", "Pruning unused images", steps, input)
	if err != nil {
		logger.Error("Failed to create task for image pruning",
			logger.Err(err),
			logger.Bool("all", all),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for image pruning: " + err.Error(),
		})
		return
	}

	// 执行任务
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute image pruning task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute image pruning task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Image pruning task started",
		"task_id": task.ID,
	})
}
