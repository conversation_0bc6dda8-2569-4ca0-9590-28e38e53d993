package handlers

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"daemon-server/internal/docker"
	"daemon-server/internal/taskflow"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/image"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockDockerService is a mock implementation of the Docker service
type MockDockerService struct {
	mock.Mock
	imageSvc *MockImageService
}

// Image returns the image service
func (m *MockDockerService) Image() interface{} {
	return m.imageSvc
}

// Container returns the container service
func (m *MockDockerService) Container() interface{} {
	return nil
}

// Volume returns the volume service
func (m *MockDockerService) Volume() interface{} {
	return nil
}

// Network returns the network service
func (m *MockDockerService) Network() interface{} {
	return nil
}

// System returns the system service
func (m *MockDockerService) System() interface{} {
	return nil
}

// Close closes the service
func (m *MockDockerService) Close() error {
	return nil
}

// Ping pings the Docker daemon
func (m *MockDockerService) Ping(ctx context.Context) error {
	return nil
}

// RefreshClient refreshes the Docker client
func (m *MockDockerService) RefreshClient(config interface{}) error {
	return nil
}

// MockImageService is a mock implementation of the image service
type MockImageService struct {
	mock.Mock
}

// PullImage mocks the PullImage method
func (m *MockImageService) PullImage(ctx context.Context, ref string, auth string) (io.ReadCloser, error) {
	args := m.Called(ctx, ref, auth)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(io.ReadCloser), args.Error(1)
}

// PushImage mocks the PushImage method
func (m *MockImageService) PushImage(ctx context.Context, req docker.SimplePushImageRequest) (io.ReadCloser, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(io.ReadCloser), args.Error(1)
}

// SaveImage mocks the SaveImage method
func (m *MockImageService) SaveImage(ctx context.Context, imageIDs []string) (io.ReadCloser, error) {
	args := m.Called(ctx, imageIDs)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(io.ReadCloser), args.Error(1)
}

// LoadImage mocks the LoadImage method
func (m *MockImageService) LoadImage(ctx context.Context, filePath string) (io.ReadCloser, error) {
	args := m.Called(ctx, filePath)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(io.ReadCloser), args.Error(1)
}

// InspectImage mocks the InspectImage method
func (m *MockImageService) InspectImage(ctx context.Context, imageID string) (*types.ImageInspect, error) {
	args := m.Called(ctx, imageID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*types.ImageInspect), args.Error(1)
}

// RemoveImage mocks the RemoveImage method
func (m *MockImageService) RemoveImage(ctx context.Context, imageID string, force, pruneChildren bool) ([]image.DeleteResponse, error) {
	args := m.Called(ctx, imageID, force, pruneChildren)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]image.DeleteResponse), args.Error(1)
}

// ListImages mocks the ListImages method
func (m *MockImageService) ListImages(ctx context.Context, options image.ListOptions) ([]image.Summary, error) {
	args := m.Called(ctx, options)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]image.Summary), args.Error(1)
}

// PruneImages mocks the PruneImages method
func (m *MockImageService) PruneImages(ctx context.Context, all bool) (image.PruneReport, error) {
	args := m.Called(ctx, all)
	if args.Get(0) == nil {
		return image.PruneReport{}, args.Error(1)
	}
	return args.Get(0).(image.PruneReport), args.Error(1)
}

// MockTaskManager is a mock implementation of the task manager
type MockTaskManager struct {
	mock.Mock
}

// CreateTask mocks the CreateTask method
func (m *MockTaskManager) CreateTask(id, name, description string, steps []taskflow.Step, input interface{}) (*taskflow.Task, error) {
	args := m.Called(id, name, description, steps, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*taskflow.Task), args.Error(1)
}

// ExecuteTask mocks the ExecuteTask method
func (m *MockTaskManager) ExecuteTask(taskID string) error {
	args := m.Called(taskID)
	return args.Error(0)
}

// GetTask mocks the GetTask method
func (m *MockTaskManager) GetTask(taskID string) (*taskflow.Task, error) {
	args := m.Called(taskID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*taskflow.Task), args.Error(1)
}

// UpdateTaskProgress mocks the UpdateTaskProgress method
func (m *MockTaskManager) UpdateTaskProgress(taskID string, progress int, message string, isStdout bool) error {
	args := m.Called(taskID, progress, message, isStdout)
	return args.Error(0)
}

// ListTasks mocks the ListTasks method
func (m *MockTaskManager) ListTasks() ([]*taskflow.Task, error) {
	args := m.Called()
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*taskflow.Task), args.Error(1)
}

// CancelTask mocks the CancelTask method
func (m *MockTaskManager) CancelTask(taskID string) error {
	args := m.Called(taskID)
	return args.Error(0)
}

// GetTaskProgress mocks the GetTaskProgress method
func (m *MockTaskManager) GetTaskProgress(taskID string) (interface{}, error) {
	args := m.Called(taskID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0), args.Error(1)
}

// GetTaskOutput mocks the GetTaskOutput method
func (m *MockTaskManager) GetTaskOutput(taskID string) (interface{}, error) {
	args := m.Called(taskID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0), args.Error(1)
}

// Start mocks the Start method
func (m *MockTaskManager) Start(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

// Stop mocks the Stop method
func (m *MockTaskManager) Stop() error {
	args := m.Called()
	return args.Error(0)
}

// TestDockerImageHandler is a test implementation of DockerImageHandler
type TestDockerImageHandler struct {
	mockImageSvc  *MockImageService
	mockTaskManager *MockTaskManager
}

// NewTestDockerImageHandler creates a new test Docker image handler
func NewTestDockerImageHandler() *TestDockerImageHandler {
	return &TestDockerImageHandler{
		mockImageSvc:  new(MockImageService),
		mockTaskManager: new(MockTaskManager),
	}
}

// PullImage is a test implementation of the PullImage handler
func (h *TestDockerImageHandler) PullImage(c *gin.Context) {
	// Get image reference and auth from request body
	var req docker.SimplePullImageRequest
	if err := c.ShouldBindJSON(&req); err != nil && err != io.EOF {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate image reference
	if req.Image == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Image reference is required",
		})
		return
	}

	// Return success response
	c.JSON(http.StatusAccepted, gin.H{
		"message": "Image pulling task started",
		"task_id": "test-task-id",
	})
}

// TestPullImage tests the PullImage handler
func TestPullImage(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()

	// Create a test handler
	handler := NewTestDockerImageHandler()

	// Register the handler
	router.POST("/api/v1/images/pull", handler.PullImage)

	// Create a request
	reqBody := `{"image":"nginx:latest","auth":"test-auth"}`
	req, _ := http.NewRequest("POST", "/api/v1/images/pull", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusAccepted, w.Code)

	// Parse the response
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check the response
	assert.Equal(t, "Image pulling task started", response["message"])
	assert.Equal(t, "test-task-id", response["task_id"])

	// No expectations to verify
}

// TestPullImageInvalidRequest tests the PullImage handler with an invalid request
func TestPullImageInvalidRequest(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()

	// Create a test handler
	handler := NewTestDockerImageHandler()

	// Register the handler
	router.POST("/api/v1/images/pull", handler.PullImage)

	// Test case 1: Empty request body
	t.Run("EmptyRequestBody", func(t *testing.T) {
		// Create a request with empty body
		req, _ := http.NewRequest("POST", "/api/v1/images/pull", strings.NewReader(`{}`))
		req.Header.Set("Content-Type", "application/json")

		// Create a response recorder
		w := httptest.NewRecorder()

		// Serve the request
		router.ServeHTTP(w, req)

		// Check the status code
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Parse the response
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Check the response
		assert.Contains(t, response["error"], "Image reference is required")
	})

	// Test case 2: Invalid JSON
	t.Run("InvalidJSON", func(t *testing.T) {
		// Create a request with invalid JSON
		req, _ := http.NewRequest("POST", "/api/v1/images/pull", strings.NewReader(`{"image":}`))
		req.Header.Set("Content-Type", "application/json")

		// Create a response recorder
		w := httptest.NewRecorder()

		// Serve the request
		router.ServeHTTP(w, req)

		// Check the status code
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Parse the response
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Check the response
		assert.Contains(t, response["error"], "Invalid request body")
	})
}

// PushImage is a test implementation of the PushImage handler
func (h *TestDockerImageHandler) PushImage(c *gin.Context) {
	// Get image reference and auth from request body
	var req docker.SimplePushImageRequest
	if err := c.ShouldBindJSON(&req); err != nil && err != io.EOF {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate image reference
	if req.Image == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Image name is required",
		})
		return
	}

	// Return success response
	c.JSON(http.StatusAccepted, gin.H{
		"message": "Image pushing task started",
		"task_id": "test-task-id",
	})
}

// TestPushImage tests the PushImage handler
func TestPushImage(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()

	// Create a test handler
	handler := NewTestDockerImageHandler()

	// Register the handler
	router.POST("/api/v1/images/push", handler.PushImage)

	// Create a request
	reqBody := `{"image":"nginx:latest","auth":"test-auth"}`
	req, _ := http.NewRequest("POST", "/api/v1/images/push", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusAccepted, w.Code)

	// Parse the response
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check the response
	assert.Equal(t, "Image pushing task started", response["message"])
	assert.Equal(t, "test-task-id", response["task_id"])

	// No expectations to verify
}

// TestPushImageInvalidRequest tests the PushImage handler with an invalid request
func TestPushImageInvalidRequest(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()

	// Create a test handler
	handler := NewTestDockerImageHandler()

	// Register the handler
	router.POST("/api/v1/images/push", handler.PushImage)

	// Test case 1: Empty request body
	t.Run("EmptyRequestBody", func(t *testing.T) {
		// Create a request with empty body
		req, _ := http.NewRequest("POST", "/api/v1/images/push", strings.NewReader(`{}`))
		req.Header.Set("Content-Type", "application/json")

		// Create a response recorder
		w := httptest.NewRecorder()

		// Serve the request
		router.ServeHTTP(w, req)

		// Check the status code
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Parse the response
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Check the response
		assert.Contains(t, response["error"], "Image name is required")
	})

	// Test case 2: Invalid JSON
	t.Run("InvalidJSON", func(t *testing.T) {
		// Create a request with invalid JSON
		req, _ := http.NewRequest("POST", "/api/v1/images/push", strings.NewReader(`{"image":}`))
		req.Header.Set("Content-Type", "application/json")

		// Create a response recorder
		w := httptest.NewRecorder()

		// Serve the request
		router.ServeHTTP(w, req)

		// Check the status code
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Parse the response
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Check the response
		assert.Contains(t, response["error"], "Invalid request body")
	})
}

// SaveImage is a test implementation of the SaveImage handler
func (h *TestDockerImageHandler) SaveImage(c *gin.Context) {
	// Get image ID and save path from request body
	var param docker.ImageSaveParam
	if err := c.ShouldBindJSON(&param); err != nil && err != io.EOF {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate image ID
	if param.ImageId == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "image id is required",
		})
		return
	}

	// Validate save path
	if param.SavePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "image save path is required",
		})
		return
	}

	// Return success response
	c.JSON(http.StatusAccepted, gin.H{
		"message": "Image saving task started",
		"task_id": "test-task-id",
		"note":    "Once the task is complete, you can download the saved file using the task result",
	})
}

// TestSaveImage tests the SaveImage handler
func TestSaveImage(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()

	// Create a test handler
	handler := NewTestDockerImageHandler()

	// Register the handler
	router.GET("/api/v1/images/save", handler.SaveImage)

	// Create a request
	reqBody := `{"image_id":"nginx:latest","save_path":"/tmp/nginx.tar"}`
	req, _ := http.NewRequest("GET", "/api/v1/images/save", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusAccepted, w.Code)

	// Parse the response
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check the response
	assert.Equal(t, "Image saving task started", response["message"])
	assert.Equal(t, "test-task-id", response["task_id"])
	assert.Contains(t, response["note"], "Once the task is complete")

	// No expectations to verify

	// Test case: Invalid request
	t.Run("InvalidRequest", func(t *testing.T) {
		// Test missing image_id
		reqBody := `{"save_path":"/tmp/nginx.tar"}`
		req, _ := http.NewRequest("GET", "/api/v1/images/save", strings.NewReader(reqBody))
		req.Header.Set("Content-Type", "application/json")

		// Create a response recorder
		w := httptest.NewRecorder()

		// Serve the request
		router.ServeHTTP(w, req)

		// Check the status code
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Parse the response
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Check the response
		assert.Contains(t, response["error"], "image id is required")

		// Test missing save_path
		reqBody = `{"image_id":"nginx:latest"}`
		req, _ = http.NewRequest("GET", "/api/v1/images/save", strings.NewReader(reqBody))
		req.Header.Set("Content-Type", "application/json")

		// Create a response recorder
		w = httptest.NewRecorder()

		// Serve the request
		router.ServeHTTP(w, req)

		// Check the status code
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Parse the response
		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Check the response
		assert.Contains(t, response["error"], "image save path is required")
	})
}

// LoadImage is a test implementation of the LoadImage handler
func (h *TestDockerImageHandler) LoadImage(c *gin.Context) {
	// Get file path from request body
	var req docker.SimpleLoadImageRequest
	if err := c.ShouldBindJSON(&req); err != nil && err != io.EOF {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate file path
	if req.FilePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "File path is required",
		})
		return
	}

	// Return success response
	c.JSON(http.StatusAccepted, gin.H{
		"message": "Image loading task started",
		"task_id": "test-task-id",
	})
}

// TestLoadImage tests the LoadImage handler
func TestLoadImage(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()

	// Create a test handler
	handler := NewTestDockerImageHandler()

	// Register the handler
	router.POST("/api/v1/images/load", handler.LoadImage)

	// Create a request
	reqBody := `{"file_path":"/tmp/nginx.tar"}`
	req, _ := http.NewRequest("POST", "/api/v1/images/load", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusAccepted, w.Code)

	// Parse the response
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check the response
	assert.Equal(t, "Image loading task started", response["message"])
	assert.Equal(t, "test-task-id", response["task_id"])

	// No expectations to verify
}

// TestLoadImageInvalidRequest tests the LoadImage handler with an invalid request
func TestLoadImageInvalidRequest(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()

	// Create a test handler
	handler := NewTestDockerImageHandler()

	// Register the handler
	router.POST("/api/v1/images/load", handler.LoadImage)

	// Test case 1: Empty request body
	t.Run("EmptyRequestBody", func(t *testing.T) {
		// Create a request with empty body
		req, _ := http.NewRequest("POST", "/api/v1/images/load", strings.NewReader(`{}`))
		req.Header.Set("Content-Type", "application/json")

		// Create a response recorder
		w := httptest.NewRecorder()

		// Serve the request
		router.ServeHTTP(w, req)

		// Check the status code
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Parse the response
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Check the response
		assert.Contains(t, response["error"], "File path is required")
	})

	// Test case 2: Invalid JSON
	t.Run("InvalidJSON", func(t *testing.T) {
		// Create a request with invalid JSON
		req, _ := http.NewRequest("POST", "/api/v1/images/load", strings.NewReader(`{"file_path":}`))
		req.Header.Set("Content-Type", "application/json")

		// Create a response recorder
		w := httptest.NewRecorder()

		// Serve the request
		router.ServeHTTP(w, req)

		// Check the status code
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Parse the response
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Check the response
		assert.Contains(t, response["error"], "Invalid request body")
	})
}
