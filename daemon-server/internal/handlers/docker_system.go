package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"daemon-server/internal/docker"
	"daemon-server/internal/logger"
)

// DockerSystemHandler 处理Docker系统操作
type DockerSystemHandler struct {
	dockerSvc *docker.Service
}

// NewDockerSystemHandler 创建一个新的Docker系统处理程序
func NewDockerSystemHandler(dockerSvc *docker.Service) *DockerSystemHandler {
	return &DockerSystemHandler{
		dockerSvc: dockerSvc,
	}
}

// GetDiskUsage 获取Docker存储空间信息
// GET /api/v1/system/disk-usage
func (h *DockerSystemHandler) GetDiskUsage(c *gin.Context) {
	// 获取磁盘使用情况
	usage, err := h.dockerSvc.System().GetDiskUsage(c.Request.Context())
	if err != nil {
		logger.Error("Failed to get disk usage", logger.Err(err))
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"error": "Failed to get disk usage: " + err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, usage)
}
