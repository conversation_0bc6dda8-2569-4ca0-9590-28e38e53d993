package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"daemon-server/internal/logger"
)

func Ping(c *gin.Context) {
	currentTime := time.Now().Format(time.RFC3339)

	logger.Debug("Health check requested",
		logger.String("client_ip", c.ClientIP()),
		logger.String("time", currentTime),
	)

	c.<PERSON>(http.StatusOK, gin.H{
		"status": "running",
		"time":   currentTime,
	})
}

func GetServerInfo(c *gin.Context) {
	c.<PERSON>SO<PERSON>(http.StatusOK, GetServerStatus())
}
