package handlers

import (
	"daemon-server/pkg/utils"
	"net/http"

	"github.com/gin-gonic/gin"

	"daemon-server/internal/logger"
	"daemon-server/internal/system"
	"daemon-server/internal/taskflow"
)

// SystemHandler handles system-related HTTP requests
type SystemHandler struct {
	systemSvc   *system.CommandService
	taskManager *taskflow.Manager
}

// NewSystemHandler creates a new system handler
func NewSystemHandler(systemSvc *system.CommandService, taskManager *taskflow.Manager) *SystemHandler {
	return &SystemHandler{
		systemSvc:   systemSvc,
		taskManager: taskManager,
	}
}

// ExecuteCommandRequest represents a request to execute a command
type ExecuteCommandRequest struct {
	Command    string   `json:"command" binding:"required"` // Command to execute
	Args       []string `json:"args,omitempty"`             // Command arguments
	WorkingDir string   `json:"working_dir,omitempty"`      // Working directory
	Env        []string `json:"env,omitempty"`              // Environment variables
	Timeout    int      `json:"timeout,omitempty"`          // Timeout in seconds
	TaskID     string   `json:"task_id,omitempty"`          // Task ID for real-time output
}

// ExecuteCommand executes a command asynchronously
// POST /api/v1/system/exec
func (h *SystemHandler) ExecuteCommand(c *gin.Context) {
	// Parse request body
	var req ExecuteCommandRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to parse request body", logger.Err(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate request
	if req.Command == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Command is required",
		})
		return
	}

	// Create task input
	input := map[string]interface{}{
		"command":     req.Command,
		"args":        req.Args,
		"working_dir": req.WorkingDir,
		"env":         req.Env,
		"timeout":     req.Timeout,
	}

	// Create task steps
	steps := []taskflow.Step{
		{
			Name:        "Execute Command",
			Description: "Executing command: " + req.Command,
			Input:       input,
			Func:        system.ExecuteCommandTask(h.systemSvc),
		},
	}

	// Create task
	task, err := h.taskManager.CreateTask("", "Execute Command", "Executing command: "+req.Command, steps, input)
	if err != nil {
		logger.Error("Failed to create task for command execution",
			logger.Err(err),
			logger.String("command", req.Command),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for command execution: " + err.Error(),
		})
		return
	}

	// Execute task
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute command task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute command task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Command execution task started",
		"task_id": task.ID,
	})
}

// ExecuteCommandWithRealTimeOutput executes a command asynchronously with real-time output
// POST /api/v1/system/exec/realtime
func (h *SystemHandler) ExecuteCommandWithRealTimeOutput(c *gin.Context) {
	// Parse request body (same as in ExecuteCommand)
	var req ExecuteCommandRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to parse request body", logger.Err(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate request
	if req.Command == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Command is required",
		})
		return
	}
	var taskID string
	if req.TaskID == "" {
		taskID = utils.GetUuidString()
	}
	taskID = req.TaskID

	// Create task input
	input := map[string]interface{}{
		"command":     req.Command,
		"args":        req.Args,
		"working_dir": req.WorkingDir,
		"env":         req.Env,
		"timeout":     req.Timeout,
		"task_id":     taskID,
	}

	// Create progress callback function
	progressCallback := func(taskID string, isStdout bool, progress int, message string) {
		// Update task progress
		// This will be available via the task progress API endpoint
		if err := h.taskManager.UpdateTaskProgress(taskID, progress, message, false); err != nil {
			logger.Error("Failed to update task progress",
				logger.String("task_id", taskID),
				logger.Int("progress", progress),
				logger.String("message", message),
				logger.Err(err),
			)
		} else {
			logger.Debug("Task progress updated",
				logger.String("task_id", taskID),
				logger.Int("progress", progress),
				logger.String("message", message),
			)
		}
	}

	// Create task steps
	steps := []taskflow.Step{
		{
			Name:        "Execute Command with Real-time Output",
			Description: "Executing command: " + req.Command,
			Input:       input,
			Func:        system.ExecuteCommandTaskWithRealTimeOutput(h.systemSvc, progressCallback),
		},
	}

	// Create and execute task (same as in ExecuteCommand)
	task, err := h.taskManager.CreateTask(taskID, "Execute Command with Real-time Output", "Executing command: "+req.Command, steps, input)
	if err != nil {
		logger.Error("Failed to create task for command execution",
			logger.Err(err),
			logger.String("command", req.Command),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task for command execution: " + err.Error(),
		})
		return
	}

	// Execute task
	if err := h.taskManager.ExecuteTask(task.ID); err != nil {
		logger.Error("Failed to execute command task",
			logger.Err(err),
			logger.String("task_id", task.ID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute command task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Command execution task with real-time output started",
		"task_id": task.ID,
	})
}
