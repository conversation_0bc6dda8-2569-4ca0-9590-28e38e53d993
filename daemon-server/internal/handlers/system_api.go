package handlers

import (
	"github.com/gin-gonic/gin"

	"daemon-server/internal/system"
	"daemon-server/internal/taskflow"
)

// RegisterSystemAPIRoutes registers system API routes
func RegisterSystemAPIRoutes(router *gin.Engine, systemSvc *system.CommandService, taskManager *taskflow.Manager) {
	// Skip if system service is not available
	if systemSvc == nil {
		return
	}

	// Create API group
	apiV1 := router.Group("/api/v1")

	// Create handler
	systemHandler := NewSystemHandler(systemSvc, taskManager)

	// Register system routes
	systemGroup := apiV1.Group("/system")
	{
		systemGroup.POST("/exec", systemHandler.ExecuteCommand)
		systemGroup.POST("/exec/realtime", systemHandler.ExecuteCommandWithRealTimeOutput)
	}
}
