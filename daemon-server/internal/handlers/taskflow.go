package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"daemon-server/internal/logger"
	"daemon-server/internal/taskflow"
)

// TaskflowHandler handles taskflow-related HTTP requests
type TaskflowHandler struct {
	taskManager *taskflow.Manager
}

// NewTaskflowHandler creates a new taskflow handler
func NewTaskflowHandler(taskManager *taskflow.Manager) *TaskflowHandler {
	return &TaskflowHandler{
		taskManager: taskManager,
	}
}

// ListTasks lists all tasks
// GET /api/v1/tasks
func (h *TaskflowHandler) ListTasks(c *gin.Context) {
	// Get query parameters for filtering
	status := taskflow.TaskStatus(c.DefaultQuery("status", ""))

	// Parse time filters if provided
	var startTime, endTime time.Time
	startTimeStr := c.Default<PERSON>uery("start_time", "")
	if startTimeStr != "" {
		parsedTime, err := time.Parse(time.RFC3339, startTimeStr)
		if err == nil {
			startTime = parsedTime
		} else {
			logger.Warn("Invalid start_time format",
				logger.String("start_time", startTimeStr),
				logger.Err(err),
			)
		}
	}

	endTimeStr := c.DefaultQuery("end_time", "")
	if endTimeStr != "" {
		parsedTime, err := time.Parse(time.RFC3339, endTimeStr)
		if err == nil {
			endTime = parsedTime
		} else {
			logger.Warn("Invalid end_time format",
				logger.String("end_time", endTimeStr),
				logger.Err(err),
			)
		}
	}

	// Create filter
	filter := taskflow.TaskFilter{
		Status:    status,
		StartTime: startTime,
		EndTime:   endTime,
	}

	// List tasks with filter
	var tasks []taskflow.TaskInfo
	if status != "" || !startTime.IsZero() || !endTime.IsZero() {
		tasks = h.taskManager.ListTasksWithFilter(filter)
	} else {
		tasks = h.taskManager.ListTasks()
	}

	c.JSON(http.StatusOK, gin.H{
		"tasks": tasks,
	})
}

// GetTask gets a task by ID
// GET /api/v1/tasks/:id
func (h *TaskflowHandler) GetTask(c *gin.Context) {
	// Get task ID from path
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Task ID is required",
		})
		return
	}

	// Get task
	task, err := h.taskManager.GetTask(taskID)
	if err != nil {
		logger.Error("Failed to get task",
			logger.String("task_id", taskID),
			logger.Err(err),
		)
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Task not found: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, task)
}

// CreateTaskRequest represents a request to create a task
type CreateTaskRequest struct {
	Name        string                  `json:"name" binding:"required"`
	Description string                  `json:"description"`
	Steps       []CreateTaskStepRequest `json:"steps" binding:"required"`
	Input       interface{}             `json:"input"`
}

// CreateTaskStepRequest represents a request to create a task step
type CreateTaskStepRequest struct {
	Name        string      `json:"name" binding:"required"`
	Description string      `json:"description"`
	Input       interface{} `json:"input"`
}

// CreateTask creates a new task
// POST /api/v1/tasks
func (h *TaskflowHandler) CreateTask(c *gin.Context) {
	// Parse request body
	var req CreateTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to parse request body", logger.Err(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate request
	if len(req.Steps) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "At least one step is required",
		})
		return
	}

	// Create steps
	steps := make([]taskflow.Step, len(req.Steps))
	for i, stepReq := range req.Steps {
		steps[i] = taskflow.Step{
			Name:        stepReq.Name,
			Description: stepReq.Description,
			Input:       stepReq.Input,
			// Note: The actual function will be set by the task executor
			// This is just a placeholder that returns the input as the result
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				return input, nil
			},
		}
	}

	// Create task
	task, err := h.taskManager.CreateTask("", req.Name, req.Description, steps, req.Input)
	if err != nil {
		logger.Error("Failed to create task",
			logger.String("task_name", req.Name),
			logger.Err(err),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create task: " + err.Error(),
		})
		return
	}

	// Get task info
	taskInfo, err := h.taskManager.GetTask(task.ID)
	if err != nil {
		logger.Error("Failed to get created task",
			logger.String("task_id", task.ID),
			logger.Err(err),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get created task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, taskInfo)
}

// ExecuteTask executes a task
// POST /api/v1/tasks/:id/execute
func (h *TaskflowHandler) ExecuteTask(c *gin.Context) {
	// Get task ID from path
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Task ID is required",
		})
		return
	}

	// Get timeout from query parameters
	timeoutStr := c.DefaultQuery("timeout", "")
	if timeoutStr != "" {
		timeout, err := time.ParseDuration(timeoutStr)
		if err != nil {
			logger.Warn("Invalid timeout format",
				logger.String("timeout", timeoutStr),
				logger.Err(err),
			)
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid timeout format: " + err.Error(),
			})
			return
		}

		// Execute task with timeout
		if err := h.taskManager.ExecuteTaskWithTimeout(taskID, timeout); err != nil {
			logger.Error("Failed to execute task with timeout",
				logger.String("task_id", taskID),
				logger.String("timeout", timeout.String()),
				logger.Err(err),
			)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to execute task: " + err.Error(),
			})
			return
		}
	} else {
		// Execute task without timeout
		if err := h.taskManager.ExecuteTask(taskID); err != nil {
			logger.Error("Failed to execute task",
				logger.String("task_id", taskID),
				logger.Err(err),
			)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to execute task: " + err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Task execution started",
		"id":      taskID,
	})
}

// CancelTask cancels a running task
// POST /api/v1/tasks/:id/cancel
func (h *TaskflowHandler) CancelTask(c *gin.Context) {
	// Get task ID from path
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Task ID is required",
		})
		return
	}

	// Cancel task
	if err := h.taskManager.CancelTask(taskID); err != nil {
		logger.Error("Failed to cancel task",
			logger.String("task_id", taskID),
			logger.Err(err),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to cancel task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Task cancelled",
		"id":      taskID,
	})
}

// DeleteTask deletes a task
// DELETE /api/v1/tasks/:id
func (h *TaskflowHandler) DeleteTask(c *gin.Context) {
	// Get task ID from path
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Task ID is required",
		})
		return
	}

	// Get task to check if it exists
	_, err := h.taskManager.GetTask(taskID)
	if err != nil {
		logger.Error("Failed to get task for deletion",
			logger.String("task_id", taskID),
			logger.Err(err),
		)
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Task not found: " + err.Error(),
		})
		return
	}

	// Delete task from store
	if err := h.taskManager.DeleteTask(taskID); err != nil {
		logger.Error("Failed to delete task",
			logger.String("task_id", taskID),
			logger.Err(err),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Task deleted",
		"id":      taskID,
	})
}

// UpdateTaskProgressRequest represents a request to update task progress
type UpdateTaskProgressRequest struct {
	Progress int    `json:"progress" binding:"required,min=0,max=100"`
	Message  string `json:"message"`
}

// UpdateTaskProgress updates the progress of a task
// POST /api/v1/tasks/:id/progress
func (h *TaskflowHandler) UpdateTaskProgress(c *gin.Context) {
	// Get task ID from path
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Task ID is required",
		})
		return
	}

	// Parse request body
	var req UpdateTaskProgressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to parse request body", logger.Err(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// Update task progress
	if err := h.taskManager.UpdateTaskProgress(taskID, req.Progress, req.Message, false); err != nil {
		logger.Error("Failed to update task progress",
			logger.String("task_id", taskID),
			logger.Int("progress", req.Progress),
			logger.Err(err),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update task progress: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":  "Task progress updated",
		"id":       taskID,
		"progress": req.Progress,
	})
}

// GetTaskProgress gets the progress of a task
// GET /api/v1/tasks/:id/progress
func (h *TaskflowHandler) GetTaskProgress(c *gin.Context) {
	// Get task ID from path
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Task ID is required",
		})
		return
	}

	// Get task info
	taskInfo, err := h.taskManager.GetTask(taskID)
	if err != nil {
		logger.Error("Failed to get task",
			logger.String("task_id", taskID),
			logger.Err(err),
		)
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Task not found: " + err.Error(),
		})
		return
	}

	// Return progress information
	c.JSON(http.StatusOK, gin.H{
		"progress": taskInfo.Progress,
		"status":   taskInfo.Status,
		"error":    taskInfo.Error,
		"stdout":   taskInfo.Stdout,
		"stderr":   taskInfo.Stderr,
	})
}

// GetTaskOutput gets the output/result of a task
// GET /api/v1/tasks/:id/output
func (h *TaskflowHandler) GetTaskOutput(c *gin.Context) {
	// Get task ID from path
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Task ID is required",
		})
		return
	}

	// Get task info
	taskInfo, err := h.taskManager.GetTask(taskID)
	if err != nil {
		logger.Error("Failed to get task",
			logger.String("task_id", taskID),
			logger.Err(err),
		)
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Task not found: " + err.Error(),
		})
		return
	}

	// Return task output information
	output := gin.H{
		"status":       taskInfo.Status,
		"progress":     taskInfo.Progress,
		"result":       taskInfo.Result,
		"error":        taskInfo.Error,
		"started_at":   taskInfo.StartedAt,
		"completed_at": taskInfo.CompletedAt,
	}

	// Include step results if available
	if len(taskInfo.Steps) > 0 {
		stepResults := make([]gin.H, len(taskInfo.Steps))
		for i, step := range taskInfo.Steps {
			stepResults[i] = gin.H{
				"name":         step.Name,
				"description":  step.Description,
				"status":       step.Status,
				"result":       step.Result,
				"error":        step.Error,
				"started_at":   step.StartedAt,
				"completed_at": step.CompletedAt,
			}
		}
		output["steps"] = stepResults
	}

	c.JSON(http.StatusOK, output)
}
