package handlers

import (
	"github.com/gin-gonic/gin"

	"daemon-server/internal/taskflow"
)

// RegisterTaskflowAPIRoutes registers taskflow API routes
func RegisterTaskflowAPIRoutes(router *gin.Engine, taskManager *taskflow.Manager) {
	// Skip if taskflow manager is not available
	if taskManager == nil {
		return
	}

	// Create API group
	apiV1 := router.Group("/api/v1")

	// Create handler
	taskflowHandler := NewTaskflowHandler(taskManager)

	// Register taskflow routes
	tasks := apiV1.Group("/tasks")
	{
		tasks.GET("", taskflowHandler.ListTasks)
		tasks.POST("", taskflowHandler.CreateTask)
		tasks.GET("/:id", taskflowHandler.GetTask)
		tasks.DELETE("/:id", taskflowHandler.DeleteTask)
		tasks.POST("/:id/execute", taskflowHandler.ExecuteTask)
		tasks.POST("/:id/cancel", taskflowHandler.CancelTask)
		tasks.POST("/:id/progress", taskflowHandler.UpdateTaskProgress)
		tasks.GET("/:id/progress", taskflowHandler.GetTaskProgress)
		tasks.GET("/:id/output", taskflowHandler.GetTaskOutput)
	}
}
