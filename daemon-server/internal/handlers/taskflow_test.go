package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"daemon-server/configs"
	"daemon-server/internal/taskflow"
)

func setupTaskflowTestRouter() (*gin.Engine, *taskflow.Manager) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()

	// Create a task manager
	config := &configs.Config{}
	taskManager := taskflow.NewManager(config)

	// Register taskflow routes
	RegisterTaskflowAPIRoutes(router, taskManager)

	return router, taskManager
}

func TestListTasks(t *testing.T) {
	router, taskManager := setupTaskflowTestRouter()

	// Create a test task
	steps := []taskflow.Step{
		{
			Name:        "Test Step",
			Description: "A test step",
			Input:       "test input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				return "test result", nil
			},
		},
	}
	task, err := taskManager.CreateTask("", "Test Task", "A test task", steps, "test input")
	assert.NoError(t, err)
	assert.NotNil(t, task)

	// Create a request
	req, err := http.NewRequest("GET", "/api/v1/tasks", nil)
	assert.NoError(t, err)

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response
	var response map[string][]taskflow.TaskInfo
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check the response
	assert.Contains(t, response, "tasks")
	assert.Len(t, response["tasks"], 1)
	assert.Equal(t, task.ID, response["tasks"][0].ID)
}

func TestGetTask(t *testing.T) {
	router, taskManager := setupTaskflowTestRouter()

	// Create a test task
	steps := []taskflow.Step{
		{
			Name:        "Test Step",
			Description: "A test step",
			Input:       "test input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				return "test result", nil
			},
		},
	}
	task, err := taskManager.CreateTask("", "Test Task", "A test task", steps, "test input")
	assert.NoError(t, err)
	assert.NotNil(t, task)

	// Create a request
	req, err := http.NewRequest("GET", "/api/v1/tasks/"+task.ID, nil)
	assert.NoError(t, err)

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response
	var response taskflow.TaskInfo
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check the response
	assert.Equal(t, task.ID, response.ID)
	assert.Equal(t, "Test Task", response.Name)
	assert.Equal(t, "A test task", response.Description)
}

func TestCreateTask(t *testing.T) {
	router, _ := setupTaskflowTestRouter()

	// Create a request body
	requestBody := map[string]interface{}{
		"name":        "New Task",
		"description": "A new task",
		"steps": []map[string]interface{}{
			{
				"name":        "New Step",
				"description": "A new step",
				"input":       "new input",
			},
		},
		"input": "new task input",
	}
	requestJSON, err := json.Marshal(requestBody)
	assert.NoError(t, err)

	// Create a request
	req, err := http.NewRequest("POST", "/api/v1/tasks", bytes.NewBuffer(requestJSON))
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusCreated, w.Code)

	// Parse the response
	var response taskflow.TaskInfo
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check the response
	assert.NotEmpty(t, response.ID)
	assert.Equal(t, "New Task", response.Name)
	assert.Equal(t, "A new task", response.Description)
	assert.Equal(t, taskflow.TaskStatusPending, response.Status)
	assert.Len(t, response.Steps, 1)
	assert.Equal(t, "New Step", response.Steps[0].Name)
}

func TestExecuteTask(t *testing.T) {
	router, taskManager := setupTaskflowTestRouter()

	// Create a test task
	steps := []taskflow.Step{
		{
			Name:        "Test Step",
			Description: "A test step",
			Input:       "test input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				time.Sleep(50 * time.Millisecond) // Simulate work
				return "test result", nil
			},
		},
	}
	task, err := taskManager.CreateTask("", "Test Task", "A test task", steps, "test input")
	assert.NoError(t, err)
	assert.NotNil(t, task)

	// Create a request
	req, err := http.NewRequest("POST", "/api/v1/tasks/"+task.ID+"/execute", nil)
	assert.NoError(t, err)

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response
	var response map[string]string
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check the response
	assert.Contains(t, response, "message")
	assert.Contains(t, response, "id")
	assert.Equal(t, task.ID, response["id"])

	// Wait for the task to complete
	time.Sleep(100 * time.Millisecond)

	// Verify task status
	taskInfo, err := taskManager.GetTask(task.ID)
	assert.NoError(t, err)
	assert.Equal(t, taskflow.TaskStatusCompleted, taskInfo.Status)
}

func TestDeleteTask(t *testing.T) {
	router, taskManager := setupTaskflowTestRouter()

	// Create a test task
	steps := []taskflow.Step{
		{
			Name:        "Test Step",
			Description: "A test step",
			Input:       "test input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				return "test result", nil
			},
		},
	}
	task, err := taskManager.CreateTask("", "Test Task", "A test task", steps, "test input")
	assert.NoError(t, err)
	assert.NotNil(t, task)

	// Create a request
	req, err := http.NewRequest("DELETE", "/api/v1/tasks/"+task.ID, nil)
	assert.NoError(t, err)

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response
	var response map[string]string
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check the response
	assert.Contains(t, response, "message")
	assert.Contains(t, response, "id")
	assert.Equal(t, task.ID, response["id"])

	// Verify task was deleted
	_, err = taskManager.GetTask(task.ID)
	assert.Error(t, err)
}

func TestGetTaskProgress(t *testing.T) {
	router, taskManager := setupTaskflowTestRouter()

	// Create a test task
	steps := []taskflow.Step{
		{
			Name:        "Test Step",
			Description: "A test step",
			Input:       "test input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				time.Sleep(50 * time.Millisecond) // Simulate work
				return "test result", nil
			},
		},
	}
	task, err := taskManager.CreateTask("", "Test Task", "A test task", steps, "test input")
	assert.NoError(t, err)
	assert.NotNil(t, task)

	// Update task progress
	err = taskManager.UpdateTaskProgress(task.ID, 50, "Halfway there", false)
	assert.NoError(t, err)

	// Create a request
	req, err := http.NewRequest("GET", "/api/v1/tasks/"+task.ID+"/progress", nil)
	assert.NoError(t, err)

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check the response
	assert.Contains(t, response, "progress")
	assert.Contains(t, response, "status")
	assert.Equal(t, float64(50), response["progress"])
	assert.Equal(t, string(taskflow.TaskStatusPending), response["status"])
}

func TestGetTaskOutput(t *testing.T) {
	router, taskManager := setupTaskflowTestRouter()

	// Create a test task
	steps := []taskflow.Step{
		{
			Name:        "Test Step",
			Description: "A test step",
			Input:       "test input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				time.Sleep(50 * time.Millisecond) // Simulate work
				return "test result", nil
			},
		},
	}
	task, err := taskManager.CreateTask("", "Test Task", "A test task", steps, "test input")
	assert.NoError(t, err)
	assert.NotNil(t, task)

	// Execute the task
	err = taskManager.ExecuteTask(task.ID)
	assert.NoError(t, err)

	// Wait for the task to complete
	time.Sleep(100 * time.Millisecond)

	// Create a request
	req, err := http.NewRequest("GET", "/api/v1/tasks/"+task.ID+"/output", nil)
	assert.NoError(t, err)

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check the response
	assert.Contains(t, response, "status")
	assert.Contains(t, response, "progress")
	assert.Contains(t, response, "result")
	assert.Contains(t, response, "steps")
	assert.Equal(t, string(taskflow.TaskStatusCompleted), response["status"])
	assert.Equal(t, float64(100), response["progress"])
	assert.Equal(t, "test result", response["result"])

	// Check step results
	stepsArray, ok := response["steps"].([]interface{})
	assert.True(t, ok)
	assert.Len(t, stepsArray, 1)
	step, ok := stepsArray[0].(map[string]interface{})
	assert.True(t, ok)
	assert.Equal(t, "Test Step", step["name"])
	assert.Equal(t, "test result", step["result"])
}
