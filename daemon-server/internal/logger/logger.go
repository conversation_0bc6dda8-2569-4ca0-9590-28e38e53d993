package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"

	"daemon-server/configs"
)

// Logger is the interface for logging
type Logger interface {
	Debug(msg string, fields ...Field)
	Info(msg string, fields ...Field)
	Warn(msg string, fields ...Field)
	Error(msg string, fields ...Field)
	Fatal(msg string, fields ...Field)
	With(fields ...Field) Logger
}

// Field represents a log field
type Field = zapcore.Field

// String creates a field with a string value
func String(key, value string) Field {
	return zap.String(key, value)
}

// Int creates a field with an int value
func Int(key string, value int) Field {
	return zap.Int(key, value)
}

// Int64 creates a field with an int64 value
func Int64(key string, value int64) Field {
	return zap.Int64(key, value)
}

// Float64 creates a field with a float64 value
func Float64(key string, value float64) Field {
	return zap.Float64(key, value)
}

// <PERSON>ol creates a field with a bool value
func Bool(key string, value bool) Field {
	return zap.Bool(key, value)
}

// Err creates a field with an error value
func Err(err error) Field {
	return zap.Error(err)
}

// Any creates a field with any value
func Any(key string, value interface{}) Field {
	return zap.Any(key, value)
}

// Duration creates a field with a duration value
func Duration(key string, value time.Duration) Field {
	return zap.Duration(key, value)
}

// zapLogger implements the Logger interface using zap
type zapLogger struct {
	logger     *zap.Logger
	callerSkip int
}

// Debug logs a debug message
func (l *zapLogger) Debug(msg string, fields ...Field) {
	l.logger.WithOptions(zap.AddCallerSkip(l.callerSkip)).Debug(msg, fields...)
}

// Info logs an info message
func (l *zapLogger) Info(msg string, fields ...Field) {
	l.logger.WithOptions(zap.AddCallerSkip(l.callerSkip)).Info(msg, fields...)
}

// Warn logs a warning message
func (l *zapLogger) Warn(msg string, fields ...Field) {
	l.logger.WithOptions(zap.AddCallerSkip(l.callerSkip)).Warn(msg, fields...)
}

// Error logs an error message
func (l *zapLogger) Error(msg string, fields ...Field) {
	l.logger.WithOptions(zap.AddCallerSkip(l.callerSkip)).Error(msg, fields...)
}

// Fatal logs a fatal message and exits
func (l *zapLogger) Fatal(msg string, fields ...Field) {
	l.logger.WithOptions(zap.AddCallerSkip(l.callerSkip)).Fatal(msg, fields...)
}

// With returns a logger with the given fields
func (l *zapLogger) With(fields ...Field) Logger {
	return &zapLogger{
		logger:     l.logger.With(fields...),
		callerSkip: l.callerSkip,
	}
}

// Global logger instance
var globalLogger Logger

// Init initializes the global logger
func Init(config *configs.Config) error {
	// Create logger directory if it doesn't exist
	logDir := filepath.Dir(config.Logger.File)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("failed to create log directory: %w", err)
	}

	// Parse log level
	level := parseLevel(config.Logger.Level)

	// Configure encoder
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// Configure log rotation
	sink := zapcore.AddSync(&lumberjack.Logger{
		Filename:   config.Logger.File,
		MaxSize:    config.Logger.MaxSize,
		MaxBackups: config.Logger.MaxBackups,
		MaxAge:     config.Logger.MaxAge,
		Compress:   config.Logger.Compress,
	})

	// Create encoder based on format
	var encoder zapcore.Encoder
	if strings.ToLower(config.Logger.Format) == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// Create core
	core := zapcore.NewCore(
		encoder,
		zapcore.NewMultiWriteSyncer(zapcore.AddSync(os.Stdout), sink),
		zap.NewAtomicLevelAt(level),
	)

	// Create logger with caller information
	// We use AddCaller() to add caller information, but don't add any CallerSkip here
	// because we'll handle that in the individual logging methods
	logger := zap.New(core, zap.AddCaller())
	globalLogger = &zapLogger{logger: logger, callerSkip: 0}

	return nil
}

// parseLevel parses the log level string
func parseLevel(level string) zapcore.Level {
	switch strings.ToLower(level) {
	case "debug":
		return zapcore.DebugLevel
	case "info":
		return zapcore.InfoLevel
	case "warn", "warning":
		return zapcore.WarnLevel
	case "error":
		return zapcore.ErrorLevel
	case "fatal":
		return zapcore.FatalLevel
	default:
		return zapcore.InfoLevel
	}
}

// Debug logs a debug message
func Debug(msg string, fields ...Field) {
	if globalLogger != nil {
		// Use callerSkip=1 to skip this wrapper function
		l := globalLogger.(*zapLogger)
		l.logger.WithOptions(zap.AddCallerSkip(1)).Debug(msg, fields...)
	}
}

// Info logs an info message
func Info(msg string, fields ...Field) {
	if globalLogger != nil {
		// Use callerSkip=1 to skip this wrapper function
		l := globalLogger.(*zapLogger)
		l.logger.WithOptions(zap.AddCallerSkip(1)).Info(msg, fields...)
	}
}

// Warn logs a warning message
func Warn(msg string, fields ...Field) {
	if globalLogger != nil {
		// Use callerSkip=1 to skip this wrapper function
		l := globalLogger.(*zapLogger)
		l.logger.WithOptions(zap.AddCallerSkip(1)).Warn(msg, fields...)
	}
}

// Error logs an error message
func Error(msg string, fields ...Field) {
	if globalLogger != nil {
		// Use callerSkip=1 to skip this wrapper function
		l := globalLogger.(*zapLogger)
		l.logger.WithOptions(zap.AddCallerSkip(1)).Error(msg, fields...)
	}
}

// Fatal logs a fatal message and exits
func Fatal(msg string, fields ...Field) {
	if globalLogger != nil {
		// Use callerSkip=1 to skip this wrapper function
		l := globalLogger.(*zapLogger)
		l.logger.WithOptions(zap.AddCallerSkip(1)).Fatal(msg, fields...)
	}
}

// With returns a logger with the given fields
func With(fields ...Field) Logger {
	if globalLogger != nil {
		// Create a new logger with the fields and correct caller skip
		l := globalLogger.(*zapLogger)
		return &zapLogger{
			logger:     l.logger.With(fields...),
			callerSkip: 0, // Reset caller skip for the new logger
		}
	}
	return nil
}
