package middleware

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"

	"daemon-server/configs"
	"daemon-server/internal/logger"
)

// Logger returns a middleware that logs request details
func Logger(config *configs.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Start timer
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)

		// Get client IP
		clientIP := c.ClientIP()

		// Get method
		method := c.Request.Method

		// Get status code
		statusCode := c.Writer.Status()

		// Get error message if any
		errorMessage := c.Errors.ByType(gin.ErrorTypePrivate).String()

		// Get user agent
		userAgent := c.Request.UserAgent()

		// Construct full path
		if raw != "" {
			path = path + "?" + raw
		}

		// Log request
		logFields := []logger.Field{
			logger.String("client_ip", clientIP),
			logger.String("method", method),
			logger.String("path", path),
			logger.String("proto", c.Request.Proto),
			logger.Int("status", statusCode),
			logger.String("latency", latency.String()),
			logger.String("user_agent", userAgent),
		}

		// Add error message if any
		if errorMessage != "" {
			logFields = append(logFields, logger.String("error", errorMessage))
		}

		// Log with appropriate level based on status code
		msg := fmt.Sprintf("%s %s %d %s", method, path, statusCode, latency)
		if statusCode >= 500 {
			logger.Error(msg, logFields...)
		} else if statusCode >= 400 {
			logger.Warn(msg, logFields...)
		} else {
			logger.Info(msg, logFields...)
		}
	}
}

// Recovery returns a middleware that recovers from panics
func Recovery() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// Log the panic
				logger.Error("Panic recovered",
					logger.Any("error", err),
					logger.String("path", c.Request.URL.Path),
					logger.String("method", c.Request.Method),
				)

				// Return 500 error
				c.AbortWithStatus(500)
			}
		}()
		c.Next()
	}
}
