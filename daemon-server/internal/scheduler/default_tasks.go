package scheduler

import (
	"context"
	"time"

	"daemon-server/internal/logger"
	"daemon-server/internal/taskflow"
)

// registerTaskCleanupTask registers a task to clean up old completed tasks
func (s *Scheduler) registerTaskCleanupTask() {
	s.AddTask(
		"Task Cleanup",
		"Cleans up old completed tasks",
		SchedulerTaskflowCleanupIntervalHours*time.Hour,
		func(ctx context.Context) (*taskflow.Task, error) {
			// Create a task with steps
			steps := []taskflow.Step{
				{
					Name:        "Clean Old Tasks",
					Description: "Removing old completed tasks",
					Func: func(ctx context.Context, input interface{}) (interface{}, error) {
						// Get all tasks
						tasks := s.taskManager.ListTasks()

						// Count of tasks deleted
						deletedCount := 0

						// Current time
						now := time.Now()

						// Keep track of task IDs to delete
						tasksToDelete := []string{}

						// Find tasks that are completed and older than 7 days
						for _, task := range tasks {
							// Skip tasks that are still running
							if task.Status == taskflow.TaskStatusRunning {
								continue
							}

							// Check if the task has a completion time
							if task.CompletedAt != nil {
								// Calculate age of the task
								taskAge := now.Sub(*task.CompletedAt)

								// If task is older than 7 days, mark for deletion
								if taskAge > SchedulerTaskflowCleanupExpiredTimeDays*24*time.Hour {
									tasksToDelete = append(tasksToDelete, task.ID)
								}
							}
						}

						// Delete the tasks
						for _, taskID := range tasksToDelete {
							if err := s.taskManager.DeleteTask(taskID); err == nil {
								deletedCount++
							} else {
								logger.Error("Failed to delete old task",
									logger.String("task_id", taskID),
									logger.Err(err),
								)
							}
						}

						logger.Info("Task cleanup completed",
							logger.Int("deleted_count", deletedCount),
							logger.Int("total_tasks", len(tasks)),
						)

						return map[string]interface{}{
							"deleted_count": deletedCount,
							"total_tasks":   len(tasks),
						}, nil
					},
				},
			}

			// Create the task
			task, err := s.taskManager.CreateTask("", "Task Cleanup", "Cleaning up old completed tasks", steps, nil)
			if err != nil {
				logger.Error("Failed to create task cleanup task", logger.Err(err))
				return nil, err
			}

			return task, nil
		},
	)

	logger.Info("Task cleanup scheduled task registered")
}

// Example of what a Docker cleanup task might look like
/*
func (s *Scheduler) registerDockerCleanupTask() {
	// This would require Docker service to be passed to the scheduler
	s.AddTask(
		"Docker Cleanup",
		"Cleans up unused Docker resources",
		7*24*time.Hour, // Run once a week
		func(ctx context.Context) (*taskflow.Task, error) {
			// Create a task with steps
			steps := []taskflow.Step{
				{
					Name:        "Prune Docker Images",
					Description: "Removing unused Docker images",
					Func: docker.EnhancedPruneImagesTask(s.dockerSvc),
					Input: map[string]interface{}{
						"all": false, // Only remove dangling images
					},
				},
			}

			// Create the task
			task, err := s.taskManager.CreateTask("", "Docker Cleanup", "Cleaning up unused Docker resources", steps, nil)
			if err != nil {
				logger.Error("Failed to create Docker cleanup task", logger.Err(err))
				return nil, err
			}

			return task, nil
		},
	)

	logger.Info("Docker cleanup scheduled task registered")
}
*/
