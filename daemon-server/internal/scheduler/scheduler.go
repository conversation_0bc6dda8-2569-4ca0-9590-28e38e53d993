package scheduler

import (
	"context"
	"daemon-server/pkg/utils"
	"sync"
	"time"

	"daemon-server/configs"
	"daemon-server/internal/logger"
	"daemon-server/internal/taskflow"
)

// ScheduledTask represents a task that is scheduled to run at specific intervals
type ScheduledTask struct {
	ID          string
	Name        string
	Description string
	Interval    time.Duration
	LastRun     time.Time
	NextRun     time.Time
	IsRunning   bool
	Enabled     bool
	TaskFunc    func(ctx context.Context) (*taskflow.Task, error)
}

// Scheduler manages scheduled tasks
type Scheduler struct {
	tasks       map[string]*ScheduledTask
	taskManager *taskflow.Manager
	config      *configs.Config
	mu          sync.RWMutex
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
}

// NewScheduler creates a new scheduler
func NewScheduler(config *configs.Config, taskManager *taskflow.Manager) *Scheduler {
	ctx, cancel := context.WithCancel(context.Background())

	return &Scheduler{
		tasks:       make(map[string]*ScheduledTask),
		taskManager: taskManager,
		config:      config,
		ctx:         ctx,
		cancel:      cancel,
	}
}

// Start starts the scheduler
func (s *Scheduler) Start() {
	if len(s.tasks) == 0 {
		logger.Info("No scheduled tasks to start")
		return
	} else {
		logger.Info("Starting scheduler")
		s.wg.Add(1)
		go s.run()
	}
}

// Stop stops the scheduler
func (s *Scheduler) Stop() {
	logger.Info("Stopping scheduler")

	s.cancel()
	s.wg.Wait()

	logger.Info("Scheduler stopped")
}

// run is the main scheduler loop
func (s *Scheduler) run() {
	defer s.wg.Done()

	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.checkScheduledTasks()
		}
	}
}

// checkScheduledTasks checks if any tasks need to be executed
func (s *Scheduler) checkScheduledTasks() {
	s.mu.RLock()
	defer s.mu.RUnlock()

	now := time.Now()

	for id, task := range s.tasks {
		if !task.Enabled || task.IsRunning {
			continue
		}

		if now.After(task.NextRun) || now.Equal(task.NextRun) {
			// Execute the task in a separate goroutine
			s.wg.Add(1)
			go s.executeTask(id)
		}
	}
}

// executeTask executes a scheduled task
func (s *Scheduler) executeTask(taskID string) {
	defer s.wg.Done()

	s.mu.Lock()
	task, exists := s.tasks[taskID]
	if !exists {
		s.mu.Unlock()
		return
	}

	// Mark task as running
	task.IsRunning = true
	task.LastRun = time.Now()
	task.NextRun = task.LastRun.Add(task.Interval)
	s.mu.Unlock()

	logger.Info("Executing scheduled task",
		logger.String("task_id", taskID),
		logger.String("task_name", task.Name),
	)

	// Execute the task
	ctx, cancel := context.WithTimeout(s.ctx, 30*time.Minute) // Default timeout of 30 minutes
	defer cancel()

	flowTask, err := task.TaskFunc(ctx)
	if err != nil {
		logger.Error("Failed to create task",
			logger.String("task_id", taskID),
			logger.String("task_name", task.Name),
			logger.Err(err),
		)

		s.mu.Lock()
		task.IsRunning = false
		s.mu.Unlock()
		return
	}

	// Execute the task using the task manager
	err = s.taskManager.ExecuteTask(flowTask.ID)
	if err != nil {
		logger.Error("Failed to execute task",
			logger.String("task_id", taskID),
			logger.String("task_name", task.Name),
			logger.Err(err),
		)
	}

	s.mu.Lock()
	task.IsRunning = false
	s.mu.Unlock()

	logger.Info("Scheduled task execution completed",
		logger.String("task_id", taskID),
		logger.String("task_name", task.Name),
		logger.String("next_run", task.NextRun.Format(time.RFC3339)),
	)
}

// AddTask adds a new scheduled task
func (s *Scheduler) AddTask(name, description string, interval time.Duration, taskFunc func(ctx context.Context) (*taskflow.Task, error)) string {
	s.mu.Lock()
	defer s.mu.Unlock()

	task := &ScheduledTask{
		ID:          generateID(),
		Name:        name,
		Description: description,
		Interval:    interval,
		LastRun:     time.Time{}, // Zero time
		NextRun:     time.Now(),  // Run immediately
		IsRunning:   false,
		Enabled:     true,
		TaskFunc:    taskFunc,
	}

	s.tasks[task.ID] = task

	logger.Info("Task added to scheduler",
		logger.String("task_id", task.ID),
		logger.String("task_name", task.Name),
		logger.String("interval", interval.String()),
	)

	return task.ID
}

// RemoveTask removes a scheduled task
func (s *Scheduler) RemoveTask(taskID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return ErrTaskNotFound
	}

	delete(s.tasks, taskID)

	logger.Info("Task removed from scheduler",
		logger.String("task_id", taskID),
		logger.String("task_name", task.Name),
	)

	return nil
}

// GetTask gets a scheduled task by ID
func (s *Scheduler) GetTask(taskID string) (*ScheduledTask, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return nil, ErrTaskNotFound
	}

	return task, nil
}

// ListTasks lists all scheduled tasks
func (s *Scheduler) ListTasks() []*ScheduledTask {
	s.mu.RLock()
	defer s.mu.RUnlock()

	tasks := make([]*ScheduledTask, 0, len(s.tasks))
	for _, task := range s.tasks {
		tasks = append(tasks, task)
	}

	return tasks
}

// EnableTask enables a scheduled task
func (s *Scheduler) EnableTask(taskID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return ErrTaskNotFound
	}

	task.Enabled = true
	task.NextRun = time.Now() // Run immediately

	logger.Info("Task enabled",
		logger.String("task_id", taskID),
		logger.String("task_name", task.Name),
	)

	return nil
}

// DisableTask disables a scheduled task
func (s *Scheduler) DisableTask(taskID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return ErrTaskNotFound
	}

	task.Enabled = false

	logger.Info("Task disabled",
		logger.String("task_id", taskID),
		logger.String("task_name", task.Name),
	)

	return nil
}

// UpdateTaskInterval updates the interval of a scheduled task
func (s *Scheduler) UpdateTaskInterval(taskID string, interval time.Duration) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return ErrTaskNotFound
	}

	task.Interval = interval
	task.NextRun = time.Now().Add(interval)

	logger.Info("Task interval updated",
		logger.String("task_id", taskID),
		logger.String("task_name", task.Name),
		logger.String("interval", interval.String()),
	)

	return nil
}

// generateID generates a unique ID for a task
func generateID() string {
	return utils.GetUuidString()
}
