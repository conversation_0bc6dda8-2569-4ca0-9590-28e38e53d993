package scheduler

import (
	"context"
	"testing"
	"time"

	"daemon-server/configs"
	"daemon-server/internal/taskflow"
)

func TestScheduler(t *testing.T) {
	// Create a test config
	config := &configs.Config{
		Scheduler: configs.SchedulerConfig{
			Enabled:     true,
			Concurrency: 5,
		},
	}

	// Create a task manager
	taskManager := taskflow.NewManager(config)

	// Create a scheduler
	scheduler := NewScheduler(config, taskManager)

	// Add a test task
	taskExecuted := false
	taskID := scheduler.AddTask(
		"Test Task",
		"A test task",
		1*time.Second,
		func(ctx context.Context) (*taskflow.Task, error) {
			// Create a task with steps
			steps := []taskflow.Step{
				{
					Name:        "Test Step",
					Description: "A test step",
					Func: func(ctx context.Context, input interface{}) (interface{}, error) {
						taskExecuted = true
						return "test result", nil
					},
				},
			}

			// Create the task
			task, err := taskManager.CreateTask("", "Test Task", "A test task", steps, nil)

			return task, err
		},
	)

	// Start the scheduler
	scheduler.Start()

	// Wait for the task to execute
	time.Sleep(3 * time.Second)

	// Stop the scheduler
	scheduler.Stop()

	// Check if the task was executed
	if !taskExecuted {
		t.Error("Task was not executed")
	}

	// Test getting a task
	task, err := scheduler.GetTask(taskID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if task.Name != "Test Task" {
		t.Errorf("Expected task name to be 'Test Task', got '%s'", task.Name)
	}

	// Test listing tasks
	tasks := scheduler.ListTasks()
	if len(tasks) != 1 {
		t.Errorf("Expected 1 task, got %d", len(tasks))
	}

	// Test removing a task
	err = scheduler.RemoveTask(taskID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Test getting a non-existent task
	_, err = scheduler.GetTask(taskID)
	if err != ErrTaskNotFound {
		t.Errorf("Expected ErrTaskNotFound, got %v", err)
	}

	// Test listing tasks after removal
	tasks = scheduler.ListTasks()
	if len(tasks) != 0 {
		t.Errorf("Expected 0 tasks, got %d", len(tasks))
	}
}

func TestSchedulerDisableEnable(t *testing.T) {
	// Create a test config
	config := &configs.Config{
		Scheduler: configs.SchedulerConfig{
			Enabled:     true,
			Concurrency: 5,
		},
	}

	// Create a task manager
	taskManager := taskflow.NewManager(config)

	// Create a scheduler
	scheduler := NewScheduler(config, taskManager)

	// Add a test task
	taskID := scheduler.AddTask(
		"Test Task",
		"A test task",
		1*time.Minute, // Long interval so it doesn't execute during the test
		func(ctx context.Context) (*taskflow.Task, error) {
			// Create a task with steps
			steps := []taskflow.Step{
				{
					Name:        "Test Step",
					Description: "A test step",
					Func: func(ctx context.Context, input interface{}) (interface{}, error) {
						return "test result", nil
					},
				},
			}

			// Create the task
			task, err := taskManager.CreateTask("", "Test Task", "A test task", steps, nil)

			return task, err
		},
	)

	// Test disabling a task
	err := scheduler.DisableTask(taskID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Get the task and check if it's disabled
	task, err := scheduler.GetTask(taskID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if task.Enabled {
		t.Error("Task should be disabled")
	}

	// Test enabling a task
	err = scheduler.EnableTask(taskID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Get the task and check if it's enabled
	task, err = scheduler.GetTask(taskID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if !task.Enabled {
		t.Error("Task should be enabled")
	}

	// Test updating task interval
	err = scheduler.UpdateTaskInterval(taskID, 5*time.Minute)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Get the task and check if the interval was updated
	task, err = scheduler.GetTask(taskID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if task.Interval != 5*time.Minute {
		t.Errorf("Expected interval to be 5 minutes, got %v", task.Interval)
	}
}
