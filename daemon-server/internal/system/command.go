package system

import (
	"bufio"
	"bytes"
	"context"
	"fmt"
	"os/exec"
	"strings"
	"syscall"
	"time"

	"daemon-server/internal/logger"
)

// CommandService provides system command execution functionality
type CommandService struct{}

// NewCommandService creates a new command service
func NewCommandService() *CommandService {
	return &CommandService{}
}

// CommandRequest represents a request to execute a command
type CommandRequest struct {
	Command    string   `json:"command"`               // Command to execute
	Args       []string `json:"args"`                  // Command arguments
	WorkingDir string   `json:"working_dir,omitempty"` // Working directory
	Env        []string `json:"env,omitempty"`         // Environment variables
	Timeout    int      `json:"timeout,omitempty"`     // Timeout in seconds
}

// CommandResponse represents the result of a command execution
type CommandResponse struct {
	Command   string    `json:"command"`         // Command that was executed
	ExitCode  int       `json:"exit_code"`       // Exit code of the command
	Output    string    `json:"output"`          // Combined stdout and stderr
	Stdout    string    `json:"stdout"`          // Standard output
	Stderr    string    `json:"stderr"`          // Standard error
	Error     string    `json:"error,omitempty"` // Error message if any
	StartTime time.Time `json:"start_time"`      // When the command started
	EndTime   time.Time `json:"end_time"`        // When the command completed
	Duration  string    `json:"duration"`        // Duration of execution
}

// ExecuteCommand executes a command and returns the result
func (s *CommandService) ExecuteCommand(ctx context.Context, req CommandRequest) (*CommandResponse, error) {
	startTime := time.Now()

	logger.Info("Executing command",
		logger.String("command", req.Command),
		logger.Any("args", req.Args),
		logger.String("working_dir", req.WorkingDir),
	)

	// Create command
	shellCmd := req.Command
	if len(req.Args) > 0 {
		shellCmd += " " + strings.Join(req.Args, " ")
	}
	cmd := exec.CommandContext(ctx, "sh", "-c", shellCmd)

	// Set working directory if specified
	if req.WorkingDir != "" {
		cmd.Dir = req.WorkingDir
	}

	// Set environment variables if specified
	if len(req.Env) > 0 {
		cmd.Env = req.Env
	}

	// Create buffers for stdout and stderr
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// Execute command
	err := cmd.Run()

	// Get command output
	stdoutStr := stdout.String()
	stderrStr := stderr.String()
	output := stdoutStr
	if stderrStr != "" {
		if output != "" {
			output += "\n"
		}
		output += stderrStr
	}

	// Get exit code
	exitCode := 0
	if err != nil {
		// Try to get exit code
		if exitErr, ok := err.(*exec.ExitError); ok {
			if status, ok := exitErr.Sys().(syscall.WaitStatus); ok {
				exitCode = status.ExitStatus()
			}
		}
	}

	endTime := time.Now()
	duration := endTime.Sub(startTime)
	output = strings.TrimSpace(output)
	output = strings.TrimRight(output, "\n")
	// Create response
	resp := &CommandResponse{
		Command:   req.Command + " " + strings.Join(req.Args, " "),
		ExitCode:  exitCode,
		Output:    output,
		Stdout:    stdoutStr,
		Stderr:    stderrStr,
		StartTime: startTime,
		EndTime:   endTime,
		Duration:  duration.String(),
	}

	if err != nil {
		resp.Error = err.Error()
		logger.Error("Command execution failed",
			logger.String("command", req.Command),
			logger.Any("args", req.Args),
			logger.Int("exit_code", exitCode),
			logger.Err(err),
			logger.Duration("duration", duration),
		)
		return resp, fmt.Errorf("command execution failed: %w", err)
	}

	logger.Info("Command executed successfully",
		logger.String("command", req.Command),
		logger.Any("args", req.Args),
		logger.Int("exit_code", exitCode),
		logger.Duration("duration", duration),
	)

	return resp, nil
}

// ExecuteCommandWithProgress executes a command and reports progress
// This is a simplified implementation that doesn't actually report real-time progress
// In a real implementation, you would need to parse the command output to determine progress
func (s *CommandService) ExecuteCommandWithProgress(ctx context.Context, req CommandRequest,
	progressCallback func(int, string)) (*CommandResponse, error) {

	// Report initial progress
	progressCallback(0, "Starting command execution")

	// Create a context with timeout if specified
	if req.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(req.Timeout)*time.Second)
		defer cancel()
	}

	// Execute command
	resp, err := s.ExecuteCommand(ctx, req)

	// Report final progress
	if err != nil {
		progressCallback(100, "Command execution failed: "+err.Error())
	} else {
		progressCallback(100, "Command execution completed")
	}

	return resp, err
}

// ExecuteCommandWithRealTimeOutput executes a command and streams output in real-time
// The outputCallback receives the output line and a boolean indicating if it's from stdout (true) or stderr (false)
func (s *CommandService) ExecuteCommandWithRealTimeOutput(ctx context.Context, req CommandRequest,
	outputCallback func(string, bool)) (*CommandResponse, error) {

	startTime := time.Now()

	logger.Info("Executing command with real-time output",
		logger.String("command", req.Command),
		logger.Any("args", req.Args),
		logger.String("working_dir", req.WorkingDir),
	)

	// Create command
	shellCmd := req.Command
	if len(req.Args) > 0 {
		shellCmd += " " + strings.Join(req.Args, " ")
	}
	cmd := exec.CommandContext(ctx, "sh", "-c", shellCmd)

	// Set working directory if specified
	if req.WorkingDir != "" {
		cmd.Dir = req.WorkingDir
	}

	// Set environment variables if specified
	if len(req.Env) > 0 {
		cmd.Env = req.Env
	}

	// Create pipes for stdout and stderr
	stdoutPipe, err := cmd.StdoutPipe()
	if err != nil {
		return nil, fmt.Errorf("failed to create stdout pipe: %w", err)
	}

	stderrPipe, err := cmd.StderrPipe()
	if err != nil {
		return nil, fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	// Start command
	if err := cmd.Start(); err != nil {
		return nil, fmt.Errorf("failed to start command: %w", err)
	}

	// Collect output for the final response
	var allOutput strings.Builder
	var stderrOutput strings.Builder
	var stdoutOutput strings.Builder

	// Process stdout in real-time
	go func() {
		scanner := bufio.NewScanner(stdoutPipe)
		for scanner.Scan() {
			line := scanner.Text()
			allOutput.WriteString(line + "\n")
			stdoutOutput.WriteString(line + "\n")
			outputCallback(line, true) // true indicates stdout
		}
	}()

	// Process stderr in real-time
	go func() {
		scanner := bufio.NewScanner(stderrPipe)
		for scanner.Scan() {
			line := scanner.Text()
			allOutput.WriteString(line + "\n")
			stderrOutput.WriteString(line + "\n")
			outputCallback(line, false) // false indicates stderr
		}
	}()

	// Wait for command to complete
	err = cmd.Wait()

	// Get exit code
	exitCode := 0
	if err != nil {
		// Try to get exit code
		if exitErr, ok := err.(*exec.ExitError); ok {
			if status, ok := exitErr.Sys().(syscall.WaitStatus); ok {
				exitCode = status.ExitStatus()
			}
		}
	}

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// Create response
	resp := &CommandResponse{
		Command:   req.Command + " " + strings.Join(req.Args, " "),
		ExitCode:  exitCode,
		Output:    allOutput.String(),
		Stdout:    stdoutOutput.String(),
		Stderr:    stderrOutput.String(),
		StartTime: startTime,
		EndTime:   endTime,
		Duration:  duration.String(),
	}

	if err != nil {
		resp.Error = err.Error()
		logger.Error("Command execution failed",
			logger.String("command", req.Command),
			logger.Any("args", req.Args),
			logger.Int("exit_code", exitCode),
			logger.Err(err),
			logger.Duration("duration", duration),
		)
		return resp, fmt.Errorf("command execution failed: %w", err)
	}

	logger.Info("Command executed successfully",
		logger.String("command", req.Command),
		logger.Any("args", req.Args),
		logger.Int("exit_code", exitCode),
		logger.Duration("duration", duration),
	)

	return resp, nil
}
