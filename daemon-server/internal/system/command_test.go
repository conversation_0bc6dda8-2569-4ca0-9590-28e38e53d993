package system

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCommandService_ExecuteCommand(t *testing.T) {
	// Create command service
	svc := NewCommandService()

	// Create context
	ctx := context.Background()

	// Test cases
	tests := []struct {
		name        string
		req         CommandRequest
		expectError bool
		checkOutput func(t *testing.T, resp *CommandResponse)
	}{
		{
			name: "echo command",
			req: CommandRequest{
				Command: "echo",
				Args:    []string{"hello", "world"},
			},
			expectError: false,
			checkOutput: func(t *testing.T, resp *CommandResponse) {
				assert.Equal(t, 0, resp.ExitCode)
				assert.Equal(t, "hello world\n", resp.Output)
				assert.Empty(t, resp.Error)
			},
		},
		{
			name: "ls command",
			req: CommandRequest{
				Command: "ls",
				Args:    []string{"-la"},
			},
			expectError: false,
			checkOutput: func(t *testing.T, resp *CommandResponse) {
				assert.Equal(t, 0, resp.ExitCode)
				assert.NotEmpty(t, resp.Output)
				assert.Empty(t, resp.Error)
			},
		},
		{
			name: "command with timeout",
			req: CommandRequest{
				Command: "sleep",
				Args:    []string{"1"},
				Timeout: 2,
			},
			expectError: false,
			checkOutput: func(t *testing.T, resp *CommandResponse) {
				assert.Equal(t, 0, resp.ExitCode)
				assert.Empty(t, resp.Error)
			},
		},
		{
			name: "command with working directory",
			req: CommandRequest{
				Command:    "pwd",
				WorkingDir: "/tmp",
			},
			expectError: false,
			checkOutput: func(t *testing.T, resp *CommandResponse) {
				assert.Equal(t, 0, resp.ExitCode)
				assert.Contains(t, resp.Output, "/tmp")
				assert.Empty(t, resp.Error)
			},
		},
		{
			name: "command not found",
			req: CommandRequest{
				Command: "command_not_found",
			},
			expectError: true,
			checkOutput: func(t *testing.T, resp *CommandResponse) {
				// On some systems, the exit code might be 0 even for command not found
				// So we only check that the error message is not empty
				assert.NotEmpty(t, resp.Error)
			},
		},
	}

	// Run tests
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute command
			resp, err := svc.ExecuteCommand(ctx, tt.req)

			// Check error
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Check response
			assert.NotNil(t, resp)
			assert.NotEmpty(t, resp.Command)
			assert.NotZero(t, resp.StartTime)
			assert.NotZero(t, resp.EndTime)
			assert.NotEmpty(t, resp.Duration)

			// Check output
			if tt.checkOutput != nil {
				tt.checkOutput(t, resp)
			}
		})
	}
}

func TestCommandService_ExecuteCommandWithProgress(t *testing.T) {
	// Create command service
	svc := NewCommandService()

	// Create context
	ctx := context.Background()

	// Create request
	req := CommandRequest{
		Command: "echo",
		Args:    []string{"hello", "world"},
	}

	// Create progress callback
	var progress []string
	progressCallback := func(percent int, message string) {
		progress = append(progress, fmt.Sprintf("%d%%: %s", percent, message))
	}

	// Execute command with progress
	resp, err := svc.ExecuteCommandWithProgress(ctx, req, progressCallback)

	// Check error
	assert.NoError(t, err)

	// Check response
	assert.NotNil(t, resp)
	assert.Equal(t, 0, resp.ExitCode)
	assert.Equal(t, "hello world\n", resp.Output)
	assert.Empty(t, resp.Error)

	// Check progress
	assert.Len(t, progress, 2)
	assert.Contains(t, progress[0], "0%: Starting")
	assert.Contains(t, progress[1], "100%: Command execution completed")
}

func TestCommandService_ExecuteCommandWithRealTimeOutput(t *testing.T) {
	// Create a command service
	svc := NewCommandService()

	// Create a command that outputs to both stdout and stderr
	req := CommandRequest{
		Command: "sh",
		Args:    []string{"-c", "echo stdout message; echo stderr message >&2; echo another stdout; echo another stderr >&2"},
	}

	// Collect output
	var stdoutLines []string
	var stderrLines []string

	// Create callback function
	outputCallback := func(line string, isStdout bool) {
		if isStdout {
			stdoutLines = append(stdoutLines, line)
		} else {
			stderrLines = append(stderrLines, line)
		}
	}

	// Execute command
	ctx := context.Background()

	resp, err := svc.ExecuteCommandWithRealTimeOutput(ctx, req, outputCallback)

	// Check error
	assert.NoError(t, err)

	// Check response
	assert.NotNil(t, resp)
	assert.Equal(t, 0, resp.ExitCode)
	assert.Empty(t, resp.Error)

	// Check stdout and stderr lines
	t.Logf("Stdout lines: %v", stdoutLines)
	t.Logf("Stderr lines: %v", stderrLines)

	// We should have at least some output
	assert.True(t, len(stdoutLines) > 0 || len(stderrLines) > 0, "Expected some output lines")

	// Check stdout content
	assert.Contains(t, resp.Stdout, "stdout message")
	assert.Contains(t, resp.Stdout, "another stdout")

	// Check stderr content
	assert.Contains(t, resp.Stderr, "stderr message")
	assert.Contains(t, resp.Stderr, "another stderr")

	// Check combined output
	assert.Contains(t, resp.Output, "stdout message")
	assert.Contains(t, resp.Output, "stderr message")
}
