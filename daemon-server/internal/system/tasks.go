package system

import (
	"context"
	"fmt"
	"time"

	"daemon-server/internal/logger"
	"daemon-server/internal/taskflow"
)

// ExecuteCommandTask creates a task function for command execution
func ExecuteCommandTask(svc *CommandService) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()

		// Convert input to command request
		inputMap, ok := input.(map[string]interface{})
		if !ok {
			err := fmt.Errorf("invalid input type for command execution task: expected map, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract command
		command, ok := inputMap["command"].(string)
		if !ok {
			err := fmt.Errorf("command is required and must be a string")
			logger.Error("Task validation failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract args
		var args []string
		if argsInterface, ok := inputMap["args"].([]interface{}); ok {
			args = make([]string, len(argsInterface))
			for i, arg := range argsInterface {
				if argStr, ok := arg.(string); ok {
					args[i] = argStr
				} else {
					err := fmt.Errorf("args must be an array of strings")
					logger.Error("Task validation failed",
						logger.Err(err),
						logger.Any("args", argsInterface),
					)
					return nil, err
				}
			}
		}

		// Extract working directory
		workingDir, _ := inputMap["working_dir"].(string)

		// Extract environment variables
		var env []string
		if envInterface, ok := inputMap["env"].([]interface{}); ok {
			env = make([]string, len(envInterface))
			for i, e := range envInterface {
				if envStr, ok := e.(string); ok {
					env[i] = envStr
				} else {
					err := fmt.Errorf("env must be an array of strings")
					logger.Error("Task validation failed",
						logger.Err(err),
						logger.Any("env", envInterface),
					)
					return nil, err
				}
			}
		}

		// Extract timeout
		var timeout int
		if timeoutFloat, ok := inputMap["timeout"].(float64); ok {
			timeout = int(timeoutFloat)
		}

		// Create command request
		req := CommandRequest{
			Command:    command,
			Args:       args,
			WorkingDir: workingDir,
			Env:        env,
			Timeout:    timeout,
		}

		logger.Info("Executing command task",
			logger.String("command", command),
			logger.Any("args", args),
			logger.String("working_dir", workingDir),
		)

		// Execute command
		resp, err := svc.ExecuteCommand(ctx, req)
		if err != nil {
			logger.Error("Task: Failed to execute command",
				logger.Err(err),
				logger.String("command", command),
				logger.Any("args", args),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to execute command: %w", err)
		}

		logger.Info("Command executed successfully",
			logger.String("command", command),
			logger.Any("args", args),
			logger.Int("exit_code", resp.ExitCode),
			logger.Duration("duration", time.Since(startTime)),
		)

		return resp, nil
	}
}

// ExecuteCommandTaskWithRealTimeOutput creates a task function for command execution with real-time output
func ExecuteCommandTaskWithRealTimeOutput(svc *CommandService, progressCallback func(string, bool, int, string)) taskflow.TaskFunc {
	return func(ctx context.Context, input interface{}) (interface{}, error) {
		startTime := time.Now()

		// Convert input to command request (same as in ExecuteCommandTask)
		inputMap, ok := input.(map[string]interface{})
		if !ok {
			err := fmt.Errorf("invalid input type for command execution task: expected map, got %T", input)
			logger.Error("Task input conversion failed",
				logger.Err(err),
				logger.Any("input", input),
			)
			return nil, err
		}

		// Extract command and args (same as in ExecuteCommandTask)
		command, args, workingDir, env, timeout, taskID := extractCommandParams(inputMap)

		// Create command request
		req := CommandRequest{
			Command:    command,
			Args:       args,
			WorkingDir: workingDir,
			Env:        env,
			Timeout:    timeout,
		}

		logger.Info("Executing command task with real-time output",
			logger.String("command", command),
			logger.Any("args", args),
			logger.String("working_dir", workingDir),
		)

		// Execute command with real-time output
		outputCallback := func(line string, isStdout bool) {
			// Send output line as progress update with source indicator
			progressCallback(taskID, isStdout, 50, line) // Using 50 as a placeholder progress value
		}

		resp, err := svc.ExecuteCommandWithRealTimeOutput(ctx, req, outputCallback)
		if err != nil {
			logger.Error("Task: Failed to execute command",
				logger.Err(err),
				logger.String("command", command),
				logger.Any("args", args),
				logger.Duration("duration", time.Since(startTime)),
			)
			return nil, fmt.Errorf("failed to execute command: %w", err)
		}

		logger.Info("Command executed successfully",
			logger.String("command", command),
			logger.Any("args", args),
			logger.Int("exit_code", resp.ExitCode),
			logger.Duration("duration", time.Since(startTime)),
		)

		return resp, nil
	}
}

// Helper function to extract command parameters
func extractCommandParams(inputMap map[string]interface{}) (string, []string, string, []string, int, string) {
	// Extract command
	command, _ := inputMap["command"].(string)

	// Extract args
	var args []string
	if argsInterface, ok := inputMap["args"].([]interface{}); ok {
		args = make([]string, len(argsInterface))
		for i, a := range argsInterface {
			if argStr, ok := a.(string); ok {
				args[i] = argStr
			}
		}
	}

	// Extract working directory
	workingDir, _ := inputMap["working_dir"].(string)

	// Extract environment variables
	var env []string
	if envInterface, ok := inputMap["env"].([]interface{}); ok {
		env = make([]string, len(envInterface))
		for i, e := range envInterface {
			if envStr, ok := e.(string); ok {
				env[i] = envStr
			}
		}
	}

	// Extract timeout
	var timeout int
	if timeoutFloat, ok := inputMap["timeout"].(float64); ok {
		timeout = int(timeoutFloat)
	}

	taskID, _ := inputMap["task_id"].(string)

	return command, args, workingDir, env, timeout, taskID
}
