package taskflow

import (
	"context"
	"sync"
	"time"

	"daemon-server/internal/logger"
)

// Executor is responsible for executing tasks
type Executor struct {
	store       TaskStore
	concurrency int
	workers     chan struct{}
	wg          sync.WaitGroup
}

// NewExecutor creates a new task executor
func NewExecutor(store TaskStore, concurrency int) *Executor {
	if concurrency <= 0 {
		concurrency = 5 // Default concurrency
	}

	return &Executor{
		store:       store,
		concurrency: concurrency,
		workers:     make(chan struct{}, concurrency),
	}
}

// ExecuteTask executes a task asynchronously
func (e *Executor) ExecuteTask(ctx context.Context, task *Task) {
	e.wg.Add(1)

	go func() {
		defer e.wg.Done()

		// Acquire a worker slot
		e.workers <- struct{}{}
		defer func() {
			// Release the worker slot
			<-e.workers
		}()

		logger.Info("Starting task execution",
			logger.String("task_id", task.ID),
			logger.String("task_name", task.Name),
		)

		// Execute the task
		err := task.Execute(ctx)
		if err != nil {
			logger.Error("Task execution failed",
				logger.String("task_id", task.ID),
				logger.String("task_name", task.Name),
				logger.Err(err),
			)
		}

		// Update the task in the store
		if err := e.store.Update(task); err != nil {
			logger.Error("Failed to update task in store",
				logger.String("task_id", task.ID),
				logger.Err(err),
			)
		}
	}()
}

// ExecuteTaskWithTimeout executes a task with a timeout
func (e *Executor) ExecuteTaskWithTimeout(task *Task, timeout time.Duration) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	e.ExecuteTask(ctx, task)
}

// WaitForAll waits for all tasks to complete
func (e *Executor) WaitForAll() {
	e.wg.Wait()
}

// Shutdown gracefully shuts down the executor
func (e *Executor) Shutdown(ctx context.Context) {
	// Wait for all tasks to complete or context to be cancelled
	done := make(chan struct{})
	go func() {
		e.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		logger.Info("All tasks completed gracefully")
	case <-ctx.Done():
		logger.Warn("Shutdown timeout reached, some tasks may not have completed")
	}
}
