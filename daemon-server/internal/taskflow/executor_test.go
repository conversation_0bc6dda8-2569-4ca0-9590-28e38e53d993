package taskflow

import (
	"context"
	"sync"
	"testing"
	"time"
)

func TestExecutor(t *testing.T) {
	store := NewInMemoryTaskStore()
	executor := NewExecutor(store, 2) // Concurrency of 2

	// Create test tasks
	steps1 := []Step{
		{
			Name:        "Task 1 Step 1",
			Description: "First step of task 1",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				time.Sleep(100 * time.Millisecond)
				return nil, nil
			},
		},
	}
	task1 := NewTask("", "Task 1", "First test task", steps1, nil)
	store.Add(task1)

	steps2 := []Step{
		{
			Name:        "Task 2 Step 1",
			Description: "First step of task 2",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				time.Sleep(100 * time.Millisecond)
				return nil, nil
			},
		},
	}
	task2 := NewTask("", "Task 2", "Second test task", steps2, nil)
	store.Add(task2)

	steps3 := []Step{
		{
			Name:        "Task 3 Step 1",
			Description: "First step of task 3",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				time.Sleep(100 * time.Millisecond)
				return nil, nil
			},
		},
	}
	task3 := NewTask("", "Task 3", "Third test task", steps3, nil)
	store.Add(task3)

	// Execute tasks
	executor.ExecuteTask(context.Background(), task1)
	executor.ExecuteTask(context.Background(), task2)
	executor.ExecuteTask(context.Background(), task3)

	// Wait for all tasks to complete
	executor.WaitForAll()

	// Verify all tasks completed
	retrievedTask1, _ := store.Get(task1.ID)
	if retrievedTask1.Status != TaskStatusCompleted {
		t.Errorf("Expected task 1 status to be 'completed', got '%s'", retrievedTask1.Status)
	}

	retrievedTask2, _ := store.Get(task2.ID)
	if retrievedTask2.Status != TaskStatusCompleted {
		t.Errorf("Expected task 2 status to be 'completed', got '%s'", retrievedTask2.Status)
	}

	retrievedTask3, _ := store.Get(task3.ID)
	if retrievedTask3.Status != TaskStatusCompleted {
		t.Errorf("Expected task 3 status to be 'completed', got '%s'", retrievedTask3.Status)
	}
}

func TestExecutorWithTimeout(t *testing.T) {
	store := NewInMemoryTaskStore()
	executor := NewExecutor(store, 1)

	// Create a task that takes longer than the timeout
	steps := []Step{
		{
			Name:        "Long Step",
			Description: "A step that takes a long time",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				select {
				case <-ctx.Done():
					return nil, ctx.Err()
				case <-time.After(500 * time.Millisecond):
					return nil, nil
				}
			},
		},
	}
	task := NewTask("", "Timeout Task", "Task with timeout", steps, nil)
	store.Add(task)

	// Execute task with a short timeout
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()
	executor.ExecuteTask(ctx, task)

	// Wait for the task to complete or timeout
	executor.WaitForAll()

	// Verify task was cancelled due to timeout
	retrievedTask, _ := store.Get(task.ID)
	if retrievedTask.Status != TaskStatusFailed && retrievedTask.Status != TaskStatusCancelled {
		t.Errorf("Expected task status to be 'failed' or 'cancelled', got '%s'", retrievedTask.Status)
	}
}

func TestExecutorConcurrency(t *testing.T) {
	store := NewInMemoryTaskStore()
	executor := NewExecutor(store, 2) // Concurrency of 2

	// Create a channel to signal when a task starts
	taskStarted := make(chan struct{}, 4)

	// Use a mutex to protect the concurrent counter
	var mu sync.Mutex
	concurrentTasks := 0
	maxConcurrentTasks := 0

	// Create 4 tasks that will block until released
	taskReleaseCh := make(chan struct{})
	createBlockingTask := func(name string) *Task {
		steps := []Step{
			{
				Name:        "Blocking Step",
				Description: "A step that blocks until released",
				Func: func(ctx context.Context, input interface{}) (interface{}, error) {
					mu.Lock()
					concurrentTasks++
					if concurrentTasks > maxConcurrentTasks {
						maxConcurrentTasks = concurrentTasks
					}
					mu.Unlock()

					// Signal that the task has started
					taskStarted <- struct{}{}

					// Block until released
					<-taskReleaseCh

					mu.Lock()
					concurrentTasks--
					mu.Unlock()

					return nil, nil
				},
			},
		}
		task := NewTask("", name, "Blocking task", steps, nil)
		store.Add(task)
		return task
	}

	// Create and execute 4 tasks
	task1 := createBlockingTask("Task 1")
	task2 := createBlockingTask("Task 2")
	task3 := createBlockingTask("Task 3")
	task4 := createBlockingTask("Task 4")

	executor.ExecuteTask(context.Background(), task1)
	executor.ExecuteTask(context.Background(), task2)
	executor.ExecuteTask(context.Background(), task3)
	executor.ExecuteTask(context.Background(), task4)

	// Wait for 2 tasks to start (due to concurrency limit)
	<-taskStarted
	<-taskStarted

	// Give a little time for any additional tasks to start (which shouldn't happen)
	time.Sleep(100 * time.Millisecond)

	// Verify that at most 2 tasks were running concurrently
	if maxConcurrentTasks > 2 {
		t.Errorf("Expected at most 2 concurrent tasks, got %d", maxConcurrentTasks)
	}

	// Release all tasks
	close(taskReleaseCh)

	// Wait for all tasks to complete
	executor.WaitForAll()

	// Verify all tasks completed
	for _, taskID := range []string{task1.ID, task2.ID, task3.ID, task4.ID} {
		retrievedTask, _ := store.Get(taskID)
		if retrievedTask.Status != TaskStatusCompleted {
			t.Errorf("Expected task status to be 'completed', got '%s'", retrievedTask.Status)
		}
	}

	// Drain the channel to avoid goroutine leak
	for len(taskStarted) > 0 {
		<-taskStarted
	}
}

func TestExecutorShutdown(t *testing.T) {
	store := NewInMemoryTaskStore()
	executor := NewExecutor(store, 1)

	// Create a task that takes a long time
	longTaskDone := make(chan struct{})
	steps := []Step{
		{
			Name:        "Long Step",
			Description: "A step that takes a long time",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				select {
				case <-ctx.Done():
					return nil, ctx.Err()
				case <-time.After(500 * time.Millisecond):
					close(longTaskDone)
					return nil, nil
				}
			},
		},
	}
	task := NewTask("", "Long Task", "Task that takes a long time", steps, nil)
	store.Add(task)

	// Execute the task
	executor.ExecuteTask(context.Background(), task)

	// Shutdown with a short timeout
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()
	executor.Shutdown(ctx)

	// Verify the task is still running (not completed yet)
	select {
	case <-longTaskDone:
		t.Error("Task should not have completed before the timeout")
	default:
		// Expected behavior
	}

	// Wait for the task to actually complete
	<-longTaskDone
}
