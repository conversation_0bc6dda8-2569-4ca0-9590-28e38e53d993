package taskflow

import (
	"context"
	"fmt"
	"time"

	"daemon-server/configs"
	"daemon-server/internal/logger"
)

// Manager is responsible for managing tasks
type Manager struct {
	store    TaskStore
	executor *Executor
	config   *configs.Config
}

// NewManager creates a new task manager
func NewManager(config *configs.Config) *Manager {
	store := NewInMemoryTaskStore()
	executor := NewExecutor(store, 10) // Default concurrency of 10

	return &Manager{
		store:    store,
		executor: executor,
		config:   config,
	}
}

// CreateTask creates a new task
func (m *Manager) CreateTask(taskID, name, description string, steps []Step, input interface{}) (*Task, error) {
	task := NewTask(taskID, name, description, steps, input)

	if err := m.store.Add(task); err != nil {
		logger.Error("Failed to add task to store",
			logger.String("task_name", name),
			logger.Err(err),
		)
		return nil, err
	}

	logger.Info("Task created",
		logger.String("task_id", task.ID),
		logger.String("task_name", task.Name),
	)

	return task, nil
}

// ExecuteTask executes a task
func (m *Manager) ExecuteTask(taskID string) error {
	task, err := m.store.Get(taskID)
	if err != nil {
		logger.Error("Failed to get task",
			logger.String("task_id", taskID),
			logger.Err(err),
		)
		return err
	}

	if task.Status == TaskStatusRunning {
		return fmt.Errorf("task is already running")
	}

	// Execute the task
	m.executor.ExecuteTask(context.Background(), task)

	logger.Info("Task execution started",
		logger.String("task_id", task.ID),
		logger.String("task_name", task.Name),
	)

	return nil
}

// ExecuteTaskWithTimeout executes a task with a timeout
func (m *Manager) ExecuteTaskWithTimeout(taskID string, timeout time.Duration) error {
	task, err := m.store.Get(taskID)
	if err != nil {
		logger.Error("Failed to get task",
			logger.String("task_id", taskID),
			logger.Err(err),
		)
		return err
	}

	if task.Status == TaskStatusRunning {
		return fmt.Errorf("task is already running")
	}

	// Create a context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// Execute the task
	m.executor.ExecuteTask(ctx, task)

	logger.Info("Task execution started with timeout",
		logger.String("task_id", task.ID),
		logger.String("task_name", task.Name),
		logger.String("timeout", timeout.String()),
	)

	return nil
}

// CancelTask cancels a running task
func (m *Manager) CancelTask(taskID string) error {
	task, err := m.store.Get(taskID)
	if err != nil {
		logger.Error("Failed to get task",
			logger.String("task_id", taskID),
			logger.Err(err),
		)
		return err
	}

	if task.Status != TaskStatusRunning {
		return fmt.Errorf("task is not running")
	}

	// Cancel the task
	if err := task.Cancel(); err != nil {
		logger.Error("Failed to cancel task",
			logger.String("task_id", task.ID),
			logger.Err(err),
		)
		return err
	}

	logger.Info("Task cancelled",
		logger.String("task_id", task.ID),
		logger.String("task_name", task.Name),
	)

	return nil
}

// GetTask gets a task by ID
func (m *Manager) GetTask(taskID string) (*TaskInfo, error) {
	task, err := m.store.Get(taskID)
	if err != nil {
		return nil, err
	}

	info := task.Info()
	return &info, nil
}

// ListTasks lists all tasks
func (m *Manager) ListTasks() []TaskInfo {
	tasks := m.store.List()
	infos := make([]TaskInfo, len(tasks))

	for i, task := range tasks {
		infos[i] = task.Info()
	}

	return infos
}

// ListTasksWithFilter lists tasks matching the filter
func (m *Manager) ListTasksWithFilter(filter TaskFilter) []TaskInfo {
	tasks := m.store.ListWithFilter(filter)
	infos := make([]TaskInfo, len(tasks))

	for i, task := range tasks {
		infos[i] = task.Info()
	}

	return infos
}

// DeleteTask deletes a task
func (m *Manager) DeleteTask(taskID string) error {
	task, err := m.store.Get(taskID)
	if err != nil {
		return err
	}

	if task.Status == TaskStatusRunning {
		return fmt.Errorf("cannot delete a running task")
	}

	return m.store.Delete(taskID)
}

// UpdateTaskProgress updates the progress of a task
func (m *Manager) UpdateTaskProgress(taskID string, progress int, message string, isStdout bool) error {
	task, err := m.store.Get(taskID)
	if err != nil {
		logger.Error("Failed to get task for progress update",
			logger.String("task_id", taskID),
			logger.Err(err),
		)
		return err
	}

	// Update progress
	task.UpdateProgress(progress, message, isStdout)

	// Update task in store
	if err := m.store.Update(task); err != nil {
		logger.Error("Failed to update task in store after progress update",
			logger.String("task_id", taskID),
			logger.Err(err),
		)
		return err
	}

	return nil
}

// Shutdown gracefully shuts down the manager
func (m *Manager) Shutdown(ctx context.Context) {
	m.executor.Shutdown(ctx)
}
