package taskflow

import (
	"context"
	"testing"
	"time"

	"daemon-server/configs"
)

func TestManager(t *testing.T) {
	// Create a test config
	config := &configs.Config{}
	manager := NewManager(config)

	// Test CreateTask
	steps := []Step{
		{
			Name:        "Step 1",
			Description: "First step",
			Input:       "step input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				return "step result", nil
			},
		},
	}

	task, err := manager.CreateTask("", "Test Task", "A test task", steps, "task input")
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if task.ID == "" {
		t.Error("Task ID should not be empty")
	}
	if task.Name != "Test Task" {
		t.Errorf("Expected task name to be 'Test Task', got '%s'", task.Name)
	}

	// Test GetTask
	taskInfo, err := manager.GetTask(task.ID)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Expected no error, got %v", err)
	}
	if taskInfo.ID != task.ID {
		t.E<PERSON>rf("Expected task ID to be '%s', got '%s'", task.ID, taskInfo.ID)
	}
	if taskInfo.Status != TaskStatusPending {
		t.Errorf("Expected task status to be 'pending', got '%s'", taskInfo.Status)
	}

	// Test ExecuteTask
	err = manager.ExecuteTask(task.ID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Wait for the task to complete
	time.Sleep(100 * time.Millisecond)

	// Verify task completed
	taskInfo, err = manager.GetTask(task.ID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if taskInfo.Status != TaskStatusCompleted {
		t.Errorf("Expected task status to be 'completed', got '%s'", taskInfo.Status)
	}

	// Test ListTasks
	tasks := manager.ListTasks()
	if len(tasks) != 1 {
		t.Errorf("Expected 1 task, got %d", len(tasks))
	}
	if tasks[0].ID != task.ID {
		t.Errorf("Expected task ID to be '%s', got '%s'", task.ID, tasks[0].ID)
	}

	// Test DeleteTask
	err = manager.DeleteTask(task.ID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Verify task was deleted
	tasks = manager.ListTasks()
	if len(tasks) != 0 {
		t.Errorf("Expected 0 tasks, got %d", len(tasks))
	}
}

func TestManagerTaskCancellation(t *testing.T) {
	config := &configs.Config{}
	manager := NewManager(config)

	// Create a task with a long-running step
	longRunningDone := make(chan struct{})
	steps := []Step{
		{
			Name:        "Long Running Step",
			Description: "A step that runs for a long time",
			Input:       "long step input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				select {
				case <-ctx.Done():
					return nil, ctx.Err()
				case <-time.After(1 * time.Second):
					close(longRunningDone)
					return "long step result", nil
				}
			},
		},
	}

	task, err := manager.CreateTask("", "Long Task", "A long-running task", steps, "task input")
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Execute the task
	err = manager.ExecuteTask(task.ID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Wait for the task to start
	time.Sleep(100 * time.Millisecond)

	// Cancel the task
	err = manager.CancelTask(task.ID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Wait for the cancellation to take effect
	time.Sleep(100 * time.Millisecond)

	// Verify task was cancelled
	taskInfo, err := manager.GetTask(task.ID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if taskInfo.Status != TaskStatusCancelled {
		t.Errorf("Expected task status to be 'cancelled', got '%s'", taskInfo.Status)
	}

	// Verify the task didn't complete normally
	select {
	case <-longRunningDone:
		t.Error("Task should not have completed normally")
	default:
		// Expected behavior
	}
}

func TestManagerTaskTimeout(t *testing.T) {
	config := &configs.Config{}
	manager := NewManager(config)

	// Create a task with a long-running step
	steps := []Step{
		{
			Name:        "Long Running Step",
			Description: "A step that runs for a long time",
			Input:       "timeout step input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				select {
				case <-ctx.Done():
					return nil, ctx.Err()
				case <-time.After(500 * time.Millisecond):
					return "timeout step result", nil
				}
			},
		},
	}

	task, err := manager.CreateTask("", "Timeout Task", "A task with timeout", steps, "task input")
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Execute the task with a short timeout
	err = manager.ExecuteTaskWithTimeout(task.ID, 100*time.Millisecond)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Wait for the timeout to occur
	time.Sleep(200 * time.Millisecond)

	// Verify task was cancelled due to timeout
	taskInfo, err := manager.GetTask(task.ID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if taskInfo.Status != TaskStatusFailed && taskInfo.Status != TaskStatusCancelled {
		t.Errorf("Expected task status to be 'failed' or 'cancelled', got '%s'", taskInfo.Status)
	}
}

func TestManagerListTasksWithFilter(t *testing.T) {
	config := &configs.Config{}
	manager := NewManager(config)

	// Create tasks with different statuses
	createTask := func(name string, status TaskStatus) *Task {
		steps := []Step{
			{
				Name:        "Step",
				Description: "Test step",
				Input:       "filter test input",
				Func: func(ctx context.Context, input interface{}) (interface{}, error) {
					return "filter test result", nil
				},
			},
		}
		task, _ := manager.CreateTask("", name, "Test task", steps, "filter task input")

		// Manually update the task status for testing
		if status != TaskStatusPending {
			task.Status = status
			// Update the task in the store
			manager.store.(*InMemoryTaskStore).Update(task)
		}

		return task
	}

	// Create tasks with different statuses
	_ = createTask("Task 1", TaskStatusPending)
	task2 := createTask("Task 2", TaskStatusRunning)
	_ = createTask("Task 3", TaskStatusCompleted)
	_ = createTask("Task 4", TaskStatusFailed)

	// Test filter by status
	filter := TaskFilter{
		Status: TaskStatusRunning,
	}
	tasks := manager.ListTasksWithFilter(filter)
	if len(tasks) != 1 {
		t.Errorf("Expected 1 task, got %d", len(tasks))
	}
	if len(tasks) > 0 && tasks[0].ID != task2.ID {
		t.Errorf("Expected task ID to be '%s', got '%s'", task2.ID, tasks[0].ID)
	}

	// Test filter by time range
	now := time.Now()
	pastTime := now.Add(-1 * time.Hour)
	futureTime := now.Add(1 * time.Hour)

	filter = TaskFilter{
		StartTime: pastTime,
		EndTime:   futureTime,
	}
	tasks = manager.ListTasksWithFilter(filter)
	if len(tasks) != 4 {
		t.Errorf("Expected 4 tasks, got %d", len(tasks))
	}
}
