package taskflow

import (
	"context"
	"time"
)



// ProgressInfo represents progress information for a task
type ProgressInfo struct {
	Progress int    `json:"progress"`
	Message  string `json:"message,omitempty"`
}

// StepOutput represents output information for a task step
type StepOutput struct {
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Status      TaskStatus `json:"status"`
	Result      interface{} `json:"result,omitempty"`
	Error       string     `json:"error,omitempty"`
	StartedAt   *time.Time `json:"started_at,omitempty"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
}

// TaskOutput represents output information for a task
type TaskOutput struct {
	Status      TaskStatus   `json:"status"`
	Progress    int          `json:"progress"`
	Result      interface{}  `json:"result,omitempty"`
	Error       string       `json:"error,omitempty"`
	StartedAt   *time.Time   `json:"started_at,omitempty"`
	CompletedAt *time.Time   `json:"completed_at,omitempty"`
	Steps       []StepOutput `json:"steps,omitempty"`
}

// MockManager is a mock implementation of the Manager struct for testing
type MockManager struct {
	// Add fields as needed
}

// NewMockManager creates a new mock manager
func NewMockManager() *Manager {
	return &Manager{}
}

// CreateTask creates a new task
func (m *MockManager) CreateTask(name, description string, steps []Step, input interface{}) (*Task, error) {
	// Create a mock task
	return &Task{
		ID:          "mock-task-id",
		Name:        name,
		Description: description,
		Status:      TaskStatusPending,
		Steps:       steps,
		Input:       input,
		CreatedAt:   time.Now(),
	}, nil
}

// ExecuteTask executes a task
func (m *MockManager) ExecuteTask(taskID string) error {
	// Mock implementation
	return nil
}

// GetTask gets a task by ID
func (m *MockManager) GetTask(taskID string) (*Task, error) {
	// Mock implementation
	return &Task{
		ID:          taskID,
		Name:        "Mock Task",
		Description: "Mock task description",
		Status:      TaskStatusRunning,
		CreatedAt:   time.Now(),
	}, nil
}

// ListTasks lists tasks
func (m *MockManager) ListTasks() ([]*Task, error) {
	// Mock implementation
	return []*Task{
		{
			ID:          "mock-task-id",
			Name:        "Mock Task",
			Description: "Mock task description",
			Status:      TaskStatusRunning,
			CreatedAt:   time.Now(),
		},
	}, nil
}

// CancelTask cancels a task
func (m *MockManager) CancelTask(taskID string) error {
	// Mock implementation
	return nil
}

// GetTaskProgress gets task progress
func (m *MockManager) GetTaskProgress(taskID string) (*ProgressInfo, error) {
	// Mock implementation
	return &ProgressInfo{
		Progress: 50,
		Message:  "Mock progress",
	}, nil
}

// GetTaskOutput gets task output
func (m *MockManager) GetTaskOutput(taskID string) (*TaskOutput, error) {
	// Mock implementation
	return &TaskOutput{
		Status:   TaskStatusRunning,
		Progress: 50,
		Result:   map[string]interface{}{"key": "value"},
		Error:    "",
		Steps:    []StepOutput{},
	}, nil
}

// Start starts the manager
func (m *MockManager) Start(ctx context.Context) error {
	// Mock implementation
	return nil
}

// Stop stops the manager
func (m *MockManager) Stop() error {
	// Mock implementation
	return nil
}
