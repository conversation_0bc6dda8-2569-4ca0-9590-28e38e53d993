package taskflow

import (
	"fmt"
	"sync"

	"daemon-server/internal/logger"
)

// TaskStore is an interface for storing and retrieving tasks
type TaskStore interface {
	// Add adds a task to the store
	Add(task *Task) error
	// Get retrieves a task by ID
	Get(id string) (*Task, error)
	// List retrieves all tasks
	List() []*Task
	// ListWithFilter retrieves tasks matching the filter
	ListWithFilter(filter TaskFilter) []*Task
	// Update updates a task in the store
	Update(task *Task) error
	// Delete deletes a task from the store
	Delete(id string) error
}

// InMemoryTaskStore is an in-memory implementation of TaskStore
type InMemoryTaskStore struct {
	tasks map[string]*Task
	mu    sync.RWMutex
}

// NewInMemoryTaskStore creates a new in-memory task store
func NewInMemoryTaskStore() *InMemoryTaskStore {
	return &InMemoryTaskStore{
		tasks: make(map[string]*Task),
	}
}

// Add adds a task to the store
func (s *InMemoryTaskStore) Add(task *Task) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if _, exists := s.tasks[task.ID]; exists {
		return fmt.Errorf("task with ID %s already exists", task.ID)
	}

	s.tasks[task.ID] = task
	logger.Debug("Task added to store",
		logger.String("task_id", task.ID),
		logger.String("task_name", task.Name),
	)
	return nil
}

// Get retrieves a task by ID
func (s *InMemoryTaskStore) Get(id string) (*Task, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	task, exists := s.tasks[id]
	if !exists {
		return nil, fmt.Errorf("task with ID %s not found", id)
	}

	return task, nil
}

// List retrieves all tasks
func (s *InMemoryTaskStore) List() []*Task {
	s.mu.RLock()
	defer s.mu.RUnlock()

	tasks := make([]*Task, 0, len(s.tasks))
	for _, task := range s.tasks {
		tasks = append(tasks, task)
	}

	return tasks
}

// ListWithFilter retrieves tasks matching the filter
func (s *InMemoryTaskStore) ListWithFilter(filter TaskFilter) []*Task {
	s.mu.RLock()
	defer s.mu.RUnlock()

	tasks := make([]*Task, 0)
	for _, task := range s.tasks {
		if matchesFilter(task, filter) {
			tasks = append(tasks, task)
		}
	}

	return tasks
}

// Update updates a task in the store
func (s *InMemoryTaskStore) Update(task *Task) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if _, exists := s.tasks[task.ID]; !exists {
		return fmt.Errorf("task with ID %s not found", task.ID)
	}

	s.tasks[task.ID] = task
	logger.Debug("Task updated in store",
		logger.String("task_id", task.ID),
		logger.String("task_name", task.Name),
		logger.String("task_status", string(task.Status)),
	)
	return nil
}

// Delete deletes a task from the store
func (s *InMemoryTaskStore) Delete(id string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if _, exists := s.tasks[id]; !exists {
		return fmt.Errorf("task with ID %s not found", id)
	}

	delete(s.tasks, id)
	logger.Debug("Task deleted from store", logger.String("task_id", id))
	return nil
}

// matchesFilter checks if a task matches the given filter
func matchesFilter(task *Task, filter TaskFilter) bool {
	// Check status
	if filter.Status != "" && task.Status != filter.Status {
		return false
	}

	// Check time range
	if !filter.StartTime.IsZero() {
		if task.CreatedAt.Before(filter.StartTime) {
			return false
		}
	}

	if !filter.EndTime.IsZero() {
		if task.CreatedAt.After(filter.EndTime) {
			return false
		}
	}

	return true
}
