package taskflow

import (
	"context"
	"testing"
	"time"
)

func TestInMemoryTaskStore(t *testing.T) {
	store := NewInMemoryTaskStore()

	// Create a test task
	steps := []Step{
		{
			Name:        "Step 1",
			Description: "First step",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				return nil, nil
			},
		},
	}
	task := NewTask("", "Test Task", "A test task", steps, nil)

	// Test Add
	err := store.Add(task)
	if err != nil {
		t.<PERSON>rrorf("Expected no error, got %v", err)
	}

	// Test adding duplicate task
	err = store.Add(task)
	if err == nil {
		t.Error("Expected error when adding duplicate task, got nil")
	}

	// Test Get
	retrievedTask, err := store.Get(task.ID)
	if err != nil {
		t.<PERSON><PERSON>rf("Expected no error, got %v", err)
	}
	if retrievedTask.ID != task.ID {
		t.<PERSON>("Expected task ID to be '%s', got '%s'", task.ID, retrievedTask.ID)
	}

	// Test Get with non-existent ID
	_, err = store.Get("non-existent-id")
	if err == nil {
		t.Error("Expected error when getting non-existent task, got nil")
	}

	// Test List
	tasks := store.List()
	if len(tasks) != 1 {
		t.Errorf("Expected 1 task, got %d", len(tasks))
	}
	if tasks[0].ID != task.ID {
		t.Errorf("Expected task ID to be '%s', got '%s'", task.ID, tasks[0].ID)
	}

	// Test Update
	task.Status = TaskStatusRunning
	err = store.Update(task)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Verify update
	retrievedTask, err = store.Get(task.ID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if retrievedTask.Status != TaskStatusRunning {
		t.Errorf("Expected task status to be 'running', got '%s'", retrievedTask.Status)
	}

	// Test Update with non-existent ID
	nonExistentTask := NewTask("", "Non-existent", "A non-existent task", steps, nil)
	err = store.Update(nonExistentTask)
	if err == nil {
		t.Error("Expected error when updating non-existent task, got nil")
	}

	// Test Delete
	err = store.Delete(task.ID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Verify deletion
	tasks = store.List()
	if len(tasks) != 0 {
		t.Errorf("Expected 0 tasks, got %d", len(tasks))
	}

	// Test Delete with non-existent ID
	err = store.Delete("non-existent-id")
	if err == nil {
		t.Error("Expected error when deleting non-existent task, got nil")
	}
}

func TestListWithFilter(t *testing.T) {
	store := NewInMemoryTaskStore()

	// Create test tasks with different statuses
	steps := []Step{
		{
			Name:        "Step 1",
			Description: "First step",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				return nil, nil
			},
		},
	}

	// Task 1: Pending
	task1 := NewTask("", "Task 1", "Pending task", steps, nil)
	store.Add(task1)

	// Task 2: Running
	task2 := NewTask("", "Task 2", "Running task", steps, nil)
	task2.Status = TaskStatusRunning
	store.Add(task2)

	// Task 3: Completed
	task3 := NewTask("", "Task 3", "Completed task", steps, nil)
	task3.Status = TaskStatusCompleted
	store.Add(task3)

	// Test filter by status
	filter := TaskFilter{
		Status: TaskStatusRunning,
	}
	tasks := store.ListWithFilter(filter)
	if len(tasks) != 1 {
		t.Errorf("Expected 1 task, got %d", len(tasks))
	}
	if len(tasks) > 0 && tasks[0].Status != TaskStatusRunning {
		t.Errorf("Expected task status to be 'running', got '%s'", tasks[0].Status)
	}

	// Test filter by time range
	now := time.Now()
	pastTime := now.Add(-1 * time.Hour)
	futureTime := now.Add(1 * time.Hour)

	filter = TaskFilter{
		StartTime: pastTime,
		EndTime:   futureTime,
	}
	tasks = store.ListWithFilter(filter)
	if len(tasks) != 3 {
		t.Errorf("Expected 3 tasks, got %d", len(tasks))
	}

	// Test filter with no matches
	filter = TaskFilter{
		StartTime: futureTime,
	}
	tasks = store.ListWithFilter(filter)
	if len(tasks) != 0 {
		t.Errorf("Expected 0 tasks, got %d", len(tasks))
	}
}
