package taskflow

import (
	"context"
	"daemon-server/pkg/utils"
	"fmt"
	"sync"
	"time"

	"daemon-server/internal/logger"
)

// TaskFunc represents a function that can be executed as a task step
type TaskFunc func(ctx context.Context, input interface{}) (interface{}, error)

// Step represents a step in a task
type Step struct {
	Name        string
	Description string
	Func        TaskFunc
	Input       interface{}
}

// Task represents a task that can be executed
type Task struct {
	ID          string
	Name        string
	Description string
	Steps       []Step
	Status      TaskStatus
	CreatedAt   time.Time
	StartedAt   *time.Time
	CompletedAt *time.Time
	Error       string
	Progress    int
	Result      interface{}
	Input       interface{}
	mu          sync.RWMutex
	stepInfos   []StepInfo
	ctx         context.Context
	cancel      context.CancelFunc
	Stdout      []string
	Stderr      []string
}

// NewTask creates a new task
func NewTask(taskID, name, description string, steps []Step, input interface{}) *Task {
	if taskID == "" {
		taskID = utils.GetUuidString()
	}

	stepInfos := make([]StepInfo, len(steps))
	for i, step := range steps {
		stepInfos[i] = StepInfo{
			Name:        step.Name,
			Description: step.Description,
			Status:      TaskStatusPending,
			Input:       step.Input,
		}
	}

	return &Task{
		ID:          taskID,
		Name:        name,
		Description: description,
		Steps:       steps,
		Status:      TaskStatusPending,
		CreatedAt:   time.Now(),
		Progress:    0,
		stepInfos:   stepInfos,
		Input:       input,
		Stderr:      make([]string, 0),
		Stdout:      make([]string, 0),
	}
}

// Info returns information about the task
func (t *Task) Info() TaskInfo {
	t.mu.RLock()
	defer t.mu.RUnlock()

	return TaskInfo{
		ID:          t.ID,
		Name:        t.Name,
		Description: t.Description,
		Status:      t.Status,
		CreatedAt:   t.CreatedAt,
		StartedAt:   t.StartedAt,
		CompletedAt: t.CompletedAt,
		Error:       t.Error,
		Progress:    t.Progress,
		Steps:       t.stepInfos,
		Result:      t.Result,
		Input:       t.Input,
		Stdout:      t.Stdout,
		Stderr:      t.Stderr,
	}
}

// Execute executes the task with the given context
func (t *Task) Execute(ctx context.Context) error {
	t.mu.Lock()
	if t.Status == TaskStatusRunning {
		t.mu.Unlock()
		return fmt.Errorf("task is already running")
	}

	// Create a new context with cancel function
	t.ctx, t.cancel = context.WithCancel(ctx)
	t.Status = TaskStatusRunning
	now := time.Now()
	t.StartedAt = &now
	t.mu.Unlock()

	logger.Info("Starting task execution",
		logger.String("task_id", t.ID),
		logger.String("task_name", t.Name),
	)

	// Execute each step
	for i, step := range t.Steps {
		// Check if context is cancelled
		select {
		case <-t.ctx.Done():
			t.markAsCancelled()
			return t.ctx.Err()
		default:
			// Continue execution
		}

		t.mu.Lock()
		t.stepInfos[i].Status = TaskStatusRunning
		stepStartTime := time.Now()
		t.stepInfos[i].StartedAt = &stepStartTime
		t.mu.Unlock()

		logger.Info("Executing task step",
			logger.String("task_id", t.ID),
			logger.String("step_name", step.Name),
			logger.Int("step_index", i),
		)

		// Execute the step with input
		result, err := step.Func(t.ctx, step.Input)

		t.mu.Lock()
		stepEndTime := time.Now()
		t.stepInfos[i].CompletedAt = &stepEndTime

		// Store the result regardless of error
		t.stepInfos[i].Result = result

		if err != nil {
			t.stepInfos[i].Status = TaskStatusFailed
			t.stepInfos[i].Error = err.Error()

			// Check if the error is due to context cancellation
			if err == context.Canceled {
				t.Status = TaskStatusCancelled
				t.Error = "task was cancelled"
			} else {
				t.Status = TaskStatusFailed
				t.Error = fmt.Sprintf("step %s failed: %v", step.Name, err)
			}

			t.CompletedAt = &stepEndTime
			t.mu.Unlock()

			if t.Status == TaskStatusCancelled {
				logger.Info("Task step cancelled",
					logger.String("task_id", t.ID),
					logger.String("step_name", step.Name),
					logger.Int("step_index", i),
				)
			} else {
				logger.Error("Task step failed",
					logger.String("task_id", t.ID),
					logger.String("step_name", step.Name),
					logger.Int("step_index", i),
					logger.Err(err),
				)
			}

			return err
		}

		t.stepInfos[i].Status = TaskStatusCompleted
		t.Progress = int((float64(i+1) / float64(len(t.Steps))) * 100)
		t.mu.Unlock()

		logger.Info("Task step completed",
			logger.String("task_id", t.ID),
			logger.String("step_name", step.Name),
			logger.Int("step_index", i),
			logger.Int("progress", t.Progress),
		)
	}

	// Mark task as completed
	t.mu.Lock()
	t.Status = TaskStatusCompleted
	now = time.Now()
	t.CompletedAt = &now
	t.Progress = 100

	// If there are steps, use the result of the last step as the task result
	if len(t.Steps) > 0 {
		lastStepIndex := len(t.Steps) - 1
		t.Result = t.stepInfos[lastStepIndex].Result
		t.ClearStdout(true, true)
	}

	t.mu.Unlock()

	logger.Info("Task execution completed",
		logger.String("task_id", t.ID),
		logger.String("task_name", t.Name),
	)

	return nil
}

// Cancel cancels the task
func (t *Task) Cancel() error {
	t.mu.Lock()
	defer t.mu.Unlock()

	if t.Status != TaskStatusRunning {
		return fmt.Errorf("task is not running")
	}

	if t.cancel != nil {
		t.cancel()
	}

	logger.Info("Task cancelled",
		logger.String("task_id", t.ID),
		logger.String("task_name", t.Name),
	)

	return nil
}

// markAsCancelled marks the task as cancelled
func (t *Task) markAsCancelled() {
	t.mu.Lock()
	defer t.mu.Unlock()

	t.Status = TaskStatusCancelled
	now := time.Now()
	t.CompletedAt = &now
	t.Error = "task was cancelled"

	logger.Info("Task marked as cancelled",
		logger.String("task_id", t.ID),
		logger.String("task_name", t.Name),
	)
}

// UpdateProgress updates the progress of the task
func (t *Task) UpdateProgress(progress int, message string, isStdout bool) {
	t.mu.Lock()
	defer t.mu.Unlock()

	// Ensure progress is between 0 and 100
	if progress < 0 {
		progress = 0
	} else if progress > 100 {
		progress = 100
	}

	t.Progress = progress

	// Store the message if it's not empty
	if message != "" {
		if isStdout {
			t.Stdout = append(t.Stdout, message)
		} else {
			t.Stderr = append(t.Stderr, message)
		}
	}

	logger.Debug("Task progress updated",
		logger.String("task_id", t.ID),
		logger.String("task_name", t.Name),
		logger.Int("progress", progress),
		logger.String("message", message),
	)
}

func (t *Task) ClearStdout(isStdout bool, isStderr bool) {
	if isStdout {
		t.Stdout = make([]string, 0)
	}
	if isStderr {
		t.Stderr = make([]string, 0)
	}
}
