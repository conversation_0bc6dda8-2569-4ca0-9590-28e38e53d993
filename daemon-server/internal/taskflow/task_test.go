package taskflow

import (
	"context"
	"errors"
	"testing"
	"time"
)

func TestNewTask(t *testing.T) {
	steps := []Step{
		{
			Name:        "Step 1",
			Description: "First step",
			Input:       "step1 input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				return "step1 result", nil
			},
		},
		{
			Name:        "Step 2",
			Description: "Second step",
			Input:       "step2 input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				return "step2 result", nil
			},
		},
	}

	task := NewTask("", "Test Task", "A test task", steps, "task input")

	if task.ID == "" {
		t.<PERSON>rror("Task ID should not be empty")
	}

	if task.Name != "Test Task" {
		t.<PERSON>rrorf("Expected task name to be 'Test Task', got '%s'", task.Name)
	}

	if task.Description != "A test task" {
		t.<PERSON><PERSON><PERSON>("Expected task description to be 'A test task', got '%s'", task.Description)
	}

	if task.Status != TaskStatusPending {
		t.<PERSON>("Expected task status to be 'pending', got '%s'", task.Status)
	}

	if len(task.Steps) != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 steps, got %d", len(task.Steps))
	}

	if task.Progress != 0 {
		t.Errorf("Expected progress to be 0, got %d", task.Progress)
	}
}

func TestTaskExecute(t *testing.T) {
	executed := make([]bool, 2)
	results := make([]interface{}, 2)
	steps := []Step{
		{
			Name:        "Step 1",
			Description: "First step",
			Input:       "step1 input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				executed[0] = true
				results[0] = "step1 result"
				return results[0], nil
			},
		},
		{
			Name:        "Step 2",
			Description: "Second step",
			Input:       "step2 input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				executed[1] = true
				results[1] = "step2 result"
				return results[1], nil
			},
		},
	}

	task := NewTask("", "Test Task", "A test task", steps, "task input")
	err := task.Execute(context.Background())

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if task.Status != TaskStatusCompleted {
		t.Errorf("Expected task status to be 'completed', got '%s'", task.Status)
	}

	if task.Progress != 100 {
		t.Errorf("Expected progress to be 100, got %d", task.Progress)
	}

	if !executed[0] || !executed[1] {
		t.Error("Not all steps were executed")
	}

	// Check task result
	if task.Result != "step2 result" {
		t.Errorf("Expected task result to be 'step2 result', got '%v'", task.Result)
	}

	// Check step results
	info := task.Info()
	if info.Steps[0].Result != "step1 result" {
		t.Errorf("Expected step 1 result to be 'step1 result', got '%v'", info.Steps[0].Result)
	}
	if info.Steps[1].Result != "step2 result" {
		t.Errorf("Expected step 2 result to be 'step2 result', got '%v'", info.Steps[1].Result)
	}
}

func TestTaskExecuteWithError(t *testing.T) {
	expectedErr := errors.New("step error")
	executed := make([]bool, 2)
	results := make([]interface{}, 2)
	steps := []Step{
		{
			Name:        "Step 1",
			Description: "First step",
			Input:       "step1 input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				executed[0] = true
				results[0] = "step1 result"
				return results[0], nil
			},
		},
		{
			Name:        "Step 2",
			Description: "Second step",
			Input:       "step2 input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				executed[1] = true
				results[1] = "step2 result"
				return results[1], expectedErr
			},
		},
	}

	task := NewTask("", "Test Task", "A test task", steps, "task input")
	err := task.Execute(context.Background())

	if err != expectedErr {
		t.Errorf("Expected error '%v', got '%v'", expectedErr, err)
	}

	if task.Status != TaskStatusFailed {
		t.Errorf("Expected task status to be 'failed', got '%s'", task.Status)
	}

	if !executed[0] || !executed[1] {
		t.Error("Not all steps were executed")
	}
}

func TestTaskCancel(t *testing.T) {
	longRunningStep := make(chan struct{})
	steps := []Step{
		{
			Name:        "Long Running Step",
			Description: "A step that runs for a long time",
			Input:       "long step input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				select {
				case <-ctx.Done():
					return nil, ctx.Err()
				case <-longRunningStep:
					return "long step result", nil
				}
			},
		},
	}

	task := NewTask("", "Test Task", "A test task", steps, "task input")

	// Execute the task in a goroutine
	go func() {
		_ = task.Execute(context.Background())
	}()

	// Wait for the task to start
	time.Sleep(100 * time.Millisecond)

	// Cancel the task
	err := task.Cancel()
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Wait for the task to be cancelled
	time.Sleep(100 * time.Millisecond)

	if task.Status != TaskStatusCancelled {
		t.Errorf("Expected task status to be 'cancelled', got '%s'", task.Status)
	}

	// Close the channel to avoid goroutine leak
	close(longRunningStep)
}

func TestTaskInfo(t *testing.T) {
	steps := []Step{
		{
			Name:        "Step 1",
			Description: "First step",
			Input:       "step input",
			Func: func(ctx context.Context, input interface{}) (interface{}, error) {
				return "step result", nil
			},
		},
	}

	task := NewTask("", "Test Task", "A test task", steps, "task input")
	// Set a result for testing
	task.Result = "task result"
	info := task.Info()

	if info.ID != task.ID {
		t.Errorf("Expected info ID to be '%s', got '%s'", task.ID, info.ID)
	}

	if info.Name != task.Name {
		t.Errorf("Expected info name to be '%s', got '%s'", task.Name, info.Name)
	}

	if info.Description != task.Description {
		t.Errorf("Expected info description to be '%s', got '%s'", task.Description, info.Description)
	}

	if info.Status != task.Status {
		t.Errorf("Expected info status to be '%s', got '%s'", task.Status, info.Status)
	}

	if len(info.Steps) != len(task.Steps) {
		t.Errorf("Expected %d steps, got %d", len(task.Steps), len(info.Steps))
	}

	// Check result and input
	if info.Result != "task result" {
		t.Errorf("Expected info result to be 'task result', got '%v'", info.Result)
	}

	if info.Input != "task input" {
		t.Errorf("Expected info input to be 'task input', got '%v'", info.Input)
	}
}
