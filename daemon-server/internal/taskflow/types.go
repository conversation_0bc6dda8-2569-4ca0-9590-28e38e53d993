package taskflow

import (
	"time"
)

// TaskStatus represents the status of a task
type TaskStatus string

const (
	// TaskStatusPending indicates the task is pending execution
	TaskStatusPending TaskStatus = "pending"
	// TaskStatusRunning indicates the task is currently running
	TaskStatusRunning TaskStatus = "running"
	// TaskStatusCompleted indicates the task has completed successfully
	TaskStatusCompleted TaskStatus = "completed"
	// TaskStatusFailed indicates the task has failed
	TaskStatusFailed TaskStatus = "failed"
	// TaskStatusCancelled indicates the task was cancelled
	TaskStatusCancelled TaskStatus = "cancelled"
)

// TaskInfo represents information about a task
type TaskInfo struct {
	ID          string      `json:"id"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Status      TaskStatus  `json:"status"`
	CreatedAt   time.Time   `json:"created_at"`
	StartedAt   *time.Time  `json:"started_at,omitempty"`
	CompletedAt *time.Time  `json:"completed_at,omitempty"`
	Error       string      `json:"error,omitempty"`
	Progress    int         `json:"progress"`
	Steps       []StepInfo  `json:"steps"`
	Result      interface{} `json:"result,omitempty"`
	Input       interface{} `json:"input,omitempty"`
	Stdout      []string    `json:"stdout,omitempty"`
	Stderr      []string    `json:"stderr,omitempty"`
}

// StepInfo represents information about a step in a task
type StepInfo struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Status      TaskStatus  `json:"status"`
	StartedAt   *time.Time  `json:"started_at,omitempty"`
	CompletedAt *time.Time  `json:"completed_at,omitempty"`
	Error       string      `json:"error,omitempty"`
	Result      interface{} `json:"result,omitempty"`
	Input       interface{} `json:"input,omitempty"`
}

// TaskResult represents the result of a task execution
type TaskResult struct {
	Success bool        `json:"success"`
	Error   string      `json:"error,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// StepResult represents the result of a step execution
type StepResult struct {
	Success bool        `json:"success"`
	Error   string      `json:"error,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// TaskFilter represents filters for querying tasks
type TaskFilter struct {
	Status    TaskStatus `json:"status,omitempty"`
	StartTime time.Time  `json:"start_time,omitempty"`
	EndTime   time.Time  `json:"end_time,omitempty"`
}
