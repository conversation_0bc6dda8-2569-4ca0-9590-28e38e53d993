package command

import (
	"bufio"
	"bytes"
	"context"
	"fmt"
	"os/exec"
	"strings"
	"syscall"
	"time"
)

// Request represents a command execution request
type Request struct {
	Command    string   `json:"command"`               // Command to execute
	Args       []string `json:"args"`                  // Command arguments
	WorkingDir string   `json:"working_dir,omitempty"` // Working directory
	Env        []string `json:"env,omitempty"`         // Environment variables
	Timeout    int      `json:"timeout,omitempty"`     // Timeout in seconds
}

// Response represents the result of a command execution
type Response struct {
	Command   string    `json:"command"`         // Command that was executed
	ExitCode  int       `json:"exit_code"`       // Exit code of the command
	Output    string    `json:"output"`          // Combined stdout and stderr
	Stdout    string    `json:"stdout"`          // Standard output
	Stderr    string    `json:"stderr"`          // Standard error
	Error     string    `json:"error,omitempty"` // Error message if any
	StartTime time.Time `json:"start_time"`      // When the command started
	EndTime   time.Time `json:"end_time"`        // When the command completed
	Duration  string    `json:"duration"`        // Duration of execution
}

// Execute runs a command and returns the combined output
func Execute(req Request) (*Response, error) {
	// Create context with timeout if specified
	ctx := context.Background()
	if req.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(req.Timeout)*time.Second)
		defer cancel()
	}

	return ExecuteWithContext(ctx, req)
}

// ExecuteWithContext runs a command with the given context and returns the combined output
func ExecuteWithContext(ctx context.Context, req Request) (*Response, error) {
	startTime := time.Now()

	// Create command
	cmd := exec.CommandContext(ctx, req.Command, req.Args...)

	// Set working directory if specified
	if req.WorkingDir != "" {
		cmd.Dir = req.WorkingDir
	}

	// Set environment variables if specified
	if len(req.Env) > 0 {
		cmd.Env = req.Env
	}

	// Create buffers for stdout and stderr
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// Execute command
	err := cmd.Run()

	// Get command output
	stdoutStr := stdout.String()
	stderrStr := stderr.String()
	output := stdoutStr
	if stderrStr != "" {
		if output != "" {
			output += "\n"
		}
		output += stderrStr
	}

	// Get exit code
	exitCode := 0
	if err != nil {
		// Try to get exit code
		if exitErr, ok := err.(*exec.ExitError); ok {
			if status, ok := exitErr.Sys().(syscall.WaitStatus); ok {
				exitCode = status.ExitStatus()
			}
		}
	}

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// Create response
	resp := &Response{
		Command:   req.Command + " " + strings.Join(req.Args, " "),
		ExitCode:  exitCode,
		Output:    output,
		Stdout:    stdoutStr,
		Stderr:    stderrStr,
		StartTime: startTime,
		EndTime:   endTime,
		Duration:  duration.String(),
	}

	if err != nil {
		resp.Error = err.Error()
		return resp, fmt.Errorf("command execution failed: %w", err)
	}

	return resp, nil
}

// ExecuteWithOutput runs a command and returns separate stdout and stderr
func ExecuteWithOutput(req Request) (*Response, error) {
	// This is just a wrapper around ExecuteWithContext which already provides separate stdout/stderr
	return Execute(req)
}

// ExecuteWithRealTimeOutput executes a command and streams output in real-time
func ExecuteWithRealTimeOutput(ctx context.Context, req Request, outputCallback func(string)) (*Response, error) {
	startTime := time.Now()

	// Create command
	cmd := exec.CommandContext(ctx, req.Command, req.Args...)

	// Set working directory if specified
	if req.WorkingDir != "" {
		cmd.Dir = req.WorkingDir
	}

	// Set environment variables if specified
	if len(req.Env) > 0 {
		cmd.Env = req.Env
	}

	// Create pipes for stdout and stderr
	stdoutPipe, err := cmd.StdoutPipe()
	if err != nil {
		return nil, fmt.Errorf("failed to create stdout pipe: %w", err)
	}

	stderrPipe, err := cmd.StderrPipe()
	if err != nil {
		return nil, fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	// Start command
	if err := cmd.Start(); err != nil {
		return nil, fmt.Errorf("failed to start command: %w", err)
	}

	// Collect all output for the final response
	var allOutput strings.Builder
	var stdoutOutput strings.Builder
	var stderrOutput strings.Builder

	// Process stdout in real-time
	go func() {
		scanner := bufio.NewScanner(stdoutPipe)
		for scanner.Scan() {
			line := scanner.Text()
			allOutput.WriteString(line + "\n")
			stdoutOutput.WriteString(line + "\n")
			outputCallback(line)
		}
	}()

	// Process stderr in real-time
	go func() {
		scanner := bufio.NewScanner(stderrPipe)
		for scanner.Scan() {
			line := scanner.Text()
			allOutput.WriteString(line + "\n")
			stderrOutput.WriteString(line + "\n")
			outputCallback(line)
		}
	}()

	// Wait for command to complete
	err = cmd.Wait()

	// Get exit code
	exitCode := 0
	if err != nil {
		// Try to get exit code
		if exitErr, ok := err.(*exec.ExitError); ok {
			if status, ok := exitErr.Sys().(syscall.WaitStatus); ok {
				exitCode = status.ExitStatus()
			}
		}
	}

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// Create response
	resp := &Response{
		Command:   req.Command + " " + strings.Join(req.Args, " "),
		ExitCode:  exitCode,
		Output:    allOutput.String(),
		Stdout:    stdoutOutput.String(),
		Stderr:    stderrOutput.String(),
		StartTime: startTime,
		EndTime:   endTime,
		Duration:  duration.String(),
	}

	if err != nil {
		resp.Error = err.Error()
		return resp, fmt.Errorf("command execution failed: %w", err)
	}

	return resp, nil
}

// ExecuteInBackground runs a command in the background and returns immediately
func ExecuteInBackground(req Request) (*exec.Cmd, error) {
	// Create command
	cmd := exec.Command(req.Command, req.Args...)

	// Set working directory if specified
	if req.WorkingDir != "" {
		cmd.Dir = req.WorkingDir
	}

	// Set environment variables if specified
	if len(req.Env) > 0 {
		cmd.Env = req.Env
	}

	// Start command
	if err := cmd.Start(); err != nil {
		return nil, fmt.Errorf("failed to start command: %w", err)
	}

	return cmd, nil
}
