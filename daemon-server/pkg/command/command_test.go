package command

import (
	"context"
	"strings"
	"testing"
	"time"
)

func TestExecute(t *testing.T) {
	// Test successful command execution
	req := Request{
		Command: "echo",
		Args:    []string{"hello", "world"},
	}

	resp, err := Execute(req)
	if err != nil {
		t.Fatalf("Execute failed: %v", err)
	}

	if resp.ExitCode != 0 {
		t.<PERSON><PERSON>("Expected exit code 0, got %d", resp.ExitCode)
	}

	expectedOutput := "hello world"
	if !strings.Contains(resp.Output, expectedOutput) {
		t.<PERSON>rrorf("Expected output to contain '%s', got '%s'", expectedOutput, resp.Output)
	}

	// Test command with error
	req = Request{
		Command: "ls",
		Args:    []string{"/nonexistent/directory"},
	}

	resp, err = Execute(req)
	if err == nil {
		t.Error("Expected error for nonexistent directory, got nil")
	}

	if resp.ExitCode == 0 {
		t.<PERSON><PERSON><PERSON>("Expected non-zero exit code, got %d", resp.ExitCode)
	}

	if resp.Error == "" {
		t.<PERSON>r("Expected error message, got empty string")
	}
}

func TestExecuteWithContext(t *testing.T) {
	// Test timeout
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	req := Request{
		Command: "sleep",
		Args:    []string{"5"},
	}

	resp, err := ExecuteWithContext(ctx, req)
	if err == nil {
		t.Error("Expected error for timeout, got nil")
	}

	if resp.ExitCode == 0 {
		t.Errorf("Expected non-zero exit code for timeout, got %d", resp.ExitCode)
	}

	// Test successful execution with context
	ctx = context.Background()
	req = Request{
		Command: "echo",
		Args:    []string{"context", "test"},
	}

	resp, err = ExecuteWithContext(ctx, req)
	if err != nil {
		t.Fatalf("ExecuteWithContext failed: %v", err)
	}

	if resp.ExitCode != 0 {
		t.Errorf("Expected exit code 0, got %d", resp.ExitCode)
	}

	expectedOutput := "context test"
	if !strings.Contains(resp.Output, expectedOutput) {
		t.Errorf("Expected output to contain '%s', got '%s'", expectedOutput, resp.Output)
	}
}

func TestExecuteWithOutput(t *testing.T) {
	// Test command with stdout and stderr
	req := Request{
		Command: "sh",
		Args:    []string{"-c", "echo stdout message; echo stderr message >&2"},
	}

	resp, err := ExecuteWithOutput(req)
	if err != nil {
		t.Fatalf("ExecuteWithOutput failed: %v", err)
	}

	if resp.ExitCode != 0 {
		t.Errorf("Expected exit code 0, got %d", resp.ExitCode)
	}

	if !strings.Contains(resp.Stdout, "stdout message") {
		t.Errorf("Expected stdout to contain 'stdout message', got '%s'", resp.Stdout)
	}

	if !strings.Contains(resp.Stderr, "stderr message") {
		t.Errorf("Expected stderr to contain 'stderr message', got '%s'", resp.Stderr)
	}
}

func TestExecuteWithRealTimeOutput(t *testing.T) {
	// Test real-time output
	req := Request{
		Command: "sh",
		Args:    []string{"-c", "echo line1; sleep 0.1; echo line2; sleep 0.1; echo line3"},
	}

	var outputLines []string
	outputCallback := func(line string) {
		outputLines = append(outputLines, line)
	}

	ctx := context.Background()
	resp, err := ExecuteWithRealTimeOutput(ctx, req, outputCallback)
	if err != nil {
		t.Fatalf("ExecuteWithRealTimeOutput failed: %v", err)
	}

	if resp.ExitCode != 0 {
		t.Errorf("Expected exit code 0, got %d", resp.ExitCode)
	}

	if len(outputLines) != 3 {
		t.Errorf("Expected 3 output lines, got %d", len(outputLines))
	}

	expectedLines := []string{"line1", "line2", "line3"}
	for i, expected := range expectedLines {
		if i < len(outputLines) && !strings.Contains(outputLines[i], expected) {
			t.Errorf("Expected line %d to contain '%s', got '%s'", i+1, expected, outputLines[i])
		}
	}
}

func TestExecuteInBackground(t *testing.T) {
	// Test background execution
	req := Request{
		Command: "sleep",
		Args:    []string{"1"},
	}

	cmd, err := ExecuteInBackground(req)
	if err != nil {
		t.Fatalf("ExecuteInBackground failed: %v", err)
	}

	// Process should be running
	if cmd.Process == nil {
		t.Fatal("Expected process to be running, got nil Process")
	}

	// Wait for process to complete
	err = cmd.Wait()
	if err != nil {
		t.Errorf("Background process failed: %v", err)
	}
}
