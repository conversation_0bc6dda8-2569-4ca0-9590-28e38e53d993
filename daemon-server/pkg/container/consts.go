package container

import (
	"errors"
)

const (
	ContainerMetadataFileSuffix = ".container.json" // 容器元数据文件后缀
	DockerMetadataFileSuffix    = ".docker.json"    // Docker元数据文件后缀
	ContainerLayerFileSuffix    = ".diff.tar.gz"    // 容器层文件后缀
)

type DockerInfo struct {
	OperatingSystem    string `json:"operating_system"`
	OSVersion          string `json:"os_version"`
	OSType             string `json:"os_type"`
	Architecture       string `json:"architecture"`
	LiveRestoreEnabled bool   `json:"live_restore_enabled"`
	ServerVersion      string `json:"server_version"`
	StorageDriver      string `json:"storage_driver"`
	RootDir            string `json:"root_dir"`
}

// ContainerLayerInfo 存储容器层信息
type ContainerLayerInfo struct {
	ContainerMarkID string `json:"container_mark_id"` // 容器标记ID，用于区分不同的容器备份
	ContainerID     string `json:"container_id"`
	ContainerName   string `json:"container_name"`
	ImageName       string `json:"image_name"`
	ImageID         string `json:"image_id"`
	UpperDir        string `json:"upper_dir"`
	MountID         string `json:"mount_id"`
	Size            int64  `json:"size"`
}

// BackupOptions 备份选项
type BackupOptions struct {
	ContainerMarkID string // 容器标记ID，用于区分不同的容器备份
	ContainerID     string
	BackupDir       string
	DockerHost      string
	Verbose         bool
}

// RestoreOptions 恢复选项
type RestoreOptions struct {
	ContainerMarkID          string // 容器标记ID，用于区分不同的容器备份
	BackupDir                string
	ContainerName            string // 覆盖的容器名称、占位， 目前不支持
	ContainerID              string // 覆盖的容器ID
	DockerHost               string
	Verbose                  bool
	CheckLiveRestoreConfig   bool // 是否检查LiveRestore配置
	SkipPull                 bool // 跳过拉取镜像步骤、目前不支持拉取镜像
	SkipCreate               bool // 跳过创建容器步骤、目前不支持创建容器
	SkipStart                bool // 跳过启动容器步骤、目前不支持启动容器
	TempDir                  string
	SkipDockerSystemCompare  bool // 跳过Docker系统版本比较步骤
	SkipDockerVersionCompare bool // 跳过Docker版本比较步骤
}

// ErrEmptyContainerMarkID 当Docker主机地址为空时返回的错误
var ErrEmptyContainerMarkID = errors.New("container mark id cannot be empty")

// ErrEmptyContainerID 当容器ID为空时返回的错误
var ErrEmptyContainerID = errors.New("container ID cannot be empty")

// ErrEmptyBackupDir 当备份目录为空时返回的错误
var ErrEmptyBackupDir = errors.New("backup directory cannot be empty")

// ErrEmptyContainerName 当容器名称为空时返回的错误
var ErrEmptyContainerName = errors.New("container name cannot be empty")
