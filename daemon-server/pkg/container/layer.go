package container

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/docker/docker/api/types/system"
	"io"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/client"
)

// newDockerClient 创建Docker客户端
func newDockerClient(dockerHost string) (*client.Client, error) {
	if dockerHost == "" {
		dockerHost = "unix:///var/run/docker.sock"
	} else {
		if !strings.HasPrefix(dockerHost, "tcp://") {
			dockerHost = "tcp://" + dockerHost
		}
	}

	return client.NewClientWithOpts(
		client.WithHost(dockerHost),
		client.WithVersion("1.41"),
		client.WithTimeout(time.Minute*2),
	)
}

// logIf 根据条件输出日志
func logIf(verbose bool, format string, args ...interface{}) {
	if verbose {
		log.Printf(format, args...)
	}
}

func CheckBeforeExport(opts BackupOptions) error {
	// 参数验证
	if opts.ContainerMarkID == "" {
		return ErrEmptyContainerMarkID
	}

	if opts.ContainerID == "" {
		return ErrEmptyContainerID
	}

	if opts.BackupDir == "" {
		return ErrEmptyBackupDir
	}

	// 确保备份目录存在
	if _, err := os.Stat(opts.BackupDir); os.IsNotExist(err) {
		logIf(opts.Verbose, "Creating backup directory: %s", opts.BackupDir)
		if err := os.MkdirAll(opts.BackupDir, 0755); err != nil {
			return fmt.Errorf("failed to create backup directory: %w", err)
		}
	}
	return nil
}

// BackupContainer 备份容器层并返回容器信息
func BackupContainer(opts BackupOptions) (*ContainerLayerInfo, error) {
	if err := CheckBeforeExport(opts); err != nil {
		return nil, err
	}

	// 创建Docker客户端
	logIf(opts.Verbose, "Connecting to Docker host: %s", opts.DockerHost)
	cli, err := newDockerClient(opts.DockerHost)
	if err != nil {
		return nil, fmt.Errorf("failed to create Docker client: %w", err)
	}
	defer cli.Close()

	// 获取容器信息
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 获取docker info
	dockerServerInfo, err := cli.Info(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get Docker info: %w", err)
	}
	dockerInfo, err := getDockerInfoFromServerInfo(dockerServerInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to convert Docker info: %w", err)
	}

	logIf(opts.Verbose, "Inspecting container: %s", opts.ContainerID)
	containerInspectInfo, err := cli.ContainerInspect(ctx, opts.ContainerID)
	if err != nil {
		return nil, fmt.Errorf("failed to inspect container: %w", err)
	}

	containerInfo, err := convertToContainerInfo(containerInspectInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to convert container info: %w", err)
	}
	logIf(opts.Verbose, "Container name: %s, Image: %s", containerInfo.ContainerName, containerInfo.ImageName)

	// 获取容器的存储层信息
	upperDir, ok := containerInspectInfo.GraphDriver.Data["UpperDir"]
	if !ok {
		return nil, errors.New("failed to get UpperDir from container info")
	}
	if upperDir == "" {
		return nil, fmt.Errorf("UpperDir is empty for container[%s]", opts.ContainerID)
	}
	if !strings.HasPrefix(upperDir, dockerInfo.RootDir) {
		return nil, fmt.Errorf("UpperDir[%s] is not in the docker root dir[%s]", upperDir, dockerInfo.RootDir)
	}

	// 提取MountID
	parts := strings.Split(upperDir, "/")
	if len(parts) < 2 {
		return nil, errors.New("invalid UpperDir format")
	}
	mountID := parts[len(parts)-2]
	mountIDParentDir := filepath.Join(parts[0 : len(parts)-2]...)
	if mountID == "" {
		return nil, fmt.Errorf("MountID is empty for container[%s]", opts.ContainerID)
	}
	if !strings.HasPrefix(mountIDParentDir, "/") {
		mountIDParentDir = "/" + mountIDParentDir
	}
	logIf(opts.Verbose, "MountID parent dir: %s", mountIDParentDir)
	logIf(opts.Verbose, "Mount ID: %s, Upper Directory: %s", mountID, upperDir)

	empty, err := isDirectoryEmpty(filepath.Join(mountIDParentDir, mountID))
	if err != nil {
		return nil, fmt.Errorf("failed to check if mount id directory is empty: %w", err)
	}
	if empty {
		return nil, fmt.Errorf("mountID directory %s is empty, cannot create tar archive", mountID)
	}

	// 创建备份文件路径
	backupPath := filepath.Join(opts.BackupDir, opts.ContainerMarkID+ContainerLayerFileSuffix)
	containerInfoPath := filepath.Join(opts.BackupDir, opts.ContainerMarkID+ContainerMetadataFileSuffix)
	dockerInfoPath := filepath.Join(opts.BackupDir, opts.ContainerMarkID+DockerMetadataFileSuffix)

	// 创建tar包
	// 注意：这部分需要在Docker主机上执行，因为需要访问docker root dir
	logIf(opts.Verbose, "Creating tar archive at: %s", backupPath)
	cmd := exec.Command("tar", "--preserve-permissions", "--same-owner", "--numeric-owner", "--acls", "--xattrs",
		"-czf", backupPath, "-C", mountID, ".")
	cmd.Dir = mountIDParentDir
	logIf(opts.Verbose, "Executing: %s", cmd.String())
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("failed to create tar archive: %w, output: %s", err, string(output))
	}

	// 获取备份文件大小
	fileInfo, err := os.Stat(backupPath)
	if err != nil {
		logIf(opts.Verbose, "Warning: Could not get backup file size: %v", err)
	}
	if fileInfo != nil {
		containerInfo.Size = fileInfo.Size()
	}

	containerInfo.ContainerMarkID = opts.ContainerMarkID

	// 保存容器元数据
	logIf(opts.Verbose, "Saving container info to: %s", containerInfoPath)
	containerMetadata, err := json.Marshal(containerInfo)
	if err != nil {
		logIf(opts.Verbose, "Warning: Could not marshal containerMetadata: %v", err)
	} else {
		if err := os.WriteFile(containerInfoPath, containerMetadata, 0644); err != nil {
			logIf(opts.Verbose, "Warning: Could not write containerMetadata file: %v", err)
		}
	}

	// 保存docker元数据
	logIf(opts.Verbose, "Saving docker info to: %s", dockerInfoPath)
	dockerMetadata, err := json.Marshal(dockerInfo)
	if err != nil {
		logIf(opts.Verbose, "Warning: Could not marshal dockerMetadata: %v", err)
	} else {
		if err := os.WriteFile(dockerInfoPath, dockerMetadata, 0644); err != nil {
			logIf(opts.Verbose, "Warning: Could not write dockerMetadata file: %v", err)
		}
	}

	// 打印备份信息
	fmt.Printf("Container backup completed:\n")
	fmt.Printf("Container Name: %s\n", containerInfo.ContainerName)
	fmt.Printf("Image: %s\n", containerInfo.ImageName)
	fmt.Printf("Mount ID: %s\n", mountID)
	fmt.Printf("Upper Directory: %s\n", upperDir)
	fmt.Printf("Backup saved to: %s\n", backupPath)
	fmt.Printf("Container Config saved to: %s\n", containerInfoPath)
	fmt.Printf("Docker Config saved to: %s\n", dockerInfoPath)
	if fileInfo != nil {
		fmt.Printf("Backup size: %.2f MB\n", float64(fileInfo.Size())/(1024*1024))
	}

	return containerInfo, nil
}

func convertToContainerInfo(info types.ContainerJSON) (*ContainerLayerInfo, error) {
	return &ContainerLayerInfo{
		ContainerID:   info.ID,
		ContainerName: strings.TrimPrefix(info.Name, "/"),
		ImageName:     info.Config.Image,
		ImageID:       info.Image,
		UpperDir:      info.GraphDriver.Data["UpperDir"],
		MountID:       info.GraphDriver.Data["UpperDir"],
	}, nil
}

func getDockerInfoFromServerInfo(info system.Info) (*DockerInfo, error) {
	return &DockerInfo{
		OperatingSystem:    info.OperatingSystem,
		OSVersion:          info.OSVersion,
		OSType:             info.OSType,
		Architecture:       info.Architecture,
		LiveRestoreEnabled: info.LiveRestoreEnabled,
		ServerVersion:      info.ServerVersion,
		StorageDriver:      info.Driver,
		RootDir:            info.DockerRootDir,
	}, nil
}

// LoadContainerInfo 恢复容器层
func LoadContainerInfo(opts RestoreOptions) error {
	if err := CheckBeforeRestore(opts); err != nil {
		return err
	}

	logIf(opts.Verbose, "Restoring container %s to %s", opts.ContainerMarkID, opts.ContainerID)

	// 确认元数据文件
	containerInfoPath := filepath.Join(opts.BackupDir, opts.ContainerMarkID+ContainerMetadataFileSuffix)
	dockerInfoPath := filepath.Join(opts.BackupDir, opts.ContainerMarkID+DockerMetadataFileSuffix)
	containerLayerFilePath := filepath.Join(opts.BackupDir, opts.ContainerMarkID+ContainerLayerFileSuffix)
	if _, err := os.Stat(containerInfoPath); os.IsNotExist(err) {
		return fmt.Errorf("container info file %s not found", containerInfoPath)
	}
	if _, err := os.Stat(dockerInfoPath); os.IsNotExist(err) {
		return fmt.Errorf("docker info file %s not found", dockerInfoPath)
	}
	if _, err := os.Stat(containerLayerFilePath); os.IsNotExist(err) {
		return fmt.Errorf("container layer file %s not found", containerLayerFilePath)
	}

	// 读取元数据
	logIf(opts.Verbose, "Reading container info from: %s", containerInfoPath)
	containerInfo, err := readContainerInfo(containerInfoPath)
	if err != nil {
		return fmt.Errorf("failed to read container info: %w", err)
	}

	logIf(opts.Verbose, "Reading docker info from: %s", dockerInfoPath)
	dockerInfo, err := readDockerInfo(dockerInfoPath)
	if err != nil {
		return fmt.Errorf("failed to read docker info: %w", err)
	}

	// 创建Docker客户端
	dockerStopped := false
	logIf(opts.Verbose, "Connecting to Docker host: %s", opts.DockerHost)
	cli, err := newDockerClient(opts.DockerHost)
	if err != nil {
		return fmt.Errorf("failed to create Docker client: %v", err)
	}
	defer func() {
		if !dockerStopped {
			cli.Close()
		}
	}()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// get docker info
	dockerServerInfo, err := cli.Info(ctx)
	if err != nil {
		return fmt.Errorf("failed to get Docker server info: %v", err)
	}

	// compare docker info
	if err := compareDockerInfo(dockerInfo, &dockerServerInfo, &opts); err != nil {
		return fmt.Errorf("docker info is not compatible with source container: %v", err)
	}

	// 确保镜像存在
	logIf(opts.Verbose, "Checking if image exists: %s", containerInfo.ImageName)
	imageInfo, _, err := cli.ImageInspectWithRaw(ctx, containerInfo.ImageName)
	if err != nil {
		// Todo: 拉取镜像
		return fmt.Errorf("find image %s err: %v", containerInfo.ImageName, err)
	}

	if err := compareImageInfo(containerInfo.ImageID, containerInfo.ImageName, &imageInfo); err != nil {
		return fmt.Errorf("image %s is not compatible with source container: %v", containerInfo.ImageName, err)
	}

	// Todo: 检查容器是否存在，不存在则创建容器

	// 获取新容器的存储信息
	newContainerInfo, err := cli.ContainerInspect(ctx, opts.ContainerID)
	if err != nil {
		return fmt.Errorf("failed to inspect new container: %v", err)
	}

	// compare container info
	if err := compareContainerInfo(containerInfo, &newContainerInfo); err != nil {
		return fmt.Errorf("container %s is not compatible with source container[%s]: %v", opts.ContainerID, containerInfo.ContainerID, err)
	}

	newUpperDir, ok := newContainerInfo.GraphDriver.Data["UpperDir"]
	if !ok {
		return errors.New("failed to get UpperDir from new container info")
	}
	if newUpperDir == "" {
		return fmt.Errorf("UpperDir is empty in target container[%s]", newContainerInfo.ID)
	}
	if !strings.HasPrefix(newUpperDir, dockerServerInfo.DockerRootDir) {
		return fmt.Errorf("UpperDir[%s] does not start with docker root dir[%s]", newUpperDir, dockerServerInfo.DockerRootDir)
	}

	// 提取新容器的MountID
	parts := strings.Split(newUpperDir, "/")
	if len(parts) < 2 {
		return fmt.Errorf("invalid UpperDir format for new container[%s]", opts.ContainerID)
	}
	newMountID := parts[len(parts)-2]
	if newMountID == "" {
		return fmt.Errorf("MountID is empty for new container[%s]", opts.ContainerID)
	}

	// 解压源容器差异层至临时目录
	extractedDir := filepath.Join(opts.TempDir, containerInfo.ContainerMarkID)
	logIf(opts.Verbose, "Creating extraction directory: %s", extractedDir)
	if err := os.MkdirAll(extractedDir, 0755); err != nil {
		return fmt.Errorf("failed to create extraction directory: %v", err)
	}

	backupLayerPath := filepath.Join(opts.BackupDir, opts.ContainerMarkID+ContainerLayerFileSuffix)
	logIf(opts.Verbose, "Extracting backup from: %s to %s", backupLayerPath, extractedDir)
	cmd := exec.Command("tar", "-xzf", backupLayerPath, "-C", extractedDir)
	logIf(opts.Verbose, "Executing command: %s", cmd.String())
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to extract backup: %v, output: %s", err, string(output))
	}

	// 关闭docker client
	logIf(opts.Verbose, "close docker client ...")
	err = cli.Close()
	if err != nil {
		logIf(opts.Verbose, "Failed to close Docker client: %v", err)
		logIf(opts.Verbose, "continue ...")
	} else {
		dockerStopped = true
	}

	// 停止docker服务
	logIf(opts.Verbose, "stop docker service before restore files")
	cmd = exec.Command("systemctl", "stop", "docker")
	logIf(opts.Verbose, "Executing command: %s", cmd.String())
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to stop docker service: %v, output: %s", err, string(output))
	}

	// 清空目标上层目录 - 使用shell执行通配符删除
	cmd = exec.Command("sh", "-c", fmt.Sprintf("rm -rf %s/*", newUpperDir))
	logIf(opts.Verbose, "Executing command: %s", cmd.String())
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to clean upper directory: %v, output: %s", err, string(output))
	}

	// 删除隐藏文件
	cmd = exec.Command("sh", "-c", fmt.Sprintf("rm -rf %s/.[!.]*", newUpperDir))
	logIf(opts.Verbose, "Executing command: %s", cmd.String())
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to clean hidden files: %v, output: %s", err, string(output))
	}

	// 复制所有内容，保留所有特殊文件属性、权限和所有者
	sourceDiffDir := filepath.Join(extractedDir, "diff")
	// 使用rsync而非cp，更好地保留所有文件属性
	cmd = exec.Command("rsync", "-a", "--numeric-ids", "--xattrs", "--acls", sourceDiffDir+"/.", newUpperDir+"/")
	logIf(opts.Verbose, "Executing command: %s", cmd.String())
	output, err = cmd.CombinedOutput()
	if err != nil {
		// 如果rsync失败，尝试使用cp命令
		logIf(opts.Verbose, "rsync failed, falling back to cp: %v", err)
		cmd = exec.Command("cp", "-a", "--preserve=all", sourceDiffDir+"/.", newUpperDir+"/")
		logIf(opts.Verbose, "Executing command: %s", cmd.String())
		output, err = cmd.CombinedOutput()
		if err != nil {
			return fmt.Errorf("failed to copy files: %v, output: %s", err, string(output))
		}
	}

	// 不再强制修改所有文件的权限和所有者，保留原始权限
	// 只修复根目录的权限，确保Docker可以访问
	cmd = exec.Command("chmod", "755", newUpperDir)
	logIf(opts.Verbose, "Executing command: %s", cmd.String())
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to fix root directory permissions: %v, output: %s", err, string(output))
	}

	// Todo: 重启docker（重新init docker client）， 启动容器

	logIf(opts.Verbose, "Restoration completed successfully")
	logIf(opts.Verbose, "delete temp extracted dir: %s", extractedDir)
	if err := os.RemoveAll(extractedDir); err != nil {
		fmt.Printf("failed to delete temp extracted dir %s: %v \n", extractedDir, err)
	}
	return nil
}

func compareDockerInfo(info *DockerInfo, serverInfo *system.Info, opts *RestoreOptions) error {
	if !opts.SkipDockerSystemCompare {
		// compare system and version like debian 12 /ubuntu 22.10
		if info.OperatingSystem != serverInfo.OperatingSystem {
			return fmt.Errorf("target operating system[%s] is not compatible with source container[%s]", serverInfo.OperatingSystem, info.OperatingSystem)
		}
		if info.OSVersion != serverInfo.OSVersion {
			return fmt.Errorf("target os version[%s] is not compatible with source container[%s]", serverInfo.OSVersion, info.OSVersion)
		}
	}

	if !opts.SkipDockerVersionCompare {
		if info.ServerVersion != serverInfo.ServerVersion {
			return fmt.Errorf("target server version[%s] is not compatible with source container[%s]", serverInfo.ServerVersion, info.ServerVersion)
		}
	}

	if info.OSType != serverInfo.OSType {
		return fmt.Errorf("os type[%s] is not compatible with source container[%s]", serverInfo.OSType, info.OSType)
	}
	if info.Architecture != serverInfo.Architecture {
		return fmt.Errorf("target architecture[%s] is not compatible with source container[%s]", serverInfo.Architecture, info.Architecture)
	}
	if info.StorageDriver != serverInfo.Driver {
		return fmt.Errorf("target storage driver[%s] is not compatible with source container[%s]", serverInfo.Driver, info.StorageDriver)
	}
	if opts.CheckLiveRestoreConfig {
		if !serverInfo.LiveRestoreEnabled {
			return fmt.Errorf("target live restore enabled is false")
		}
	}
	return nil
}

func compareContainerInfo(source *ContainerLayerInfo, target *types.ContainerJSON) error {
	if source.ImageName != target.Config.Image {
		return fmt.Errorf("target container image[%s] is not the same as source container[%s]", target.Config.Image, source.ImageName)
	}
	if source.ImageID != target.Image {
		return fmt.Errorf("target container image id[sha256:%s] is not the same as source container[%s]", target.Image, source.ImageID)
	}
	if target.State.Running {
		return fmt.Errorf("target container[%s:%s] is running", target.ID, target.Name)
	}
	return nil
}

func compareImageInfo(sourceImageId string, sourceImageName string, targetImageInfo *types.ImageInspect) error {
	if sourceImageId != targetImageInfo.ID {
		return fmt.Errorf("image[%s] id[sha256:%s] is not compatible with source container[%s]", sourceImageName, targetImageInfo.ID, sourceImageId)
	}

	if sourceImageName != targetImageInfo.RepoTags[0] {
		return fmt.Errorf("image[%s] is not compatible with source container[%s]", sourceImageName, sourceImageId)
	}

	return nil
}

func readDockerInfo(path string) (*DockerInfo, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	var dockerInfo DockerInfo
	if err := json.NewDecoder(file).Decode(&dockerInfo); err != nil {
		return nil, err
	}
	return &dockerInfo, nil
}

func readContainerInfo(path string) (*ContainerLayerInfo, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	var containerInfo ContainerLayerInfo
	if err := json.NewDecoder(file).Decode(&containerInfo); err != nil {
		return nil, err
	}
	return &containerInfo, nil
}

func CheckBeforeRestore(opts RestoreOptions) error {
	// 参数验证
	if opts.BackupDir == "" {
		return ErrEmptyBackupDir
	}

	if opts.ContainerID == "" {
		return ErrEmptyContainerID
	}

	if opts.ContainerMarkID == "" {
		return ErrEmptyContainerMarkID
	}
	// 确保备份目录存在
	if _, err := os.Stat(opts.BackupDir); os.IsNotExist(err) {
		return fmt.Errorf("backup directory does not exist: %s", opts.BackupDir)
	}
	return nil
}

// isDirectoryEmpty 检查目录是否为空
func isDirectoryEmpty(dir string) (bool, error) {
	f, err := os.Open(dir)
	if err != nil {
		return false, err
	}
	defer f.Close()

	// 读取一个目录项
	_, err = f.Readdirnames(1)
	if err == io.EOF {
		return true, nil
	}
	return false, err
}
