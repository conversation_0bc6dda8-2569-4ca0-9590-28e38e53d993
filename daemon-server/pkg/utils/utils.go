package utils

import (
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"strconv"
	"strings"
)

func GetUuidString() string {
	s := uuid.New().String()
	return strings.Replace(s, "-", "", -1)
}

// PrettyJSON converts a struct to a pretty-printed JSON string
func PrettyJSON(data interface{}) (string, error) {
	bytes, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal JSON: %w", err)
	}
	return string(bytes), nil
}

// CompareVersions compares two version strings in the format "x.y.z" (e.g., "1.0.3")
// Returns:
//   - Positive integer if version1 > version2
//   - Zero if version1 == version2
//   - Negative integer if version1 < version2
func CompareVersions(version1, version2 string) (int, error) {
	// Split versions into components
	v1Parts := strings.Split(version1, ".")
	v2Parts := strings.Split(version2, ".")

	// Ensure we have at least 3 parts for each version
	if len(v1Parts) < 3 || len(v2Parts) < 3 {
		return 0, fmt.Errorf("invalid version format: versions must be in format 'x.y.z'")
	}

	// Compare major version
	major1, err := strconv.Atoi(v1Parts[0])
	if err != nil {
		return 0, fmt.Errorf("invalid major version in %s: %w", version1, err)
	}

	major2, err := strconv.Atoi(v2Parts[0])
	if err != nil {
		return 0, fmt.Errorf("invalid major version in %s: %w", version2, err)
	}

	if major1 != major2 {
		return major1 - major2, nil
	}

	// Compare minor version
	minor1, err := strconv.Atoi(v1Parts[1])
	if err != nil {
		return 0, fmt.Errorf("invalid minor version in %s: %w", version1, err)
	}

	minor2, err := strconv.Atoi(v2Parts[1])
	if err != nil {
		return 0, fmt.Errorf("invalid minor version in %s: %w", version2, err)
	}

	if minor1 != minor2 {
		return minor1 - minor2, nil
	}

	// Compare patch version
	patch1, err := strconv.Atoi(v1Parts[2])
	if err != nil {
		return 0, fmt.Errorf("invalid patch version in %s: %w", version1, err)
	}

	patch2, err := strconv.Atoi(v2Parts[2])
	if err != nil {
		return 0, fmt.Errorf("invalid patch version in %s: %w", version2, err)
	}

	return patch1 - patch2, nil
}
