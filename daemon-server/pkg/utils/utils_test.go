package utils

import (
	"github.com/docker/docker/api/types/filters"
	"net/url"
	"testing"
)

// TestPrettyJSON tests the PrettyJSON function
func TestPrettyJSON(t *testing.T) {
	// Test case 1: Simple struct
	type testStruct struct {
		Name  string `json:"name"`
		Value int    `json:"value"`
	}

	testData := testStruct{
		Name:  "test",
		Value: 42,
	}

	expected := `{
  "name": "test",
  "value": 42
}`

	result, err := PrettyJSON(testData)
	if err != nil {
		t.<PERSON>rrorf("PrettyJSON returned an error: %v", err)
	}

	if result != expected {
		t.<PERSON><PERSON><PERSON>("PrettyJSON result does not match expected output.\nExpected: %s\nGot: %s", expected, result)
	}

	// Test case 2: Map
	testMap := map[string]interface{}{
		"name":    "test",
		"value":   42,
		"enabled": true,
	}

	// We can't directly compare the string output for maps because the order of keys is not guaranteed
	result, err = PrettyJSON(testMap)
	if err != nil {
		t.<PERSON><PERSON>("PrettyJSON returned an error for map: %v", err)
	}

	if len(result) == 0 {
		t.<PERSON><PERSON><PERSON>("PrettyJSON returned empty string for map")
	}

	// Test case 3: Invalid JSON
	invalidData := make(chan int) // channels cannot be marshaled to JSON
	_, err = PrettyJSON(invalidData)
	if err == nil {
		t.Error("PrettyJSON should have returned an error for invalid JSON")
	}
}

// TestCompareVersions tests the CompareVersions function
func TestCompareVersions(t *testing.T) {
	tests := []struct {
		name     string
		version1 string
		version2 string
		want     int
		wantErr  bool
	}{
		{
			name:     "Equal versions",
			version1: "1.0.0",
			version2: "1.0.0",
			want:     0,
			wantErr:  false,
		},
		{
			name:     "Greater major version",
			version1: "2.0.0",
			version2: "1.0.0",
			want:     1,
			wantErr:  false,
		},
		{
			name:     "Lesser major version",
			version1: "1.0.0",
			version2: "2.0.0",
			want:     -1,
			wantErr:  false,
		},
		{
			name:     "Greater minor version",
			version1: "1.1.0",
			version2: "1.0.0",
			want:     1,
			wantErr:  false,
		},
		{
			name:     "Lesser minor version",
			version1: "1.0.0",
			version2: "1.1.0",
			want:     -1,
			wantErr:  false,
		},
		{
			name:     "Greater patch version",
			version1: "1.0.1",
			version2: "1.0.0",
			want:     1,
			wantErr:  false,
		},
		{
			name:     "Lesser patch version",
			version1: "1.0.0",
			version2: "1.0.1",
			want:     -1,
			wantErr:  false,
		},
		{
			name:     "Complex version comparison",
			version1: "2.10.5",
			version2: "2.2.10",
			want:     8,
			wantErr:  false,
		},
		{
			name:     "Invalid version format - too few components v1",
			version1: "1.0",
			version2: "1.0.0",
			want:     0,
			wantErr:  true,
		},
		{
			name:     "Invalid version format - too few components v2",
			version1: "1.0.0",
			version2: "1.0",
			want:     0,
			wantErr:  true,
		},
		{
			name:     "Invalid version format - non-numeric major v1",
			version1: "a.0.0",
			version2: "1.0.0",
			want:     0,
			wantErr:  true,
		},
		{
			name:     "Invalid version format - non-numeric major v2",
			version1: "1.0.0",
			version2: "a.0.0",
			want:     0,
			wantErr:  true,
		},
		{
			name:     "Invalid version format - non-numeric minor v1",
			version1: "1.a.0",
			version2: "1.0.0",
			want:     0,
			wantErr:  true,
		},
		{
			name:     "Invalid version format - non-numeric minor v2",
			version1: "1.0.0",
			version2: "1.a.0",
			want:     0,
			wantErr:  true,
		},
		{
			name:     "Invalid version format - non-numeric patch v1",
			version1: "1.0.a",
			version2: "1.0.0",
			want:     0,
			wantErr:  true,
		},
		{
			name:     "Invalid version format - non-numeric patch v2",
			version1: "1.0.0",
			version2: "1.0.a",
			want:     0,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CompareVersions(tt.version1, tt.version2)

			// Check error
			if (err != nil) != tt.wantErr {
				t.Errorf("CompareVersions() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// If we expect an error, don't check the result
			if tt.wantErr {
				return
			}

			// Check result
			if got != tt.want {
				t.Errorf("CompareVersions() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetUuidString(t *testing.T) {
	f := filters.NewArgs(filters.Arg("reference", "fasfsafsfsfsdf"))
	filterStr, err := f.MarshalJSON()
	s := string(filterStr)
	qStr := url.QueryEscape(s)
	t.Logf("filter: %v", qStr)

	unescapeStr, err := url.QueryUnescape(qStr)
	t.Logf("unescape: %s, err: %v", unescapeStr, err)
	a, err := filters.FromJSON(unescapeStr)
	t.Logf("result: %v, err: %v", a, err)
}

func TestEscape(t *testing.T) {
	s := "{\\\"reference\\\":\\\"10.20.103.240/chenyu/public/c503abeeefb74af4ab4cb0e5948d4c56:v1.0.5-bate-20250515\\\"}"
	unStr, err := url.QueryUnescape(s)
	t.Logf("unescape: %s, err: %v", unStr, err)
}

func TestEncode(t *testing.T) {
	s := "{\"reference\":\"10.20.103.240/chenyu/public/c503abeeefb74af4ab4cb0e5948d4c56:v1.0.5-bate-20250515\"}"
	t.Logf("encode: %s", url.QueryEscape(s))
}
