package version

// 这些变量将在构建时通过 -ldflags 设置
var (
	// Version 是应用程序的语义版本
	Version = "0.0.1"
	// GitCommit 是构建时的 git commit hash
	GitCommit = "unknown"
	// BuildTime 是构建的时间戳
	BuildTime = "unknown"
)

// Info 返回版本信息
type Info struct {
	Version   string `json:"version"`
	GitCommit string `json:"git_commit"`
	BuildTime string `json:"build_time"`
}

// GetVersionInfo 返回当前版本信息
func GetVersionInfo() Info {
	return Info{
		Version:   Version,
		GitCommit: GitCommit,
		BuildTime: BuildTime,
	}
}
