# Docker and Taskflow SDK

This SDK provides Go clients for interacting with the Docker and Taskflow HTTP APIs.

## Installation

```bash
go get -u daemon-server/sdk
```

## Docker SDK

The Docker SDK provides clients for interacting with Docker containers, images, and system operations.

### Usage

```go
import (
    "context"
    "fmt"
    "time"

    "daemon-server/sdk/docker"
)

func main() {
    // Create a Docker client
    client := docker.NewDockerClient(
        "http://localhost:8080",
        docker.WithTimeout(30*time.Second),
    )

    // Create context
    ctx := context.Background()

    // List containers
    containers, err := client.Container.List(ctx, true)
    if err != nil {
        fmt.Printf("Failed to list containers: %v\n", err)
        return
    }

    fmt.Printf("Found %d containers\n", len(containers))
    for _, container := range containers {
        fmt.Printf("  - %s (%s): %s\n", container.Name, container.ID, container.Status)
    }

    // Pull an image asynchronously
    task, err := client.AsyncImage.Pull(ctx, "nginx:latest", "")
    if err != nil {
        fmt.Printf("Failed to start image pull: %v\n", err)
        return
    }

    // Wait for task to complete
    taskInfo, err := task.Wait(ctx, 1*time.Second, 2*time.Minute)
    if err != nil {
        fmt.Printf("Failed to wait for task: %v\n", err)
        return
    }

    fmt.Printf("Task completed with status: %s\n", taskInfo.Status)
}
```

## Taskflow SDK

The Taskflow SDK provides a client for interacting with the Taskflow API for task management.

### Usage

```go
import (
    "context"
    "fmt"
    "time"

    "daemon-server/sdk/taskflow"
)

func main() {
    // Create a Taskflow client
    client := taskflow.NewTaskflowClient(
        "http://localhost:8080",
        taskflow.WithTimeout(30*time.Second),
    )

    // Create context
    ctx := context.Background()

    // List tasks
    tasks, err := client.ListTasks(ctx)
    if err != nil {
        fmt.Printf("Failed to list tasks: %v\n", err)
        return
    }

    fmt.Printf("Found %d tasks\n", len(tasks))
    for _, task := range tasks {
        fmt.Printf("  - %s (%s): %s\n", task.Name, task.ID, task.Status)
    }

    // Create a task
    taskRequest := taskflow.CreateTaskRequest{
        Name:        "Example Task",
        Description: "A task created by the SDK example",
        Steps: []taskflow.StepRequest{
            {
                Name:        "Step 1",
                Description: "First step",
                Input:       "step 1 input",
            },
        },
        Input: "task input",
    }

    task, err := client.CreateTask(ctx, taskRequest)
    if err != nil {
        fmt.Printf("Failed to create task: %v\n", err)
        return
    }

    // Execute the task
    if err := client.ExecuteTask(ctx, task.ID, nil); err != nil {
        fmt.Printf("Failed to execute task: %v\n", err)
        return
    }

    // Wait for task completion
    completedTask, err := client.WaitForTask(ctx, task.ID, 1*time.Second, 30*time.Second)
    if err != nil {
        fmt.Printf("Failed to wait for task: %v\n", err)
        return
    }

    fmt.Printf("Task completed with status: %s\n", completedTask.Status)
}
```

## System SDK

The System SDK provides a client for executing commands on the server.

### Usage

```go
import (
    "context"
    "fmt"
    "time"

    "daemon-server/sdk/system"
)

func main() {
    // Create a system client
    client := system.NewSystemClient(
        "http://localhost:8080",
        system.WithTimeout(30*time.Second),
    )

    // Create context
    ctx := context.Background()

    // Execute a command with real-time output
    req := system.ExecuteCommandRequest{
        Command: "ping",
        Args:    []string{"-c", "5", "google.com"},
    }

    // Execute command with real-time output
    task, err := client.ExecuteCommandWithRealTimeOutput(ctx, req)
    if err != nil {
        fmt.Printf("Error executing command: %v\n", err)
        return
    }

    fmt.Printf("Task started with ID: %s\n", task.ID)

    // Stream output until task completes
    err = task.StreamOutput(ctx, 500*time.Millisecond, func(line string) {
        fmt.Println(line)
    })
    if err != nil {
        fmt.Printf("Error streaming output: %v\n", err)
        return
    }

    // Get final result
    result, err := client.GetCommandResult(ctx, task.ID)
    if err != nil {
        fmt.Printf("Error getting command result: %v\n", err)
        return
    }

    fmt.Printf("\nCommand completed with exit code: %d\n", result.ExitCode)
}
```

## Examples

See the `examples` directory for more examples of using the SDK.

## Testing

Run the tests with:

```bash
./run_tests.sh
```
