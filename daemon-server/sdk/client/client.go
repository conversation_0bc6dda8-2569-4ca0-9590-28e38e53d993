package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

// Option is a function that configures a Client
type Option func(*Client)

// Client is a base HTTP client for API requests
type Client struct {
	BaseURL    string
	HttpClient *http.Client
	timeout    time.Duration
}

// WithTimeout sets the timeout for HTTP requests
func WithTimeout(timeout time.Duration) Option {
	return func(c *Client) {
		c.timeout = timeout
		c.HttpClient.Timeout = timeout
	}
}

// WithHTTPClient sets a custom HTTP client
func WithHTTPClient(httpClient *http.Client) Option {
	return func(c *Client) {
		c.HttpClient = httpClient
	}
}

// NewClient creates a new API client
func NewClient(baseURL string, options ...Option) *Client {
	client := &Client{
		BaseURL: baseURL,
		HttpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		timeout: 30 * time.Second,
	}

	// Apply options
	for _, option := range options {
		option(client)
	}

	return client
}

// Get sends a GET request to the specified path
func (c *Client) Get(ctx context.Context, path string, query url.Values, result interface{}) error {
	return c.doRequest(ctx, http.MethodGet, path, query, nil, result)
}

// Post sends a POST request to the specified path
func (c *Client) Post(ctx context.Context, path string, query url.Values, body interface{}, result interface{}) error {
	return c.doRequest(ctx, http.MethodPost, path, query, body, result)
}

func (c *Client) GetBaseURL() string {
	return c.BaseURL
}

// Delete sends a DELETE request to the specified path
func (c *Client) Delete(ctx context.Context, path string, query url.Values, result interface{}) error {
	return c.doRequest(ctx, http.MethodDelete, path, query, nil, result)
}

// GetStream sends a GET request and returns the response body as a stream
func (c *Client) GetStream(ctx context.Context, path string, query url.Values) (io.ReadCloser, error) {
	return c.doStreamRequest(ctx, http.MethodGet, path, query, nil)
}

// PostStream sends a POST request and returns the response body as a stream
func (c *Client) PostStream(ctx context.Context, path string, query url.Values, body interface{}) (io.ReadCloser, error) {
	return c.doStreamRequest(ctx, http.MethodPost, path, query, body)
}

// doRequest performs an HTTP request and unmarshals the response into result
func (c *Client) doRequest(ctx context.Context, method, path string, query url.Values, body interface{}, result interface{}) error {
	// Create request
	req, err := c.createRequest(ctx, method, path, query, body)
	if err != nil {
		return err
	}

	// Send request
	resp, err := c.HttpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return c.handleErrorResponse(resp)
	}

	// Unmarshal response
	if result != nil {
		if err := json.NewDecoder(resp.Body).Decode(result); err != nil {
			return fmt.Errorf("failed to decode response: %w", err)
		}
	}

	return nil
}

// doStreamRequest performs an HTTP request and returns the response body as a stream
func (c *Client) doStreamRequest(ctx context.Context, method, path string, query url.Values, body interface{}) (io.ReadCloser, error) {
	// Create request
	req, err := c.createRequest(ctx, method, path, query, body)
	if err != nil {
		return nil, err
	}

	// Send request
	resp, err := c.HttpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	// Check response status
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		defer resp.Body.Close()
		return nil, c.handleErrorResponse(resp)
	}

	return resp.Body, nil
}

// createRequest creates an HTTP request
func (c *Client) createRequest(ctx context.Context, method, path string, query url.Values, body interface{}) (*http.Request, error) {
	// Build URL
	u, err := url.Parse(c.BaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse base URL: %w", err)
	}
	u.Path = path
	if query != nil {
		u.RawQuery = query.Encode()
	}

	// Create request body
	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, method, u.String(), bodyReader)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}
	req.Header.Set("Accept", "application/json")

	return req, nil
}

// handleErrorResponse handles an error response
func (c *Client) handleErrorResponse(resp *http.Response) error {
	// Try to read the response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("request failed with status %d, unable to read response body: %w", resp.StatusCode, err)
	}

	// Try to decode as JSON error response
	var errorResp struct {
		Error   string `json:"error"`
		Message string `json:"message"`
	}

	if err := json.Unmarshal(bodyBytes, &errorResp); err != nil {
		// If we can't decode the error response as JSON, return the raw body as string if possible
		bodyStr := string(bodyBytes)
		if bodyStr != "" {
			return fmt.Errorf("request failed with status %d: %s", resp.StatusCode, bodyStr)
		}
		return fmt.Errorf("request failed with status %d", resp.StatusCode)
	}

	// Use Error field if available, otherwise use Message field
	errorMsg := errorResp.Error
	if errorMsg == "" {
		errorMsg = errorResp.Message
	}

	if errorMsg != "" {
		return fmt.Errorf("request failed with status %d: %s", resp.StatusCode, errorMsg)
	}

	return fmt.Errorf("request failed with status %d", resp.StatusCode)
}
