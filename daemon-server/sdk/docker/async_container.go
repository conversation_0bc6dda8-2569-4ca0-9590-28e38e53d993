package docker

import (
	"context"
	"daemon-server/sdk/client"
	"daemon-server/sdk/taskflow"
	"fmt"
	"github.com/docker/docker/api/types/container"
	"net/url"
	"strconv"
)

// AsyncContainerClient is a client for asynchronous container operations
type AsyncContainerClient struct {
	client *client.Client
}

// NewAsyncContainerClient creates a new asynchronous container client
func NewAsyncContainerClient(client *client.Client) *AsyncContainerClient {
	return &AsyncContainerClient{
		client: client,
	}
}

// C<PERSON> creates a new container asynchronously
func (c *AsyncContainerClient) Create(ctx context.Context, config ContainerConfig) (*taskflow.Task, error) {
	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Post(ctx, "/api/v1/containers", nil, config, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}

// Start starts a container asynchronously
func (c *AsyncContainerClient) Start(ctx context.Context, id string) (*taskflow.Task, error) {
	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Post(ctx, fmt.Sprintf("/api/v1/containers/%s/start", id), nil, nil, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}

// Stop stops a container asynchronously
func (c *AsyncContainerClient) Stop(ctx context.Context, id string, timeout *int) (*taskflow.Task, error) {
	// Create query parameters
	query := url.Values{}
	if timeout != nil {
		query.Set("timeout", strconv.Itoa(*timeout))
	}

	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Post(ctx, fmt.Sprintf("/api/v1/containers/%s/stop", id), query, nil, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}

// Restart restarts a container asynchronously
func (c *AsyncContainerClient) Restart(ctx context.Context, id string, timeout *int) (*taskflow.Task, error) {
	// Create query parameters
	query := url.Values{}
	if timeout != nil {
		query.Set("timeout", strconv.Itoa(*timeout))
	}

	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Post(ctx, fmt.Sprintf("/api/v1/containers/%s/restart", id), query, nil, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}

// Remove removes a container asynchronously
func (c *AsyncContainerClient) Remove(ctx context.Context, id string, force, removeVolumes bool) (*taskflow.Task, error) {
	// Create query parameters
	query := url.Values{}
	query.Set("force", strconv.FormatBool(force))
	query.Set("volumes", strconv.FormatBool(removeVolumes))
	query.Set("async", "true") // Use async mode

	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Delete(ctx, fmt.Sprintf("/api/v1/containers/%s", id), query, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}

// Commit commits a container to an image asynchronously
func (c *AsyncContainerClient) Commit(ctx context.Context, id string, options container.CommitOptions) (*taskflow.Task, error) {
	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Post(ctx, fmt.Sprintf("/api/v1/containers/%s/commit", id), nil, options, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}

// Export exports a container to a tar file asynchronously
func (c *AsyncContainerClient) Export(ctx context.Context, id string) (*taskflow.Task, error) {
	// Create query parameters
	query := url.Values{}
	query.Set("async", "true") // Use async mode

	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Get(ctx, fmt.Sprintf("/api/v1/containers/%s/export", id), query, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}

// Exec executes a command in a container asynchronously
func (c *AsyncContainerClient) Exec(ctx context.Context, id string, options container.ExecOptions) (*taskflow.Task, error) {
	query := url.Values{}
	query.Set("id", "true")
	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Post(ctx, fmt.Sprintf("/api/v1/containers/%s/exec", id), nil, options, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}
