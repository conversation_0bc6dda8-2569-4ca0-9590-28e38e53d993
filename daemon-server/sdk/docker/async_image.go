package docker

import (
	"context"
	"daemon-server/sdk/client"
	"daemon-server/sdk/taskflow"
	"net/url"
	"strconv"
)

// AsyncImageClient is a client for asynchronous image operations
type AsyncImageClient struct {
	client *client.Client
}

// NewAsyncImageClient creates a new asynchronous image client
func NewAsyncImageClient(client *client.Client) *AsyncImageClient {
	return &AsyncImageClient{
		client: client,
	}
}

// Pull pulls an image asynchronously
func (c *AsyncImageClient) Pull(ctx context.Context, image, auth string) (*taskflow.Task, error) {
	// Create request body
	body := struct {
		Image string `json:"image"`
		Auth  string `json:"auth,omitempty"`
	}{
		Image: image,
		Auth:  auth,
	}

	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Post(ctx, "/api/v1/images/pull", nil, body, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}

// Push pushes an image asynchronously
func (c *AsyncImageClient) Push(ctx context.Context, image, auth string) (*taskflow.Task, error) {
	// Create request body
	body := PushImageConfig{
		Image: image,
		Auth:  auth,
	}

	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Post(ctx, "/api/v1/images/push", nil, body, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}

// Save saves images to a tar file asynchronously
func (c *AsyncImageClient) Save(ctx context.Context, imageID string, savePath string) (*taskflow.Task, error) {
	// Create query parameters
	body := struct {
		ImageId  string `json:"image_id"`
		SavePath string `json:"save_path"`
	}{
		ImageId:  imageID,
		SavePath: savePath,
	}

	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Post(ctx, "/api/v1/images/save", nil, body, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}

// Prune removes unused images asynchronously
func (c *AsyncImageClient) Prune(ctx context.Context, all bool) (*taskflow.Task, error) {
	// Create query parameters
	query := url.Values{}
	query.Set("all", strconv.FormatBool(all))

	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Post(ctx, "/api/v1/images/prune", query, nil, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}

// Remove removes an image asynchronously
func (c *AsyncImageClient) Remove(ctx context.Context, id string, force, pruneChildren bool) (*taskflow.Task, error) {
	// Create query parameters
	query := url.Values{}
	query.Set("force", strconv.FormatBool(force))
	query.Set("prune", strconv.FormatBool(pruneChildren))
	query.Set("async", "true") // Use async mode

	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Delete(ctx, "/api/v1/images/"+id, query, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}

// Load loads an image from a local file asynchronously
func (c *AsyncImageClient) Load(ctx context.Context, filePath string) (*taskflow.Task, error) {
	// Create request body
	body := struct {
		FilePath string `json:"file_path"`
	}{
		FilePath: filePath,
	}

	// Send request
	var response struct {
		Message string `json:"message"`
		TaskID  string `json:"task_id"`
	}
	if err := c.client.Post(ctx, "/api/v1/images/load", nil, body, &response); err != nil {
		return nil, err
	}

	return NewTaskflowTask(response.TaskID, c.client), nil
}
