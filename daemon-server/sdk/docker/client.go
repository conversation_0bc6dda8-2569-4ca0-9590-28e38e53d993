package docker

import (
	"context"
	"daemon-server/sdk/client"
	"fmt"
	"time"
)

// DockerClient is a client for the Docker API
type DockerClient struct {
	BaseClient     *client.Client
	Container      *ContainerClient
	Image          *ImageClient
	System         *SystemClient
	AsyncContainer *AsyncContainerClient
	AsyncImage     *AsyncImageClient
}

// Option is a function that configures a DockerClient
type Option func(*DockerClient)

// WithTimeout sets the timeout for HTTP requests
func WithTimeout(timeout time.Duration) Option {
	return func(c *DockerClient) {
		client.WithTimeout(timeout)(c.BaseClient)
	}
}

// Import the client interface from task.go
// The client.Client type should implement this interface

// NewDockerClient creates a new Docker client
func NewDockerClient(baseURL string, options ...Option) *DockerClient {
	baseClient := client.NewClient(baseURL)

	dockerClient := &DockerClient{
		BaseClient: baseClient,
	}

	// Apply options
	for _, option := range options {
		option(dockerClient)
	}

	// Initialize clients
	dockerClient.Container = NewContainerClient(baseClient)
	dockerClient.Image = NewImageClient(baseClient)
	dockerClient.System = NewSystemClient(baseClient)
	dockerClient.AsyncContainer = NewAsyncContainerClient(baseClient)
	dockerClient.AsyncImage = NewAsyncImageClient(baseClient)

	return dockerClient
}

// RefreshConnectionFunc is a function type for refreshing the Docker connection
type RefreshConnectionFunc func(ctx context.Context, getInfoFunc func(ctx context.Context) (interface{}, error)) error

// RefreshConnection is the default implementation of RefreshConnectionFunc
var RefreshConnection RefreshConnectionFunc = func(ctx context.Context, getInfoFunc func(ctx context.Context) (interface{}, error)) error {
	// Try to get system info to check connection
	_, err := getInfoFunc(ctx)
	if err != nil {
		// Connection failed, wait a bit and try again
		time.Sleep(2 * time.Second)

		// Try again
		_, err = getInfoFunc(ctx)
		if err != nil {
			return fmt.Errorf("failed to connect to Docker daemon: %w", err)
		}
	}

	return nil
}

// RefreshConnection checks if the Docker daemon is running and refreshes the connection if needed
func (c *DockerClient) RefreshConnection() error {
	// Create a context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Use the RefreshConnection variable to allow for testing
	return RefreshConnection(ctx, func(ctx context.Context) (interface{}, error) {
		return c.System.GetInfo(ctx)
	})
}
