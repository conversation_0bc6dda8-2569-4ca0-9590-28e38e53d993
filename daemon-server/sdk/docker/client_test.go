package docker

import (
	"bytes"
	"context"
	"encoding/json"
	"github.com/docker/docker/api/types/image"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestDockerClient(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Handle different endpoints
		switch r.URL.Path {
		case "/api/v1/system/info":
			// Get system info
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"info": {
					"id": "mock-id",
					"containers": 1,
					"containers_running": 1,
					"containers_paused": 0,
					"containers_stopped": 0,
					"images": 1,
					"driver": "overlay2",
					"server_version": "20.10.12",
					"operating_system": "Linux",
					"os_type": "linux",
					"architecture": "amd64",
					"ncpu": 4,
					"mem_total": 8589934592,
					"docker_root_dir": "/var/lib/docker"
				}
			}`))
		case "/api/v1/containers":
			// List containers
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"containers": [
					{
						"id": "container1",
						"names": ["test-container"],
						"image": "nginx:latest",
						"command": "nginx -g 'daemon off;'",
						"created": 1672531200,
						"status": "running",
						"ports": [
							{
								"IP": "0.0.0.0",
								"PrivatePort": 80,
								"PublicPort": 8080,
								"Type": "tcp"
							}
						],
						"labels": {
							"test": "true"
						},
						"state": "running"
					}
				]
			}`))
		case "/api/v1/images":
			// List images
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"images": [
					{
						"id": "image1",
						"repo_tags": ["nginx:latest"],
						"repo_digests": ["nginx@sha256:123"],
						"created": "2023-01-01T00:00:00Z",
						"size": 12345,
						"labels": {
							"test": "true"
						}
					}
				]
			}`))
		case "/api/v1/system/disk-usage":
			// Get disk usage
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"layers_size": 12345,
				"images": [
					{
						"id": "image1",
						"repo_tags": ["nginx:latest"],
						"repo_digests": ["nginx@sha256:123"],
						"created": 1672531200,
						"size": 12345,
						"shared_size": 0,
						"unique_size": 12345,
						"containers": 1
					}
				],
				"containers": [
					{
						"id": "container1",
						"names": "test-container",
						"image": "nginx:latest",
						"size_rw": 1234,
						"size_root_fs": 12345
					}
				],
				"volumes": [],
				"build_cache": []
			}`))
		case "/api/v1/images/pull":
			// Pull image
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusAccepted)
			w.Write([]byte(`{
				"message": "Image pulling task started",
				"task_id": "task1"
			}`))
		case "/api/v1/images/image1":
			// Remove image
			if r.Method == http.MethodDelete {
				// Check query parameters
				query := r.URL.Query()
				if query.Get("async") == "true" {
					// Async mode
					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(http.StatusAccepted)
					w.Write([]byte(`{
						"message": "Image removal task started",
						"task_id": "task2"
					}`))
				} else {
					// Sync mode
					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(http.StatusOK)
					w.Write([]byte(`{
						"message": "Image removed"
					}`))
				}
			}
		case "/api/v1/tasks/task1":
			// Get task
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"id": "task1",
				"name": "Pull Image",
				"description": "Pulling image nginx:latest",
				"status": "completed",
				"created_at": "2023-01-01T00:00:00Z",
				"started_at": "2023-01-01T00:00:01Z",
				"completed_at": "2023-01-01T00:00:10Z",
				"error": "",
				"progress": 100,
				"steps": [
					{
						"name": "Pull Image",
						"description": "Pulling image nginx:latest",
						"status": "completed",
						"started_at": "2023-01-01T00:00:01Z",
						"completed_at": "2023-01-01T00:00:10Z",
						"error": "",
						"result": {
							"message": "Image pulled successfully"
						},
						"input": {
							"image": "nginx:latest"
						}
					}
				],
				"result": {
					"message": "Image pulled successfully"
				},
				"input": {
					"image": "nginx:latest"
				}
			}`))
		default:
			// Unknown endpoint
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"error": "Not found"}`))
		}
	}))
	defer server.Close()

	// Create a Docker client
	client := NewDockerClient(server.URL)

	// Test container list
	t.Run("ListContainers", func(t *testing.T) {
		containers, err := client.Container.List(context.Background(), true)
		assert.NoError(t, err)
		assert.Len(t, containers, 1)
		assert.Equal(t, "container1", containers[0].ID)
		assert.Equal(t, "test-container", containers[0].Names[0])
		assert.Equal(t, "nginx:latest", containers[0].Image)
		assert.Equal(t, "running", containers[0].Status)
		assert.Len(t, containers[0].Ports, 1)
		assert.Equal(t, uint16(80), containers[0].Ports[0].PrivatePort)
		assert.Equal(t, uint16(8080), containers[0].Ports[0].PublicPort)
	})

	// Test image list
	t.Run("ListImages", func(t *testing.T) {
		images, err := client.Image.List(context.Background(), image.ListOptions{All: false})
		assert.NoError(t, err)
		assert.Len(t, images, 1)
		assert.Equal(t, "image1", images[0].ID)
		assert.Len(t, images[0].RepoTags, 1)
		assert.Equal(t, "nginx:latest", images[0].RepoTags[0])
		assert.Equal(t, int64(12345), images[0].Size)
	})

	// Test disk usage
	t.Run("GetDiskUsage", func(t *testing.T) {
		diskUsage, err := client.System.GetDiskUsage(context.Background())
		assert.NoError(t, err)
		assert.Equal(t, int64(12345), diskUsage.LayersSize)
		assert.Len(t, diskUsage.Images, 1)
		assert.Len(t, diskUsage.Containers, 1)
		assert.Equal(t, "image1", diskUsage.Images[0].ID)
		assert.Equal(t, "container1", diskUsage.Containers[0].ID)
	})

	// Test async image pull
	t.Run("AsyncImagePull", func(t *testing.T) {
		task, err := client.AsyncImage.Pull(context.Background(), "nginx:latest", "")
		assert.NoError(t, err)
		assert.Equal(t, "task1", task.ID)

		// Get task status
		taskInfo, err := task.GetStatus(context.Background())
		assert.NoError(t, err)
		assert.Equal(t, "task1", taskInfo.ID)
		assert.Equal(t, string(TaskStatusCompleted), string(taskInfo.Status))
		assert.Equal(t, 100, taskInfo.Progress)
		assert.Len(t, taskInfo.Steps, 1)
		assert.Equal(t, "Pull Image", taskInfo.Steps[0].Name)
	})

	// Test async image remove
	t.Run("AsyncImageRemove", func(t *testing.T) {
		task, err := client.AsyncImage.Remove(context.Background(), "image1", false, false)
		assert.NoError(t, err)
		assert.Equal(t, "task2", task.ID)
	})

	// Test system info
	t.Run("GetSystemInfo", func(t *testing.T) {
		info, err := client.System.GetInfo(context.Background())
		assert.NoError(t, err)
		assert.Equal(t, "mock-id", info.ID)
		assert.Equal(t, 1, info.Containers)
		assert.Equal(t, 1, info.Images)
		assert.Equal(t, "20.10.12", info.ServerVersion)
		assert.Equal(t, "Linux", info.OperatingSystem)
		assert.Equal(t, "linux", info.OSType)
		assert.Equal(t, "amd64", info.Architecture)
		assert.Equal(t, 4, info.NCPU)
		assert.Equal(t, int64(8589934592), info.MemTotal)
		assert.Equal(t, "/var/lib/docker", info.DockerRootDir)
	})

	// Test refresh connection
	t.Run("RefreshConnection", func(t *testing.T) {
		err := client.RefreshConnection()
		assert.NoError(t, err)
	})

	// Test get container stats
	t.Run("GetContainerStats", func(t *testing.T) {
		// Create a new test server for container stats
		statsServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.URL.Path == "/api/v1/containers/container1/stats" {
				// Check query parameters
				query := r.URL.Query()
				if query.Get("stream") != "false" {
					t.Errorf("Expected stream=false, got %s", query.Get("stream"))
				}

				// Return container stats
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
					"read": "2023-01-01T00:00:00Z",
					"preread": "2023-01-01T00:00:00Z",
					"cpu_stats": {
						"cpu_usage": {
							"total_usage": 100,
							"percpu_usage": [50, 50],
							"usage_in_kernelmode": 30,
							"usage_in_usermode": 70
						},
						"system_cpu_usage": 200,
						"throttling_data": {
							"periods": 10,
							"throttled_periods": 2,
							"throttled_time": 5
						}
					},
					"memory_stats": {
						"usage": 300,
						"max_usage": 400,
						"stats": {
							"cache": 100,
							"rss": 200
						},
						"failcnt": 0,
						"limit": 1000
					},
					"pids_stats": {
						"current": 5,
						"limit": 100
					},
					"networks": {
						"eth0": {
							"rx_bytes": 1000,
							"rx_packets": 10,
							"rx_errors": 0,
							"rx_dropped": 0,
							"tx_bytes": 2000,
							"tx_packets": 20,
							"tx_errors": 0,
							"tx_dropped": 0
						}
					}
				}`))
			} else if r.URL.Path == "/api/v1/system/info" {
				// Get system info
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
					"info": {
						"id": "mock-id",
						"containers": 1,
						"containers_running": 1,
						"containers_paused": 0,
						"containers_stopped": 0,
						"images": 1,
						"driver": "overlay2",
						"server_version": "20.10.12",
						"operating_system": "Linux",
						"os_type": "linux",
						"architecture": "amd64",
						"ncpu": 4,
						"mem_total": 8589934592,
						"docker_root_dir": "/var/lib/docker"
					}
				}`))
			} else {
				// Unknown endpoint
				w.WriteHeader(http.StatusNotFound)
				w.Write([]byte(`{"error": "Not found"}`))
			}
		}))
		defer statsServer.Close()

		// Create a new client for stats test
		statsClient := NewDockerClient(statsServer.URL)

		// Get container stats
		readtime, err := time.ParseInLocation(time.RFC3339, "2023-01-01T00:00:00Z", time.UTC)
		assert.NoError(t, err)
		prereadtime, err := time.ParseInLocation(time.RFC3339, "2023-01-01T00:00:00Z", time.UTC)
		assert.NoError(t, err)
		stats, err := statsClient.Container.GetContainerStats(context.Background(), "container1", false)
		assert.NoError(t, err)
		assert.NotNil(t, stats)
		assert.Equal(t, readtime, stats.Read)
		assert.Equal(t, prereadtime, stats.PreRead)
		assert.Equal(t, uint64(100), stats.CPUStats.CPUUsage.TotalUsage)
		assert.Equal(t, uint64(200), stats.CPUStats.SystemUsage)
		assert.Equal(t, uint64(300), stats.MemoryStats.Usage)
		assert.Equal(t, uint64(1000), stats.MemoryStats.Limit)
		assert.Equal(t, uint64(5), stats.PidsStats.Current)
		assert.Equal(t, uint64(100), stats.PidsStats.Limit)
		assert.Contains(t, stats.Networks, "eth0")
		assert.Equal(t, uint64(1000), stats.Networks["eth0"].RxBytes)
		assert.Equal(t, uint64(2000), stats.Networks["eth0"].TxBytes)
	})
}

func TestTask(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Handle different endpoints
		switch r.URL.Path {
		case "/api/v1/tasks/task1":
			// Get task
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"id": "task1",
				"name": "Test Task",
				"description": "A test task",
				"status": "completed",
				"created_at": "2023-01-01T00:00:00Z",
				"started_at": "2023-01-01T00:00:01Z",
				"completed_at": "2023-01-01T00:00:10Z",
				"error": "",
				"progress": 100,
				"steps": [
					{
						"name": "Test Step",
						"description": "A test step",
						"status": "completed",
						"started_at": "2023-01-01T00:00:01Z",
						"completed_at": "2023-01-01T00:00:10Z",
						"error": "",
						"result": "step result",
						"input": "step input"
					}
				],
				"result": "task result",
				"input": "task input"
			}`))
		case "/api/v1/tasks/task1/cancel":
			// Cancel task
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"message": "Task cancelled",
				"id": "task1"
			}`))
		case "/api/v1/tasks/task2":
			// Get task for image removal
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"id": "task2",
				"name": "Remove Image",
				"description": "Removing image image1",
				"status": "completed",
				"created_at": "2023-01-01T00:00:00Z",
				"started_at": "2023-01-01T00:00:01Z",
				"completed_at": "2023-01-01T00:00:10Z",
				"error": "",
				"progress": 100,
				"steps": [
					{
						"name": "Remove Image",
						"description": "Removing image image1",
						"status": "completed",
						"started_at": "2023-01-01T00:00:01Z",
						"completed_at": "2023-01-01T00:00:10Z",
						"error": "",
						"result": {
							"message": "Image removed successfully",
							"id": "image1"
						},
						"input": {
							"image_id": "image1",
							"force": false,
							"prune_children": false
						}
					}
				],
				"result": {
					"message": "Image removed successfully",
					"id": "image1"
				},
				"input": {
					"image_id": "image1",
					"force": false,
					"prune_children": false
				}
			}`))
		default:
			// Unknown endpoint
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"error": "Not found"}`))
		}
	}))
	defer server.Close()

	// Create a client
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}
	baseClient := &mockClient{
		baseURL:    server.URL,
		httpClient: httpClient,
	}

	// Create a task
	task := NewTask("task1", baseClient)

	// Test get status
	t.Run("GetStatus", func(t *testing.T) {
		taskInfo, err := task.GetStatus(context.Background())
		assert.NoError(t, err)
		assert.Equal(t, "task1", taskInfo.ID)
		assert.Equal(t, "Test Task", taskInfo.Name)
		assert.Equal(t, string(TaskStatusCompleted), string(taskInfo.Status))
		assert.Equal(t, 100, taskInfo.Progress)
		assert.Len(t, taskInfo.Steps, 1)
		assert.Equal(t, "Test Step", taskInfo.Steps[0].Name)
	})

	// Test cancel
	t.Run("Cancel", func(t *testing.T) {
		err := task.Cancel(context.Background())
		assert.NoError(t, err)
	})
}

// Mock client for testing
type mockClient struct {
	baseURL    string
	httpClient *http.Client
}

func (c *mockClient) Get(ctx context.Context, path string, query url.Values, result interface{}) error {
	// Create request
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, c.baseURL+path, nil)
	if err != nil {
		return err
	}
	if query != nil {
		req.URL.RawQuery = query.Encode()
	}

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Decode response
	return json.NewDecoder(resp.Body).Decode(result)
}

func (c *mockClient) Post(ctx context.Context, path string, query url.Values, body interface{}, result interface{}) error {
	// Create request body
	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			return err
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, c.baseURL+path, bodyReader)
	if err != nil {
		return err
	}
	if query != nil {
		req.URL.RawQuery = query.Encode()
	}
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Decode response
	if result != nil {
		return json.NewDecoder(resp.Body).Decode(result)
	}
	return nil
}

// GetBaseURL returns the base URL of the client
func (c *mockClient) GetBaseURL() string {
	return c.baseURL
}

func (c *mockClient) Delete(ctx context.Context, path string, query url.Values, result interface{}) error {
	// Create request
	req, err := http.NewRequestWithContext(ctx, http.MethodDelete, c.baseURL+path, nil)
	if err != nil {
		return err
	}
	if query != nil {
		req.URL.RawQuery = query.Encode()
	}

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Decode response
	if result != nil {
		return json.NewDecoder(resp.Body).Decode(result)
	}
	return nil
}

func (c *mockClient) GetStream(ctx context.Context, path string, query url.Values) (io.ReadCloser, error) {
	// Create request
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, c.baseURL+path, nil)
	if err != nil {
		return nil, err
	}
	if query != nil {
		req.URL.RawQuery = query.Encode()
	}

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}

	return resp.Body, nil
}

func (c *mockClient) PostStream(ctx context.Context, path string, query url.Values, body interface{}) (io.ReadCloser, error) {
	// Create request body
	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, c.baseURL+path, bodyReader)
	if err != nil {
		return nil, err
	}
	if query != nil {
		req.URL.RawQuery = query.Encode()
	}
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}

	return resp.Body, nil
}

// TestDockerClientRefreshConnectionError tests the RefreshConnection method with an error
func TestDockerClientRefreshConnectionError(t *testing.T) {
	// Create a test server that returns an error
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte(`{"error":"Internal server error"}`))
	}))
	defer server.Close()

	// Create a Docker client with a short timeout
	// Use a custom client that doesn't wait or sleep
	originalRefreshConnection := RefreshConnection
	defer func() { RefreshConnection = originalRefreshConnection }()

	// Override the RefreshConnection function for testing
	RefreshConnection = func(ctx context.Context, getInfoFunc func(ctx context.Context) (interface{}, error)) error {
		_, err := getInfoFunc(ctx)
		return err
	}

	client := NewDockerClient(server.URL)

	// Test refresh connection
	err := client.RefreshConnection()
	assert.Error(t, err)
}
