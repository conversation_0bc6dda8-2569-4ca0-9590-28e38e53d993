package docker

import (
	"context"
	"daemon-server/sdk/client"
	"fmt"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"io"
	"net/url"
	"strconv"
)

// ContainerClient is a client for container operations
type ContainerClient struct {
	client *client.Client
}

// NewContainerClient creates a new container client
func NewContainerClient(client *client.Client) *ContainerClient {
	return &ContainerClient{
		client: client,
	}
}

// List lists containers
func (c *ContainerClient) List(ctx context.Context, all bool) ([]types.Container, error) {
	// Call ListWithFilters with nil filters
	return c.ListWithFilters(ctx, container.ListOptions{All: all})
}

// ListWithFilters lists containers with filters
func (c *ContainerClient) ListWithFilters(ctx context.Context, options container.ListOptions) ([]types.Container, error) {
	// Create query parameters
	query := url.Values{}
	query.Set("all", strconv.FormatBool(options.All))
	query.Set("size", strconv.FormatBool(options.Size))
	filters := options.Filters
	f, err := filters.MarshalJSON()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal filters: %w", err)
	}
	query.Set("filters", string(f))

	// Send request
	var response struct {
		Containers []types.Container `json:"containers"`
	}
	if err := c.client.Get(ctx, "/api/v1/containers", query, &response); err != nil {
		return nil, err
	}

	return response.Containers, nil
}

// Create creates a new container
func (c *ContainerClient) Create(ctx context.Context, config ContainerConfig) (*ContainerInfo, error) {
	// Send request
	var response struct {
		Container ContainerInfo `json:"container"`
	}
	if err := c.client.Post(ctx, "/api/v1/containers", nil, config, &response); err != nil {
		return nil, err
	}

	return &response.Container, nil
}

// Inspect inspects a container
func (c *ContainerClient) Inspect(ctx context.Context, id string) (*ContainerInfo, error) {
	// Send request
	var response struct {
		Container ContainerInfo `json:"container"`
	}
	if err := c.client.Get(ctx, fmt.Sprintf("/api/v1/containers/%s", id), nil, &response); err != nil {
		return nil, err
	}

	return &response.Container, nil
}

// InspectDetailed gets enhanced container details
func (c *ContainerClient) InspectDetailed(ctx context.Context, id string) (*EnhancedContainerDetails, error) {
	// Create query parameters
	query := url.Values{}
	query.Set("detailed", "true")

	// Send request
	var details EnhancedContainerDetails
	if err := c.client.Get(ctx, fmt.Sprintf("/api/v1/containers/%s", id), query, &details); err != nil {
		return nil, err
	}

	return &details, nil
}

// Start starts a container
func (c *ContainerClient) Start(ctx context.Context, id string) error {
	// Send request
	return c.client.Post(ctx, fmt.Sprintf("/api/v1/containers/%s/start", id), nil, nil, nil)
}

// Stop stops a container
func (c *ContainerClient) Stop(ctx context.Context, id string, timeout *int) error {
	// Create query parameters
	query := url.Values{}
	if timeout != nil {
		query.Set("timeout", strconv.Itoa(*timeout))
	}

	// Send request
	return c.client.Post(ctx, fmt.Sprintf("/api/v1/containers/%s/stop", id), query, nil, nil)
}

// Restart restarts a container
func (c *ContainerClient) Restart(ctx context.Context, id string, timeout *int) error {
	// Create query parameters
	query := url.Values{}
	if timeout != nil {
		query.Set("timeout", strconv.Itoa(*timeout))
	}

	// Send request
	return c.client.Post(ctx, fmt.Sprintf("/api/v1/containers/%s/restart", id), query, nil, nil)
}

// Remove removes a container
func (c *ContainerClient) Remove(ctx context.Context, id string, force, removeVolumes bool) error {
	// Create query parameters
	query := url.Values{}
	query.Set("force", strconv.FormatBool(force))
	query.Set("volumes", strconv.FormatBool(removeVolumes))

	// Send request
	return c.client.Delete(ctx, fmt.Sprintf("/api/v1/containers/%s", id), query, nil)
}

// GetLogs gets container logs
func (c *ContainerClient) GetLogs(ctx context.Context, id string, follow bool, tail string) (io.ReadCloser, error) {
	// Create query parameters
	query := url.Values{}
	query.Set("follow", strconv.FormatBool(follow))
	if tail != "" {
		query.Set("tail", tail)
	}

	// Send request
	return c.client.GetStream(ctx, fmt.Sprintf("/api/v1/containers/%s/logs", id), query)
}

// Commit commits a container to an image
func (c *ContainerClient) Commit(ctx context.Context, id string, config ContainerCommitConfig) (string, error) {
	// Send request
	var response struct {
		Message string `json:"message"`
		ImageID string `json:"image_id"`
	}
	if err := c.client.Post(ctx, fmt.Sprintf("/api/v1/containers/%s/commit", id), nil, config, &response); err != nil {
		return "", err
	}

	return response.ImageID, nil
}

// Export exports a container to a tar file
func (c *ContainerClient) Export(ctx context.Context, id string) (io.ReadCloser, error) {
	// Send request
	return c.client.GetStream(ctx, fmt.Sprintf("/api/v1/containers/%s/export", id), nil)
}

// GetPorts gets container port mappings
func (c *ContainerClient) GetPorts(ctx context.Context, id string) ([]PortMapping, error) {
	// Send request
	var response struct {
		Ports []PortMapping `json:"ports"`
	}
	if err := c.client.Get(ctx, fmt.Sprintf("/api/v1/containers/%s/ports", id), nil, &response); err != nil {
		return nil, err
	}

	return response.Ports, nil
}

// ExecConfig represents the configuration for executing a command in a container
type ExecConfig struct {
	Cmd        []string `json:"cmd"`                   // Command to execute
	WorkingDir string   `json:"working_dir,omitempty"` // Working directory
	Env        []string `json:"env,omitempty"`         // Environment variables
	User       string   `json:"user,omitempty"`        // User
	Detach     bool     `json:"detach,omitempty"`      // Whether to detach
	Tty        bool     `json:"tty,omitempty"`         // Whether to allocate a TTY
	Privileged bool     `json:"privileged,omitempty"`  // Whether to run in privileged mode
}

// ExecResult represents the result of executing a command in a container
type ExecResult struct {
	Output   string `json:"output"`    // Command output
	ExitCode int    `json:"exit_code"` // Exit code
}

// GetContainerStats gets container stats
func (c *ContainerClient) GetContainerStats(ctx context.Context, id string, stream bool) (*container.StatsResponse, error) {
	// Create query parameters
	query := url.Values{}
	query.Set("stream", strconv.FormatBool(stream))

	// Send request
	var stats container.StatsResponse
	if err := c.client.Get(ctx, fmt.Sprintf("/api/v1/containers/%s/stats", id), query, &stats); err != nil {
		return nil, err
	}

	return &stats, nil
}
