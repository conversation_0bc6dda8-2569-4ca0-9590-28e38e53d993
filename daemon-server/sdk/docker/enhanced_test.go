package docker

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"daemon-server/sdk/client"
)

func TestContainerInspectDetailed(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check request
		if r.URL.Path != "/api/v1/containers/test-container" {
			t.<PERSON><PERSON><PERSON>("Expected path '/api/v1/containers/test-container', got '%s'", r.URL.Path)
		}
		if r.URL.Query().Get("detailed") != "true" {
			t.Errorf("Expected query parameter 'detailed=true', got '%s'", r.URL.Query().Get("detailed"))
		}

		// Return response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{
			"id": "test-container",
			"name": "test-container",
			"image": "test-image",
			"image_id": "sha256:test-image-id",
			"command": "echo hello",
			"created": "2023-01-01T00:00:00Z",
			"started": "2023-01-01T00:01:00Z",
			"finished": "2023-01-01T00:02:00Z",
			"state": "running",
			"status": "Up 1 hour",
			"exit_code": 0,
			"error": "",
			"restart_count": 0,
			"ports": {
				"80/tcp": [
					{
						"host_ip": "0.0.0.0",
						"host_port": "8080"
					}
				],
				"443/tcp": [
					{
						"host_ip": "0.0.0.0",
						"host_port": "8443"
					}
				]
			},
			"network_mode": "bridge",
			"network_settings": {
				"networks": {
					"bridge": {
						"ip_address": "**********",
						"gateway": "**********"
					}
				}
			},
			"ip_address": "**********",
			"ip_prefix_len": 16,
			"gateway": "**********",
			"mac_address": "02:42:ac:11:00:02",
			"size_rw": 12345,
			"size_root_fs": 67890,
			"host_config": {
				"privileged": false,
				"port_bindings": {
					"80/tcp": [
						{
							"host_ip": "0.0.0.0",
							"host_port": "8080"
						}
					],
					"443/tcp": [
						{
							"host_ip": "0.0.0.0",
							"host_port": "8443"
						}
					]
				}
			},
			"config": {
				"hostname": "test-container",
				"domainname": "",
				"user": "",
				"env": ["PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"],
				"cmd": ["echo", "hello"],
				"image": "test-image",
				"labels": {
					"test": "true",
					"version": "1.0.0"
				}
			},
			"health": {
				"status": "healthy",
				"failing_streak": 0,
				"log": []
			},
			"mounts": [
				{
					"type": "bind",
					"source": "/host/path",
					"destination": "/container/path",
					"mode": "rw",
					"rw": true,
					"propagation": "rprivate"
				}
			],
			"labels": {
				"test": "true",
				"version": "1.0.0"
			}
		}`))
	}))
	defer server.Close()

	// Create client
	baseClient := client.NewClient(server.URL)
	containerClient := NewContainerClient(baseClient)

	// Call InspectDetailed
	details, err := containerClient.InspectDetailed(context.Background(), "test-container")
	if err != nil {
		t.Fatalf("Failed to get enhanced container details: %v", err)
	}

	// Check basic container information
	if details.ID != "test-container" {
		t.Errorf("Expected ID 'test-container', got '%s'", details.ID)
	}
	if details.Name != "test-container" {
		t.Errorf("Expected Name 'test-container', got '%s'", details.Name)
	}
	if details.Image != "test-image" {
		t.Errorf("Expected Image 'test-image', got '%s'", details.Image)
	}
	if details.ImageID != "sha256:test-image-id" {
		t.Errorf("Expected ImageID 'sha256:test-image-id', got '%s'", details.ImageID)
	}
	if details.Command != "echo hello" {
		t.Errorf("Expected Command 'echo hello', got '%s'", details.Command)
	}

	// Check timestamps
	expectedCreated, _ := time.Parse(time.RFC3339, "2023-01-01T00:00:00Z")
	if !details.Created.Equal(expectedCreated) {
		t.Errorf("Expected Created '%s', got '%s'", expectedCreated, details.Created)
	}
	expectedStarted, _ := time.Parse(time.RFC3339, "2023-01-01T00:01:00Z")
	if !details.Started.Equal(expectedStarted) {
		t.Errorf("Expected Started '%s', got '%s'", expectedStarted, details.Started)
	}
	expectedFinished, _ := time.Parse(time.RFC3339, "2023-01-01T00:02:00Z")
	if !details.Finished.Equal(expectedFinished) {
		t.Errorf("Expected Finished '%s', got '%s'", expectedFinished, details.Finished)
	}

	// Check state information
	if details.State != "running" {
		t.Errorf("Expected State 'running', got '%s'", details.State)
	}
	if details.Status != "Up 1 hour" {
		t.Errorf("Expected Status 'Up 1 hour', got '%s'", details.Status)
	}
	if details.ExitCode != 0 {
		t.Errorf("Expected ExitCode 0, got %d", details.ExitCode)
	}
	if details.Error != "" {
		t.Errorf("Expected Error '', got '%s'", details.Error)
	}
	if details.RestartCount != 0 {
		t.Errorf("Expected RestartCount 0, got %d", details.RestartCount)
	}

	// Check network settings
	if details.NetworkMode != "bridge" {
		t.Errorf("Expected NetworkMode 'bridge', got '%s'", details.NetworkMode)
	}
	if details.IPAddress != "**********" {
		t.Errorf("Expected IPAddress '**********', got '%s'", details.IPAddress)
	}
	if details.IPPrefixLen != 16 {
		t.Errorf("Expected IPPrefixLen 16, got %d", details.IPPrefixLen)
	}
	if details.Gateway != "**********" {
		t.Errorf("Expected Gateway '**********', got '%s'", details.Gateway)
	}
	if details.MacAddress != "02:42:ac:11:00:02" {
		t.Errorf("Expected MacAddress '02:42:ac:11:00:02', got '%s'", details.MacAddress)
	}

	// Check resource settings
	if details.SizeRw != 12345 {
		t.Errorf("Expected SizeRw 12345, got %d", details.SizeRw)
	}
	if details.SizeRootFs != 67890 {
		t.Errorf("Expected SizeRootFs 67890, got %d", details.SizeRootFs)
	}

	// Check ports
	if len(details.Ports) != 2 {
		t.Errorf("Expected 2 port mappings, got %d", len(details.Ports))
	} else {
		if len(details.Ports["80/tcp"]) != 1 {
			t.Errorf("Expected 1 port binding for 80/tcp, got %d", len(details.Ports["80/tcp"]))
		} else {
			if details.Ports["80/tcp"][0].HostIP != "0.0.0.0" {
				t.Errorf("Expected HostIP '0.0.0.0', got '%s'", details.Ports["80/tcp"][0].HostIP)
			}
			if details.Ports["80/tcp"][0].HostPort != "8080" {
				t.Errorf("Expected HostPort '8080', got '%s'", details.Ports["80/tcp"][0].HostPort)
			}
		}
		if len(details.Ports["443/tcp"]) != 1 {
			t.Errorf("Expected 1 port binding for 443/tcp, got %d", len(details.Ports["443/tcp"]))
		} else {
			if details.Ports["443/tcp"][0].HostIP != "0.0.0.0" {
				t.Errorf("Expected HostIP '0.0.0.0', got '%s'", details.Ports["443/tcp"][0].HostIP)
			}
			if details.Ports["443/tcp"][0].HostPort != "8443" {
				t.Errorf("Expected HostPort '8443', got '%s'", details.Ports["443/tcp"][0].HostPort)
			}
		}
	}

	// Check mounts
	if len(details.Mounts) != 1 {
		t.Errorf("Expected 1 mount, got %d", len(details.Mounts))
	} else {
		if details.Mounts[0].Type != "bind" {
			t.Errorf("Expected mount Type 'bind', got '%s'", details.Mounts[0].Type)
		}
		if details.Mounts[0].Source != "/host/path" {
			t.Errorf("Expected mount Source '/host/path', got '%s'", details.Mounts[0].Source)
		}
		if details.Mounts[0].Destination != "/container/path" {
			t.Errorf("Expected mount Destination '/container/path', got '%s'", details.Mounts[0].Destination)
		}
		if details.Mounts[0].Mode != "rw" {
			t.Errorf("Expected mount Mode 'rw', got '%s'", details.Mounts[0].Mode)
		}
		if !details.Mounts[0].RW {
			t.Errorf("Expected mount RW true, got %v", details.Mounts[0].RW)
		}
		if details.Mounts[0].Propagation != "rprivate" {
			t.Errorf("Expected mount Propagation 'rprivate', got '%s'", details.Mounts[0].Propagation)
		}
	}

	// Check labels
	if len(details.Labels) != 2 {
		t.Errorf("Expected 2 labels, got %d", len(details.Labels))
	} else {
		if details.Labels["test"] != "true" {
			t.Errorf("Expected Labels['test'] 'true', got '%s'", details.Labels["test"])
		}
		if details.Labels["version"] != "1.0.0" {
			t.Errorf("Expected Labels['version'] '1.0.0', got '%s'", details.Labels["version"])
		}
	}
}

func TestImageInspectDetailed(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check request
		if r.URL.Path != "/api/v1/images/test-image" {
			t.Errorf("Expected path '/api/v1/images/test-image', got '%s'", r.URL.Path)
		}

		// Return response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{
			"ID": "sha256:test-image-id",
			"RepoTags": ["test-image:latest"],
			"RepoDigests": ["test-image@sha256:digest"],
			"Parent": "sha256:parent-image-id",
			"Comment": "Test comment",
			"Created": "2023-01-01T00:00:00Z",
			"Container": "test-container-id",
			"DockerVersion": "20.10.12",
			"Author": "Test Author",
			"Architecture": "amd64",
			"Os": "linux",
			"OsVersion": "10.0",
			"Size": 10000,
			"VirtualSize": 20000,
			"Config": {
				"Labels": {
					"test": "true",
					"version": "1.0.0"
				}
			},
			"GraphDriver": {
				"Name": "overlay2",
				"Data": {
					"MergedDir": "/var/lib/docker/overlay2/123/merged"
				}
			},
			"RootFS": {
				"Type": "layers",
				"Layers": ["sha256:layer1", "sha256:layer2"]
			}
		}`))
	}))
	defer server.Close()

	// Create client
	baseClient := client.NewClient(server.URL)
	imageClient := NewImageClient(baseClient)

	// Call InspectDetailed
	details, err := imageClient.InspectDetailed(context.Background(), "test-image")
	if err != nil {
		t.Fatalf("Failed to get enhanced image details: %v", err)
	}

	// Check response
	if details.ID != "sha256:test-image-id" {
		t.Errorf("Expected ID 'sha256:test-image-id', got '%s'", details.ID)
	}
	if len(details.RepoTags) != 1 || details.RepoTags[0] != "test-image:latest" {
		t.Errorf("Expected RepoTags ['test-image:latest'], got %v", details.RepoTags)
	}
	if len(details.RepoDigests) != 1 || details.RepoDigests[0] != "test-image@sha256:digest" {
		t.Errorf("Expected RepoDigests ['test-image@sha256:digest'], got %v", details.RepoDigests)
	}
	expectedTime := "2023-01-01T00:00:00Z"
	if details.Created != expectedTime {
		t.Errorf("Expected Created '%s', got '%s'", expectedTime, details.Created)
	}
	if details.Size != 10000 {
		t.Errorf("Expected Size 10000, got %d", details.Size)
	}
	if details.VirtualSize != 20000 {
		t.Errorf("Expected VirtualSize 20000, got %d", details.VirtualSize)
	}
	if details.Architecture != "amd64" {
		t.Errorf("Expected Architecture 'amd64', got '%s'", details.Architecture)
	}
	if details.Os != "linux" {
		t.Errorf("Expected Os 'linux', got '%s'", details.Os)
	}
	if details.OsVersion != "10.0" {
		t.Errorf("Expected OsVersion '10.0', got '%s'", details.OsVersion)
	}
	if details.Author != "Test Author" {
		t.Errorf("Expected Author 'Test Author', got '%s'", details.Author)
	}
	if details.Parent != "sha256:parent-image-id" {
		t.Errorf("Expected Parent 'sha256:parent-image-id', got '%s'", details.Parent)
	}
	if details.Comment != "Test comment" {
		t.Errorf("Expected Comment 'Test comment', got '%s'", details.Comment)
	}
	if details.Container != "test-container-id" {
		t.Errorf("Expected Container 'test-container-id', got '%s'", details.Container)
	}
	if details.DockerVersion != "20.10.12" {
		t.Errorf("Expected DockerVersion '20.10.12', got '%s'", details.DockerVersion)
	}

	// Check labels
	if details.Config.Labels["test"] != "true" {
		t.Errorf("Expected Config.Labels['test'] 'true', got '%s'", details.Config.Labels["test"])
	}
	if details.Config.Labels["version"] != "1.0.0" {
		t.Errorf("Expected Config.Labels['version'] '1.0.0', got '%s'", details.Config.Labels["version"])
	}

	// Check RootFS
	if details.RootFS.Type != "layers" {
		t.Errorf("Expected RootFS.Type 'layers', got '%s'", details.RootFS.Type)
	}
	if len(details.RootFS.Layers) != 2 {
		t.Errorf("Expected RootFS.Layers to have 2 items, got %d", len(details.RootFS.Layers))
	}
}

func TestContainerInspectDetailedError(t *testing.T) {
	// Create a test server that returns an error
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Return error response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusNotFound)
		w.Write([]byte(`{"message":"Container not found"}`));
	}))
	defer server.Close()

	// Create client
	baseClient := client.NewClient(server.URL)
	containerClient := NewContainerClient(baseClient)

	// Call InspectDetailed
	_, err := containerClient.InspectDetailed(context.Background(), "non-existent-container")

	// Check error
	if err == nil {
		t.Fatalf("Expected error for non-existent container, got nil")
	}

	// Check error message
	if !strings.Contains(err.Error(), "Container not found") {
		t.Errorf("Expected error message to contain 'Container not found', got '%s'", err.Error())
	}
}

func TestImageInspectDetailedError(t *testing.T) {
	// Create a test server that returns an error
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Return error response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusNotFound)
		w.Write([]byte(`{"message":"Image not found"}`));
	}))
	defer server.Close()

	// Create client
	baseClient := client.NewClient(server.URL)
	imageClient := NewImageClient(baseClient)

	// Call InspectDetailed
	_, err := imageClient.InspectDetailed(context.Background(), "non-existent-image")

	// Check error
	if err == nil {
		t.Fatalf("Expected error for non-existent image, got nil")
	}

	// Check error message
	if !strings.Contains(err.Error(), "Image not found") {
		t.Errorf("Expected error message to contain 'Image not found', got '%s'", err.Error())
	}
}