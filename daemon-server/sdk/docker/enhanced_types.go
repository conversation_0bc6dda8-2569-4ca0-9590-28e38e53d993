package docker

import (
	"github.com/docker/docker/api/types"
	"time"
)

// EnhancedContainerDetails represents enhanced container details with additional fields
type EnhancedContainerDetails struct {
	// Basic container information
	ID           string    `json:"id"`
	Name         string    `json:"name"`
	Image        string    `json:"image"`
	ImageID      string    `json:"image_id"`
	Command      string    `json:"command"`
	Created      time.Time `json:"created"`
	Started      time.Time `json:"started,omitempty"`
	Finished     time.Time `json:"finished,omitempty"`
	State        string    `json:"state"`
	Status       string    `json:"status"`
	ExitCode     int       `json:"exit_code"`
	Error        string    `json:"error,omitempty"`
	RestartCount int       `json:"restart_count"`

	// Network settings
	Ports           map[string][]PortBinding `json:"ports,omitempty"`
	NetworkMode     string                   `json:"network_mode"`
	NetworkSettings interface{}              `json:"network_settings,omitempty"`
	IPAddress       string                   `json:"ip_address,omitempty"`
	IPPrefixLen     int                      `json:"ip_prefix_len,omitempty"`
	Gateway         string                   `json:"gateway,omitempty"`
	MacAddress      string                   `json:"mac_address,omitempty"`

	// Resource settings
	SizeRw     int64       `json:"size_rw,omitempty"`
	SizeRootFs int64       `json:"size_root_fs,omitempty"`
	HostConfig interface{} `json:"host_config,omitempty"`
	Config     interface{} `json:"config,omitempty"`

	// Health information
	Health interface{} `json:"health,omitempty"`

	// Mount information
	Mounts []types.MountPoint `json:"mounts,omitempty"`

	// Labels
	Labels map[string]string `json:"labels,omitempty"`
}

// PortBinding represents a port binding
type PortBinding struct {
	HostIP   string `json:"host_ip"`
	HostPort string `json:"host_port"`
}

// EnhancedImageDetails represents enhanced image details with additional fields
type EnhancedImageDetails struct {
	// Basic image information
	ID            string    `json:"id"`
	RepoTags      []string  `json:"repo_tags"`
	RepoDigests   []string  `json:"repo_digests"`
	Parent        string    `json:"parent,omitempty"`
	Comment       string    `json:"comment,omitempty"`
	Created       time.Time `json:"created"`
	Container     string    `json:"container,omitempty"`
	DockerVersion string    `json:"docker_version,omitempty"`
	Author        string    `json:"author,omitempty"`
	Architecture  string    `json:"architecture,omitempty"`
	Os            string    `json:"os,omitempty"`
	OsVersion     string    `json:"os_version,omitempty"`
	Size          int64     `json:"size"`
	VirtualSize   int64     `json:"virtual_size"`

	// Config information
	Config interface{} `json:"config,omitempty"`

	// GraphDriver information
	GraphDriver interface{} `json:"graph_driver,omitempty"`

	// RootFS information
	RootFS interface{} `json:"root_fs,omitempty"`

	// Additional metadata
	Metadata map[string]string `json:"metadata,omitempty"`

	// Labels
	Labels map[string]string `json:"labels,omitempty"`

	// History information
	History []string `json:"history,omitempty"`
}
