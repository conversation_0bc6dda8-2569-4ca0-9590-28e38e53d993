package docker

import (
	"context"
	"daemon-server/sdk/client"
	"fmt"
	"github.com/docker/docker/api/types/image"
	"io"
	"net/url"
	"strconv"
	"strings"
)

// ImageClient is a client for image operations
type ImageClient struct {
	client *client.Client
}

// NewImageClient creates a new image client
func NewImageClient(client *client.Client) *ImageClient {
	return &ImageClient{
		client: client,
	}
}

// List lists images
func (c *ImageClient) List(ctx context.Context, options image.ListOptions) ([]image.Summary, error) {
	// convert options to query parameters
	query := url.Values{}
	query.Set("all", strconv.FormatBool(options.All))
	filters := options.Filters
	f, err := filters.MarshalJSON()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal filters: %w", err)
	}
	query.Set("filters", string(f))

	// Send request
	var response struct {
		Images []image.Summary `json:"images"`
	}
	if err := c.client.Get(ctx, "/api/v1/images", query, &response); err != nil {
		return nil, err
	}

	return response.Images, nil
}

// Pull pulls an image
func (c *ImageClient) Pull(ctx context.Context, image, auth string) (io.ReadCloser, error) {
	// Create request body
	body := struct {
		Image string `json:"image"`
		Auth  string `json:"auth,omitempty"`
	}{
		Image: image,
		Auth:  auth,
	}

	// Send request
	return c.client.PostStream(ctx, "/api/v1/images/pull", nil, body)
}

// Inspect inspects an image
func (c *ImageClient) Inspect(ctx context.Context, id string) (*image.InspectResponse, error) {
	// Send request
	var response image.InspectResponse
	if err := c.client.Get(ctx, fmt.Sprintf("/api/v1/images/%s", id), nil, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// InspectDetailed gets enhanced image details
func (c *ImageClient) InspectDetailed(ctx context.Context, id string) (*image.InspectResponse, error) {
	return c.Inspect(ctx, id)
}

// History gets the history of an image
func (c *ImageClient) History(ctx context.Context, id string) ([]image.HistoryResponseItem, error) {
	// Send request
	var response struct {
		History []image.HistoryResponseItem `json:"history"`
	}
	if err := c.client.Get(ctx, fmt.Sprintf("/api/v1/images/%s/history", id), nil, &response); err != nil {
		return nil, err
	}

	return response.History, nil
}

// Remove removes an image
func (c *ImageClient) Remove(ctx context.Context, id string, force, pruneChildren bool) error {
	// Create query parameters
	query := url.Values{}
	query.Set("force", strconv.FormatBool(force))
	query.Set("prune", strconv.FormatBool(pruneChildren))

	// Send request
	return c.client.Delete(ctx, fmt.Sprintf("/api/v1/images/%s", id), query, nil)
}

// Push pushes an image
func (c *ImageClient) Push(ctx context.Context, image, auth string) (io.ReadCloser, error) {
	// Create request body
	body := PushImageConfig{
		Image: image,
		Auth:  auth,
	}

	// Send request
	return c.client.PostStream(ctx, "/api/v1/images/push", nil, body)
}

// Save saves images to a tar file
func (c *ImageClient) Save(ctx context.Context, imageIDs []string) (io.ReadCloser, error) {
	// Create query parameters
	query := url.Values{}
	query.Set("images", strings.Join(imageIDs, ","))

	// Send request
	return c.client.GetStream(ctx, "/api/v1/images/save", query)
}

// Prune removes unused images
func (c *ImageClient) Prune(ctx context.Context, all bool) (*ImagePruneResponse, error) {
	// Create query parameters
	query := url.Values{}
	query.Set("all", strconv.FormatBool(all))

	// Send request
	var response ImagePruneResponse
	if err := c.client.Post(ctx, "/api/v1/images/prune", query, nil, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// Load loads an image from a local file
func (c *ImageClient) Load(ctx context.Context, filePath string) (io.ReadCloser, error) {
	// Create request body
	body := struct {
		FilePath string `json:"file_path"`
	}{
		FilePath: filePath,
	}

	// Send request
	return c.client.PostStream(ctx, "/api/v1/images/load", nil, body)
}
