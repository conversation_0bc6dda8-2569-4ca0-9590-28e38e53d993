package docker

import (
	"context"
	"daemon-server/sdk/client"
)

// SystemClient is a client for system operations
type SystemClient struct {
	client *client.Client
}

// NewSystemClient creates a new system client
func NewSystemClient(client *client.Client) *SystemClient {
	return &SystemClient{
		client: client,
	}
}

// GetDiskUsage gets disk usage information
func (c *SystemClient) GetDiskUsage(ctx context.Context) (*DiskUsageInfo, error) {
	// Send request
	var response DiskUsageInfo
	if err := c.client.Get(ctx, "/api/v1/system/disk-usage", nil, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// GetInfo gets system information
func (c *SystemClient) GetInfo(ctx context.Context) (*SystemInfo, error) {
	// Send request
	var response struct {
		Info SystemInfo `json:"info"`
	}
	if err := c.client.Get(ctx, "/api/v1/system/info", nil, &response); err != nil {
		return nil, err
	}

	return &response.Info, nil
}

// GetVersion gets Docker version information
func (c *SystemClient) GetVersion(ctx context.Context) (*VersionInfo, error) {
	// Send request
	var response struct {
		Version VersionInfo `json:"version"`
	}
	if err := c.client.Get(ctx, "/api/v1/system/version", nil, &response); err != nil {
		return nil, err
	}

	return &response.Version, nil
}
