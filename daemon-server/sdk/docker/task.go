package docker

import (
	"context"
	"daemon-server/sdk/taskflow"
	"net/url"
	"time"
)

// ClientInterface defines the interface for client operations
type ClientInterface interface {
	Get(ctx context.Context, path string, query url.Values, result interface{}) error
	Post(ctx context.Context, path string, query url.Values, body interface{}, result interface{}) error
	GetBaseURL() string
}

// Task represents an asynchronous task
// This is a wrapper around taskflow.Task for backward compatibility
type Task struct {
	*taskflow.Task
}

// NewTask creates a new task
func NewTask(id string, client ClientInterface) *Task {
	return &Task{
		Task: NewTaskflowTask(id, client),
	}
}

func NewTaskflowTask(id string, client ClientInterface) *taskflow.Task {
	return taskflow.NewTask(id, taskflow.NewTaskflowClient(client.GetBaseURL()))
}

// GetStatus gets the status of the task
func (t *Task) GetStatus(ctx context.Context) (*TaskInfo, error) {
	// Get task info from taskflow
	taskInfo, err := t.Task.GetStatus(ctx)
	if err != nil {
		return nil, err
	}

	// Convert taskflow.TaskInfo to docker.TaskInfo
	return convertTaskInfo(taskInfo), nil
}

// Wait waits for the task to complete
func (t *Task) Wait(ctx context.Context, pollInterval time.Duration, timeout time.Duration) (*TaskInfo, error) {
	// Wait for task to complete using taskflow
	taskInfo, err := t.Task.Wait(ctx, pollInterval, timeout)
	if err != nil {
		return nil, err
	}

	// Convert taskflow.TaskInfo to docker.TaskInfo
	return convertTaskInfo(taskInfo), nil
}

// Helper function to convert taskflow.TaskInfo to docker.TaskInfo
func convertTaskInfo(info *taskflow.TaskInfo) *TaskInfo {
	if info == nil {
		return nil
	}

	// Convert steps
	steps := make([]StepInfo, len(info.Steps))
	for i, step := range info.Steps {
		steps[i] = StepInfo{
			Name:        step.Name,
			Description: step.Description,
			Status:      TaskStatus(step.Status),
			StartedAt:   step.StartedAt,
			CompletedAt: step.CompletedAt,
			Error:       step.Error,
			Result:      step.Result,
			Input:       step.Input,
		}
	}

	return &TaskInfo{
		ID:          info.ID,
		Name:        info.Name,
		Description: info.Description,
		Status:      TaskStatus(info.Status),
		CreatedAt:   info.CreatedAt,
		StartedAt:   info.StartedAt,
		CompletedAt: info.CompletedAt,
		Error:       info.Error,
		Progress:    info.Progress,
		Steps:       steps,
		Result:      info.Result,
		Input:       info.Input,
	}
}
