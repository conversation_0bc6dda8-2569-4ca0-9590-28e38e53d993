package docker

import (
	"time"
)

// TaskStatus represents the status of a task
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"
	TaskStatusRunning   TaskStatus = "running"
	TaskStatusCompleted TaskStatus = "completed"
	TaskStatusFailed    TaskStatus = "failed"
	TaskStatusCancelled TaskStatus = "cancelled"
)

// TaskInfo represents information about a task
type TaskInfo struct {
	ID          string      `json:"id"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Status      TaskStatus  `json:"status"`
	CreatedAt   time.Time   `json:"created_at"`
	StartedAt   *time.Time  `json:"started_at,omitempty"`
	CompletedAt *time.Time  `json:"completed_at,omitempty"`
	Error       string      `json:"error,omitempty"`
	Progress    int         `json:"progress"`
	Steps       []StepInfo  `json:"steps"`
	Result      interface{} `json:"result,omitempty"`
	Input       interface{} `json:"input,omitempty"`
}

// StepInfo represents information about a task step
type StepInfo struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Status      TaskStatus  `json:"status"`
	StartedAt   *time.Time  `json:"started_at,omitempty"`
	CompletedAt *time.Time  `json:"completed_at,omitempty"`
	Error       string      `json:"error,omitempty"`
	Result      interface{} `json:"result,omitempty"`
	Input       interface{} `json:"input,omitempty"`
}

// ContainerConfig represents the configuration for creating a container
type ContainerConfig struct {
	Name        string            `json:"name"`
	Image       string            `json:"image"`
	Command     []string          `json:"command,omitempty"`
	Entrypoint  []string          `json:"entrypoint,omitempty"`
	Env         []string          `json:"env,omitempty"`
	Labels      map[string]string `json:"labels,omitempty"`
	Ports       []string          `json:"ports,omitempty"`
	Volumes     []string          `json:"volumes,omitempty"`
	NetworkMode string            `json:"network_mode,omitempty"`
	Restart     string            `json:"restart,omitempty"`
	Privileged  bool              `json:"privileged,omitempty"`
	AutoRemove  bool              `json:"auto_remove,omitempty"`
}

// ContainerInfo represents information about a container
type ContainerInfo struct {
	ID      string            `json:"id"`
	Name    string            `json:"name"`
	Image   string            `json:"image"`
	Command string            `json:"command"`
	Created time.Time         `json:"created"`
	Status  string            `json:"status"`
	Ports   []PortMapping     `json:"ports"`
	Labels  map[string]string `json:"labels"`
	State   string            `json:"state"`
}

// PortMapping represents a port mapping
type PortMapping struct {
	IP          string `json:"ip"`
	PrivatePort uint16 `json:"private_port"`
	PublicPort  uint16 `json:"public_port"`
	Type        string `json:"type"`
}

// ContainerCommitConfig represents the configuration for committing a container
type ContainerCommitConfig struct {
	Repository string   `json:"repository"`
	Tag        string   `json:"tag"`
	Author     string   `json:"author,omitempty"`
	Comment    string   `json:"comment,omitempty"`
	Changes    []string `json:"changes,omitempty"`
	Pause      bool     `json:"pause,omitempty"`
}

// PushImageConfig represents the configuration for pushing an image
type PushImageConfig struct {
	Image string `json:"image"`
	Auth  string `json:"auth,omitempty"`
}

// DiskUsageInfo represents disk usage information
type DiskUsageInfo struct {
	LayersSize int64                 `json:"layers_size"`
	Images     []ImageDiskUsageInfo  `json:"images"`
	Containers []ContainerDiskUsage  `json:"containers"`
	Volumes    []VolumeDiskUsageInfo `json:"volumes"`
	BuildCache []BuildCacheDiskUsage `json:"build_cache"`
}

// ImageDiskUsageInfo represents disk usage information for an image
type ImageDiskUsageInfo struct {
	ID          string   `json:"id"`
	RepoTags    []string `json:"repo_tags"`
	RepoDigests []string `json:"repo_digests"`
	Created     int64    `json:"created"`
	Size        int64    `json:"size"`
	SharedSize  int64    `json:"shared_size"`
	UniqueSize  int64    `json:"unique_size"`
	Containers  int      `json:"containers"`
}

// ContainerDiskUsage represents disk usage information for a container
type ContainerDiskUsage struct {
	ID         string `json:"id"`
	Names      string `json:"names"`
	Image      string `json:"image"`
	SizeRw     int64  `json:"size_rw"`
	SizeRootFs int64  `json:"size_root_fs"`
}

// VolumeDiskUsageInfo represents disk usage information for a volume
type VolumeDiskUsageInfo struct {
	Name       string `json:"name"`
	Driver     string `json:"driver"`
	Mountpoint string `json:"mountpoint"`
	Size       int64  `json:"size"`
	RefCount   int    `json:"ref_count"`
}

// BuildCacheDiskUsage represents disk usage information for build cache
type BuildCacheDiskUsage struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Size        int64     `json:"size"`
	CreatedAt   time.Time `json:"created_at"`
	LastUsedAt  time.Time `json:"last_used_at"`
	UsageCount  int       `json:"usage_count"`
	Shared      bool      `json:"shared"`
	Description string    `json:"description"`
}

// ImagePruneResponse represents the response from pruning images
type ImagePruneResponse struct {
	ImagesDeleted  []ImageDeleteResponseItem `json:"images_deleted"`
	SpaceReclaimed int64                     `json:"space_reclaimed"`
}

// ImageDeleteResponseItem represents an item in the image delete response
type ImageDeleteResponseItem struct {
	Untagged string `json:"untagged"`
	Deleted  string `json:"deleted"`
}

// SystemInfo represents information about the Docker system
type SystemInfo struct {
	ID                string `json:"id"`
	Containers        int    `json:"containers"`
	ContainersRunning int    `json:"containers_running"`
	ContainersPaused  int    `json:"containers_paused"`
	ContainersStopped int    `json:"containers_stopped"`
	Images            int    `json:"images"`
	Driver            string `json:"driver"`
	ServerVersion     string `json:"server_version"`
	OperatingSystem   string `json:"operating_system"`
	OSType            string `json:"os_type"`
	Architecture      string `json:"architecture"`
	NCPU              int    `json:"ncpu"`
	MemTotal          int64  `json:"mem_total"`
	DockerRootDir     string `json:"docker_root_dir"`
}

// VersionInfo represents version information
type VersionInfo struct {
	Version       string `json:"version"`
	APIVersion    string `json:"api_version"`
	MinAPIVersion string `json:"min_api_version"`
	GitCommit     string `json:"git_commit"`
	GoVersion     string `json:"go_version"`
	Os            string `json:"os"`
	Arch          string `json:"arch"`
	BuildTime     string `json:"build_time"`
}

// ContainerStats represents container stats
type ContainerStats struct {
	Read        string                  `json:"read"`
	PreRead     string                  `json:"preread"`
	CPUStats    CPUStats                `json:"cpu_stats,omitempty"`
	PreCPUStats CPUStats                `json:"precpu_stats,omitempty"`
	MemoryStats MemoryStats             `json:"memory_stats,omitempty"`
	BlkioStats  BlkioStats              `json:"blkio_stats,omitempty"`
	PidsStats   PidsStats               `json:"pids_stats,omitempty"`
	Networks    map[string]NetworkStats `json:"networks,omitempty"`
}

// CPUStats aggregates and wraps all CPU related info of container
type CPUStats struct {
	CPUUsage       CPUUsage       `json:"cpu_usage"`
	SystemUsage    uint64         `json:"system_cpu_usage"`
	ThrottlingData ThrottlingData `json:"throttling_data,omitempty"`
}

// CPUUsage stores All CPU stats aggregated since container inception
type CPUUsage struct {
	TotalUsage        uint64   `json:"total_usage"`
	PercpuUsage       []uint64 `json:"percpu_usage"`
	UsageInKernelmode uint64   `json:"usage_in_kernelmode"`
	UsageInUsermode   uint64   `json:"usage_in_usermode"`
}

// ThrottlingData stores CPU throttling stats of one running container
type ThrottlingData struct {
	Periods          uint64 `json:"periods"`
	ThrottledPeriods uint64 `json:"throttled_periods"`
	ThrottledTime    uint64 `json:"throttled_time"`
}

// MemoryStats aggregates all memory stats since container inception
type MemoryStats struct {
	Usage    uint64            `json:"usage"`
	MaxUsage uint64            `json:"max_usage"`
	Stats    map[string]uint64 `json:"stats"`
	Failcnt  uint64            `json:"failcnt"`
	Limit    uint64            `json:"limit"`
}

// BlkioStats stores All IO service stats for data read and write
type BlkioStats struct {
	IoServiceBytesRecursive []BlkioStatEntry `json:"io_service_bytes_recursive"`
	IoServicedRecursive     []BlkioStatEntry `json:"io_serviced_recursive"`
	IoQueuedRecursive       []BlkioStatEntry `json:"io_queue_recursive"`
	IoServiceTimeRecursive  []BlkioStatEntry `json:"io_service_time_recursive"`
	IoWaitTimeRecursive     []BlkioStatEntry `json:"io_wait_time_recursive"`
	IoMergedRecursive       []BlkioStatEntry `json:"io_merged_recursive"`
	IoTimeRecursive         []BlkioStatEntry `json:"io_time_recursive"`
	SectorsRecursive        []BlkioStatEntry `json:"sectors_recursive"`
}

// BlkioStatEntry is one small entity to store a piece of Blkio stats
type BlkioStatEntry struct {
	Major uint64 `json:"major"`
	Minor uint64 `json:"minor"`
	Op    string `json:"op"`
	Value uint64 `json:"value"`
}

// NetworkStats aggregates the network stats of one container
type NetworkStats struct {
	RxBytes   uint64 `json:"rx_bytes"`
	RxPackets uint64 `json:"rx_packets"`
	RxErrors  uint64 `json:"rx_errors"`
	RxDropped uint64 `json:"rx_dropped"`
	TxBytes   uint64 `json:"tx_bytes"`
	TxPackets uint64 `json:"tx_packets"`
	TxErrors  uint64 `json:"tx_errors"`
	TxDropped uint64 `json:"tx_dropped"`
}

// PidsStats contains the stats of a container's pids
type PidsStats struct {
	Current uint64 `json:"current,omitempty"`
	Limit   uint64 `json:"limit,omitempty"`
}
