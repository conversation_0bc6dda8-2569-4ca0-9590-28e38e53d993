package system

import (
	"context"
	"daemon-server/internal/logger"
	"fmt"
	"time"

	"daemon-server/sdk/client"
	"daemon-server/sdk/taskflow"
)

// SystemClient provides a client for the system API
type SystemClient struct {
	client *client.Client
}

// WithTimeout sets the timeout for HTTP requests
func WithTimeout(timeout time.Duration) client.Option {
	return client.WithTimeout(timeout)
}

// NewSystemClient creates a new system client
func NewSystemClient(baseURL string, options ...client.Option) *SystemClient {
	return &SystemClient{
		client: client.NewClient(baseURL, options...),
	}
}

// ExecuteCommandRequest represents a request to execute a command
type ExecuteCommandRequest struct {
	Command    string   `json:"command"`               // Command to execute
	Args       []string `json:"args,omitempty"`        // Command arguments
	WorkingDir string   `json:"working_dir,omitempty"` // Working directory
	Env        []string `json:"env,omitempty"`         // Environment variables
	Timeout    int      `json:"timeout,omitempty"`     // Timeout in seconds
	TaskID     string   `json:"task_id,omitempty"`     // Task ID for real-time output
}

// ExecuteCommandResponse represents the response from executing a command
type ExecuteCommandResponse struct {
	Message string `json:"message"`
	TaskID  string `json:"task_id"`
}

// ExecuteCommand executes a command asynchronously
func (c *SystemClient) ExecuteCommand(ctx context.Context, req ExecuteCommandRequest) (*taskflow.Task, error) {
	var resp ExecuteCommandResponse
	if err := c.client.Post(ctx, "/api/v1/system/exec", nil, req, &resp); err != nil {
		return nil, fmt.Errorf("failed to execute command: %w", err)
	}

	// Create a task object for the client to track
	taskClient := taskflow.NewTaskflowClient(c.client.BaseURL)
	return taskflow.NewTask(resp.TaskID, taskClient), nil
}

// ExecuteCommandAndWait executes a command and waits for it to complete
func (c *SystemClient) ExecuteCommandAndWait(ctx context.Context, req ExecuteCommandRequest,
	pollInterval, timeoutSecond int) (*taskflow.TaskInfo, error) {
	logger.Debug("ExecuteCommandAndWait", logger.String("command", req.Command), logger.String("args", fmt.Sprintf("%v", req.Args)))
	// Execute command
	task, err := c.ExecuteCommand(ctx, req)
	if err != nil {
		return nil, err
	}
	logger.Debug("Task: Executing command, taskID: %s", logger.String("taskID", task.ID))
	// Wait for task to complete
	return task.Wait(ctx, time.Second*time.Duration(pollInterval), time.Second*time.Duration(timeoutSecond))
}

// CommandResult represents the result of a command execution
type CommandResult struct {
	Command   string    `json:"command"`         // Command that was executed
	ExitCode  int       `json:"exit_code"`       // Exit code of the command
	Output    string    `json:"output"`          // Combined stdout and stderr
	Stdout    string    `json:"stdout"`          // Standard output
	Stderr    string    `json:"stderr"`          // Standard error
	Error     string    `json:"error,omitempty"` // Error message if any
	StartTime time.Time `json:"start_time"`      // When the command started
	EndTime   time.Time `json:"end_time"`        // When the command completed
	Duration  string    `json:"duration"`        // Duration of execution
}

// ExecuteCommandWithRealTimeOutput executes a command asynchronously with real-time output
func (c *SystemClient) ExecuteCommandWithRealTimeOutput(ctx context.Context, req ExecuteCommandRequest) (*taskflow.Task, error) {
	var resp ExecuteCommandResponse
	if err := c.client.Post(ctx, "/api/v1/system/exec/realtime", nil, req, &resp); err != nil {
		return nil, fmt.Errorf("failed to execute command[%s] with real-time output: %w", req.Command, err)
	}

	// Create a task object for the client to track
	taskClient := taskflow.NewTaskflowClient(c.client.BaseURL)
	return taskflow.NewTask(resp.TaskID, taskClient), nil
}

// GetCommandResult gets the result of a command execution
func (c *SystemClient) GetCommandResult(ctx context.Context, taskID string) (*CommandResult, error) {
	// Get task info
	taskClient := taskflow.NewTaskflowClient(c.client.BaseURL)
	taskInfo, err := taskClient.GetTask(ctx, taskID)
	logger.Debug("GetCommandResult", logger.String("taskID", taskID), logger.Any("taskInfo", taskInfo))
	if err != nil {
		return nil, fmt.Errorf("failed to get task[%s] info: %w", taskID, err)
	}

	// Check if task is completed
	if taskInfo.Status == taskflow.TaskStatusFailed {
		return nil, fmt.Errorf("task[%s] is failed[%s]: %s", taskID, taskInfo.Status, taskInfo.Error)
	}
	if taskInfo.Status == taskflow.TaskStatusCancelled {
		return nil, fmt.Errorf("task[%s] is canceled[%s]: %s", taskID, taskInfo.Status, taskInfo.Stdout)
	}
	if taskInfo.Status != taskflow.TaskStatusCompleted {
		return nil, fmt.Errorf("task[%s] is not completed: %s", taskID, taskInfo.Status)
	}

	// Extract command result from task result
	if taskInfo.Result == nil {
		return nil, fmt.Errorf("task result is nil")
	}

	// Convert result to CommandResult
	resultMap, ok := taskInfo.Result.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid result type: %T", taskInfo.Result)
	}

	// Extract fields
	command, _ := resultMap["command"].(string)
	exitCode := 0
	if exitCodeFloat, ok := resultMap["exit_code"].(float64); ok {
		exitCode = int(exitCodeFloat)
	}
	output, _ := resultMap["output"].(string)
	stdout, _ := resultMap["stdout"].(string)
	stderr, _ := resultMap["stderr"].(string)
	errorMsg, _ := resultMap["error"].(string)

	// Parse times
	var startTime, endTime time.Time
	if startTimeStr, ok := resultMap["start_time"].(string); ok {
		startTime, _ = time.Parse(time.RFC3339, startTimeStr)
	}
	if endTimeStr, ok := resultMap["end_time"].(string); ok {
		endTime, _ = time.Parse(time.RFC3339, endTimeStr)
	}

	duration, _ := resultMap["duration"].(string)

	return &CommandResult{
		Command:   command,
		ExitCode:  exitCode,
		Output:    output,
		Stdout:    stdout,
		Stderr:    stderr,
		Error:     errorMsg,
		StartTime: startTime,
		EndTime:   endTime,
		Duration:  duration,
	}, nil
}
