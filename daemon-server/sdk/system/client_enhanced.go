package system

import (
	"context"
	"daemon-server/sdk/taskflow"
	"fmt"
)

// ExecuteCommandWithRealTimeOutputAndSource executes a command asynchronously with real-time output
// The callback receives the output line and a boolean indicating if it's from stdout (true) or stderr (false)
func (c *SystemClient) ExecuteCommandWithRealTimeOutputAndSource(ctx context.Context, req ExecuteCommandRequest) (*taskflow.Task, error) {
	var resp ExecuteCommandResponse
	if err := c.client.Post(ctx, "/api/v1/system/exec/realtime", nil, req, &resp); err != nil {
		return nil, fmt.Errorf("failed to execute command with real-time output: %w", err)
	}

	// Create a task object for the client to track
	taskClient := taskflow.NewTaskflowClient(c.client.BaseURL)
	return taskflow.NewTask(resp.TaskID, taskClient), nil
}
