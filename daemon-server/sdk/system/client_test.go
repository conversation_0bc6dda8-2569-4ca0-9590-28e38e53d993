package system

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestExecuteCommandWithRealTimeOutput(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check request method and path
		assert.Equal(t, http.MethodPost, r.Method)
		assert.Equal(t, "/api/v1/system/exec/realtime", r.URL.Path)

		// Check request body
		var req ExecuteCommandRequest
		err := json.NewDecoder(r.Body).Decode(&req)
		assert.NoError(t, err)
		assert.Equal(t, "echo", req.Command)
		assert.Equal(t, []string{"hello", "world"}, req.Args)

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusAccepted)
		json.NewEncoder(w).Encode(ExecuteCommandResponse{
			Message: "Command execution task with real-time output started",
			TaskID:  "test-task-id",
		})
	}))
	defer server.Close()

	// Create client
	client := NewSystemClient(server.URL)

	// Execute command with real-time output
	req := ExecuteCommandRequest{
		Command: "echo",
		Args:    []string{"hello", "world"},
	}
	task, err := client.ExecuteCommandWithRealTimeOutput(context.Background(), req)
	assert.NoError(t, err)
	assert.Equal(t, "test-task-id", task.ID)
}

func TestExecuteCommand(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check request method and path
		assert.Equal(t, http.MethodPost, r.Method)
		assert.Equal(t, "/api/v1/system/exec", r.URL.Path)

		// Check request body
		var req ExecuteCommandRequest
		err := json.NewDecoder(r.Body).Decode(&req)
		assert.NoError(t, err)
		assert.Equal(t, "ls", req.Command)
		assert.Equal(t, []string{"-la"}, req.Args)

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusAccepted)
		json.NewEncoder(w).Encode(ExecuteCommandResponse{
			Message: "Command execution task started",
			TaskID:  "test-task-id",
		})
	}))
	defer server.Close()

	// Create client
	client := NewSystemClient(server.URL)

	// Execute command
	req := ExecuteCommandRequest{
		Command: "ls",
		Args:    []string{"-la"},
	}
	task, err := client.ExecuteCommand(context.Background(), req)
	assert.NoError(t, err)
	assert.Equal(t, "test-task-id", task.ID)
}

func TestGetCommandResult(t *testing.T) {
	// Create a test server for taskflow API
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check request method and path
		assert.Equal(t, http.MethodGet, r.Method)
		assert.Equal(t, "/api/v1/tasks/test-task-id", r.URL.Path)

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"id":          "test-task-id",
			"name":        "Execute Command",
			"description": "Executing command: ls -la",
			"status":      "completed",
			"progress":    100,
			"created_at":  "2023-01-01T00:00:00Z",
			"updated_at":  "2023-01-01T00:00:01Z",
			"started_at":  "2023-01-01T00:00:00Z",
			"completed_at": "2023-01-01T00:00:01Z",
			"result": map[string]interface{}{
				"command":    "ls -la",
				"exit_code":  0,
				"output":     "total 0\ndrwxr-xr-x 2 <USER> <GROUP> 0 Jan 1 00:00 .",
				"start_time": "2023-01-01T00:00:00Z",
				"end_time":   "2023-01-01T00:00:01Z",
				"duration":   "1s",
			},
		})
	}))
	defer server.Close()

	// Create client
	client := NewSystemClient(server.URL)

	// Get command result
	result, err := client.GetCommandResult(context.Background(), "test-task-id")
	assert.NoError(t, err)
	assert.Equal(t, "ls -la", result.Command)
	assert.Equal(t, 0, result.ExitCode)
	assert.Equal(t, "total 0\ndrwxr-xr-x 2 <USER> <GROUP> 0 Jan 1 00:00 .", result.Output)
	assert.Equal(t, "1s", result.Duration)
}

func TestExecuteCommandAndWait(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Handle different endpoints
		switch r.URL.Path {
		case "/api/v1/system/exec":
			// Check request method
			assert.Equal(t, http.MethodPost, r.Method)

			// Check request body
			var req ExecuteCommandRequest
			err := json.NewDecoder(r.Body).Decode(&req)
			assert.NoError(t, err)
			assert.Equal(t, "ls", req.Command)
			assert.Equal(t, []string{"-la"}, req.Args)

			// Send response
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusAccepted)
			json.NewEncoder(w).Encode(ExecuteCommandResponse{
				Message: "Command execution task started",
				TaskID:  "test-task-id",
			})

		case "/api/v1/tasks/test-task-id":
			// Check request method
			assert.Equal(t, http.MethodGet, r.Method)

			// Send response
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(map[string]interface{}{
				"id":          "test-task-id",
				"name":        "Execute Command",
				"description": "Executing command: ls -la",
				"status":      "completed",
				"progress":    100,
				"created_at":  "2023-01-01T00:00:00Z",
				"updated_at":  "2023-01-01T00:00:01Z",
				"started_at":  "2023-01-01T00:00:00Z",
				"completed_at": "2023-01-01T00:00:01Z",
				"result": map[string]interface{}{
					"command":    "ls -la",
					"exit_code":  0,
					"output":     "total 0\ndrwxr-xr-x 2 <USER> <GROUP> 0 Jan 1 00:00 .",
					"start_time": "2023-01-01T00:00:00Z",
					"end_time":   "2023-01-01T00:00:01Z",
					"duration":   "1s",
				},
			})

		default:
			t.Fatalf("Unexpected request to %s", r.URL.Path)
		}
	}))
	defer server.Close()

	// Create client
	client := NewSystemClient(server.URL)

	// Execute command and wait
	req := ExecuteCommandRequest{
		Command: "ls",
		Args:    []string{"-la"},
	}
	taskInfo, err := client.ExecuteCommandAndWait(context.Background(), req, 10*time.Millisecond, 100*time.Millisecond)
	assert.NoError(t, err)
	assert.Equal(t, "test-task-id", taskInfo.ID)
	assert.Equal(t, "Execute Command", taskInfo.Name)
	assert.Equal(t, "completed", string(taskInfo.Status))
	assert.Equal(t, 100, taskInfo.Progress)
}
