package taskflow

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"daemon-server/sdk/client"
)

// TaskflowClient is a client for the Taskflow API
type TaskflowClient struct {
	client *client.Client
}

// Option is a function that configures a TaskflowClient
type Option func(*TaskflowClient)

// WithTimeout sets the timeout for HTTP requests
func WithTimeout(timeout time.Duration) Option {
	return func(c *TaskflowClient) {
		client.WithTimeout(timeout)(c.client)
	}
}

// NewTaskflowClient creates a new Taskflow client
func NewTaskflowClient(baseURL string, options ...Option) *TaskflowClient {
	baseClient := client.NewClient(baseURL)

	taskflowClient := &TaskflowClient{
		client: baseClient,
	}

	// Apply options
	for _, option := range options {
		option(taskflowClient)
	}

	return taskflowClient
}

// ListTasks lists all tasks
func (c *TaskflowClient) ListTasks(ctx context.Context) ([]TaskInfo, error) {
	// Send request
	var response struct {
		Tasks []TaskInfo `json:"tasks"`
	}
	if err := c.client.Get(ctx, "/api/v1/tasks", nil, &response); err != nil {
		return nil, err
	}

	return response.Tasks, nil
}

// ListTasksWithFilter lists tasks matching the filter
func (c *TaskflowClient) ListTasksWithFilter(ctx context.Context, filter TaskFilter) ([]TaskInfo, error) {
	// Create query parameters
	query := url.Values{}
	if filter.Status != "" {
		query.Set("status", string(filter.Status))
	}
	if !filter.StartTime.IsZero() {
		query.Set("start_time", filter.StartTime.Format(time.RFC3339))
	}
	if !filter.EndTime.IsZero() {
		query.Set("end_time", filter.EndTime.Format(time.RFC3339))
	}

	// Send request
	var response struct {
		Tasks []TaskInfo `json:"tasks"`
	}
	if err := c.client.Get(ctx, "/api/v1/tasks", query, &response); err != nil {
		return nil, err
	}

	return response.Tasks, nil
}

// CreateTask creates a new task
func (c *TaskflowClient) CreateTask(ctx context.Context, request CreateTaskRequest) (*TaskInfo, error) {
	// Send request
	var taskInfo TaskInfo
	if err := c.client.Post(ctx, "/api/v1/tasks", nil, request, &taskInfo); err != nil {
		return nil, err
	}

	return &taskInfo, nil
}

// GetTask gets a task by ID
func (c *TaskflowClient) GetTask(ctx context.Context, id string) (*TaskInfo, error) {
	// Send request
	var taskInfo TaskInfo
	if err := c.client.Get(ctx, fmt.Sprintf("/api/v1/tasks/%s", id), nil, &taskInfo); err != nil {
		return nil, err
	}

	return &taskInfo, nil
}

// DeleteTask deletes a task
func (c *TaskflowClient) DeleteTask(ctx context.Context, id string) error {
	// Send request
	return c.client.Delete(ctx, fmt.Sprintf("/api/v1/tasks/%s", id), nil, nil)
}

// ExecuteTask executes a task
func (c *TaskflowClient) ExecuteTask(ctx context.Context, id string, timeout *time.Duration) error {
	// Create query parameters
	query := url.Values{}
	if timeout != nil {
		query.Set("timeout", timeout.String())
	}

	// Send request
	return c.client.Post(ctx, fmt.Sprintf("/api/v1/tasks/%s/execute", id), query, nil, nil)
}

// CancelTask cancels a task
func (c *TaskflowClient) CancelTask(ctx context.Context, id string) error {
	// Send request
	return c.client.Post(ctx, fmt.Sprintf("/api/v1/tasks/%s/cancel", id), nil, nil, nil)
}

// UpdateTaskProgress updates the progress of a task
func (c *TaskflowClient) UpdateTaskProgress(ctx context.Context, id string, progress int, message string) error {
	// Create request body
	body := ProgressUpdateRequest{
		Progress: progress,
		Message:  message,
	}

	// Send request
	return c.client.Post(ctx, fmt.Sprintf("/api/v1/tasks/%s/progress", id), nil, body, nil)
}

// GetTaskProgress gets the progress of a task
func (c *TaskflowClient) GetTaskProgress(ctx context.Context, id string) (*ProgressInfo, error) {
	// Send request
	var progressInfo ProgressInfo
	if err := c.client.Get(ctx, fmt.Sprintf("/api/v1/tasks/%s/progress", id), nil, &progressInfo); err != nil {
		return nil, err
	}

	return &progressInfo, nil
}

// GetTaskOutput gets the output/result of a task
func (c *TaskflowClient) GetTaskOutput(ctx context.Context, id string) (*TaskOutput, error) {
	// Send request
	var taskOutput TaskOutput
	if err := c.client.Get(ctx, fmt.Sprintf("/api/v1/tasks/%s/output", id), nil, &taskOutput); err != nil {
		return nil, err
	}

	return &taskOutput, nil
}

// WaitForTask waits for a task to complete
func (c *TaskflowClient) WaitForTask(ctx context.Context, id string, pollInterval time.Duration, timeout time.Duration) (*TaskInfo, error) {
	// Create a context with timeout
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// Poll for task status
	ticker := time.NewTicker(pollInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-ticker.C:
			// Get task status
			taskInfo, err := c.GetTask(ctx, id)
			if err != nil {
				return nil, err
			}

			// Check if task is completed
			if taskInfo.Status == TaskStatusCompleted || taskInfo.Status == TaskStatusFailed || taskInfo.Status == TaskStatusCancelled {
				return taskInfo, nil
			}
		}
	}
}
