package taskflow

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestTaskflowClient(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Handle different endpoints
		switch r.URL.Path {
		case "/api/v1/tasks":
			if r.Method == http.MethodGet {
				// List tasks
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
					"tasks": [
						{
							"id": "task1",
							"name": "Test Task 1",
							"description": "A test task",
							"status": "completed",
							"created_at": "2023-01-01T00:00:00Z",
							"started_at": "2023-01-01T00:00:01Z",
							"completed_at": "2023-01-01T00:00:10Z",
							"error": "",
							"progress": 100,
							"steps": [
								{
									"name": "Test Step",
									"description": "A test step",
									"status": "completed",
									"started_at": "2023-01-01T00:00:01Z",
									"completed_at": "2023-01-01T00:00:10Z",
									"error": "",
									"result": "step result",
									"input": "step input"
								}
							],
							"result": "task result",
							"input": "task input"
						}
					]
				}`))
			} else if r.Method == http.MethodPost {
				// Create task
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusCreated)
				w.Write([]byte(`{
					"id": "task2",
					"name": "New Task",
					"description": "A new task",
					"status": "pending",
					"created_at": "2023-01-01T00:00:00Z",
					"started_at": null,
					"completed_at": null,
					"error": "",
					"progress": 0,
					"steps": [
						{
							"name": "New Step",
							"description": "A new step",
							"status": "pending",
							"started_at": null,
							"completed_at": null,
							"error": "",
							"result": null,
							"input": "step input"
						}
					],
					"result": null,
					"input": "task input"
				}`))
			}
		case "/api/v1/tasks/task1":
			if r.Method == http.MethodGet {
				// Get task
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
					"id": "task1",
					"name": "Test Task 1",
					"description": "A test task",
					"status": "completed",
					"created_at": "2023-01-01T00:00:00Z",
					"started_at": "2023-01-01T00:00:01Z",
					"completed_at": "2023-01-01T00:00:10Z",
					"error": "",
					"progress": 100,
					"steps": [
						{
							"name": "Test Step",
							"description": "A test step",
							"status": "completed",
							"started_at": "2023-01-01T00:00:01Z",
							"completed_at": "2023-01-01T00:00:10Z",
							"error": "",
							"result": "step result",
							"input": "step input"
						}
					],
					"result": "task result",
					"input": "task input"
				}`))
			} else if r.Method == http.MethodDelete {
				// Delete task
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
					"message": "Task deleted",
					"id": "task1"
				}`))
			}
		case "/api/v1/tasks/task1/execute":
			// Execute task
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"message": "Task execution started",
				"id": "task1"
			}`))
		case "/api/v1/tasks/task1/cancel":
			// Cancel task
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"message": "Task cancelled",
				"id": "task1"
			}`))
		case "/api/v1/tasks/task1/progress":
			if r.Method == http.MethodPost {
				// Update task progress
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
					"message": "Task progress updated",
					"id": "task1"
				}`))
			} else if r.Method == http.MethodGet {
				// Get task progress
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{
					"progress": 50,
					"message": "Halfway there"
				}`))
			}
		default:
			// Unknown endpoint
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"error": "Not found"}`))
		}
	}))
	defer server.Close()

	// Create a Taskflow client
	client := NewTaskflowClient(server.URL)

	// Test list tasks
	t.Run("ListTasks", func(t *testing.T) {
		tasks, err := client.ListTasks(context.Background())
		assert.NoError(t, err)
		assert.Len(t, tasks, 1)
		assert.Equal(t, "task1", tasks[0].ID)
		assert.Equal(t, "Test Task 1", tasks[0].Name)
		assert.Equal(t, TaskStatusCompleted, tasks[0].Status)
		assert.Equal(t, 100, tasks[0].Progress)
		assert.Len(t, tasks[0].Steps, 1)
		assert.Equal(t, "Test Step", tasks[0].Steps[0].Name)
	})

	// Test create task
	t.Run("CreateTask", func(t *testing.T) {
		request := CreateTaskRequest{
			Name:        "New Task",
			Description: "A new task",
			Steps: []StepRequest{
				{
					Name:        "New Step",
					Description: "A new step",
					Input:       "step input",
				},
			},
			Input: "task input",
		}
		task, err := client.CreateTask(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, "task2", task.ID)
		assert.Equal(t, "New Task", task.Name)
		assert.Equal(t, TaskStatusPending, task.Status)
		assert.Equal(t, 0, task.Progress)
		assert.Len(t, task.Steps, 1)
		assert.Equal(t, "New Step", task.Steps[0].Name)
	})

	// Test get task
	t.Run("GetTask", func(t *testing.T) {
		task, err := client.GetTask(context.Background(), "task1")
		assert.NoError(t, err)
		assert.Equal(t, "task1", task.ID)
		assert.Equal(t, "Test Task 1", task.Name)
		assert.Equal(t, TaskStatusCompleted, task.Status)
		assert.Equal(t, 100, task.Progress)
		assert.Len(t, task.Steps, 1)
		assert.Equal(t, "Test Step", task.Steps[0].Name)
	})

	// Test execute task
	t.Run("ExecuteTask", func(t *testing.T) {
		err := client.ExecuteTask(context.Background(), "task1", nil)
		assert.NoError(t, err)
	})

	// Test cancel task
	t.Run("CancelTask", func(t *testing.T) {
		err := client.CancelTask(context.Background(), "task1")
		assert.NoError(t, err)
	})

	// Test update task progress
	t.Run("UpdateTaskProgress", func(t *testing.T) {
		err := client.UpdateTaskProgress(context.Background(), "task1", 50, "Halfway there")
		assert.NoError(t, err)
	})

	// Test get task progress
	t.Run("GetTaskProgress", func(t *testing.T) {
		progress, err := client.GetTaskProgress(context.Background(), "task1")
		assert.NoError(t, err)
		assert.Equal(t, 50, progress.Progress)
		assert.Equal(t, "Halfway there", progress.Message)
	})

	// Test delete task
	t.Run("DeleteTask", func(t *testing.T) {
		err := client.DeleteTask(context.Background(), "task1")
		assert.NoError(t, err)
	})
}

func TestWaitForTask(t *testing.T) {
	// Create a test server that simulates a task that completes after a few calls
	callCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/api/v1/tasks/task1" {
			callCount++
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)

			status := TaskStatusRunning
			progress := 25 * callCount
			var completedAt *time.Time

			if callCount >= 4 {
				status = TaskStatusCompleted
				now := time.Now()
				completedAt = &now
			}

			task := TaskInfo{
				ID:          "task1",
				Name:        "Test Task",
				Description: "A test task",
				Status:      status,
				CreatedAt:   time.Now().Add(-time.Minute),
				StartedAt:   timePtr(time.Now().Add(-time.Minute)),
				CompletedAt: completedAt,
				Error:       "",
				Progress:    progress,
				Steps: []StepInfo{
					{
						Name:        "Test Step",
						Description: "A test step",
						Status:      status,
						StartedAt:   timePtr(time.Now().Add(-time.Minute)),
						CompletedAt: completedAt,
						Error:       "",
						Result:      nil,
						Input:       "step input",
					},
				},
				Result: nil,
				Input:  "task input",
			}

			if callCount >= 4 {
				task.Result = "task result"
				task.Steps[0].Result = "step result"
			}

			json.NewEncoder(w).Encode(task)
		} else {
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"error": "Not found"}`))
		}
	}))
	defer server.Close()

	// Create a Taskflow client
	client := NewTaskflowClient(server.URL)

	// Test wait for task
	t.Run("WaitForTask", func(t *testing.T) {
		task, err := client.WaitForTask(context.Background(), "task1", 10*time.Millisecond, 1*time.Second)
		assert.NoError(t, err)
		assert.Equal(t, "task1", task.ID)
		assert.Equal(t, TaskStatusCompleted, task.Status)
		assert.Equal(t, 100, task.Progress)
		assert.NotNil(t, task.CompletedAt)
		assert.Equal(t, "task result", task.Result)
	})
}

// Helper function to create a time pointer
func timePtr(t time.Time) *time.Time {
	return &t
}
