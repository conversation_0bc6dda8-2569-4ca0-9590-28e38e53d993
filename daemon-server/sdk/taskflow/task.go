package taskflow

import (
	"context"
	"daemon-server/internal/logger"
	"fmt"
	"time"
)

// TaskClient defines the interface for task operations
type TaskClient interface {
	GetTask(ctx context.Context, id string) (*TaskInfo, error)
	GetTaskProgress(ctx context.Context, id string) (*ProgressInfo, error)
	GetTaskOutput(ctx context.Context, id string) (*TaskOutput, error)
	CancelTask(ctx context.Context, id string) error
}

// Task represents an asynchronous task
type Task struct {
	ID     string
	client TaskClient
}

// NewTask creates a new task
func NewTask(id string, client TaskClient) *Task {
	return &Task{
		ID:     id,
		client: client,
	}
}

// GetStatus gets the status of the task
func (t *Task) GetStatus(ctx context.Context) (*TaskInfo, error) {
	return t.client.GetTask(ctx, t.ID)
}

// Wait waits for the task to complete
func (t *Task) Wait(ctx context.Context, pollInterval time.Duration, timeout time.Duration) (*TaskInfo, error) {
	// Create a context with timeout
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	logger.Debug("waiting for task: %s, timeout: %v, poll interval: %v", logger.String("task_id", t.ID), logger.Any("timeout", timeout), logger.Any("poll_interval", pollInterval))
	// Poll for task status
	ticker := time.NewTicker(pollInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			logger.Debug("ctx done with task: %s", logger.String("task_id", t.ID))
			return nil, ctx.Err()
		case <-ticker.C:
			// Get task status
			taskInfo, err := t.GetStatus(ctx)
			logger.Debug("get task status: %s", logger.String("task_id", t.ID))
			if err != nil {
				return nil, err
			}

			// Check if task is completed
			if taskInfo.Status == TaskStatusCompleted || taskInfo.Status == TaskStatusFailed || taskInfo.Status == TaskStatusCancelled {
				return taskInfo, nil
			}
		}
	}
}

// Cancel cancels the task
func (t *Task) Cancel(ctx context.Context) error {
	return t.client.CancelTask(ctx, t.ID)
}

// GetProgress gets the progress of the task
func (t *Task) GetProgress(ctx context.Context) (int, string, error) {
	progress, err := t.client.GetTaskProgress(ctx, t.ID)
	if err != nil {
		return 0, "", err
	}
	return progress.Progress, progress.Message, nil
}

// GetProgressInfo gets the progress of the task
func (t *Task) GetProgressInfo(ctx context.Context) (*ProgressInfo, error) {
	return t.client.GetTaskProgress(ctx, t.ID)
}

// GetOutput gets the output/result of the task
func (t *Task) GetOutput(ctx context.Context) (*TaskOutput, error) {
	return t.client.GetTaskOutput(ctx, t.ID)
}

// GetResult gets the result of the task
func (t *Task) GetResult(ctx context.Context) (interface{}, error) {
	taskInfo, err := t.GetStatus(ctx)
	if err != nil {
		return nil, err
	}

	if taskInfo.Status != TaskStatusCompleted {
		return nil, fmt.Errorf("task is not completed: %s", taskInfo.Status)
	}

	return taskInfo.Result, nil
}

// GetInfo is an alias for GetStatus for backward compatibility
func (t *Task) GetInfo(ctx context.Context) (*TaskInfo, error) {
	return t.GetStatus(ctx)
}

// StreamOutput streams the output of the task to a callback function
func (t *Task) StreamOutput(ctx context.Context, pollInterval time.Duration, callback func(string)) error {
	// Create a ticker for polling
	ticker := time.NewTicker(pollInterval)
	defer ticker.Stop()

	// Keep track of the last message to avoid duplicates
	var lastMessage string

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			// Check if task is complete first to avoid unnecessary progress calls
			taskInfo, err := t.GetStatus(ctx)
			if err != nil {
				return fmt.Errorf("failed to get task status: %w", err)
			}

			// If task is complete, stop streaming
			if taskInfo.Status == TaskStatusCompleted || taskInfo.Status == TaskStatusFailed || taskInfo.Status == TaskStatusCancelled {
				return nil
			}

			// Get progress and message
			progressInfo, err := t.GetProgressInfo(ctx)
			if err != nil {
				return fmt.Errorf("failed to get progress: %w", err)
			}

			// If we have a new message, call the callback
			if progressInfo.Message != "" && progressInfo.Message != lastMessage {
				callback(progressInfo.Message)
				lastMessage = progressInfo.Message
			}
		}
	}
}

func (t *Task) GetTaskOutputByOffset(ctx context.Context, offset int, isStdout bool) ([]string, error) {
	taskInfo, err := t.GetStatus(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get task status: %w", err)
	}

	// If task is complete, stop streaming
	if taskInfo.Status == TaskStatusCompleted {
		return nil, nil
	} else if taskInfo.Status == TaskStatusFailed {
		output, err := t.GetOutput(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to get task[%s] output by get stream output: %w", t.ID, err)
		}
		return nil, fmt.Errorf("task[%s] is failed: %s, output: %v", t.ID, taskInfo.Status, output)
	} else if taskInfo.Status == TaskStatusCancelled {
		return nil, fmt.Errorf("task[%s] is canceled: %s", t.ID, taskInfo.Status)
	}

	// Get progress and messages
	info, err := t.GetProgressInfo(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get progress: %w", err)
	}
	result := make([]string, 0)
	if isStdout {
		// If we have messages, check for new ones
		if len(info.Stdout) > 0 && len(info.Stdout) > offset {
			result = info.Stdout[offset:]
		}
	} else {
		if len(info.Stderr) > 0 && len(info.Stderr) > offset {
			result = info.Stderr[offset:]
		}
	}
	return result, nil
}
