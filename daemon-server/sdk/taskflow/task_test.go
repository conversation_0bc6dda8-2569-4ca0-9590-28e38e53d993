package taskflow

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// MockTaskflowClient is a mock implementation of the TaskflowClient for testing
type MockTaskflowClient struct {
	BaseURL                 string
	GetTaskFunc             func(ctx context.Context, id string) (*TaskInfo, error)
	GetTaskProgressFunc     func(ctx context.Context, id string) (*ProgressInfo, error)
	GetTaskOutputFunc       func(ctx context.Context, id string) (*TaskOutput, error)
	CancelTaskFunc          func(ctx context.Context, id string) error
	UpdateTaskProgressFunc  func(ctx context.Context, id string, progress int, message string) error
	ListTasksFunc           func(ctx context.Context) ([]TaskInfo, error)
	ListTasksWithFilterFunc func(ctx context.Context, filter TaskFilter) ([]TaskInfo, error)
	CreateTaskFunc          func(ctx context.Context, request CreateTaskRequest) (*TaskInfo, error)
	DeleteTaskFunc          func(ctx context.Context, id string) error
	ExecuteTaskFunc         func(ctx context.Context, id string, timeout *time.Duration) error
	WaitForTaskFunc         func(ctx context.Context, id string, pollInterval time.Duration, timeout time.Duration) (*TaskInfo, error)
}

func (m *MockTaskflowClient) GetTask(ctx context.Context, id string) (*TaskInfo, error) {
	if m.GetTaskFunc != nil {
		return m.GetTaskFunc(ctx, id)
	}
	return nil, errors.New("GetTaskFunc not implemented")
}

func (m *MockTaskflowClient) GetTaskProgress(ctx context.Context, id string) (*ProgressInfo, error) {
	if m.GetTaskProgressFunc != nil {
		return m.GetTaskProgressFunc(ctx, id)
	}
	return nil, errors.New("GetTaskProgressFunc not implemented")
}

func (m *MockTaskflowClient) GetTaskOutput(ctx context.Context, id string) (*TaskOutput, error) {
	if m.GetTaskOutputFunc != nil {
		return m.GetTaskOutputFunc(ctx, id)
	}
	return nil, errors.New("GetTaskOutputFunc not implemented")
}

func (m *MockTaskflowClient) CancelTask(ctx context.Context, id string) error {
	if m.CancelTaskFunc != nil {
		return m.CancelTaskFunc(ctx, id)
	}
	return errors.New("CancelTaskFunc not implemented")
}

func (m *MockTaskflowClient) UpdateTaskProgress(ctx context.Context, id string, progress int, message string) error {
	if m.UpdateTaskProgressFunc != nil {
		return m.UpdateTaskProgressFunc(ctx, id, progress, message)
	}
	return errors.New("UpdateTaskProgressFunc not implemented")
}

func (m *MockTaskflowClient) ListTasks(ctx context.Context) ([]TaskInfo, error) {
	if m.ListTasksFunc != nil {
		return m.ListTasksFunc(ctx)
	}
	return nil, errors.New("ListTasksFunc not implemented")
}

func (m *MockTaskflowClient) ListTasksWithFilter(ctx context.Context, filter TaskFilter) ([]TaskInfo, error) {
	if m.ListTasksWithFilterFunc != nil {
		return m.ListTasksWithFilterFunc(ctx, filter)
	}
	return nil, errors.New("ListTasksWithFilterFunc not implemented")
}

func (m *MockTaskflowClient) CreateTask(ctx context.Context, request CreateTaskRequest) (*TaskInfo, error) {
	if m.CreateTaskFunc != nil {
		return m.CreateTaskFunc(ctx, request)
	}
	return nil, errors.New("CreateTaskFunc not implemented")
}

func (m *MockTaskflowClient) DeleteTask(ctx context.Context, id string) error {
	if m.DeleteTaskFunc != nil {
		return m.DeleteTaskFunc(ctx, id)
	}
	return errors.New("DeleteTaskFunc not implemented")
}

func (m *MockTaskflowClient) ExecuteTask(ctx context.Context, id string, timeout *time.Duration) error {
	if m.ExecuteTaskFunc != nil {
		return m.ExecuteTaskFunc(ctx, id, timeout)
	}
	return errors.New("ExecuteTaskFunc not implemented")
}

func (m *MockTaskflowClient) WaitForTask(ctx context.Context, id string, pollInterval time.Duration, timeout time.Duration) (*TaskInfo, error) {
	if m.WaitForTaskFunc != nil {
		return m.WaitForTaskFunc(ctx, id, pollInterval, timeout)
	}
	return nil, errors.New("WaitForTaskFunc not implemented")
}

func TestGetInfo(t *testing.T) {
	// Create a mock client
	mockClient := &MockTaskflowClient{
		GetTaskFunc: func(ctx context.Context, id string) (*TaskInfo, error) {
			assert.Equal(t, "test-task", id)
			return &TaskInfo{
				ID:     "test-task",
				Name:   "Test Task",
				Status: TaskStatusRunning,
			}, nil
		},
	}

	// Create a task
	task := NewTask("test-task", mockClient)

	// Call GetInfo
	info, err := task.GetInfo(context.Background())
	assert.NoError(t, err)
	assert.Equal(t, "test-task", info.ID)
	assert.Equal(t, "Test Task", info.Name)
	assert.Equal(t, TaskStatusRunning, info.Status)
}

func TestStreamOutput(t *testing.T) {
	// Create a counter for the number of times GetTaskFunc is called
	getTaskCalls := 0
	getProgressCalls := 0

	// Create a mock client
	mockClient := &MockTaskflowClient{
		GetTaskFunc: func(ctx context.Context, id string) (*TaskInfo, error) {
			assert.Equal(t, "test-task", id)
			getTaskCalls++

			// Return different statuses based on the call count
			if getTaskCalls < 3 {
				return &TaskInfo{
					ID:     "test-task",
					Name:   "Test Task",
					Status: TaskStatusRunning,
				}, nil
			} else {
				return &TaskInfo{
					ID:     "test-task",
					Name:   "Test Task",
					Status: TaskStatusCompleted,
				}, nil
			}
		},
		GetTaskProgressFunc: func(ctx context.Context, id string) (*ProgressInfo, error) {
			assert.Equal(t, "test-task", id)
			getProgressCalls++

			// Return different progress messages based on the call count
			message := "Progress message " + string(rune('A'+getProgressCalls-1)) // A, B, C
			return &ProgressInfo{
				Progress: getProgressCalls * 33, // 33, 66, 99
				Message:  message,
			}, nil
		},
	}

	// Create a task
	task := NewTask("test-task", mockClient)

	// Create a slice to store the output messages
	var messages []string

	// Create a context with cancel to manually stop the streaming
	ctx, cancel := context.WithCancel(context.Background())

	// Call StreamOutput in a goroutine
	done := make(chan error)
	go func() {
		err := task.StreamOutput(ctx, 10*time.Millisecond, func(msg string) {
			messages = append(messages, msg)
			// After receiving a message, cancel the context to stop streaming
			cancel()
		})
		done <- err
	}()

	// Wait for StreamOutput to complete or timeout
	select {
		case err := <-done:
			// Check results
			assert.Error(t, err) // Should error since we cancelled the context
			assert.Contains(t, err.Error(), "context canceled")
		case <-time.After(500 * time.Millisecond):
			// If it takes too long, cancel and fail
			cancel()
			t.Fatal("Test timed out")
	}

	// Check that we got the expected number of calls
	assert.GreaterOrEqual(t, getTaskCalls, 1)
	assert.GreaterOrEqual(t, getProgressCalls, 1)

	// Check that we got at least one message
	assert.GreaterOrEqual(t, len(messages), 1)
	assert.Contains(t, messages[0], "Progress message")
}

func TestStreamOutputError(t *testing.T) {
	callbackCalled := false

	// Create a mock client
	mockClient := &MockTaskflowClient{
		GetTaskFunc: func(ctx context.Context, id string) (*TaskInfo, error) {
			return nil, errors.New("task error")
		},
		GetTaskProgressFunc: func(ctx context.Context, id string) (*ProgressInfo, error) {
			return &ProgressInfo{
				Progress: 50,
				Message:  "Progress message",
			}, nil
		},
	}

	// Create a task
	task := NewTask("test-task", mockClient)

	// Call StreamOutput
	err := task.StreamOutput(context.Background(), 10*time.Millisecond, func(msg string) {
		callbackCalled = true
	})

	// Check results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to get task status")
	assert.False(t, callbackCalled, "Callback should not have been called")
}

func TestStreamOutputProgressError(t *testing.T) {
	callbackCalled := false

	// Create a mock client
	mockClient := &MockTaskflowClient{
		GetTaskFunc: func(ctx context.Context, id string) (*TaskInfo, error) {
			return &TaskInfo{
				ID:     "test-task",
				Name:   "Test Task",
				Status: TaskStatusRunning,
			}, nil
		},
		GetTaskProgressFunc: func(ctx context.Context, id string) (*ProgressInfo, error) {
			return nil, errors.New("progress error")
		},
	}

	// Create a task
	task := NewTask("test-task", mockClient)

	// Call StreamOutput
	err := task.StreamOutput(context.Background(), 10*time.Millisecond, func(msg string) {
		callbackCalled = true
	})

	// Check results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to get progress")
	assert.False(t, callbackCalled, "Callback should not have been called")
}
