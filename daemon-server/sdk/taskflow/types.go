package taskflow

import (
	"time"
)

// TaskStatus represents the status of a task
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"
	TaskStatusRunning   TaskStatus = "running"
	TaskStatusCompleted TaskStatus = "completed"
	TaskStatusFailed    TaskStatus = "failed"
	TaskStatusCancelled TaskStatus = "cancelled"
)

// TaskInfo represents information about a task
type TaskInfo struct {
	ID          string      `json:"id"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Status      TaskStatus  `json:"status"`
	CreatedAt   time.Time   `json:"created_at"`
	StartedAt   *time.Time  `json:"started_at,omitempty"`
	CompletedAt *time.Time  `json:"completed_at,omitempty"`
	Error       string      `json:"error,omitempty"`
	Progress    int         `json:"progress"`
	Steps       []StepInfo  `json:"steps"`
	Result      interface{} `json:"result,omitempty"`
	Input       interface{} `json:"input,omitempty"`
	Stdout      []string    `json:"stdout,omitempty"`
	Stderr      []string    `json:"stderr,omitempty"`
}

// StepInfo represents information about a task step
type StepInfo struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Status      TaskStatus  `json:"status"`
	StartedAt   *time.Time  `json:"started_at,omitempty"`
	CompletedAt *time.Time  `json:"completed_at,omitempty"`
	Error       string      `json:"error,omitempty"`
	Result      interface{} `json:"result,omitempty"`
	Input       interface{} `json:"input,omitempty"`
}

// TaskFilter represents a filter for listing tasks
type TaskFilter struct {
	Status    TaskStatus `json:"status,omitempty"`
	StartTime time.Time  `json:"start_time,omitempty"`
	EndTime   time.Time  `json:"end_time,omitempty"`
}

// StepRequest represents a request to create a step
type StepRequest struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Input       interface{} `json:"input,omitempty"`
}

// CreateTaskRequest represents a request to create a task
type CreateTaskRequest struct {
	Name        string        `json:"name"`
	Description string        `json:"description"`
	Steps       []StepRequest `json:"steps"`
	Input       interface{}   `json:"input,omitempty"`
}

// ProgressUpdateRequest represents a request to update task progress
type ProgressUpdateRequest struct {
	Progress int    `json:"progress"`
	Message  string `json:"message,omitempty"`
}

// ProgressInfo represents progress information for a task
type ProgressInfo struct {
	Progress int      `json:"progress"`
	Message  string   `json:"message,omitempty"`
	Stdout   []string `json:"stdout,omitempty"`
	Stderr   []string `json:"stderr,omitempty"`
}

// StepOutput represents output information for a task step
type StepOutput struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Status      TaskStatus  `json:"status"`
	Result      interface{} `json:"result,omitempty"`
	Error       string      `json:"error,omitempty"`
	StartedAt   *time.Time  `json:"started_at,omitempty"`
	CompletedAt *time.Time  `json:"completed_at,omitempty"`
}

// TaskOutput represents output information for a task
type TaskOutput struct {
	Status      TaskStatus   `json:"status"`
	Progress    int          `json:"progress"`
	Result      interface{}  `json:"result,omitempty"`
	Error       string       `json:"error,omitempty"`
	StartedAt   *time.Time   `json:"started_at,omitempty"`
	CompletedAt *time.Time   `json:"completed_at,omitempty"`
	Steps       []StepOutput `json:"steps,omitempty"`
	Stdout      []string     `json:"stdout,omitempty"`
	Stderr      []string     `json:"stderr,omitempty"`
}
