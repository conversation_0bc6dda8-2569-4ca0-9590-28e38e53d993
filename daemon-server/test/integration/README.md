# Integration Tests

This directory contains integration tests for the Docker and Taskflow SDKs.

## Prerequisites

- The daemon server must be running on http://localhost:8080 (or the URL specified in the SERVER_URL environment variable)

## Running the Tests

You can run all integration tests using the run_tests.sh script in the parent directory:

```bash
cd ..
./run_tests.sh
```

Or you can run individual tests:

### Docker API Tests

```bash
cd docker_api
go build -o docker_api_test
./docker_api_test
```

### Image Remove Tests

```bash
cd image_remove
go build -o image_remove_test
./image_remove_test
```
