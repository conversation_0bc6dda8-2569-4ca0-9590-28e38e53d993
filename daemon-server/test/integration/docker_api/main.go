package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"daemon-server/sdk/docker"
	"daemon-server/sdk/taskflow"
	"github.com/docker/docker/api/types/image"
)

func main() {
	// Get server URL from environment variable or use default
	serverURL := os.Getenv("SERVER_URL")
	if serverURL == "" {
		serverURL = "http://localhost:8080"
	}

	// Create Docker client
	client := docker.NewDockerClient(
		serverURL,
		docker.WithTimeout(30*time.Second),
	)

	// Create context
	ctx := context.Background()

	// Test async container creation
	fmt.Println("Testing async container creation...")
	testAsyncContainerCreate(ctx, client)

	// Test async image pull
	fmt.Println("\nTesting async image pull...")
	testAsyncImagePull(ctx, client)

	// Test async image push
	fmt.Println("\nTesting async image push...")
	testAsyncImagePush(ctx, client)

	// Test async image save
	fmt.Println("\nTesting async image save...")
	testAsyncImageSave(ctx, client)

	// Test async image load
	fmt.Println("\nTesting async image load...")
	testAsyncImageLoad(ctx, client)

	// Test async container start
	fmt.Println("\nTesting async container start...")
	testAsyncContainerStart(ctx, client)

	// Test async container stop
	fmt.Println("\nTesting async container stop...")
	testAsyncContainerStop(ctx, client)

	// Test async container remove
	fmt.Println("\nTesting async container remove...")
	testAsyncContainerRemove(ctx, client)
}

func testAsyncContainerCreate(ctx context.Context, client *docker.DockerClient) {
	// Create container asynchronously
	task, err := client.AsyncContainer.Create(ctx, docker.ContainerConfig{
		Name:  "test-async-container",
		Image: "nginx:latest",
	})
	if err != nil {
		fmt.Printf("Failed to create container: %v\n", err)
		return
	}

	fmt.Printf("Container creation task started with ID: %s\n", task.ID)

	// Wait for task completion
	taskInfo, err := task.Wait(ctx, 1*time.Second, 2*time.Minute)
	if err != nil {
		fmt.Printf("Failed to wait for container creation: %v\n", err)
		return
	}

	fmt.Printf("Container creation completed with status: %s\n", taskInfo.Status)
	fmt.Printf("Result: %v\n", taskInfo.Result)
}

func testAsyncImagePull(ctx context.Context, client *docker.DockerClient) {
	// Pull image asynchronously
	task, err := client.AsyncImage.Pull(ctx, "nginx:latest", "")
	if err != nil {
		fmt.Printf("Failed to pull image: %v\n", err)
		return
	}

	fmt.Printf("Image pull task started with ID: %s\n", task.ID)

	// Poll for task status
	for i := 0; i < 10; i++ {
		taskInfo, err := task.GetStatus(ctx)
		if err != nil {
			fmt.Printf("Failed to get task status: %v\n", err)
			return
		}

		fmt.Printf("Task status: %s, Progress: %d%%\n", taskInfo.Status, taskInfo.Progress)

		if taskInfo.Status == taskflow.TaskStatusCompleted || taskInfo.Status == taskflow.TaskStatusFailed {
			fmt.Printf("Task completed with status: %s\n", taskInfo.Status)
			fmt.Printf("Result: %v\n", taskInfo.Result)
			break
		}

		time.Sleep(5 * time.Second)
	}
}

func testAsyncImagePush(ctx context.Context, client *docker.DockerClient) {
	// First, make sure we have an image to push
	// For this test, we'll use a local image that was pulled in the previous test
	images, err := client.Image.List(ctx, image.ListOptions{All: false})
	if err != nil {
		fmt.Printf("Failed to list images: %v\n", err)
		return
	}

	// Find the nginx image
	var imageRef string
	for _, img := range images {
		for _, tag := range img.RepoTags {
			if tag == "nginx:latest" {
				imageRef = tag
				break
			}
		}
		if imageRef != "" {
			break
		}
	}

	if imageRef == "" {
		fmt.Println("Could not find nginx:latest image. Skipping push test.")
		return
	}

	// In a real test, you would push to a real registry
	// For this test, we'll just simulate the push operation
	// Note: This will likely fail in a real environment without proper registry credentials
	fmt.Println("Note: This test may fail if you don't have proper registry credentials")
	fmt.Println("This is expected in a test environment without a configured registry")

	// Push image asynchronously
	task, err := client.AsyncImage.Push(ctx, imageRef, "")
	if err != nil {
		fmt.Printf("Failed to push image: %v\n", err)
		return
	}

	fmt.Printf("Image push task started with ID: %s\n", task.ID)

	// Poll for task status
	for i := 0; i < 5; i++ {
		taskInfo, err := task.GetStatus(ctx)
		if err != nil {
			fmt.Printf("Failed to get task status: %v\n", err)
			return
		}

		fmt.Printf("Task status: %s, Progress: %d%%\n", taskInfo.Status, taskInfo.Progress)

		if taskInfo.Status == taskflow.TaskStatusCompleted || taskInfo.Status == taskflow.TaskStatusFailed {
			fmt.Printf("Task completed with status: %s\n", taskInfo.Status)
			if taskInfo.Error != "" {
				fmt.Printf("Error: %s\n", taskInfo.Error)
			}
			if taskInfo.Result != nil {
				fmt.Printf("Result: %v\n", taskInfo.Result)
			}
			break
		}

		time.Sleep(2 * time.Second)
	}
}

func testAsyncImageSave(ctx context.Context, client *docker.DockerClient) {
	// First, make sure we have an image to save
	images, err := client.Image.List(ctx, image.ListOptions{All: false})
	if err != nil {
		fmt.Printf("Failed to list images: %v\n", err)
		return
	}

	if len(images) == 0 {
		fmt.Println("No images found to save. Skipping save test.")
		return
	}

	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "docker-test-")
	if err != nil {
		fmt.Printf("Failed to create temp directory: %v\n", err)
		return
	}
	defer os.RemoveAll(tempDir)

	// Save the first image
	imageID := images[0].ID
	savePath := fmt.Sprintf("%s/image.tar", tempDir)

	// Save image asynchronously
	task, err := client.AsyncImage.Save(ctx, imageID, savePath)
	if err != nil {
		fmt.Printf("Failed to save image: %v\n", err)
		return
	}

	fmt.Printf("Image save task started with ID: %s\n", task.ID)

	// Poll for task status
	for i := 0; i < 10; i++ {
		taskInfo, err := task.GetStatus(ctx)
		if err != nil {
			fmt.Printf("Failed to get task status: %v\n", err)
			return
		}

		fmt.Printf("Task status: %s, Progress: %d%%\n", taskInfo.Status, taskInfo.Progress)

		if taskInfo.Status == taskflow.TaskStatusCompleted || taskInfo.Status == taskflow.TaskStatusFailed {
			fmt.Printf("Task completed with status: %s\n", taskInfo.Status)
			if taskInfo.Error != "" {
				fmt.Printf("Error: %s\n", taskInfo.Error)
			}
			if taskInfo.Result != nil {
				fmt.Printf("Result: %v\n", taskInfo.Result)
			}
			break
		}

		time.Sleep(2 * time.Second)
	}

	// Check if the file was created
	if _, err := os.Stat(savePath); os.IsNotExist(err) {
		fmt.Printf("Image was not saved to %s\n", savePath)
		return
	}

	fmt.Printf("Image saved successfully to %s\n", savePath)

	// Store the save path for the load test
	os.Setenv("SAVED_IMAGE_PATH", savePath)
}

func testAsyncImageLoad(ctx context.Context, client *docker.DockerClient) {
	// Get the saved image path from the environment
	savePath := os.Getenv("SAVED_IMAGE_PATH")
	if savePath == "" {
		fmt.Println("No saved image path found. Skipping load test.")
		return
	}

	// Check if the file exists
	if _, err := os.Stat(savePath); os.IsNotExist(err) {
		fmt.Printf("Saved image file %s does not exist. Skipping load test.\n", savePath)
		return
	}

	// Load image asynchronously
	task, err := client.AsyncImage.Load(ctx, savePath)
	if err != nil {
		fmt.Printf("Failed to load image: %v\n", err)
		return
	}

	fmt.Printf("Image load task started with ID: %s\n", task.ID)

	// Poll for task status
	for i := 0; i < 10; i++ {
		taskInfo, err := task.GetStatus(ctx)
		if err != nil {
			fmt.Printf("Failed to get task status: %v\n", err)
			return
		}

		fmt.Printf("Task status: %s, Progress: %d%%\n", taskInfo.Status, taskInfo.Progress)

		if taskInfo.Status == taskflow.TaskStatusCompleted || taskInfo.Status == taskflow.TaskStatusFailed {
			fmt.Printf("Task completed with status: %s\n", taskInfo.Status)
			if taskInfo.Error != "" {
				fmt.Printf("Error: %s\n", taskInfo.Error)
			}
			if taskInfo.Result != nil {
				fmt.Printf("Result: %v\n", taskInfo.Result)
			}
			break
		}

		time.Sleep(2 * time.Second)
	}
}

func testAsyncContainerStart(ctx context.Context, client *docker.DockerClient) {
	// Get container ID
	containers, err := client.Container.List(ctx, true)
	if err != nil {
		fmt.Printf("Failed to list containers: %v\n", err)
		return
	}

	var containerID string
	for _, container := range containers {
		if len(container.Names) > 0 && container.Names[0] == "test-async-container" {
			containerID = container.ID
			break
		}
	}

	if containerID == "" {
		fmt.Println("Container 'test-async-container' not found")
		return
	}

	// Start container asynchronously
	task, err := client.AsyncContainer.Start(ctx, containerID)
	if err != nil {
		fmt.Printf("Failed to start container: %v\n", err)
		return
	}

	fmt.Printf("Container start task started with ID: %s\n", task.ID)

	// Wait for task completion
	taskInfo, err := task.Wait(ctx, 1*time.Second, 1*time.Minute)
	if err != nil {
		fmt.Printf("Failed to wait for container start: %v\n", err)
		return
	}

	fmt.Printf("Container start completed with status: %s\n", taskInfo.Status)
	fmt.Printf("Result: %v\n", taskInfo.Result)
}

func testAsyncContainerStop(ctx context.Context, client *docker.DockerClient) {
	// Get container ID
	containers, err := client.Container.List(ctx, true)
	if err != nil {
		fmt.Printf("Failed to list containers: %v\n", err)
		return
	}

	var containerID string
	for _, container := range containers {
		if len(container.Names) > 0 && container.Names[0] == "test-async-container" {
			containerID = container.ID
			break
		}
	}

	if containerID == "" {
		fmt.Println("Container 'test-async-container' not found")
		return
	}

	// Stop container asynchronously
	task, err := client.AsyncContainer.Stop(ctx, containerID, nil)
	if err != nil {
		fmt.Printf("Failed to stop container: %v\n", err)
		return
	}

	fmt.Printf("Container stop task started with ID: %s\n", task.ID)

	// Wait for task completion
	taskInfo, err := task.Wait(ctx, 1*time.Second, 1*time.Minute)
	if err != nil {
		fmt.Printf("Failed to wait for container stop: %v\n", err)
		return
	}

	fmt.Printf("Container stop completed with status: %s\n", taskInfo.Status)
	fmt.Printf("Result: %v\n", taskInfo.Result)
}

func testAsyncContainerRemove(ctx context.Context, client *docker.DockerClient) {
	// Get container ID
	containers, err := client.Container.List(ctx, true)
	if err != nil {
		fmt.Printf("Failed to list containers: %v\n", err)
		return
	}

	var containerID string
	for _, container := range containers {
		if len(container.Names) > 0 && container.Names[0] == "test-async-container" {
			containerID = container.ID
			break
		}
	}

	if containerID == "" {
		fmt.Println("Container 'test-async-container' not found")
		return
	}

	// Remove container asynchronously
	task, err := client.AsyncContainer.Remove(ctx, containerID, true, true)
	if err != nil {
		fmt.Printf("Failed to remove container: %v\n", err)
		return
	}

	fmt.Printf("Container remove task started with ID: %s\n", task.ID)

	// Wait for task completion
	taskInfo, err := task.Wait(ctx, 1*time.Second, 1*time.Minute)
	if err != nil {
		fmt.Printf("Failed to wait for container removal: %v\n", err)
		return
	}

	fmt.Printf("Container removal completed with status: %s\n", taskInfo.Status)
	fmt.Printf("Result: %v\n", taskInfo.Result)
}
