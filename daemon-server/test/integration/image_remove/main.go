package main

import (
	"context"
	"daemon-server/sdk/taskflow"
	"fmt"
	"github.com/docker/docker/api/types/image"
	"os"
	"time"

	"daemon-server/sdk/docker"
)

func main() {
	// Get server URL from environment variable or use default
	serverURL := os.Getenv("SERVER_URL")
	if serverURL == "" {
		serverURL = "http://localhost:8080"
	}

	// Create Docker client
	client := docker.NewDockerClient(
		serverURL,
		docker.WithTimeout(30*time.Second),
	)

	// Create context
	ctx := context.Background()

	// Test async image removal
	fmt.Println("Testing async image removal...")
	testAsyncImageRemove(ctx, client)
}

func testAsyncImageRemove(ctx context.Context, client *docker.DockerClient) {
	// First, list images to find one to remove
	images, err := client.Image.List(ctx, image.ListOptions{All: false})
	if err != nil {
		fmt.Printf("Failed to list images: %v\n", err)
		return
	}

	if len(images) == 0 {
		fmt.Println("No images found to remove. Pulling an image first...")

		// Pull an image to remove
		pullTask, err := client.AsyncImage.Pull(ctx, "alpine:latest", "")
		if err != nil {
			fmt.Printf("Failed to pull image: %v\n", err)
			return
		}

		fmt.Printf("Image pull task started with ID: %s\n", pullTask.ID)

		// Wait for pull to complete
		pullTaskInfo, err := pullTask.Wait(ctx, 1*time.Second, 2*time.Minute)
		if err != nil {
			fmt.Printf("Failed to wait for image pull: %v\n", err)
			return
		}

		fmt.Printf("Image pull completed with status: %s\n", pullTaskInfo.Status)

		// List images again
		images, err = client.Image.List(ctx, image.ListOptions{All: false})
		if err != nil {
			fmt.Printf("Failed to list images after pull: %v\n", err)
			return
		}

		if len(images) == 0 {
			fmt.Println("Still no images found after pull. Exiting.")
			return
		}
	}

	// Select an image to remove
	imageToRemove := images[0]
	fmt.Printf("Found image to remove: %s (Tags: %v)\n", imageToRemove.ID, imageToRemove.RepoTags)

	// Remove image asynchronously
	task, err := client.AsyncImage.Remove(ctx, imageToRemove.ID, false, false)
	if err != nil {
		fmt.Printf("Failed to start image removal: %v\n", err)
		return
	}

	fmt.Printf("Image removal task started with ID: %s\n", task.ID)

	// Poll for task status
	for i := 0; i < 10; i++ {
		taskInfo, err := task.GetStatus(ctx)
		if err != nil {
			fmt.Printf("Failed to get task status: %v\n", err)
			return
		}

		fmt.Printf("Task status: %s, Progress: %d%%\n", taskInfo.Status, taskInfo.Progress)

		if taskInfo.Status == taskflow.TaskStatusCompleted ||
			taskInfo.Status == taskflow.TaskStatusFailed ||
			taskInfo.Status == taskflow.TaskStatusCancelled {
			fmt.Printf("Task completed with status: %s\n", taskInfo.Status)
			if taskInfo.Error != "" {
				fmt.Printf("Error: %s\n", taskInfo.Error)
			}
			if taskInfo.Result != nil {
				fmt.Printf("Result: %v\n", taskInfo.Result)
			}
			break
		}

		time.Sleep(1 * time.Second)
	}

	// Verify image was removed by listing images again
	updatedImages, err := client.Image.List(ctx, image.ListOptions{All: false})
	if err != nil {
		fmt.Printf("Failed to list images after removal: %v\n", err)
		return
	}

	// Check if the image is still in the list
	imageStillExists := false
	for _, img := range updatedImages {
		if img.ID == imageToRemove.ID {
			imageStillExists = true
			break
		}
	}

	if imageStillExists {
		fmt.Println("Warning: Image still exists after removal task completed")
	} else {
		fmt.Println("Success: Image was removed successfully")
	}
}
