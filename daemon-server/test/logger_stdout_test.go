package test

import (
	"os"
	"path/filepath"
	"testing"

	"daemon-server/configs"
	"daemon-server/internal/logger"
)

// TestLoggerStdoutAndFile tests that the logger writes to both stdout and a file
func TestLoggerStdoutAndFile(t *testing.T) {
	// Create a temporary log file
	tempDir := t.TempDir()
	logFile := filepath.Join(tempDir, "test.log")

	// Create a custom config with the temporary log file
	config := &configs.Config{
		Logger: configs.LoggerConfig{
			Level:      "info",
			File:       logFile,
			Format:     "json",
			MaxSize:    10,
			MaxBackups: 5,
			MaxAge:     30,
			Compress:   true,
		},
	}

	// Initialize logger
	if err := logger.Init(config); err != nil {
		t.Fatalf("Failed to initialize logger: %v", err)
	}

	// Log a test message
	logger.Info("This message should go to both stdout and the log file")

	// Check if the log file exists and has content
	if _, err := os.Stat(logFile); os.IsNotExist(err) {
		t.Fatalf("Log file was not created: %v", err)
	}

	// Read the log file content
	content, err := os.ReadFile(logFile)
	if err != nil {
		t.Fatalf("Failed to read log file: %v", err)
	}

	// Check if the log file contains the message
	if len(content) == 0 {
		t.Fatalf("Log file is empty")
	}

	t.Logf("Log file content: %s", content)
	t.Logf("Log file was created at: %s", logFile)
}
