package test

import (
	"testing"

	"daemon-server/configs"
	"daemon-server/internal/logger"
)

// TestLoggerCallerLocation tests that the logger correctly records the caller location
func TestLoggerCallerLocation(t *testing.T) {
	// Load configuration
	config, err := configs.LoadConfig()
	if err != nil {
		t.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	if err := logger.Init(config); err != nil {
		t.Fatalf("Failed to initialize logger: %v", err)
	}

	// Log messages at different levels
	logger.Debug("Debug message from test")
	logger.Info("Info message from test")
	logger.Warn("Warning message from test")
	logger.Error("Error message from test")

	// Test with fields
	logger.Info("Message with fields",
		logger.String("test_key", "test_value"),
		logger.Int("test_number", 42),
	)

	// Test with context logger
	ctxLogger := logger.With(
		logger.String("context", "test_context"),
	)
	ctxLogger.Info("Message from context logger")

	// Test nested function calls
	logFromNestedFunction()
}

// logFromNestedFunction logs a message from a nested function
func logFromNestedFunction() {
	logger.Info("Message from nested function")
}
