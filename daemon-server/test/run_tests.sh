#!/bin/bash

# Set server URL
export SERVER_URL="http://localhost:8080"

# Build and run the tests
cd "$(dirname "$0")"

# Run unit tests
go test ./...

# Run integration tests
echo "Running Docker API integration tests..."
cd integration/docker_api
go build -o docker_api_test
./docker_api_test
rm -f docker_api_test

echo "Running Image Remove integration tests..."
cd ../image_remove
go build -o image_remove_test
./image_remove_test
rm -f image_remove_test
