package manage

import (
	"crypto/md5"
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/myhttp"
	"design-ai/utils/tools"
	"encoding/hex"
	"fmt"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type roomStyleListReq struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

type roomStyleResp struct {
	ID        uint      `json:"id"`
	Title     string    `json:"title"`
	Keys      string    `json:"keys"`
	RefImg    string    `json:"ref_img"`
	UpdatedAt time.Time `json:"updated_at"`
}

func RoomStyleList(c *gin.Context) {
	var req roomStyleListReq
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	// userId := claims.UserId
	err := c.ShouldBindJSON(&req)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}
	aryRoomStyle := make([]roomStyleResp, 0)
	var roomStyle model.RoomStyle
	total, err := roomStyle.GetList(&aryRoomStyle, 1, req.Page, req.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "查询出错")
		return
	}

	for idx, val := range aryRoomStyle {
		//data[idx].RefUrl = "static/artstyle/" + val.StyleCode + ".png"
		if val.RefImg != "" {
			aryRoomStyle[idx].RefImg = config.DiffusionDomain + fmt.Sprintf("%s?t=%d", val.RefImg, aryRoomStyle[idx].UpdatedAt.Unix())
		}
	}

	result := make(map[string]interface{})
	result["room_styles"] = aryRoomStyle
	result["total"] = total
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func RoomStyleRefImgUpload(c *gin.Context) {
	var code int
	//校验登录...
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	userId := claims.UserId
	var roomStyleStr = c.PostForm("room_style")
	rsId, _ := strconv.ParseUint(roomStyleStr, 10, 64)
	file, err := c.FormFile("file")
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}

	oMd5Str := fmt.Sprintf("%d,%s", userId, time.Now().Format("2006-01-02 15:04:05.000"))
	has := md5.Sum([]byte(oMd5Str))
	md5Str := hex.EncodeToString(has[:])
	if len(md5Str) != 32 {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参考图名称生成失败")
		return
	}
	relativePath := "roomstyle"
	if config.Env == enums.EnvEnum.DEV {
		relativePath = "roodesign/static/debug/" + relativePath
	} else {
		relativePath = "roodesign/static/" + relativePath
	}
	var roomStyle model.RoomStyle
	roomStyle.GetByID(uint(rsId))
	ext := path.Ext(file.Filename) // 后缀

	filename := strconv.Itoa(int(roomStyle.ID)) + strings.ToLower(ext)

	relativeFilePath := relativePath + "/" + filename
	dirPath := config.DiffusionFilePath + relativePath + "/"
	filepath := dirPath + filename
	if err := c.SaveUploadedFile(file, filepath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}

	roomStyle.RefImg = relativeFilePath
	if err = roomStyle.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片数据生成失败")
		return
	}

	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		// mkdir 创建目录，mkdirAll 可创建多层级目录
		if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
			logger.Error(err, dirPath)
			errmsg.Abort(c, errmsg.FAIL, "创建路径失败")
			return
		}
	}
	if code == errmsg.SUCCESS {

		fmt.Println("图片上传成功：go on next step>>>>>>>>", relativeFilePath)

		if config.Env == enums.EnvEnum.DEV { //如果是本地调试 要把文件上传到服务器
			url := "https://dev.cyuai.com/api/v1/sys/trans_upload"
			// url := "http://10.0.51.152:5002/api/v1/sys/trans_upload"
			fields := map[string]string{
				// "relative_path_file": strings.Replace(relativeFilePath, "/debug/", " /", -1),
				"relative_path_file": relativeFilePath,
			}
			headers := map[string]string{
				"Authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJtb2JpbGUiOiIxMzAqKioqMzI3OSIsInVzZXJuYW1lIjoidmJlbiIsImlkIjoxLCJleHAiOjMzMjE3MjY1MjEwLCJpc3MiOiJkZXNpZ24iLCJuYmYiOjE2ODEyNjUxMTB9.qp8ZVk_IBT1zj1epVBN_PgBySGv2zsdYRzIL2iG-yac",
			}
			strJson, err := myhttp.UploadFile(url, filepath, "file", fields, headers)
			if err != nil {
				errmsg.Abort(c, errmsg.FAIL, "上传图片到服务器失败")
				return
			}

			mapRes := tools.GetMapFromJson(strJson)
			if mapRes == nil {
				errmsg.Abort(c, errmsg.FAIL, "上传图片到服务器失败")
				return
			}
			if int(mapRes["code"].(float64)) != 0 {
				errmsg.Abort(c, errmsg.FAIL, mapRes["msg"].(string))
				return
			}
			fmt.Println("文件已上传服务器：go on next step>>>>>>>>")
		}
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  "封面上传成功",
		})
	}

}
