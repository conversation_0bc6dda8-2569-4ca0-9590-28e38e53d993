package v1

import (
	"crypto/md5"
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/myhttp"
	"design-ai/utils/myimg"
	"design-ai/utils/tools"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type goodsTaskRes struct {
	ID          uint             `json:"id"`
	CreatedAt   time.Time        `json:"created_at"`
	Uuid        string           `json:"uuid"`
	InputPath   string           `json:"input_path"`
	InputImgUrl string           `json:"input_img_url"`
	OutputImgs  []SceneItemModel `json:"output_imgs"`
}

type createProjectReq struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

type updateProjectReq struct {
	Uuid        string `json:"uuid"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

type sceneReq struct {
	ProjectId uint   `json:"project_id"`
	Scene     string `json:"scene"`
}
type modelListReq struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}
type queryProjectReq struct {
	ProjectId uint `json:"project_id"`
}

type queryCustomProductReq struct {
	ID uint `json:"id"`
}
type removeCustomProductReq struct {
	MD5 string `json:"md5"`
}

type customProductListRes struct {
	Name        string `json:"name"`
	Md5         string `json:"md5"`
	UserId      uint   `json:"user_id"`
	Description string `json:"description"`
	Path        string `json:"path"`
	Width       int    `json:"width"`
	Height      int    `json:"height"`
}

type queryCustomProductRes struct {
	Name   string  `json:"name"`
	Src    string  `json:"src"`
	Dx     int     `json:"dx"`
	Dy     int     `json:"dy"`
	Scale  float32 `json:"scale"`
	Width  int     `json:"width"`
	Height int     `json:"height"`
	Rotate float32 `json:"rotate"`
}

type paginReq struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

type projectRes struct {
	CreatedAt   time.Time `json:"created_at"`
	Name        string    `json:"name"`
	UserId      uint      `json:"user_id"`
	Description string    `json:"description"`
	Uuid        string    `json:"uuid"`
	RefImg      string    `json:"ref_img"`
	RefImgSrc   string    `json:"ref_img_src"`
}

type SceneItemModel struct {
	ID     uint    `json:"id"`
	Scale  float32 `json:"scale"`
	Rotate float32 `json:"rotate"`
	Dx     int     `json:"dx"`
	Dy     int     `json:"dy"`
	Width  int     `json:"width"`
	Height int     `json:"height"`
	Src    string  `json:"src"`
}
type SceneModel struct {
	CustomProduct []SceneItemModel `json:"custom_product"`
	PresetModel   []SceneItemModel `json:"preset_model"`
	OutputImg     []SceneItemModel `json:"output_img"`
}

type presetModelRes struct {
	PresetModel model.GoodsPresetModel `json:"preset_model"`
	Pos         SceneItemModel         `json:"pos"`
}

type customProductRes struct {
	CustomProduct model.GoodsCustomProduct `json:"custom_product"`
	Pos           SceneItemModel           `json:"pos"`
}

type addBackgroundReq struct {
	Prompt         string `json:"prompt"`
	NagetivePrompt string `json:"negative_prompt"`
	StyleCode      int    `json:"style_code"`
	Title          string `json:"title"`
	Remark         string `json:"remark"`
	ModelName      string `json:"model_name"`
	ModelHash      string `json:"model_hash"`
}

type queryOutImgReq struct {
	Uuid string `json:"uuid"`
}

type queryOutImgRes struct {
	CreatedAt time.Time `json:"created_at"`
	UserId    uint      `json:"user_id"`
	Md5       string    `json:"md5"`
	OutPath   string    `json:"out_path"`
	Src       string    `json:"src"`
	Width     int       `json:"width"`
	Height    int       `json:"height"`
	Dx        int       `json:"dx"`
	Dy        int       `json:"dy"`
	Scale     float32   `json:"scale"`
	Rotate    float32   `json:"rotate"`
}

// 获取模版列表
type templateRes struct {
	ID              uint         `json:"ID"`
	PromptOptions   []editorItem `json:"prompt_options"`
	Prompt          string       `json:"prompt"`
	Title           string       `json:"title"`
	RefImg          string       `json:"ref_img"`
	BatchSize       int          `json:"batch_size"`
	RenderStrength  int          `json:"render_strength"`
	ColorStrength   int          `json:"color_strength"`
	OutlineStrength int          `json:"outline_strength"`
}

func GetTemplateList(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req paginReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var template model.GoodsTemplate
	templateResArr := make([]templateRes, 0)
	templateArr := make([]model.GoodsTemplate, 0)
	total, err := template.GetList(&templateArr, claims.UserId, req.Page, req.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "模版查询失败")
		return
	}
	for i := 0; i < len(templateArr); i++ {
		var editorItem []editorItem
		if err := json.Unmarshal([]byte(templateArr[i].PromptOptions), &editorItem); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "模版项查询失败")
			return
		}
		var refImg = ""
		if templateArr[i].RefImg != "" {
			refImg = config.DiffusionDomain + templateArr[i].RefImg
		}
		tempRes := templateRes{
			ID:              templateArr[i].ID,
			Title:           templateArr[i].Title,
			Prompt:          templateArr[i].Prompt,
			RefImg:          refImg,
			BatchSize:       templateArr[i].BatchSize,
			ColorStrength:   templateArr[i].ColorStrength,
			OutlineStrength: templateArr[i].OutlineStrength,
			RenderStrength:  templateArr[i].RenderStrength,
			PromptOptions:   editorItem,
		}
		templateResArr = append(templateResArr, tempRes)
	}
	result := make(map[string]interface{})
	result["total"] = total
	result["templates"] = templateResArr
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "模版获取成功",
			"result": result,
		})
	}
}

// 添加模板
type addTemplateReq struct {
	Title           string       `json:"title"`
	Prompt          string       `json:"prompt"`
	EditorItems     []editorItem `json:"editor_items"`
	BatchSize       int          `json:"batch_size"`
	RenderStrength  int          `json:"render_strength"`
	ColorStrength   int          `json:"color_strength"`
	OutlineStrength int          `json:"outline_strength"`
}

// 修改模版
type updateTemplateReq struct {
	ID              uint         `json:"id"`
	Title           string       `json:"title"`
	Prompt          string       `json:"prompt"`
	EditorItems     []editorItem `json:"editor_items"`
	BatchSize       int          `json:"batch_size"`
	RenderStrength  int          `json:"render_strength"`
	ColorStrength   int          `json:"color_strength"`
	OutlineStrength int          `json:"outline_strength"`
}

type editorItem struct {
	Type         string `json:"type"`
	EditorItemId uint   `json:"editor_item_id"`
	Position     int    `json:"position"`
}

// 修改模板
func UpdateTemplate(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req updateTemplateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var template model.GoodsTemplate
	if err := template.GetByID(req.ID); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "模版查询失败")
		return
	}
	if template.UserId == 0 {
		errmsg.Abort(c, errmsg.FAIL, "没有权限修改该模板")
		return
	}
	jsonData, err := json.Marshal(req.EditorItems)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "转换为JSON时出错")
		return
	}
	template.Title = req.Title
	template.Prompt = req.Prompt
	template.PromptOptions = string(jsonData)
	template.RenderStrength = req.RenderStrength
	template.ColorStrength = req.ColorStrength
	template.OutlineStrength = req.OutlineStrength
	template.BatchSize = req.BatchSize
	if err := template.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "模板修改失败")
		return
	}
	result := make(map[string]interface{})
	result["template_id"] = template.ID
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "模版保存成功",
			"result": result,
		})
	}
}

func AddTemplate(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req addTemplateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	jsonData, err := json.Marshal(req.EditorItems)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "转换为JSON时出错")
		return
	}
	var template = model.GoodsTemplate{
		UserId:          claims.UserId,
		Title:           req.Title,
		Prompt:          req.Prompt,
		PromptOptions:   string(jsonData),
		RenderStrength:  req.RenderStrength,
		ColorStrength:   req.ColorStrength,
		OutlineStrength: req.OutlineStrength,
		BatchSize:       req.BatchSize,
		State:           1,
	}
	if err := template.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "模版保存出错")
		return
	}
	result := make(map[string]interface{})
	result["template_id"] = template.ID
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "模版保存成功",
			"result": result,
		})
	}

}

// 添加模板项
type addTemplateEditorReq struct {
	Type    string `json:"type"`
	TitleEN string `json:"type_en"`
	TitleZH string `json:"type_zh"`
}

func AddTemplateEditorItem(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req addTemplateEditorReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var editor = model.GoodsTemplateEditor{
		Type:    req.Type,
		TitleEN: req.TitleEN,
		TitleZH: req.TitleZH,
		State:   1,
	}
	if err := editor.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "模板项保存失败")
		return
	}
	result := make(map[string]interface{})
	result["editor_item"] = editor
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "模版项保存成功",
			"result": result,
		})
	}

}

// 获取编辑选项
func GetEditorOptions(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	result := make(map[string]interface{})
	result["options"] = enums.GoodshowEditorEnum
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "模版项获取成功",
			"result": result,
		})
	}

}

// 根据类型获取模版项列表
type templateEditorReq struct {
	Type string `json:"type"`
}
type templateEditorRes struct {
	ID      uint   `json:"id"`
	Type    string `json:"type"`
	TitleEN string `json:"type_en"`
	TitleZH string `json:"type_zh"`
}

func GetTemplateEditorItems(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req templateEditorReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var tempEditor model.GoodsTemplateEditor
	arr := make([]templateEditorRes, 0)
	if err := tempEditor.GetListByType(&arr, req.Type); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "模版项获取失败")
		return
	}
	result := make(map[string]interface{})
	result["editor_items"] = arr
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "模版项获取成功",
			"result": result,
		})
	}

}

func UpdateTemplateRefImg(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	f, err := c.FormFile("file")
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "文件获取失败")
		return
	}
	templateId, err := strconv.Atoi(c.PostForm("id"))
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "模版id获取失败")
		return
	}
	pathUrl := "template/ref_img/"
	imgInfo, err, _ := saveImgTo(claims.UserId, f, c, pathUrl)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "模版获取失败")
		return
	}
	var template model.GoodsTemplate
	template.GetByID(uint(templateId))
	template.RefImg = imgInfo.RelativePathFile
	err = template.Save()
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "模版保存失败")
		return
	}
	result := make(map[string]interface{})
	result["ref_img"] = template.RefImg
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "模版封面图片修改成功",
			"result": result,
		})
	}
}

/**
** 创建项目
**/
func CreateProject(c *gin.Context) {
	var code int

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	userId := claims.UserId
	var req createProjectReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	projectName := req.Name
	if req.Name == "" {
		projectName = "未命名"
	}
	var goodsProject = model.GoodsProject{
		UserId:      userId,
		Name:        projectName,
		Uuid:        strings.Replace(uuid.New().String(), "-", "", -1),
		Description: req.Description,
		Scene:       "{}",
	}
	if err := goodsProject.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "项目保存失败")
		return
	}
	result := make(map[string]interface{})
	result["goodsProject"] = goodsProject
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "项目创建成功",
			"result": result,
		})
	}
}

func RemoveProject(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	userId := claims.UserId
	var req updateProjectReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var project model.GoodsProject
	if err := project.GetByUuid(req.Uuid); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "项目查询失败")
		return
	}
	if project.UserId != userId {
		errmsg.Abort(c, errmsg.FAIL, "没有权限操作")
		return
	}
	if err := project.Del(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "项目删除失败")
		return
	}
	result := make(map[string]interface{})
	result["uuid"] = project.Uuid
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "图片删除成功",
			"result": result,
		})
	}

}

func UploadProjectCover(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	f, err := c.FormFile("file")
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}
	uuid := c.PostForm("uuid")
	var project model.GoodsProject
	if err := project.GetByUuid(uuid); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取项目信息失败")
		return
	}
	pathUrl := fmt.Sprintf("%s/%s/cover/", project.CreatedAt.Format("200601"), project.Uuid)
	imgInfo, err, errMsg := saveImgTo(claims.UserId, f, c, pathUrl)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, errMsg)
		return
	}
	project.RefImg = imgInfo.RelativePathFile
	if err := project.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "项目保存失败")
		return
	}
	result := make(map[string]interface{})
	result["uuid"] = project.Uuid
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "图片上传成功",
			"result": result,
		})
	}

}

func UpdateProject(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	userId := claims.UserId
	var req updateProjectReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var project model.GoodsProject
	if err := project.GetByUuid(req.Uuid); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "项目查询失败")
		return
	}
	if project.UserId != userId {
		errmsg.Abort(c, errmsg.FAIL, "没有权限操作")
		return
	}
	project.Name = req.Name
	project.Description = req.Description
	if err := project.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "项目更新失败")
		return
	}
	result := make(map[string]interface{})
	result["uuid"] = project.Uuid
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "项目更新成功",
			"result": result,
		})
	}
}

func GetProjects(c *gin.Context) {
	var code int
	var req paginReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var user model.User
	if err := user.GetByID(claims.UserId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}
	var arr = make([]projectRes, 0)
	var project model.GoodsProject

	total, err := project.GetList(&arr, claims.UserId, req.Page, req.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取信息失败")
		return
	}
	for i := 0; i < len(arr); i++ {
		if arr[i].RefImg != "" {
			arr[i].RefImgSrc = config.DiffusionDomain + arr[i].RefImg
		}
	}
	result := make(map[string]interface{})
	result["total"] = total
	result["project_list"] = arr
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "项目列表获取成功",
			"result": result,
		})
	}
}

// 更新场景
func UpdateScene(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	userId := claims.UserId
	var req sceneReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var project model.GoodsProject
	if err := project.GetByID(userId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "未找到该项目")
		return
	}
	project.Scene = req.Scene
	if err := project.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存数据失败")
		return
	}

	result := make(map[string]interface{})

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "项目保存成功",
			"result": result,
		})
	}

}

// 保存图片至路径
func saveImgTo(userId uint, f *multipart.FileHeader, c *gin.Context, pathUrl string) (service.ImgInfo, error, string) {
	oMd5Str := fmt.Sprintf("%d,%s", userId, time.Now().Format("2006-01-02 15:04:05.000"))
	has := md5.Sum([]byte(oMd5Str))
	md5Str := hex.EncodeToString(has[:])
	var errImg string
	var info service.ImgInfo
	if len(md5Str) != 32 {
		errImg = "参考图名称生成失败"
		return info, fmt.Errorf(errImg), errImg
	}
	info.Md5 = md5Str
	ext := path.Ext(f.Filename) // 输出 .html
	filename := md5Str + strings.ToLower(ext)
	relativePath := service.ImgService.GetSaveGoodsRelativePath(false)
	if config.Env == enums.EnvEnum.DEV || config.Env == enums.EnvEnum.TEST {
		relativePath = service.ImgService.GetSaveGoodsRelativePath(true)
	}
	relativePathFile := relativePath + pathUrl + filename
	dirPath := config.DiffusionFilePath + relativePath + pathUrl
	filepath := config.DiffusionFilePath + relativePathFile
	info.RelativePathFile = relativePathFile
	info.RelativeOutPathFile = relativePath + pathUrl + "_" + filename

	logger.Info("dirPath:", dirPath)
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		// mkdir 创建目录，mkdirAll 可创建多层级目录
		if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
			return info, err, "创建路径失败"
		}
	}
	logger.Info("filepath:", filepath)
	if err := c.SaveUploadedFile(f, filepath); err != nil {
		return info, err, "图片保存失败"
	}

	img, _, err := myimg.FileToImg(filepath)
	width := img.Bounds().Size().X
	height := img.Bounds().Size().Y
	info.Width = width
	info.Height = height
	info.UserId = userId
	if err != nil {
		return info, err, "图片解析失败"
	}

	if config.Env == enums.EnvEnum.DEV { //如果是本地调试 要把文件上传到服务器
		url := "https://dev.cyuai.com/api/v1/sys/trans_upload"
		// url := "http://127.0.0.1:5002/api/v1/sys/trans_upload"
		fields := map[string]string{
			"relative_path_file": relativePathFile,
		}
		headers := map[string]string{
			"Authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJtb2JpbGUiOiIxMzAqKioqMzI3OSIsInVzZXJuYW1lIjoidmJlbiIsImlkIjoxLCJleHAiOjMzMjE2MTQ1NzUwLCJpc3MiOiJkZXNpZ24iLCJuYmYiOjE2ODAxNDU2NTB9.GBfjCyCz71aJBKysCVODng5L3b2sF4JyqHgZODqw6ck",
		}
		strJson, err := myhttp.UploadFile(url, filepath, "file", fields, headers)
		if err != nil {
			return info, err, "上传图片到服务器失败"
		}
		mapRes := tools.GetMapFromJson(strJson)
		if mapRes == nil {
			return info, err, "上传图片到服务器失败"
		}
		if mapRes["code"].(float64) != 0 {
			return info, err, mapRes["msg"].(string)
		}

	}
	return info, nil, ""
}

// 上传预设模型（管理权限
func UploadPresetModel(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	f, err := c.FormFile("file")
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}
	name := c.PostForm("name")
	desc := c.PostForm("description")
	if name == "" {
		name = "未命名"
	}
	var user model.User
	if err := user.GetByID(claims.UserId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}
	pathUrl := fmt.Sprintf("preset_model/%s/", time.Now().Format("200601"))
	imgInfo, err, errMsg := saveImgTo(user.ID, f, c, pathUrl)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, errMsg)
		return
	}
	presetModel := model.GoodsPresetModel{
		Name:        name,
		Description: desc,
		Md5:         imgInfo.Md5,
		Path:        imgInfo.RelativePathFile,
		Width:       imgInfo.Width,
		Height:      imgInfo.Height,
		State:       1,
	}
	if err := presetModel.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "模型保存失败")
		return
	}
	result := make(map[string]interface{})
	result["md5"] = imgInfo.Md5
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "添加成功",
			"result": result,
		})
	}

}

// 获取预设的描述
func GetPresetPrompts(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req modelListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var presetPrompts model.GoodsPresetPrompt
	arr := make([]model.GoodsPresetPrompt, 0)
	total, err := presetPrompts.GetList(arr, req.Page, req.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "列表获取失败")
		return
	}
	result := make(map[string]interface{})
	result["total"] = total
	result["preset_models"] = arr
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

// 获取预设模型列表
func GetPresetModelList(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req modelListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var presetModel model.GoodsPresetModel
	arr := make([]model.GoodsPresetModel, 0)
	total, err := presetModel.GetList(&arr, req.Page, req.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "列表获取失败")
		return
	}
	result := make(map[string]interface{})
	result["total"] = total
	result["preset_models"] = arr
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}

}

// 上传用户模型
func UploadCustomProduct(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	f, err := c.FormFile("file")
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}
	name := c.PostForm("name")
	if name == "" {
		name = "未命名"
	}
	var user model.User
	if err := user.GetByID(claims.UserId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}
	pathUrl := fmt.Sprintf("custom_product/%s/", time.Now().Format("200601"))
	imgInfo, err, errMsg := saveImgTo(user.ID, f, c, pathUrl)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, errMsg)
		return
	}
	customProduct := model.GoodsCustomProduct{
		Name:   name,
		UserId: claims.UserId,
		Md5:    imgInfo.Md5,
		Path:   imgInfo.RelativePathFile,
		Width:  imgInfo.Width,
		Height: imgInfo.Height,
	}
	if err := customProduct.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "模型保存失败")
		return
	}
	result := make(map[string]interface{})
	result["md5"] = imgInfo.Md5
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "添加成功",
			"result": result,
		})
	}
}

func RemoveCustomProduct(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req removeCustomProductReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var customProduct model.GoodsCustomProduct
	if err := customProduct.DelByMd5(req.MD5); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "删除失败")
		return
	}
	result := make(map[string]interface{})
	result["md5"] = req.MD5
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "删除产品成功",
			"result": result,
		})
	}
}

func GetCustomProductById(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req queryCustomProductReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var customProduct model.GoodsCustomProduct
	if err := customProduct.GetByID(req.ID); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "用户产品获取失败")
		return
	}
	if customProduct.UserId != claims.UserId {
		errmsg.Abort(c, errmsg.FAIL, "用户无权限操作此产品")
		return
	}
	var refImg = ""
	if customProduct.Path != "" {
		refImg = config.DiffusionDomain + customProduct.Path
	}
	var res = queryCustomProductRes{
		Name:   customProduct.Name,
		Src:    refImg,
		Dx:     0,
		Dy:     0,
		Rotate: 0,
		Scale:  1,
		Width:  customProduct.Width,
		Height: customProduct.Height,
	}
	result := make(map[string]interface{})
	result["custom_product"] = res
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "获取产品成功",
			"result": result,
		})
	}
}

// 获取用户模型列表
func GetCustomProductList(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req modelListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var customProduct model.GoodsCustomProduct
	arr := make([]customProductListRes, 0)
	total, err := customProduct.GetList(&arr, claims.UserId, req.Page, req.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "列表获取失败")
		return
	}
	for i := 0; i < len(arr); i++ {
		if arr[i].Path != "" {
			arr[i].Path = config.DiffusionDomain + arr[i].Path
		}
	}
	result := make(map[string]interface{})
	result["total"] = total
	result["preset_models"] = arr
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

func GetScene(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req queryProjectReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var project model.GoodsProject

	if err := project.GetByID(req.ProjectId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "项目获取失败")
		return
	}
	//{"custom_product": {"dx": 77, "dy": 8, "id": 97, "scale": 46}, "preset_model": [{"dx": 43, "dy": 14, "id": 95, "scale": 5}, {"dx": 42, "dy": 24, "id": 49, "scale": 19}, {"dx": 74, "dy": 84, "id": 4, "scale": 24}]}
	var scene SceneModel
	if err := json.Unmarshal([]byte(project.Scene), &scene); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "场景解析失败")
		return
	}
	// var customProduct model.GoodsCustomProduct
	// if err := customProduct.GetByID(scene.CustomProduct.ID); err != nil {
	// 	logger.Error(err)
	// 	errmsg.Abort(c, errmsg.FAIL, "产品模型解析失败")
	// 	return
	// }
	var cmRes = make([]customProductRes, 0)
	for _, custom := range scene.CustomProduct {
		var cm model.GoodsCustomProduct
		if err := cm.GetByID(custom.ID); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "预设用户解析失败")
			return
		}
		cmRes = append(cmRes, customProductRes{
			CustomProduct: cm,
			Pos:           custom,
		})
	}

	pmRes := make([]presetModelRes, 0)
	for _, preset := range scene.PresetModel {
		var pm model.GoodsPresetModel
		if err := pm.GetByID(preset.ID); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "预设模型解析失败")
			return
		}
		pmRes = append(pmRes, presetModelRes{
			PresetModel: pm,
			Pos:         preset,
		})
	}
	result := make(map[string]interface{})
	result["custom_products"] = cmRes
	result["preset_models"] = pmRes

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

func GetOutputImagesByProject(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req queryOutImgReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var project model.GoodsProject
	if err := project.GetByUuid(req.Uuid); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "项目获取失败")
		return
	}

	var goods model.Goods
	goodsTaskResArr := make([]goodsTaskRes, 0)
	goodsArr := make([]model.Goods, 0)
	if err := goods.GetListByProject(&goodsArr, project.ID); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "任务获取失败")
		return
	}

	for i := 0; i < len(goodsArr); i++ {
		good := goodsArr[i]
		url := ""
		if good.InputPath != "" {
			url = config.DiffusionDomain + good.InputPath
		}
		goodsTaskRes := goodsTaskRes{
			ID:          good.ID,
			CreatedAt:   good.CreatedAt,
			Uuid:        good.Uuid,
			InputImgUrl: url,
			OutputImgs:  make([]SceneItemModel, 0),
		}
		var outputImg model.GoodsOutImg
		outputImgArr := make([]model.GoodsOutImg, 0)
		if err := outputImg.GetListByGoodId(&outputImgArr, goodsArr[i].ID); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "输出图片获取失败")
			return
		}
		for i := 0; i < len(outputImgArr); i++ {
			out := outputImgArr[i]
			url := ""
			if out.OutPath != "" {
				url = config.DiffusionDomain + out.OutPath
			}
			outputImg := SceneItemModel{
				ID:     out.ID,
				Scale:  1,
				Rotate: 0,
				Dx:     out.Dx,
				Dy:     out.Dy,
				Width:  out.Width,
				Height: out.Height,
				Src:    url,
			}
			goodsTaskRes.OutputImgs = append(goodsTaskRes.OutputImgs, outputImg)
		}

		goodsTaskResArr = append(goodsTaskResArr, goodsTaskRes)
	}
	result := make(map[string]interface{})
	result["outputs"] = goodsTaskResArr
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

func GetOutputImages(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req queryOutImgReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var good model.Goods
	if err := good.GetByUuid(req.Uuid); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "查找任务失败")
		return
	}

	var goodOutput model.GoodsOutImg
	var arr = make([]queryOutImgRes, 0)
	if err := goodOutput.GetListByGoodId(&arr, good.ID); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "查找生成列表失败")
		return
	}
	for i := 0; i < len(arr); i++ {
		if arr[i].OutPath != "" {
			arr[i].Src = config.DiffusionDomain + arr[i].OutPath
		}
	}
	// var res = queryOutImgRes{
	// 	CreatedAt: goodOutput.CreatedAt,
	// 	UserId:    goodOutput.UserId,
	// 	Md5:       goodOutput.Md5,
	// 	OutputUrl: goodOutput.OutPath,
	// 	Width:     goodOutput.Width,
	// 	Height:    goodOutput.Height,
	// 	Dx:        goodOutput.Dx,
	// 	Dy:        goodOutput.Dy,
	// }

	result := make(map[string]interface{})
	result["out_imaeges"] = arr
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

// 生成图片
func GenerateImages(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	f, err := c.FormFile("file")
	prompt := c.PostForm("prompt")

	//批次数量
	batchSizeStr := c.PostForm("batch_size")
	// renderStrength,_ := strconv.Atoi(c.PostForm("render_strength"))
	// colorStrength,_ := strconv.Atoi(c.PostForm("color_strength"))
	// outlineStrength,_ := strconv.Atoi(c.PostForm("outline_strength"))

	var batchSize int
	if batchSizeStr != "" {
		batchSize, err = strconv.Atoi(batchSizeStr)
		if err != nil {
			logger.Error("消费产品错误")
			errmsg.Abort(c, errmsg.FAIL, "消费产品错误")
			return
		}
	} else {
		batchSize = 1
	}

	var user model.User
	if err := user.GetByID(claims.UserId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	projectUuid := c.PostForm("project")
	var project model.GoodsProject
	if err := project.GetByUuid(projectUuid); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "项目获取失败")
		return
	}

	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}

	uuid := uuid.New()
	uuidStr := strings.Replace(uuid.String(), "-", "", -1)
	pathUrl := fmt.Sprintf("%s/%s/", time.Now().Format("200601"), projectUuid)
	imgInfo, err, _ := saveImgTo(user.ID, f, c, pathUrl)

	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}
	var goodDesign = model.Goods{
		Uuid:      uuidStr,
		UserId:    claims.UserId,
		Prompt:    prompt,
		Width:     imgInfo.Width,
		Height:    imgInfo.Height,
		BatchSize: batchSize,
		ProjectId: project.ID,
		InputPath: imgInfo.RelativePathFile,
		PushJson:  "{}",
	}
	if err := goodDesign.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "设计生成失败")
		return
	}

	imgInfo.GoodId = goodDesign.ID

	json, _ := service.GoodUpScaleServiceV2.GetUpScaleJson(imgInfo.RelativePathFile, imgInfo, prompt)
	if json == "" {
		errmsg.Abort(c, errmsg.FAIL, "生成绘图数据失败")
		return
	}
	goodDesign.PushJson = json
	if err := goodDesign.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存绘图数据失败")
		return
	}
	if _, err := service.GoodUpScaleServiceV2.PushRedisQueue(json); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "发送绘图数据失败")
		return
	}

	result := make(map[string]interface{})
	resp := addDesignResp{
		Uuid: goodDesign.Uuid,
	}
	result["good_task"] = resp

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}

}
