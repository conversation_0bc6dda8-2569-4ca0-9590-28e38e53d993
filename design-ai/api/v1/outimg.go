package v1

import (
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

type outimgApi_ struct {
}

type itemOutImgReq struct {
	Md5 string `json:"md5"`
}

type outImgShareReq struct {
	Md5        string  `json:"md5"`
	Share      int     `json:"share"`
	OrderIndex float64 `json:"order_index"`
	ShowTitle  string  `json:"show_title"`
}

type listOutImgReq struct {
	Uuid      string `json:"uuid"`
	RoomType  int    `json:"room_type"`
	RoomStyle int    `json:"room_style"`
	Share     int    `json:"share"`
	Page      int    `json:"page"`
	PageSize  int    `json:"page_size"`
}

type listOutImgResp struct {
	OutputImgMd5     string  `json:"output_img_md5"`
	OutputImgUrl     string  `json:"output_img_url"`
	UpscaleImgUrl    string  `json:"upscale_img_url"`
	Width            int     `json:"width"`
	Height           int     `json:"height"`
	RoomType         int     `json:"room_type"`
	RoomStyle        int     `json:"room_style"`
	LoraId           int     `json:"lora_id"`
	LoraName         string  `json:"lora_name"`
	Share            int     `json:"share"`
	CreatedDate      string  `json:"created_date"`
	UpdatedAt        string  `json:"updated_at"`
	ShowTitle        string  `json:"show_title"`
	OrderIndex       float32 `json:"order_index"`
	Username         string  `json:"username"`
	ProgressState    int     `json:"progress_state"` //1 为启用进度条
	Progress         float64 `json:"progress"`
	ProgressPosition int64   `json:"progress_position"`
	ProgressTxt      string  `json:"progress_txt"`
}

func (obj outimgApi_) GetOutImgItem(c *gin.Context) {
	var code int
	var msg string
	var oReq itemOutImgReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var item model.OutImg
	if err := item.GetByMd5(oReq.Md5); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片信息失败")
		return
	}

	imgUrl := ""
	upImgUrl := ""
	if item.Path1 != "" {
		upImgUrl = config.DiffusionDomain + item.Path1
	}
	progress := float64(-1)
	position := int64(-1)
	//sdServer := ""
	progressTxt := ""
	if item.Path == "" {

		progress, position, progressTxt = service.GetProgress(item.Md5, "roodesign")
		//progress, sdServer = service.ShopShowService.GetProgress(item.Md5)
		//if progress == -2 { //在主队列中
		//	position = service.ShopShowService.GetProgressRank(item.Md5, item.PushJson)
		//}
		//if progress == -1 { //可能在目标队列中
		//	if sdServer != "" {
		//		position, _ = service.ShopShowService.GetProgressInTarget(sdServer, item.Md5)
		//	}
		//}
	} else {
		imgUrl = config.DiffusionDomain + item.Path
		progress = 1
	}

	resp := listOutImgResp{
		OutputImgMd5:     item.Md5,
		OutputImgUrl:     imgUrl,
		UpscaleImgUrl:    upImgUrl,
		Width:            item.Width,
		Height:           item.Height,
		RoomType:         item.RoomType,
		RoomStyle:        item.RoomStyle,
		LoraId:           item.LoraId,
		LoraName:         service.LoarService.GetNameById(item.LoraId),
		Share:            item.Share,
		ProgressState:    1,
		Progress:         progress,
		ProgressPosition: position,
		ProgressTxt:      progressTxt,
	}
	result := make(map[string]interface{})
	result["item"] = resp

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj outimgApi_) GetOutImgList(c *gin.Context) {
	var code int
	var msg string
	var oReq listOutImgReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	origId := uint(0)
	searchUserId := claims.UserId
	if oReq.Uuid != "" {
		var design model.Design
		if err := design.GetByUuid(oReq.Uuid); err != nil {
			logger.Error(err, "oReq.HandUuid:", oReq.Uuid)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		}
		origId = design.ID
	}
	if origId > 0 {
		searchUserId = 0
	}

	var outImg model.OutImg
	var ary = make([]model.OutImg, 0)
	total, err := outImg.GetList(&ary, searchUserId, enums.OrigWhereEnum.Room, origId, oReq.RoomType, oReq.RoomStyle, oReq.Page, oReq.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片列表数据出错")
		return
	}

	outAry := make([]listOutImgResp, 0)
	for _, item := range ary {
		imgUrl := ""
		upImgUrl := ""
		if item.Path != "" {
			imgUrl = config.DiffusionDomain + item.Path
		}
		if item.Path1 != "" {
			upImgUrl = config.DiffusionDomain + item.Path1
		}
		resp := listOutImgResp{
			OutputImgMd5:  item.Md5,
			OutputImgUrl:  imgUrl,
			UpscaleImgUrl: upImgUrl,
			Width:         item.Width,
			Height:        item.Height,
			RoomType:      item.RoomType,
			RoomStyle:     item.RoomStyle,
			LoraId:        item.LoraId,
			LoraName:      service.LoarService.GetNameById(item.LoraId),
			CreatedDate:   item.CreatedAt.Format("2006-01-02"),
			UpdatedAt:     item.UpdatedAt.Format(model.TimeFormat),
			Share:         item.Share,
		}
		outAry = append(outAry, resp)
	}

	result := make(map[string]interface{})
	result["items"] = outAry
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})

	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj outimgApi_) GetCaseImgList(c *gin.Context) {
	var code int
	var msg string
	var oReq listOutImgReq

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims.UserId <= 0 {
	//	errmsg.Abort(c, errmsg.FAIL, "请先登录")
	//	return
	//}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	//if oReq.Share < 3 {
	//	if !service.UserService.IsInsiderUser(claims.UserId) {
	//		logger.Error("非内部用户，无权限访问全部图片")
	//		oReq.Share = 3
	//		msg = "输出验证数据"
	//	}
	//}

	oReq.Share = 3

	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var outImg model.OutImg
	ary, total, err := outImg.GetShareList(enums.OrigWhereEnum.Room, oReq.RoomType, oReq.RoomStyle, oReq.Share, oReq.Page, oReq.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片列表数据出错")
		return
	}

	outAry := make([]listOutImgResp, 0)
	for _, item := range ary {
		imgUrl := ""
		upImgUrl := ""
		if item.Path != "" {
			imgUrl = config.DiffusionDomain + item.Path
		}
		if item.Path1 != "" {
			upImgUrl = config.DiffusionDomain + item.Path1
		}
		loraName := service.LoarService.GetNameById(item.LoraId)
		showTitle := item.ShowTitle
		aryTmp := strings.Split(loraName, "-")
		if showTitle == "" {
			showTitle = loraName
			if len(aryTmp) > 2 {
				showTitle = aryTmp[0] + "-" + aryTmp[1]
			}
		}
		username := aryTmp[0]
		resp := listOutImgResp{
			OutputImgMd5:  item.Md5,
			OutputImgUrl:  imgUrl,
			UpscaleImgUrl: upImgUrl,
			Width:         item.Width,
			Height:        item.Height,
			RoomType:      item.RoomType,
			RoomStyle:     item.RoomStyle,
			LoraId:        item.LoraId,
			LoraName:      loraName,
			CreatedDate:   item.CreatedAt.Format("2006-01-02"),
			UpdatedAt:     item.UpdatedAt.Format(model.TimeFormat),
			Share:         item.Share,
			ShowTitle:     showTitle,
			OrderIndex:    item.OrderIndex,
			Username:      username,
		}
		outAry = append(outAry, resp)
	}

	result := make(map[string]interface{})
	result["items"] = outAry
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})

	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj outimgApi_) DelByMd5(c *gin.Context) {
	var code int
	var msg string
	var oReq itemOutImgReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var item model.OutImg
	if err := item.GetByMd5(oReq.Md5); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片信息失败")
		return
	}

	if item.UserId != claims.UserId {
		logger.Error("无权限")
		errmsg.Abort(c, errmsg.FAIL, "无权限")
		return
	}

	if err := service.ImgService.DelImage(item.Path); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "删除图片失败")
		return
	}

	if err := service.ImgService.DelImage(item.Path1); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "删除超分图片失败")
		return
	}

	if err := item.Delete(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "删除图片信息失败")
		return
	}

	result := make(map[string]interface{})
	result["md5"] = oReq.Md5

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "删除成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj outimgApi_) SetShare(c *gin.Context) {
	var code int
	var msg string
	var oReq outImgShareReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	if enums.ImgShareEnum.GetKey(oReq.Share) == "" {
		logger.Error(oReq)
		errmsg.Abort(c, errmsg.FAIL, "设置参数错误")
		return
	}

	if enums.ImgShareEnum.Forbid == oReq.Share {
		logger.Error(oReq)
		errmsg.Abort(c, errmsg.FAIL, "该图片已禁止设置")
		return
	}

	var item model.OutImg
	if err := item.GetByMd5(oReq.Md5); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片信息失败")
		return
	}

	if !service.UserService.IsInsiderUser(claims.UserId) {
		logger.Error("无权限 userID:", claims.UserId)
		errmsg.Abort(c, errmsg.FAIL, "无权限")
		return
	}

	if err := item.SetShare(oReq.Share); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "设置图片分享标识失败")
		return
	}

	if (oReq.ShowTitle != "" || oReq.OrderIndex > 0) && oReq.Share == enums.ImgShareEnum.Pass {
		if err := item.SetShareInfo(oReq.ShowTitle, oReq.OrderIndex); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "设置图片分享信息失败")
			return
		}
	}

	result := make(map[string]interface{})
	result["md5"] = oReq.Md5
	result["share"] = oReq.Share

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "设置成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

var OutImgApi outimgApi_
