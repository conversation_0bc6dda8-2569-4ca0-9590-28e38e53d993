package wechatpay

import (
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/wechat/v3"
	"net/http"
)

//测试URL
//https://design.cyuai.com/api/v1/alipay/callback?charset=utf-8&out_trade_no=s2023041212131100000014&method=alipay.trade.page.pay.return&total_amount=0.01&sign=JLpOXwOd9%2B%2B4Z0pGT%2FmVucW4nMm1rBAnMng%2F9F56Ljbp6dslccYQbrA2zpiDfwIr2l5srBljTbSdJVpPTX9H%2BOXQlrw1KL%2B2%2Feizlx0HywTjIMHcEEIqOrm%2FZ4CPLUjAF6U1SAIxVdihvk0x1GvtnFO6xO%2Bt9eTgHha9fdAU9u5Fw4p3nAMB8G6%2FUatLu2M2MtWk6SmJuCoMT1cqVRC1hvulfQ%2Bhx44YlVJYqdX7yJhRbBeTbi%2Fv6ynoDAKZsOAt3No9zLo1PmsWNhUkfxuNu0cBIn5h8JAgj95RYdNdybjLCEes5D0xQcUInkRW7jPNrXa1zM9gl%2F0D1IoF9Z9VmQ%3D%3D&trade_no=2023041222001495421409100231&auth_app_id=2021003168606329&version=1.0&app_id=2021003168606329&sign_type=RSA2&seller_id=2088541511944068&timestamp=2023-04-12+12%3A13%3A47
//http://***********:5002/api/v1/alipay/callback?charset=utf-8&out_trade_no=s2023041212131100000014&method=alipay.trade.page.pay.return&total_amount=0.01&sign=JLpOXwOd9%2B%2B4Z0pGT%2FmVucW4nMm1rBAnMng%2F9F56Ljbp6dslccYQbrA2zpiDfwIr2l5srBljTbSdJVpPTX9H%2BOXQlrw1KL%2B2%2Feizlx0HywTjIMHcEEIqOrm%2FZ4CPLUjAF6U1SAIxVdihvk0x1GvtnFO6xO%2Bt9eTgHha9fdAU9u5Fw4p3nAMB8G6%2FUatLu2M2MtWk6SmJuCoMT1cqVRC1hvulfQ%2Bhx44YlVJYqdX7yJhRbBeTbi%2Fv6ynoDAKZsOAt3No9zLo1PmsWNhUkfxuNu0cBIn5h8JAgj95RYdNdybjLCEes5D0xQcUInkRW7jPNrXa1zM9gl%2F0D1IoF9Z9VmQ%3D%3D&trade_no=2023041222001495421409100231&auth_app_id=2021003168606329&version=1.0&app_id=2021003168606329&sign_type=RSA2&seller_id=2088541511944068&timestamp=2023-04-12+12%3A13%3A47
//http://***********:5002/api/v1/alipay/callback?charset=utf-8&out_trade_no=s2023041212562300000015&method=alipay.trade.page.pay.return&total_amount=0.01&sign=UGEKFEOSItkzvHo6bMdOBbLhs5YSHTzjGAFcGxYdeIfIaXlPF70IbIHGqrQOZ6eIr%2BJ8kFWL9p4b8ohQ8bPeGKHTB93sR4zhR5ivAoGmWIsGJ3I1VjppqWJUS0SJAOH6XP%2B8lDwEhNwmj4ul%2F04YxzAK57ISEh12IcQHFocTGbqEtw7%2F2q50JmsBrfrs4L%2B62AAshGOVMDEjAPsvq4OeROgFAsSK5rITjW2uv3NX1Kx4glC8dgBvv205xwO55%2BBmXjFsC0%2FwyWj3PJEbzyGkcjNEQ1RG06n5SnacUVFS%2BDR2OEN%2F7dSXyXK9w8u6TZ4hnGuowNFvR8Llo%2Fv5wbwZog%3D%3D&trade_no=2023041222001495421407619682&auth_app_id=2021003168606329&version=1.0&app_id=2021003168606329&sign_type=RSA2&seller_id=2088541511944068&timestamp=2023-04-12+12%3A57%3A09
func Callback(c *gin.Context) {

	//defer func() {
	//	if e := recover(); e != nil {
	//		logger.Error("Notify:", e)
	//	}
	//}()

	logger.Info("接收到微信支付回调")

	notifyReq, err := wechat.V3ParseNotify(c.Request) // c.Request 是 gin 框架的写法
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取数据出错")
		return
	}
	logger.Info("notifyReq", notifyReq)

	//// 获取微信平台证书
	//certMap := PayService.client.WxPublicKeyMap()
	//// 验证异步通知的签名
	//err = notifyReq.VerifySignByPKMap(certMap)
	//if err != nil {
	//	logger.Error(err)
	//	return
	//}
	logger.Info("解密后：", notifyReq)

	/*
		if success, err := PayService.VerifySign(notifyReq); err != nil || success == false {
			logger.Error(success, err)
			errmsg.Abort(c, errmsg.FAIL, "验证数据出错")
			return
		}

		outTradeNo := notifyReq["out_trade_no"].(string)
		tradeNo := notifyReq["trade_no"].(string)

		trade, err := PayService.TradeQueryByOutTradeNo(outTradeNo)
		if err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "获取支付宝订单数据出错")
			return
		}
		logger.Info(trade)
		if trade.Response.TradeStatus != "TRADE_SUCCESS" {
			logger.Error("不是支付成功标识")
			errmsg.Abort(c, errmsg.FAIL, "不是支付成功标识")
			return
		}
		payTime, err := time.ParseInLocation("2006-01-02 15:04:05", trade.Response.SendPayDate, time.Local) //这里按照当前时区转
		if err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "获取订单支付时间出错")
			return
		}

		if err := HandleRecharge(outTradeNo, tradeNo, payTime); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "支付逻辑处理失败")
			return
		}*/
	c.JSON(http.StatusOK, gin.H{
		"code": errmsg.SUCCESS,
		"msg":  "支付成功",
	})
}
