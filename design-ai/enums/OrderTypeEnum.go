package enums

type orderTypeEnum_ struct {
	<PERSON>r<PERSON><PERSON>, Manager<PERSON>dd, RechargeBuy, Cost, ManagerCost, Subscribe, RedeemCode int
}

var OrderTypeEnum = orderTypeEnum_{
	UserGift:    1, //用户赠送
	ManagerAdd:  2, //管理员添加
	RechargeBuy: 3, //充值购买
	Cost:        4, //消费
	ManagerCost: 5, //手动扣减
	Subscribe:   6, //订阅包年增加
	RedeemCode:  7, //使用兑换码增加
}

var OrderTypeMap = map[int]string{
	1: "用户赠送",
	2: "管理员添加",
	3: "充值购买",
	4: "消费",
	5: "手动扣减",
	6: "订阅包年增加",
}
