package enums

import "reflect"

type redisKeyEnum_ struct {
	AutoSerialNumber, SmsReg, SmsLogin, SmsModifyPassword, InsiderUser, InsiderUsers string
}

func (c redisKeyEnum_) Get(id string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if typeVo.Field(i).Name == id {
			return vo.Field(i).Interface().(string)
		}
	}
	return ""
}

var RedisKeyEnum = redisKeyEnum_{
	AutoSerialNumber:  "design-ai:auto_serial_number",
	SmsReg:            "design-ai:sms:reg:",
	SmsLogin:          "design-ai:sms:login:",
	SmsModifyPassword: "design-ai:sms:modify_password:",
	InsiderUser:       "design-ai:insider_user",
	InsiderUsers:      "design-ai:insider_users:hash",
}
