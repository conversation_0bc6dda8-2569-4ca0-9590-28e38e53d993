package model

import (
	"gorm.io/gorm"
)

type Goods struct {
	gorm.Model
	Name           string `json:"name" gorm:"type:varchar(50);not null;default:'';comment:产品名称"`
	Uuid           string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:唯一字符串"`
	UserId         uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	ProjectId      uint   `json:"project_id" gorm:"type:bigint;not null;default:0;comment:项目ID"`
	InputPath      string `json:"input_path" gorm:"type:varchar(150);not null;default:'';comment:输入图片路径"`
	Width          int    `json:"width" gorm:"type:int;not null;default:0;comment:产品图宽度"`
	Height         int    `json:"height" gorm:"type:int;not null;default:0;comment:产品图高度"`
	BatchSize      int    `json:"batch_size" gorm:"type:int;not null;default:1;comment:批次生成数量"`
	Cost           int    `json:"cost" gorm:"type:int;not null;default:0;comment:本次花费"`
	OrderNo        string `json:"order_no" gorm:"type:varchar(50);comment:订单编号"`
	ConsumeProduct uint   `json:"consume_prodect" gorm:"type:bigint;not null;default:0;comment:消费产品Id"`
	Description    string `json:"description" gorm:"type:varchar(100);not null;default:'';comment:产品描述"`
	OrderIndex     int    `json:"order_index" gorm:"type:int;not null;default:0;comment:序号(越小越前面)"`
	State          int    `json:"state" gorm:"type:int;not null;default:0;comment:状态(1可用)"`
	Prompt         string `json:"prompt" gorm:"type:varchar(200);not null;default:'';comment:简要画面描述"`
	PushJson       string `json:"push_json" gorm:"type:json;';comment:推送给后端的绘图JSON模板"`
}

func (Goods) TableName() string {
	return "T_Goods"
}

func (o *Goods) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *Goods) GetList(dest interface{}, state int, page int, pageSize int) error {
	tx := db.Debug().Model(o).Where("state=?", state).Order("order_index asc").Scan(dest)
	return tx.Error
}

func (o *Goods) GetListByProject(dest interface{}, project uint) error {
	tx := db.Debug().Model(o).Where("project_id=?", project).Scan(dest)
	return tx.Error
}

func (o *Goods) Save() error {
	return db.Save(o).Error
}

func (o *Goods) New(tx *gorm.DB) error {
	return tx.Save(o).Error
}

func (o *Goods) SetState(state int) error {
	return db.Model(o).Updates(Goods{State: state}).Error
}

func (o *Goods) GetByUuid(uuidStr string) error {
	err := db.Where("uuid=?", uuidStr).First(o).Error
	return err
}
