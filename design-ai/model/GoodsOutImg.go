package model

import (
	"gorm.io/gorm"
)

type GoodsOutImg struct {
	gorm.Model
	Name      string `json:"name" gorm:"type:varchar(50);not null;default:'';comment:项目名称"`
	UserId    uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	GoodId    uint   `json:"good_id" gorm:"type:bigint;not null;default:0;comment:输入的ID"`
	Md5       string `json:"md5" gorm:"type:varchar(50);not null;default:'';comment:图片md5"`
	InputPath string `json:"input_path" gorm:"type:varchar(150);not null;default:'';comment:输入图片路径"`
	OutPath   string `json:"out_path" gorm:"type:varchar(150);not null;default:'';comment:输出图片路径"`
	Width     int    `json:"width" gorm:"type:int;not null;default:0;comment:参考图宽度"`
	Height    int    `json:"height" gorm:"type:int;not null;default:0;comment:参考图高度"`
	Dx        int    `json:"dx" gorm:"type:int;not null;default:0;comment:参考图位置x坐标"`
	Dy        int    `json:"dy" gorm:"type:int;not null;default:0;comment:参考图位置y坐标"`
}

func (GoodsOutImg) TableName() string {
	return "T_GoodsOutImg"
}

func (o *GoodsOutImg) GetByID(id uint) error {
	err := db.Debug().First(o, id).Error
	return err
}

func (o *GoodsOutImg) GetList(dest interface{}, userId uint, page int, pageSize int) error {
	tx := db.Debug().Model(o).Where("user_id = ?", userId).Scan(dest)
	return tx.Error
}

func (o *GoodsOutImg) GetListByGoodId(dest interface{}, goodId uint) error {
	tx := db.Debug().Model(o).Where("good_id = ?", goodId).Scan(dest)
	return tx.Error
}

func (o *GoodsOutImg) Save() error {
	return db.Save(o).Error
}
