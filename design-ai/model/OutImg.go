package model

import (
	"errors"
	"time"

	"gorm.io/gorm"
)

type OutImg struct {
	gorm.Model
	UserId     uint      `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	OrigWhere  int       `json:"orig_where" gorm:"type:int;not null;default:0;comment:哪里来的数据"`
	OrigId     uint      `json:"orig_id" gorm:"type:bigint;not null;default:0;comment:来源记录ID"`
	BatchNum   int       `json:"batch_num"  gorm:"type:int;not null;default:0;comment:"`
	RoomType   int       `json:"room_type" gorm:"type:int;not null;default:0;comment:房间类型ID"`
	RoomStyle  int       `json:"room_style" gorm:"type:int;not null;default:0;comment:房间风格ID"`
	LoraId     int       `json:"lora_id" gorm:"type:int;not null;default:0;comment:Lora模型ID"`
	Width      int       `json:"width" gorm:"type:int;not null;default:0;comment:参考图宽度"`
	Height     int       `json:"height" gorm:"type:int;not null;default:0;comment:参考图高度"`
	Md5        string    `json:"md5" gorm:"type:varchar(50);not null;default:'';comment:图片md5"`
	Path       string    `json:"path" gorm:"type:varchar(100);not null;default:'';comment:图片路径"`
	Path1      string    `json:"path1" gorm:"type:varchar(100);not null;default:'';comment:高分图片路径1"`
	Path2      string    `json:"path2" gorm:"type:varchar(100);not null;default:'';comment:高分图片路径2"`
	ScaleAt    time.Time `json:"scale_at" gorm:"type:datetime;default:'1900-01-01';comment:高分开始时间,高分成功后置为null"`
	PriceCoin  int       `json:"price_coin" gorm:"type:int;not null;default:0;comment:所需Coin"`
	OrderNo    string    `json:"order_no" gorm:"type:varchar(50);comment:订单编号"`
	State      int       `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 3已完成"`
	Share      int       `json:"share" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 1禁止分享 2申请分享 3审核通过"`
	ShowTitle  string    `json:"show_title" gorm:"type:varchar(50);comment:显示标题"`
	OrderIndex float32   `json:"order_index" gorm:"type:float;not null;default:0;comment:排序 越大越前面"`
}

func (OutImg) TableName() string {
	return "T_OutImg"
}

func (o *OutImg) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}
func (o *OutImg) GetByMd5(md5 string) error {
	err := db.First(o, "md5 = ?", md5).Error
	return err
}

func (o *OutImg) GetShareListFall(origWhere int, share int, lastId uint, limit int) ([]OutImg, error) {
	ary := make([]OutImg, 0)
	tx := db.Debug().Model(o).Where("orig_where=?", origWhere)
	if share >= 0 {
		tx = tx.Where("share>=?", share)
	} else {
		return ary, nil
	}
	if lastId > 0 {
		tx = tx.Where("id<?", lastId)
	}
	tx = tx.Order("id desc").Limit(limit).Scan(&ary)
	return ary, tx.Error
}

func (o *OutImg) GetList(dest interface{}, userId uint, origWhere int, origId uint, roomType int, roomStyle int, page int, pageSize int) (int64, error) {

	var total int64
	tx := db.Debug().Model(o).Where("orig_where=? ", origWhere)
	if userId > 0 {
		tx.Where("user_id=?", userId)
	}
	if origId > 0 {
		tx.Where("orig_id=?", origId)
	}
	if roomType > 0 {
		tx.Where("room_type=?", roomType)
	}
	if roomStyle > 0 {
		tx.Where("room_style=?", roomStyle)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *OutImg) StatisticOutImg(start time.Time, end time.Time) (int64, error) {
	var total int64
	tx := db.Debug().Model(o).Where("created_at > ? AND created_at < ?", start, end).Where("path <> ''").Count(&total)
	err := tx.Error
	return total, err
}

func (o *OutImg) GetShareList(origWhere int, roomType int, roomStyle int, share int, page int, pageSize int) ([]OutImg, int64, error) {
	ary := make([]OutImg, 0)
	var total int64
	tx := db.Debug().Model(o).Where("orig_where=? ", origWhere)
	if roomType > 0 {
		tx.Where("room_type=?", roomType)
	}
	if roomStyle > 0 {
		tx.Where("room_style=?", roomStyle)
	}
	if share > -1 {
		tx.Where("share>=?", share)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return nil, 0, err
		}
	}
	tx.Order("order_index desc, id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(&ary)
	return ary, total, tx.Error
}

func (o *OutImg) BatchCreate(value []OutImg) error {
	return db.Create(&value).Error
}

func (o *OutImg) Save() error {
	return db.Save(o).Error
}
func (o *OutImg) Delete() error {
	return db.Delete(o).Error
}

func (o *OutImg) SetShare(share int) error {
	return db.Model(o).Updates(map[string]interface{}{"share": share}).Error
}

func (o *OutImg) SetShareInfo(showTitle string, orderIndex float64) error {
	return db.Model(o).Updates(map[string]interface{}{"show_title": showTitle, "order_index": orderIndex}).Error
}

func (o *OutImg) SetPath(path string) error {
	return db.Model(o).Updates(OutImg{Path: path}).Error
}

/*
func (o *DiffImg) SetUpscalePath(level int, path string) error {
	if level == 1 {
		return db.Model(o).Updates(DiffImg{Path1: path, o.UpdatedAt: nil}).Error
	}

}*/

func (o *OutImg) SetUpscalePath(id uint, level int, path string) error {
	if level == 1 {
		return db.Model(o).Where("id = ?", id).Updates(map[string]interface{}{"path1": path, "scale_at": nil}).Error
	} else if level == 2 {
		return db.Model(o).Where("id = ?", id).Updates(map[string]interface{}{"path2": path, "scale_at": nil}).Error
	} else {
		return errors.New("未找到level对应字段")
	}
}

func (o *OutImg) SetUpscaleAt() error {
	return db.Model(o).Updates(OutImg{ScaleAt: time.Now()}).Error
}

func (o *OutImg) Del(tx *gorm.DB, id uint, userId uint) error {
	tx.Debug().Where("id=? and user_id=? ", userId, id, userId).Delete(o)
	return tx.Error
}

func (o *OutImg) DelByOrig(tx *gorm.DB, userId uint, origWhere int, origId uint) error {
	tx.Debug().Where("user_id=? and orig_where=? and orig_id=?", userId, origWhere, origId).Delete(o)
	return tx.Error
}
