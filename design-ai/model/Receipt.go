package model

import (
	"gorm.io/gorm"
	"time"
)

type Receipt struct {
	gorm.Model
	UserId                      uint      `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	OrderNo                     string    `json:"order_no" gorm:"type:varchar(50);comment:订单编号"`
	PayChannel                  string    `json:"pay_channel" gorm:"type:varchar(50);not null;default:'';comment:收款渠道 支付渠道"`
	ProductId                   uint      `json:"product_id" gorm:"type:bigint;not null;default:0;comment:产品ID"`
	ProductCode                 string    `json:"product_code" gorm:"type:varchar(50);not null;default:'';comment:所购买产品的唯一标识符 appleiap支付对应apple的product_id"`
	BundleID                    string    `json:"bundle_id" gorm:"type:varchar(50);not null;default:'';comment:应用程序 Bundle ID"`
	ReceiptType                 string    `json:"receipt_type" gorm:"type:varchar(50);not null;default:'';comment:收据类型"`
	ReceiptCreationDate         time.Time `json:"receipt_creation_date" gorm:"type:datetime;default:'1900-01-01';comment:收据创建日期（格林尼治标准时间）已转本地时区"`
	RequestDate                 time.Time `json:"request_date" gorm:"type:datetime;default:'1900-01-01';comment:请求日期（格林尼治标准时间）已转本地时区"`
	Quantity                    int       `json:"quantity" gorm:"type:int;not null;default:0;comment:购买的产品数量"`
	TransactionId               string    `json:"transaction_id" gorm:"type:varchar(50);not null;default:'';comment:本次交易的唯一标识符"`
	OriginalTransactionId       string    `json:"original_transaction_id" gorm:"type:varchar(50);not null;default:'';comment:原始交易的唯一标识符"`
	PurchaseDate                time.Time `json:"purchase_date" gorm:"type:datetime;default:'1900-01-01';comment:购买日期（格林尼治标准时间）"`
	OriginalPurchaseDate        time.Time `json:"original_purchase_date" gorm:"type:datetime;default:'1900-01-01';comment:原始购买日期（格林尼治标准时间）已转本地时区"`
	ExpiresDate                 time.Time `json:"expires_date" gorm:"type:datetime;default:'1900-01-01';comment:订阅到期日期（格林尼治标准时间）已转本地时区"`
	WebOrderLineItemId          string    `json:"web_order_line_item_id" gorm:"type:varchar(50);not null;default:'';comment:Web 订单行项目 ID"`
	IsTrialPeriod               bool      `json:"is_trial_period" gorm:"type:tinyint;not null;default:0;comment:订阅是否处于试用期"`
	IsInIntroOfferPeriod        bool      `json:"is_in_intro_offer_period" gorm:"type:tinyint;not null;default:0;comment:订阅是否处于介绍期"`
	InAppOwnershipType          string    `json:"in_app_ownership_type" gorm:"type:varchar(50);not null;default:'';comment:应用内购买类型"`
	SubscriptionGroupIdentifier string    `json:"subscription_group_identifier" gorm:"type:varchar(50);not null;default:'';comment:订阅组标识符"`
	HandleTime                  time.Time `json:"handle_time" gorm:"type:datetime;default:'1900-01-01';comment:内容发放时间"`
}

func (Receipt) TableName() string {
	return "T_Receipt"
}

func (o *Receipt) Save() error {
	return db.Debug().Save(o).Error
}

func (o *Receipt) GetByTransactionId(tradeId string) error {
	return db.Debug().First(o, "transaction_id = ?", tradeId).Error
}

func (o *Receipt) Exists(bundleID string, transactionId string) (bool, error) {
	if err := db.Debug().First(o, "bundle_id=? and  transaction_id = ?", bundleID, transactionId).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		} else {
			return true, err
		}
	} else {
		return true, nil
	}
}

func (o *Receipt) SetOrderNo(tx *gorm.DB, orderNo string) error {
	return tx.Model(o).Updates(Receipt{OrderNo: orderNo, HandleTime: time.Now()}).Error
}
