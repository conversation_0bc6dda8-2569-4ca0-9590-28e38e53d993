package model

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type Recharge struct {
	gorm.Model
	BundleId              string          `json:"bundle_id" gorm:"type:varchar(50);not null;default:'';comment:应用唯一标识"`
	Store                 string          `json:"store" gorm:"type:varchar(50);not null;default:'';comment:销售平台商店"`
	UserId                uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	ProductId             uint            `json:"product_id" gorm:"type:bigint;not null;default:0;comment:产品ID"`
	ProductCode           string          `json:"product_code" gorm:"type:varchar(50);not null;default:'';comment:产品Code eg:apple订阅"`
	ProductCategory       string          `json:"product_category" gorm:"type:varchar(50);not null;default:'';comment:产品类型 对应ProductCategory枚举"`
	OutTradeNo            string          `json:"out_trade_no" gorm:"type:varchar(50);not null;default:'';comment:商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一"`
	DatetimeCancel        time.Time       `json:"datetime_cancel" gorm:"type:datetime;default:'1900-01-01';comment:订单取消时间"`
	Coin                  int             `json:"coin" gorm:"type:int;not null;default:0;comment:虚拟商品数量"`
	Amount                decimal.Decimal `json:"amount" gorm:"type:decimal(16,2);not null;default:0;comment:充值金额"`
	Description           string          `json:"description" gorm:"type:varchar(50);not null;default:'';comment:商品描述，Image形象店-深圳腾大-QQ公仔"`
	Gateway               string          `json:"gateway" gorm:"type:varchar(10);not null;default:'';comment:支付方式 对应枚举PayGatewayEnum"`
	PayChannel            string          `json:"pay_channel" gorm:"type:varchar(50);not null;default:'';comment:收款渠道 支付渠道"`
	PayTime               time.Time       `json:"pay_time" gorm:"type:datetime;default:'1900-01-01';comment:支付平台回调时间"`
	PayTradeId            string          `json:"pay_trade_id" gorm:"type:varchar(50);not null;default:'';comment:支付平台的交易单号"`
	PayCallbackJson       string          `json:"pay_callback_json" gorm:"type:json;comment:支付平台回调数据"`
	ExpiresDate           time.Time       `json:"expires_date" gorm:"type:datetime;default:'1900-01-01';comment:订阅到期日期"`
	PayRefundTime         time.Time       `json:"pay_refund_time" gorm:"type:datetime;default:'1900-01-01';comment:支付平台退款回调时间"`
	PayRefundCallbackJson string          `json:"pay_refund_callback_json" gorm:"type:json;comment:支付平台退款回调数据"`
	PayRefundTradeId      string          `json:"pay_refund_trade_id" gorm:"type:varchar(50);not null;default:'';comment:支付平台退款的交易单号"`
	State                 int             `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0新创建 1已付款 2已退款 3交易不存在 9已取消"`
}

func (Recharge) TableName() string {
	return "T_Recharge"
}

func (o *Recharge) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *Recharge) GetByOutTradeNo(outTradeNo string) error {
	err := db.Debug().Where("out_trade_no = ?", outTradeNo).First(o).Error
	return err
}

func (o *Recharge) GetByPayTradeId(payType string, tradeId string) error {
	return db.Debug().First(o, "pay_channel=? and  pay_trade_id = ?", payType, tradeId).Error
}

func (o *Recharge) GetList(dest interface{}, userId uint, kw string, state int, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o).Where("user_id=?", userId).Order("id desc")
	if state >= 0 {
		tx.Where("state=?", state)
	}
	if len(kw) > 0 {
		tx.Where("description like ?", "%"+kw+"%")
	}
	if err = tx.Count(&total).Error; err != nil {
		return 0, err
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Recharge) GetListByManager(dest interface{}, userId uint, state int, page int, pageSize int) (int64, error) {
	// ary := make([]Recharge, 0)
	// resArr := make([]RechargeManageRes, 0)
	var total int64
	tx := db.Debug().Model(o).Where("1 = 1")
	if userId > 0 {
		tx.Where("user_id=? ", userId)
	}
	if state > -1 {
		tx.Where("state=? ", state)
	}
	if err = tx.Count(&total).Error; err != nil {
		return 0, err
	}

	var rechargeProduct RechargeProduct
	arr, err := rechargeProduct.GetList("", "", "")
	if err != nil {
		return 0, err
	}
	rechargeProductMap := make(map[uint]string)
	for _, rcp := range arr {
		rechargeProductMap[rcp.ID] = rcp.ShowTitle
	}

	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	// for _, res := range ary {

	// 	resArr = append(resArr, RechargeManageRes{
	// 		ID:              res.ID,
	// 		CreateAt:        res.CreatedAt,
	// 		Store:           res.Store,
	// 		Product:         rechargeProductMap[res.ProductId],
	// 		ProductCategory: enums.ProductCategoryNameEnum.GetStructField(enums.ProductCategoryEnum.GetKey(res.ProductCategory)),
	// 		OutTradeNo:      res.OutTradeNo,
	// 		Coin:            res.Coin,
	// 		Amount:          res.Amount,
	// 		Description:     res.Description,
	// 		Gateway:         res.Gateway,
	// 		PayChannel:      res.PayChannel,
	// 		PayTradeId:      res.PayTradeId,
	// 		State:           res.State,
	// 		PayTime:         res.PayTime,
	// 	})
	// }
	return total, tx.Error
}

func (o *Recharge) StatisticRecharge(start time.Time, end time.Time) (int64, decimal.Decimal, error) {
	var total int64
	var amount decimal.Decimal
	tx := db.Debug().Model(o).Where("created_at > ? AND created_at < ?", start, end).Where("state = 1")
	if err = tx.Pluck("SUM(amount) as totalAmount", &amount).Error; err != nil {
		return 0, amount, err
	}
	if err = tx.Count(&total).Error; err != nil {
		return 0, amount, err
	}
	return total, amount, tx.Error
}

func (o *Recharge) GetListForCheck(lastId uint, limit int) ([]Recharge, error) {
	ary := make([]Recharge, 0)
	tx := db.Debug().Model(o).Where("state=? and id>=?", 0, lastId)
	tx.Limit(limit).Order("id asc").Scan(&ary)
	return ary, tx.Error
}

func (o *Recharge) Save() error {
	return db.Debug().Save(o).Error
}

func (o *Recharge) ExistsPayTradeId(payType string, tradeId string) (bool, error) {
	var recharge Recharge
	if err := db.Debug().First(&recharge, "pay_channel=? and  pay_trade_id = ?", payType, tradeId).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *Recharge) SetState(payCallbackJson string, state int) error {
	return db.Model(o).Updates(Recharge{PayCallbackJson: payCallbackJson, State: state}).Error
}

func (o *Recharge) SetPrepayId(prepayId string) error {
	return db.Model(o).Updates(Recharge{PayTradeId: prepayId}).Error
}

func (o *Recharge) SetAppleVerifyInfo(jsonStr string) error {
	return db.Model(o).Updates(Recharge{PayCallbackJson: jsonStr}).Error
}

func (o *Recharge) SetAppleIapSuccess(tx *gorm.DB, tradeNo string, successTime time.Time, jsonStr string) error {
	if o.ExpiresDate.After(successTime) {
		return tx.Model(o).Updates(Recharge{PayTradeId: tradeNo, PayTime: successTime, ExpiresDate: o.ExpiresDate, PayCallbackJson: jsonStr, State: 1}).Error
	}
	return tx.Model(o).Updates(Recharge{PayTradeId: tradeNo, PayTime: successTime, PayCallbackJson: jsonStr, State: 1}).Error
}

func (o *Recharge) SetPaySuccess(tx *gorm.DB, tradeNo string, successTime time.Time, jsonStr string) error {
	if o.ExpiresDate.After(successTime) {
		return tx.Model(o).Updates(Recharge{PayTradeId: tradeNo, PayTime: successTime, ExpiresDate: o.ExpiresDate, PayCallbackJson: jsonStr, State: 1}).Error
	}
	return tx.Model(o).Updates(Recharge{PayTradeId: tradeNo, PayTime: successTime, PayCallbackJson: jsonStr, State: 1}).Error
}

func (o *Recharge) SetAliPaySuccess(tx *gorm.DB, tradeNo string, successTime time.Time, jsonStr string) error {
	if o.ExpiresDate.After(successTime) {
		return tx.Model(o).Updates(Recharge{PayTradeId: tradeNo, PayTime: successTime, ExpiresDate: o.ExpiresDate, PayCallbackJson: jsonStr, State: 1}).Error
	}
	return tx.Model(o).Updates(Recharge{PayTradeId: tradeNo, PayTime: successTime, PayCallbackJson: jsonStr, State: 1}).Error
}
