package model

import "gorm.io/gorm"

type RedeemGroup struct {
	gorm.Model
	GroupName         string `json:"group_name" gorm:"type:varchar(50);not null;default:'';comment:兑换码生成的批次名称"`
	AgentUserId       uint   `json:"agent_user_id" gorm:"type:bigint;not null;default:0;comment:代理商用户ID"`
	MemberCardCount   int    `json:"member_card_count" gorm:"type:int;not null;default:0;comment:会员卡数量"`
	RechargePackCount int    `json:"recharge_pack_count" gorm:"type:int;not null;default:0;comment:充值包数量"`
	Operator          string `json:"operator" gorm:"type:varchar(50);not null;default:'';comment:操作员"`
	OperatorUserId    uint   `json:"operator_user_id" gorm:"type:bigint;not null;default:0;comment:操作员用户ID"`
	PaymentInfo       string `json:"payment_info" gorm:"type:varchar(190);not null;default:'';comment:支付信息"`
	Remark            string `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注(由管理员添加)"`
	State             int    `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 1已生成 2已交付"`
}

func (RedeemGroup) TableName() string {
	return "T_RedeemGroup"
}

func (o *RedeemGroup) New(tx *gorm.DB) error {
	return tx.Save(o).Error
}
