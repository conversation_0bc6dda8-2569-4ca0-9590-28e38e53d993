package model

import (
	"gorm.io/gorm"
)

type RoomStyle struct {
	gorm.Model
	Prompt         string `json:"prompt" gorm:"type:varchar(1500);not null;default:'';comment:风格描述"`
	NegativePrompt string `json:"negative_prompt" gorm:"type:varchar(1500);not null;default:'';comment:我不想要什么样的风格画面描述"`
	RefImg         string `json:"ref_img" gorm:"type:varchar(200);not null;default:'';comment:风格图片"`
	Title          string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	Keys           string `json:"keys" gorm:"type:varchar(200);not null;default:'';comment:lora匹配关键字 用英文逗号隔开"`
	Remark         string `json:"remark" gorm:"type:varchar(100);not null;default:'';comment:后台备注"`
	OrderIndex     int    `json:"order_index" gorm:"type:int;not null;default:0;comment:序号(越小越前面)"`
	ModelName      string `json:"model_name" gorm:"type:varchar(100);not null;default:'';comment:模型名称"`
	ModelHash      string `json:"model_hash" gorm:"type:varchar(50);not null;default:'';comment:模型hash"`
	State          int    `json:"state" gorm:"type:int;not null;default:0;comment:状态(1可用)"`
}

func (RoomStyle) TableName() string {
	return "T_RoomStyle"
}

func (o *RoomStyle) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *RoomStyle) GetList(dest interface{}, state int, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o).Where("state=?", state).Order("order_index asc")
	if page > 0 && pageSize > 0 {
		if page == 1 {
			if err = tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
		tx.Limit(pageSize).Offset((page - 1) * pageSize)
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *RoomStyle) Save() error {
	return db.Debug().Save(o).Error
}

func (o *RoomStyle) SetState(state int) error {
	return db.Model(o).Updates(RoomStyle{State: state}).Error
}
