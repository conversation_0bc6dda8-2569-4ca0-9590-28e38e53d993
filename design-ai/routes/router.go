package routes

import (
	"design-ai/api/alipay"
	"design-ai/api/appleiap"
	"design-ai/api/manage"
	v1 "design-ai/api/v1"
	"design-ai/api/wechatpay"
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/utils/config"
	"design-ai/utils/logger"
	"design-ai/utils/myredis"
	"net/http"

	"github.com/gin-gonic/gin"
)

func InitRouter() {
	r := gin.New()
	//r.Use(middleware.Log())
	r.Use(middleware.Cors())
	//r.Use(middleware.TimeoutMiddleware(time.Second * 15))

	//r.StaticFile("api/testupload", "./chunkfile/testupload.html")
	//r.Static("/", "/Users/<USER>/Downloads/h522")
	//r.Static("/", "/Users/<USER>/Desktop/zcloud/aigc-front/HomeDecoration/unpackage/dist/build/h5")

	router := r.Group("api/v1")
	{
		router.GET("hello/:action", func(c *gin.Context) {

			//http://localhost:5002/api/v1/hello/img.png
			action := c.Param("action")
			result := make(map[string]interface{})
			if action == "img1.png" {
				c.Header("Content-Type", "application/octet-stream")
				c.Header("Content-Disposition", "attachment; filename=xiaz11.png")
				c.Header("Content-Transfer-Encoding", "binary")
				c.File("/Users/<USER>/Downloads/8e88f96fdcc92553e62e6f2415a23e42.png")
				return
			}
			if action == "img2.png" {
				c.Header("Content-Type", "application/octet-stream") //
				//c.Header("Content-Disposition", "attachment; filename=xiaz11.png")
				//c.Header("Content-Transfer-Encoding", "binary")
				c.File("/Users/<USER>/Downloads/15481496764171.png")
				return
			}
			if action == "segment_map" {
				ms, err := myredis.HGetAll(enums.AigcRedisKeyEnum.SegmentPop)
				result["segments_map"] = ms
				if err != nil {
					result["segments_err"] = err
				}
			}
			if action == "segment_map_del" {
				err := myredis.Del(enums.AigcRedisKeyEnum.SegmentPop)
				if err != nil {
					result["segment_map_del_err"] = err
				}
			}
			if action == "segment_map_type" {
				value, err := myredis.Type(enums.AigcRedisKeyEnum.SegmentPop)
				if err != nil {
					result["segment_map_type_err"] = err
				}
				result["segment_map_type"] = value
			}

			c.JSON(http.StatusOK, gin.H{
				"msg":    "ok",
				"result": result,
				"value":  action,
				"host":   c.Request.Host,
				"path":   c.Request.URL.Path,
			})
		})

		//router.POST("getverifycode", v1.GetVerifyCode)
		router.POST("getsmscode", v1.SendSms)
		//router.POST("recharge/price_list", v1.RechargeApi.GetPriceList)
		//router.POST("conf/get", v1.ConfApi.Get)

		//router.POST("user/reg", v1.Reg)
		router.POST("site/get_conf", v1.SiteApi.GetConf)
		router.POST("user/login", v1.Login)
		router.POST("user/loginsms", v1.LoginSms)
		router.POST("user/log", v1.UserLogApi.Save)
		//router.POST("user/reset_password", v1.UserApi.ChangePasswordByMobile)
		//router.POST("user/logout", v1.Reg)
		//
		//router.POST("weixin/login", weixin.Login)
		//router.POST("weixin/notify", weixin.Notify)
		//

		router.POST("recharge/design_products", v1.RechargeApi.GetDesignProductList)
		router.GET("alipay/callback", alipay.Callback)
		router.POST("alipay/notify", alipay.Notify)

		router.GET("wechatpay/callback", wechatpay.Callback)
		router.POST("wechatpay/notify", wechatpay.Notify)

		router.POST("appleiap/notify", appleiap.Notify)
		router.POST("appleiap/restore", appleiap.Restore)
		router.POST("appleiap/verify_hand", appleiap.VerifyHand)

		router.POST("outimg/list_case", v1.OutImgApi.GetCaseImgList)
	}

	routerAuth := r.Group("api/v1")
	routerAuth.Use(middleware.JwtToken())
	{
		routerAuth.POST("sys/restart_project", v1.SysApi.RestartProject)
		routerAuth.POST("sys/publish_project", v1.SysApi.PublishProject)
		routerAuth.POST("sys/publish_program", v1.SysApi.PublishProgram)
		routerAuth.POST("sys/trans_upload", v1.SysApi.TransUpload)
		routerAuth.POST("sys/trans_redis", v1.SysApi.TransRedis)
		routerAuth.POST("sys/loar_redis", v1.SysApi.GetLoarRedis)
		routerAuth.POST("sys/set_insider_user", v1.SysApi.SetInsiderUser)
		routerAuth.POST("sys/reset_site_conf", v1.SysApi.ReSetSiteConf)

		routerAuth.POST("user/get_info", v1.UserApi.GetUserInfo)
		routerAuth.POST("user/get_invite_qrcode", v1.UserApi.GetInviteQrCode)
		routerAuth.POST("user/invited_users", v1.UserApi.InvitedUserList)

		routerAuth.POST("user/change_password", v1.UserApi.ChangePassword)
		routerAuth.POST("user/set_insider_state", v1.UserApi.SetInsiderUserState)
		routerAuth.POST("user/report", v1.UserApi.Report)

		routerAuth.POST("init_image/upload", v1.InitImageApi.Upload)

		routerAuth.POST("design/genloar", v1.DesignApi.GenLoar)
		routerAuth.POST("design/list", v1.DesignApi.GetList)

		routerAuth.POST("mask/analyze", v1.MaskApi.Analyze)
		routerAuth.POST("mask/refresh", v1.MaskApi.Refresh)
		routerAuth.POST("mask/get_status", v1.MaskApi.GetStatus)

		routerAuth.POST("outimg/list", v1.OutImgApi.GetOutImgList)
		routerAuth.POST("outimg/get", v1.OutImgApi.GetOutImgItem)
		routerAuth.POST("outimg/del", v1.OutImgApi.DelByMd5)
		routerAuth.POST("outimg/share", v1.OutImgApi.SetShare)

		routerAuth.POST("coin/addquick", v1.CoinApi.AddQuick)
		routerAuth.POST("coin/get_balance", v1.CoinApi.GetBalance)
		routerAuth.POST("recharge/launch", v1.RechargeApi.Launch)
		routerAuth.POST("recharge/get_balance", v1.RechargeApi.GetBalance)
		routerAuth.POST("recharge/query", v1.RechargeApi.QueryByOutTradeNo)
		routerAuth.POST("appleiap/verify", appleiap.Verify)
		routerAuth.POST("redeem/exchange", v1.RedeemApi.Exchange)

		routerAuth.POST("map/get_location_by_ip", v1.MapApi.GetLocationByIp)
		routerAuth.POST("map/get_location_by_pots", v1.MapApi.GetLocationByPots)

		routerAuth.POST("goods/create_project", v1.CreateProject)
		routerAuth.POST("goods/update_project", v1.UpdateProject)
		routerAuth.POST("goods/upload_project_cover", v1.UploadProjectCover)
		routerAuth.POST("goods/remove_project", v1.RemoveProject)
		routerAuth.POST("goods/get_projects", v1.GetProjects)

		routerAuth.POST("goods/update_scene", v1.UpdateScene)
		routerAuth.POST("goods/upload_preset_model", v1.UploadPresetModel)
		routerAuth.POST("goods/get_preset_models", v1.GetPresetModelList)
		routerAuth.POST("goods/upload_custom_product", v1.UploadCustomProduct)
		routerAuth.POST("goods/get_custom_products", v1.GetCustomProductList)
		routerAuth.POST("goods/get_custom_product_by_id", v1.GetCustomProductById)
		routerAuth.POST("goods/remove_custom_product", v1.RemoveCustomProduct)

		routerAuth.POST("goods/get_preset_prompts", v1.GetPresetPrompts)
		routerAuth.POST("goods/generateImages", v1.GenerateImages)
		routerAuth.POST("goods/getOutputImages", v1.GetOutputImages)
		routerAuth.POST("goods/getOutputImagesByProject", v1.GetOutputImagesByProject)

		routerAuth.POST("goods/getScene", v1.GetScene)

		routerAuth.POST("goods/add_template_editor_item", v1.AddTemplateEditorItem)
		routerAuth.POST("goods/get_template_editor_items", v1.GetTemplateEditorItems)
		routerAuth.POST("goods/get_editor_options", v1.GetEditorOptions)
		routerAuth.POST("goods/add_template", v1.AddTemplate)
		routerAuth.POST("goods/update_template", v1.UpdateTemplate)
		routerAuth.POST("goods/update_template_ref_img", v1.UpdateTemplateRefImg)
		routerAuth.POST("goods/get_template_list", v1.GetTemplateList)

	}

	noneAuthManage := r.Group("api/manage")
	{
		noneAuthManage.POST("login", manage.Login)
		noneAuthManage.GET("get_image_by_path", manage.GetImgByPath)
	}

	routerAuthManage := r.Group("api/manage")
	routerAuthManage.Use(middleware.JwtTokenCenter())
	{
		routerAuthManage.POST( /** 获取用户列表*/ "user/list", manage.UserList)
		routerAuthManage.POST( /** 获取用户列表*/ "user/output_mobile", manage.OutputMobile)

		routerAuthManage.POST( /** 获取设计列表*/ "design/list", manage.DesignList)
		routerAuthManage.POST( /** 获取单个设计设计*/ "design/get_design", manage.GetDesign)
		routerAuthManage.POST( /** 获取用户流水*/ "coin-balance/list", manage.GetCoinBalanceListByUser)
		routerAuthManage.POST( /** 获取用户出图列表*/ "outimg/list", manage.GetOutImgList)
		routerAuthManage.GET("outimg/sd_info", manage.GetSdInfo)
		routerAuthManage.GET("get_image/:tp/:md5/*download", manage.DownloadImg)

		routerAuthManage.POST("site/get_conf", v1.SiteApi.GetConf)
		routerAuthManage.POST("recharge/list", manage.RechargeList)
		routerAuthManage.POST("room_style/list", manage.RoomStyleList)
		routerAuthManage.POST("room_style/ref_img/upload", manage.RoomStyleRefImgUpload)
		//routerAuthManage.GET("currentUser", auth.CurrentUser)
		//
		//routerAuthManage.POST("artstyle/list", auth.ArtStyleApi.GetList)
		//routerAuthManage.POST("artstyle/add", auth.ArtStyleApi.Add)
		//routerAuthManage.POST("artstyle/upload", auth.ArtStyleApi.Upload)
		//routerAuthManage.POST("artstyle/active", auth.ArtStyleApi.Active)

		routerAuthManage.POST("redeem/list", manage.RedeemApi.RedeemList)
		routerAuthManage.POST("redeem/gen", v1.RedeemApi.Gen)

		routerAuthManage.POST("statistic/GenStatistic", manage.GenStatistic)

	}
	logger.Info("开启端口监听...", config.HttpPort)
	if err := r.Run(config.HttpPort); err != nil {
		logger.Error("端口监听失败", err)
	} else {
		logger.Info("端口监听中,port:", config.HttpPort)
	}
}
