package service

import (
	"crypto/md5"
	"design-ai/enums"
	"design-ai/model"
	"design-ai/utils/logger"
	"design-ai/utils/myredis"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
)

type locationService_ struct{}

const mapKey = "YEGBZ-RSK6H-4WDDH-W4H4M-DUU3V-PQBSA"
const sKey = "MgyZS6jx8OQe227Q5oaOepz2UitMbfwY"
const mapBaseUrl = "https://apis.map.qq.com"

type location struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type address struct {
	Nation       string `json:"nation"`
	Province     string `json:"province"`
	City         string `json:"city"`
	District     string `json:"district"`
	Street       string `json:"street"`
	StreetNumber string `json:"street_number"`
	Adcode       int    `json:"adcode"`
}

type mapResult struct {
	Location         location `json:"location"`
	Address          string   `json:"address"`
	AddressComponent address  `json:"address_component"`
}

type mapPostion struct {
	Status int       `json:"status"`
	Result mapResult `json:"result"`
}

type ipMapResult struct {
	Ip       string   `json:"ip"`
	Location location `json:"location"`
	AdInfo   address  `json:"ad_info"`
}

type ipMapPostion struct {
	Status int         `json:"status"`
	Result ipMapResult `json:"result"`
}

type locationGet_ struct {
	Type string `json:"type"`
	Id   uint   `json:"id"`
}

func (locationService_ *locationService_) GetLocationByIp(ip string) (ipMapPostion, error) {
	var _locationPosition ipMapPostion
	apiUrl := "/ws/location/v1/ip"
	url := fmt.Sprintf("%s?ip=%s&key=%s", apiUrl, ip, mapKey)
	has := md5.Sum([]byte(url + sKey))
	md5Str := hex.EncodeToString(has[:])

	if len(md5Str) != 32 {
		return _locationPosition, errors.New("地图签名生成失败")
	}
	// 发送GET请求
	response, err := http.Get(fmt.Sprintf("%s%s&sig=%s", mapBaseUrl, url, md5Str))
	if err != nil {
		logger.Error(err.Error())
		return _locationPosition, errors.New("获取地址失败")
	}
	defer response.Body.Close()
	// 读取响应的数据
	body, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return _locationPosition, errors.New("读取数据出错")

	}
	fmt.Println("读取地图返回数据:", string(body))
	err = json.Unmarshal(body, &_locationPosition)
	if err != nil {

		return _locationPosition, errors.New("读取地图返回数据出错")
	}
	return _locationPosition, nil
}

func (locationService_ *locationService_) GetLocationByPots(lat float64, lng float64) (mapPostion, error) {
	var _locationPosition mapPostion
	apiUrl := "/ws/geocoder/v1"
	position := fmt.Sprintf("%f,%f", lat, lng)
	url := fmt.Sprintf("%s?key=%s&location=%s", apiUrl, mapKey, position)
	has := md5.Sum([]byte(url + sKey))
	md5Str := hex.EncodeToString(has[:])

	if len(md5Str) != 32 {
		logger.Error("地图签名生成失败")
		return _locationPosition, errors.New("地图签名生成失败")
	}
	// 发送GET请求
	response, err := http.Get(fmt.Sprintf("%s%s&sig=%s", mapBaseUrl, url, md5Str))
	if err != nil {
		logger.Error(err.Error())
		return _locationPosition, errors.New("获取地址失败")
	}
	defer response.Body.Close()
	// 读取响应的数据
	body, err := ioutil.ReadAll(response.Body)
	if err != nil {
		fmt.Println("读取数据出错:", err)
		return _locationPosition, errors.New("读取数据出错")

	}
	err = json.Unmarshal(body, &_locationPosition)
	fmt.Println("读取地图返回数据:", string(body))
	if err != nil {
		fmt.Println("读取地图返回数据出错:", err)
		return _locationPosition, errors.New("读取地图返回数据出错")

	}
	return _locationPosition, nil
}

func (ls *locationService_) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("locationService奔溃:", e)
		}
	}()
	logger.Info("LocationService.Run 开始循环获取位置信息")
	for {
		value, err := ls.PopJson()

		if err != nil {
			logger.Error(value, err)
			continue
		}
		if value == "" {
			continue
		}
		logger.Info("接收到绘图json:", value)
		if err := ls.UpdateLocation(value); err != nil {
			logger.Error(err)
		}
	}
}

func (ls *locationService_) UpdateLocation(value string) error {
	outputData := locationGet_{}
	if value == "" {
		logger.Error("数据为空")
		return errors.New("数据为空")
	}

	if err := json.Unmarshal([]byte(value), &outputData); err != nil {
		logger.Error(err)
		return err
	}

	if outputData.Type == "user" { //用户表
		var user model.User
		if err := user.GetByID(outputData.Id); err != nil {
			logger.Error(err)
			return err
		}
		if user.RegIp != "" && user.RegAddr == "" {
			res, err := ls.GetLocationByIp(user.RegIp)
			if err != nil {
				logger.Error(err)
				return err
			}
			address := res.Result.AdInfo.Nation + " " + res.Result.AdInfo.Province + " " + res.Result.AdInfo.City + " " + res.Result.AdInfo.District
			if res.Result.AdInfo.Street != "" {
				address += " " + res.Result.AdInfo.Street
			}
			if res.Result.AdInfo.StreetNumber != "" {
				address += " " + res.Result.AdInfo.StreetNumber
			}
			if err := user.SetIpAddr(address); err != nil {
				logger.Error(err)
			}
		}
	} else if outputData.Type == "userlog" { //用户日志表
		var userlog model.UserLog
		if err := userlog.GetByID(outputData.Id); err != nil {
			logger.Error(err)
			return err
		}
		if userlog.Ip != "" && userlog.Address == "" {
			res, err := ls.GetLocationByIp(userlog.Ip)
			if err != nil {
				logger.Error(err)
				return err
			}

			address := res.Result.AdInfo.Nation + " " + res.Result.AdInfo.Province + " " + res.Result.AdInfo.City + " " + res.Result.AdInfo.District
			if res.Result.AdInfo.Street != "" {
				address += " " + res.Result.AdInfo.Street
			}
			if res.Result.AdInfo.StreetNumber != "" {
				address += " " + res.Result.AdInfo.StreetNumber
			}
			if err := userlog.SetAddr(res.Result.AdInfo.Nation, res.Result.AdInfo.Province, res.Result.AdInfo.City, res.Result.AdInfo.District, address); err != nil {
				logger.Error(err)
			}
		}
	}
	return nil
}

func (ls *locationService_) PopJson() (string, error) {
	value, err := myredis.BRPop(enums.AigcRedisKeyEnum.LocationGet)
	return value, err
}

func (ls *locationService_) PushJson(ty string, id uint) (int64, error) {

	if ty != "user" && ty != "userlog" {
		err := errors.New("ty 不正确" + ty)
		logger.Error(err)
		return 0, err
	}
	json := fmt.Sprintf(`{"type":"%s","id":%d}`, ty, id)
	size, err := myredis.LPush(enums.AigcRedisKeyEnum.LocationGet, json)
	if err != nil {
		logger.Error(err)
	}
	return size, err
}

var LocationService locationService_
