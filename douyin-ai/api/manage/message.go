package manage

import (
	"center-ai/enums"
	"center-ai/middleware"
	"center-ai/model"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"github.com/gin-gonic/gin"
	"net/http"
)

type messageApi_ struct {
}

var MessageApi messageApi_

type messageListReq struct {
	EnterId  uint   `json:"enter_id"`
	Method   string `json:"method"`
	ShortId  uint   `json:"short_id"`
	GiftId   uint   `json:"gift_id"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type messageListItem struct {
	ID         uint           `json:"id"`
	EnterId    uint           `json:"enter_id"`
	RoomId     string         `json:"room_id"`
	Method     string         `json:"method"`
	ShortId    uint           `json:"short_id"`
	NickName   string         `json:"nick_name"`
	Content    string         `json:"content"`
	GiftId     uint           `json:"gift_id"`
	GiftName   string         `json:"gift_name"`
	GiftPrice  int            `json:"gift_price"`
	ComboCount uint           `json:"combo_count"`
	SendTime   model.JsonTime `json:"send_time"`
	TraceId    string         `json:"trace_id"`
}

func (obj messageApi_) List(c *gin.Context) {
	var code int
	var oReq messageListReq
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var message model.DouyinMessage

	var arr = make([]messageListItem, 0)
	total, err := message.List(&arr, oReq.EnterId, oReq.Method, oReq.ShortId, oReq.GiftId, oReq.Page, oReq.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
		return
	}

	result := make(map[string]interface{})
	result["items"] = arr
	result["total"] = total
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}
