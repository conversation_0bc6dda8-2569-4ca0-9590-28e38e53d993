package sd

import (
	"center-ai/enums"
	"center-ai/service"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"encoding/base64"
	"fmt"
	"github.com/gin-gonic/gin"
	"io/ioutil"
	"net/http"
)

type Img2ImgRequest struct {
	InitImages             []string `json:"init_images"`
	ResizeMode             int      `json:"resize_mode"`
	DenoisingStrength      float64  `json:"denoising_strength"`
	ImageCfgScale          int      `json:"image_cfg_scale"`
	Mask                   string   `json:"mask"`
	MaskBlur               int      `json:"mask_blur"`
	MaskBlurX              int      `json:"mask_blur_x"`
	MaskBlurY              int      `json:"mask_blur_y"`
	InpaintingFill         int      `json:"inpainting_fill"`
	InpaintFullRes         bool     `json:"inpaint_full_res"`
	InpaintFullResPadding  int      `json:"inpaint_full_res_padding"`
	InpaintingMaskInvert   int      `json:"inpainting_mask_invert"`
	InitialNoiseMultiplier int      `json:"initial_noise_multiplier"`
	Prompt                 string   `json:"prompt"`
	Styles                 []string `json:"styles"`
	Seed                   int      `json:"seed"`
	Subseed                int      `json:"subseed"`
	SubseedStrength        int      `json:"subseed_strength"`
	SeedResizeFromH        int      `json:"seed_resize_from_h"`
	SeedResizeFromW        int      `json:"seed_resize_from_w"`
	SamplerName            string   `json:"sampler_name"`
	BatchSize              int      `json:"batch_size"`
	NIter                  int      `json:"n_iter"`
	Steps                  int      `json:"steps"`
	CfgScale               int      `json:"cfg_scale"`
	Width                  int      `json:"width"`
	Height                 int      `json:"height"`
	RestoreFaces           bool     `json:"restore_faces"`
	Tiling                 bool     `json:"tiling"`
	DoNotSaveSamples       bool     `json:"do_not_save_samples"`
	DoNotSaveGrid          bool     `json:"do_not_save_grid"`
	NegativePrompt         string   `json:"negative_prompt"`
	Eta                    int      `json:"eta"`
	SMinUncond             int      `json:"s_min_uncond"`
	SChurn                 int      `json:"s_churn"`
	STmax                  int      `json:"s_tmax"`
	STmin                  int      `json:"s_tmin"`
	SNoise                 int      `json:"s_noise"`
	OverrideSettings       struct {
	} `json:"override_settings"`
	OverrideSettingsRestoreAfterwards bool          `json:"override_settings_restore_afterwards"`
	ScriptArgs                        []interface{} `json:"script_args"`
	SamplerIndex                      string        `json:"sampler_index"`
	IncludeInitImages                 bool          `json:"include_init_images"`
	ScriptName                        string        `json:"script_name"`
	SendImages                        bool          `json:"send_images"`
	SaveImages                        bool          `json:"save_images"`
	AlwaysonScripts                   struct {
	} `json:"alwayson_scripts"`
}

type SimpleImg2ImgRequest struct {
	InitImages     []string `json:"init_images"`
	Prompt         string   `json:"prompt"`
	NegativePrompt string   `json:"negative_prompt"`
	Mask           string   `json:"mask"`
}

func Img2img(c *gin.Context) {

	var code int
	var msg string
	var oReq ParametersRequest

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims.UserId <= 0 {
	//	errmsg.Abort(c, errmsg.FAIL, "请先登录")
	//	return
	//}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err, code, msg)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var img2Img Img2ImgRequest
	if err := tools.GetStructFromJson(&img2Img, oReq.Parameters); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据解析失败")
		return
	}

	//ScanImg2img(&img2Img)

	if oReq.InitImageUrl != "" {
		if base64, err := GetImgFromUrl(oReq.InitImageUrl); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "获取参考图失败")
			return
		} else {
			if img2Img.InitImages == nil || len(img2Img.InitImages) == 0 {
				img2Img.InitImages = make([]string, 0)
			}
			img2Img.InitImages = append(img2Img.InitImages, "data:image/png;base64,"+base64)
		}
	}
	//if oReq.MaskImageUrl != "" {
	//	if base64, err := GetImgFromUrl(oReq.MaskImageUrl); err != nil {
	//		logger.Error(err)
	//		errmsg.Abort(c, errmsg.FAIL, "获取蒙版图失败")
	//		return
	//	} else {
	//		img2Img.Mask = "data:image/png;base64," + base64
	//	}
	//}
	img2Img.Mask = `"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABHAAAAXwCAYAAAA6sby2AAAAAXNSR0IArs4c6QAAIABJREFUeF7s2MENADAMAjG6/9Ct1Cnu4UyATF6cbXeOAAECBAgQIECAAAECBAgQIEAgK3AMONluBCNAgAABAgQIECBAgAABAgQIfAEDjkcgQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGAW8LD1AAAgAElEQVTA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeOr9eE4AACAASURBVAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAgQIAAAQIEDDh+gAABAgQIECBAgAABAgQIECAQFzDgxAsSjwABAgQIECBAgAABAgQIECBgwPEDBAgQIECAAAECBAgQIECAAIG4gAEnXpB4BAgQIECAAAECBAgQIECAAAEDjh8gQIAAAQIECBAgQIAAAQIECMQFDDjxgsQjQIAAAQIECBAgQIAAAQIECBhw/AABAgQIECBAgAABAgQIECBAIC5gwIkXJB4BAgQIECBAgAABAgQIECBAwIDjBwgQIECAAAECBAgQIECAAAECcQEDTrwg8QgQIECAAAECBAgQIECAAAECBhw/QIAAAQIECBAgQIAAAQIECBCICxhw4gWJR4AAAQIECBAgQIAAAQIECBAw4PgBAgQIECBAgAABAgQIECBAgEBcwIATL0g8AgQIECBAgAABAgQIECBAgIABxw8QIECAAAECBAgQIECAAAECBOICBpx4QeIRIECAAAECBAgQIECAAAECBAw4foAAAQIECBAgQIAAAQIECBAgEBcw4MQLEo8AAQIECBAgQIAAAQIECBAgYMDxAwQIECBAgAABAgQIECBAgACBuIABJ16QeAQIECBAgAABAgQIECBAgAABA44fIECAAAECBAgQIECAAAECBAjEBQw48YLEI0CAAAECBAgQIECAAAECBAgYcPwAAQIECBAgQIAAAQIECBAgQCAuYMCJFyQeAQIECBAgQIAAAQIECBAgQMCA4wcIECBAgAABAgQIECBAgAABAnEBA068IPEIECBAgAABAgQIECBAgAABAgYcP0CAAAECBAgQIECAAAECBAgQiAsYcOIFiUeAAAECBAgQIECAAAECBAgQMOD4AQIECBAgQIAAAQIECBAgQIBAXMCAEy9IPAIECBAgQIAAAQIECBAgQICAAccPECBAgAABAgQIECBAgAABAgTiAgaceEHiESBAgAABAgQIECBAgAABAgQMOH6AAAECBAgQIECAAAECBAgQIBAXMODECxKPAAECBAgQIECAAAECBAgQIGDA8QMECBAgQIAAAQIECBAgQIAAgbiAASdekHgECBAgQIAAAQIECBAgQIAAAQOOHyBAgAABAgQIECBAgAABAgQIxAUMOPGCxCNAgAABAgQIECBAgAABAgQIGHD8AAECBAgQIECAAAECBAgQIEAgLmDAiRckHgECBAgQIECAAAECBAgQIEDAgOMHCBAgQIAAAQIECBAgQIAAAQJxAQNOvCDxCBAgQIAAAQIECBAgQIAAAQIGHD9AgAABAgQIECBAgAABAgQIEIgLGHDiBYlHgAABAgQIECBAgAABAgQIEDDg+AECBAgQIECAAAECBAgQIECAQFzAgBMvSDwCBAgQIECAAAECBAgQIECAgAHHDxAgQIAAAQIECBAgQIAAAQIE4gIGnHhB4hEgQIAAAQIECBAg8Nq7mxC7zjIO4G8mLioUjOJCKSKKC20F+wESMM1kJpWUlja4UrAuigsVcSFUaWznM1VT6kJQXBf8QPcuKjbJzLSgq7pzJejChQvBii5cmBk5t6bGGpPJuefe/O95fhdCW3LPe5/n9zxNm3/uPZcAAQIECAhw7AABAgQIECBAgAABAgQIECBAIFxAgBM+IOURIECAAAECBAgQIECAAAECBAQ4doAAAQIECBAgQIAAAQIECBAgEC4gwAkfkPIIECBAgAABAgQIECBAgAABAgIcO0CAAAECBAgQIECAAAECBAgQCBcQ4IQPSHkECBAgQIAAAQIECBAgQIAAAQGOHSBAgAABAgQIECBAgAABAgQIhAsIcMIHpDwCBAgQIECAAAECBAgQIECAgADHDhAgQIAAAQIECBAgQIAAAQIEwgUEOOEDUh4BAgQIECBAgAABAgQIECBAQIBjBwgQIECAAAECBAgQIECAAAEC4QICnPABKY8AAQIECBAgQIAAAQIECBAgIMCxAwQIECBAgAABAgQIECBAgACBcAEBTviAlEeAAAECBAgQIECAAAECBAgQEODYAQIECBAgQIAAAQIECBAgQIBAuIAAJ3xAyiNAgAABAgQIECBAgAABAgQICHDsAAECBAgQIECAAAECBAgQIEAgXECAEz4g5REgQIAAAQIECBAgQIAAAQIEBDh2gAABAgQIECBAgAABAgQIECAQLiDACR+Q8ggQIECAAAECBAgQIECAAAECAhw7QIAAAQIECBAgQIAAAQIECBAIFxDghA9IeQQIECBAgAABAgQIECBAgAABAY4dIECAAAECBAgQIECAAAECBAiECwhwwgekPAIECBAgQIAAAQIECBAgQICAAMcOECBAgAABAgQIECBAgAABAgTCBQQ44QNSHgECBAgQIECAAAECBAgQIEBAgGMHCBAgQIAAAQIECBAgQIAAAQLhAgKc8AEpjwABAgQIECBAgAABAgQIECAgwLEDBAgQIECAAAECBAgQIECAAIFwAQFO+ICUR4AAAQIECBAgQIAAAQIECBAQ4NgBAgQIECBAgAABAgQIECBAgEC4gAAnfEDKI0CAAAECBAgQIECAAAECBAgIcOwAAQIECBAgQIAAAQIECBAgQCBcQIATPiDlESBAgAABAgQIECBAgAABAgQEOHaAAAECBAgQIECAAAECBAgQIBAuIMAJH5DyCBAgQIAAAQIECBAgQIAAAQICHDtAgAABAgQIECBAgAABAgQIEAgXEOCED0h5BAgQIECAAAECBAgQIECAAAEBjh0gQIAAAQIECBAgQIAAAQIECIQLCHDCB6Q8AgQIECBAgAABAgQIECBAgIAAxw4QIECAAAECBAgQIECAAAECBMIFBDjhA1IeAQIECBAgQIAAAQIECBAgQECAYwcIECBAgAABAgQIECBAgAABAuECApzwASmPAAECBAgQIECAAAECBAgQICDAsQMECBAgQIAAAQIECBAgQIAAgXABAU74gJRHgAABAgQIECBAgAABAgQIEBDg2AECBAgQIECAAAECBAgQIECAQLiAACd8QMojQIAAAQIECBAgQIAAAQIECAhw7AABAgQIECBAgAABAgQIECBAIFxAgBM+IOURIECAAAECBAgQIECAAAECBAQ4doAAAQIECBAgQIAAAQIECBAgEC4gwAkfkPIIECBAgAABAgQIECBAgAABAgIcO0CAAAECBAgQIECAAAECBAgQCBcQ4IQPSHkECBAgQIAAAQIECBAgQIAAAQGOHSBAgAABAgQIECBAgAABAgQIhAsIcMIHpDwCBAgQIECAAAECBAgQIECAgADHDhAgQIAAAQIECBAgQIAAAQIEwgUEOOEDUh4BAgQIECBAgAABAgQIECBAQIBjBwgQIECAAAECBAgQIECAAAEC4QICnPABKY8AAQIECBAgQIAAAQIECBAgIMCxAwQIECBAgAABAgQIECBAgACBcAEBTviAlEeAAAECBAgQIECAAAECBAgQEODYAQIECBAgQIAAAQIECBAgQIBAuIAAJ3xAyiNAgAABAgQIECBAgAABAgQICHDsAAECBAgQIECAAAECBAgQIEAgXECAEz4g5REgQIAAAQIECBAgQIAAAQIEBDh2gAABAgQIECBAgAABAgQIECAQLiDACR+Q8ggQIECAAAECBAgQIECAAAECAhw7QIAAAQIECBAgQIAAAQIECBAIFxDghA9IeQQIECBAgAABAgQIECBAgAABAY4dIECAAAECBAgQIECAAAECBAiECwhwwgekPAIECBAgQIAAAQIECBAgQICAAMcOECBAgAABAgQIECBAgAABAgTCBQQ44QNSHgECBAgQIECAAAECBAgQIEBAgGMHCBAgQIAAAQIECBAgQIAAAQLhAgKc8AEpjwABAgQIECBAgAABAgQIECAgwLEDBAgQIECAAAECBAgQIECAAIFwAQFO+ICUR4AAAQIECBAgQIAAAQIECBAQ4NgBAgQIECBAgAABAgQIECBAgEC4gAAnfEDKI0CAAAECBAgQIECAAAECBAgIcOwAAQIECBAgQIAAAQIECBAgQCBcQIATPiDlESBAgAABAgQIECBAgAABAgQEOHaAAAECBAgQIECAAAECBAgQIBAuIMAJH5DyCBAgQIAAAQIECBAgQIAAAQICHDtAgAABAgQIECBAgAABAgQIEAgXEOCED0h5BAgQIECAAAECBAgQIECAAAEBjh0gQIAAAQIECBAgQIAAAQIECIQLCHDCB6Q8AgQIECBAgAABAgQIECBAgIAAxw4QIECAAAECBAgQIECAAAECBMIFBDjhA1IeAQIECBAgQIAAAQIECBAgQECAYwcIECBAgAABAgQIECBAgAABAuECApzwASmPAAECBAgQIECAAAECBAgQICDAsQMECBAgQIAAAQIECBAgQIAAgXABAU74gJRHgAABAgQIECBAgAABAgQIEBDg2AECBAgQIECAAAECBAgQIECAQLiAACd8QMojQIAAAQIECBAgQIAAAQIECAhw7AABAgQIECBAgAABAgQIECBAIFxAgBM+IOURIECAAAECBAgQIECAAAECBAQ4doAAAQIECBAgQIAAAQIECBAgEC4gwAkfkPIIECBAgAABAgQIECBAgAABAgIcO0CAAAECBAgQIECAAAECBAgQCBcQ4IQPSHkECBAgQIAAAQIECBAgQIAAAQGOHSBAgAABAgQIECBAgAABAgQIhAsIcMIHpDwCBAgQIECgrsDy8nLrfgzx2N3dbd0PDwIECBAgQGAxBQQ4izk3VRMgQIAAAQILKHDHHXe0xx57rN19993tyJHuf8NaW19fn/z16j/Pq61Lly5NAp3t7e15vaTXIUCAAAECBKYQEOBMgedSAgQIECBAgMDNBB599NF24sSJdvbs2faRj3zkZk+/LT+/s7PTNjc3vUPntuh7UQIECBAgcDgBAc7hnDyLAAECBAgQIHBoge5dNd1Hn1ZXVw99TcITL1++PAly9vb2EspRAwECBAgQIHCNgADHOhAgQIAAAQIEphTowppTp069+WPK42775V2Is7W1ddvrUAABAgQIECDwHwEBjm0gQIAAAQIECPxb4NixY+3MmTPtwx/+8OSeNHfddVd729ve1v7+97+3e+65ZxLQXPuY931r5jmoV199tT377LM+VjVPdK9FgAABAgRuICDAsR4ECBAgQIBAKYGr75bpmu4CmJMnT06CmTGHMdMMuLvZ8enTp6c5wrUECBAgQIDAAAICnAEQHUGAAAECBAjcusCdd945CQbuvffeNy++GqLcytdnC15u3f5Wr+jejfPMM8+4N86twnk+AQIECBAYUECAMyCmowgQIECAAIHrC1x910sXtnTfyvTAAw94x8uCLcvBwUFbWVnxkaoFm5tyCRAgQGA8AgKc8cxSJwQIECBAYCqB7v4vXdDSvSPm2ne1HD16tJ07d64tLS3d8vneHXPLZNEXCHGix6M4AgQIEBi5gABn5APWHgECBAgQuJ5Ad2Pe7iMx3V+/8Y1v9ApnyNYU6EKchx56qHX3xvEgQIAAAQIE5icgwJmftVciQIAAAQJzEbj2Jr3dC177LpgHH3ywra6uzqUOLzJegd/97nftvvvum3w7lwcBAgQIECAwHwEBznycvQoBAgQIEBhUoHvnzNmzZ9tHP/rR9vjjj09+M+3jSoMSO+wmAnt7e5OP3HkQIECAAAEC8xEQ4MzH2asQIECAAIGpBa6+s+bhhx9ux48fn/o8BxCYVuCrX/1q++53vzvtMa4nQIAAAQIEDiEgwDkEkqcQIECAAIHbIXA1sDl16lTrfngQSBN4/fXX2wc+8IHW/dWDAAECBAgQmK2AAGe2vk4nQIAAAQITge4bnt7xjne0v/71r+1jH/vY5KMn137k6erfnzx5chLW+DiUxVkUga2trba5ubko5aqTAAECBAgsrIAAZ2FHp3ACBAgQSBNYX1+fBC/djy6sOX369OQeNcKYtEmpZ0gB78IZUtNZBAgQIEDg/wsIcGwHAQIECBC4gUAXynSP671b5no/B5NARYGnn366Pf/88xVb1zMBAgQIEJibgABnbtReiAABAgQSBB577LF2//33tzvuuKN96EMfap/61Kfa0tLSm6V5t0zClNSwaAK//e1v2z333LNoZauXAAECBAgslIAAZ6HGpVgCBAgQOKzA1RsAd4FM91GmT3ziEz7KdFg8zyPQQ+Dtb397+8c//tHjSpcQIECAAAEChxEQ4BxGyXMIECBAIFZgbW1t8g6aLqjpQpu33hw4tnCFERiZwBNPPNF+/OMfj6wr7RAgQIAAgRwBAU7OLFRCgAABAjcQuOuuu9rnP//5SVhz9d01wAgQyBG4cOFCO3fuXE5BKiFAgAABAiMTEOCMbKDaIUCAwFgE3vWud7XPfe5zk6/Ufvzxx//rPjVj6VEfBMYkcHBw4N/TMQ1ULwQIECAQJyDAiRuJgggQIFBb4O67725f/OIXJ+HNsWPHamPonsCCCbgJ+IINTLkECBAgsFACApyFGpdiCRAgMF6BLqz51re+1b7whS/4U/zxjllnIxfo3jG3u7s78i61R4AAAQIEbo+AAOf2uHtVAgQIELhG4JFHHmnf+9732gc/+EEuBAgssMDm5mbb2tpa4A6UToAAAQIEcgUEOLmzURkBAgRKCPzgBz9oX/rSl0r0qkkCYxfowpsuxPEgQIAAAQIEhhcQ4Axv6kQCBAgQOKTApUuX2srKyiGf7WkECCyCgPvgLMKU1EiAAAECiyggwFnEqamZAAECIxC4fPny5BumPAgQGJfAww8/3H7xi1+MqyndECBAgACBAAEBTsAQlECAAIFqAt55U23i+q0k8J3vfKd97Wtfq9SyXgkQIECAwFwEBDhzYfYiBAgQIHBV4OLFi211dRUIAQIjFTg4OPBNciOdrbYIECBA4PYKCHBur79XJ0CAQCmB7iNT3UenPAgQGLfAe9/73vanP/1p3E3qjgABAgQIzFlAgDNncC9HgACBqgLvf//72+9///vmBqdVN0DflQQ2Njba9vZ2pZb1SoAAAQIEZi4gwJk5sRcgQIAAgU7ATYvtAYE6At///vfbV77ylToN65QAAQIECMxBQIAzB2QvQYAAgeoCa2tr/jS++hLov5TAiy++2J588slSPWuWAAECBAjMWkCAM2th5xMgQICAd9/YAQLFBNzIuNjAtUuAAAECcxEQ4MyF2YsQIECgrsDy8nLb2dmpC6BzAkUF3O+q6OC1TYAAAQIzExDgzIzWwQQIECDQCVy6dKmtrKzAIECgmIAAp9jAtUuAAAECTgTLkgAAIABJREFUMxcQ4Myc2AsQIECgtkD3UQoPAgTqCQhw6s1cxwQIECAwWwEBzmx9nU6AAIHSAj4+VXr8mi8ucOrUqba7u1tcQfsECBAgQGA4AQHOcJZOIkCAAIG3CGxubraNjQ0uBAgUFOj+/d/a2irYuZYJECBAgMBsBAQ4s3F1KgECBAi01p577rn2zDPPsCBAoKCAAKfg0LVMgAABAjMVEODMlNfhBAgQqC2wv7/f3Aej9g7ovq5A9+6bLsTxIECAAAECBIYREOAM4+gUAgQIELiOgBsYWwsCdQUEOHVnr3MCBAgQmI2AAGc2rk4lQIBAeYHLly+37iamHgQI1BTofg1YXV2t2byuCRAgQIDADAQEODNAdSQBAgSqCwhvqm+A/gm8IeAjlDaBAAECBAgMJyDAGc7SSQQIECDQWrt48aI/dbcJBAgIcOwAAQIECBAYWECAMzCo4wgQIFBZYG1trW1vb1cm0DsBAtcIdB+j3N3dZUKAAAECBAgMICDAGQDREQQIECDQ2smTJ/1GzSIQIPBfAr5K3EIQIECAAIHhBAQ4w1k6iQABAqUFfHSq9Pg1T+C6Ar6JymIQIECAAIHhBAQ4w1k6iQABAmUFlpeX287OTtn+NU6AwPUFXnvttfbAAw/gIUCAAAECBAYQEOAMgOgIAgQIVBe4dOlSW1lZqc6gfwIE3iJwcHDQlpaWuBAgQIAAAQIDCAhwBkB0BAECBKoLdL9J8yBAgMD1BHyVuL0gQIAAAQLDCAhwhnF0CgECBMoKrK+vt+4+Fx4ECBC4nsD73ve+9sc//hEOAQIECBAgMKWAAGdKQJcTIECguoCbF1ffAP0TuLHAZz7zmfazn/0MEwECBAgQIDClgABnSkCXEyBAoLqAj09V3wD9E7ixgK8StyEECBAgQGAYAQHOMI5OIUCAQFkBAU7Z0WucwKEEBDiHYvIkAgQIECBwUwEBzk2JPIEAAQIEbiQgwLEfBAjcSKC7R1YX4ngQIECAAAEC0wkIcKbzczUBAgRKC2xsbPiNWekN0DyBmwsIcG5u5BkECBAgQOAwAgKcwyh5DgECBAhcV0CAYzEIELiZgADnZkJ+ngABAgQIHE5AgHM4J88iQIAAgesIdB+L6EIcDwIECPw/AQGO3SBAgAABAsMICHCGcXQKAQIESgoIcEqOXdMEbklgf3+/HT169Jau8WQCBAgQIEDgfwUEOLaCAAECBHoLCHB607mQQBmB7kbnS0tLZfrVKAECBAgQmJWAAGdWss4lQIBAAQEBToEha5HAAAJHjnT/y+lBgAABAgQITCMgwJlGz7UECBAoLiDAKb4A2idwSAEBziGhPI0AAQIECNxAQIBjPQgQIECgt4AApzedCwmUElheXm57e3uletYsAQIECBAYWkCAM7So8wgQIFBIQIBTaNhaJTCFQPdrRfdtVB4ECBAgQIBAfwEBTn87VxIgQKC8gACn/AoAIHAoAV8lfigmTyJAgAABAjcUEOBYEAIECBDoLSDA6U3nQgKlBF599dX24IMPlupZswQIECBAYGgBAc7Qos4jQIBAIQEBTqFha5XAlAJuZDwloMsJECBAoLyAAKf8CgAgQIBAfwEBTn87VxKoJiDAqTZx/RIgQIDA0AICnKFFnUeAAIFCAgKcQsPWKoEpBdbX19v58+enPMXlBAgQIECgroAAp+7sdU6AAIGpBQQ4UxM6gEAZAd9EVWbUGiVAgACBGQkIcGYE61gCBAhUEBDgVJiyHgkMI+CbqIZxdAoBAgQI1BUQ4NSdvc4JECAwtYAAZ2pCBxAoIyDAKTNqjRIgQIDAjAQEODOCdSwBAgQqCAhwKkxZjwSGEbhw4UI7d+7cMIc5hQABAgQIFBQQ4BQcupYJECAwlIAAZyhJ5xAYv8DBwUFbWloaf6M6JECAAAECMxIQ4MwI1rEECBCoICDAqTBlPRIYRkCAM4yjUwgQIECgroAAp+7sdU6AAIGpBQQ4UxM6gEAZAQFOmVFrlAABAgRmJCDAmRGsYwkQIFBBQIBTYcp6JDCcwJEj3f96ehAgQIAAAQJ9BAQ4fdRcQ4AAAQITAQGORSBA4FYEBDi3ouW5BAgQIEDgvwUEODaCAAECBHoLCHB607mQQEkBAU7JsWuaAAECBAYSEOAMBOkYAgQIVBQQ4FScup4J9BcQ4PS3cyUBAgQIEBDg2AECBAgQ6C0gwOlN50ICJQUEOCXHrmkCBAgQGEhAgDMQpGMIECBQUUCAU3HqeibQX0CA09/OlQQIECBAQIBjBwgQIECgt4AApzedCwmUFBDglBy7pgkQIEBgIAEBzkCQjiFAgEBFAQFOxanrmUB/AQFOfztXEiBAgAABAY4dIECAAIHeAgKc3nQuJFBSQIBTcuyaJkCAAIGBBAQ4A0E6hgABAhUFBDgVp65nAv0FBDj97VxJgAABAgQEOHaAAAECBHoLCHB607mQQEkBAU7JsWuaAAECBAYSEOAMBOkYAgQIVBQQ4FScup4J9BcQ4PS3cyUBAgQIEBDg2AECBAgQ6C0gwOlN50ICJQUEOCXHrmkCBAgQGEhAgDMQpGMIECBQUUCAU3HqeibQX0CA09/OlQQIECBAQIBjBwgQIECgt4AApzedCwmUFBDglBy7pgkQIEBgIAEBzkCQjiFAgEBFAQFOxanrmUB/AQFOfztXEiBAgAABAY4dIECAAIHeAgKc3nQuJFBSQIBTcuyaJkCAAIGBBAQ4A0E6hgABAhUFBDgVp65nAv0FBDj97VxJgAABAgQEOHaAAAECBHoLCHB607mQQEkBAU7JsWuaAAECBAYSEOAMBOkYAgQIVBQQ4FScup4J9BcQ4PS3cyUBAgQIEBDg2AECBAgQ6C0gwOlN50ICJQUEOCXHrmkCBAgQGEhAgDMQpGMIECBQUUCAU3HqeibQT+Dg4KAtLS31u9hVBAgQIECAQBPgWAICBAgQ6C0gwOlN50IC5QRefvnl9slPfrJc3xomQIAAAQJDCQhwhpJ0DgECBAoKCHAKDl3LBHoKvPjii+3JJ5/sebXLCBAgQIAAAQGOHSBAgACB3gICnN50LiRQTuC5555ra2tr5frWMAECBAgQGEpAgDOUpHMIECBQUGBjY6N1IY4HAQIEbibQ/Xqxvb19s6f5eQIECBAgQOD/CAhwrAYBAgQI9BYQ4PSmcyGBcgLvfOc72+uvv16ubw0TIECAAIGhBAQ4Q0k6hwABAgUFBDgFh65lAj0FfIV4TziXESBAgACBfwsIcKwCAQIECPQWEOD0pnMhgVIC7n9TatyaJUCAAIEZCQhwZgTrWAIECFQQEOBUmLIeCUwv0N0ra2tra/qDnECAAAECBAoLCHAKD1/rBAgQmFbg9OnT7eWXX572GNcTIDByAQHOyAesPQIECBCYi4AAZy7MXoQAAQLjFTg4OBhvczojQGAQAfe/GYTRIQQIECBQXECAU3wBtE+AAIFpBQQ40wq6nsD4BQQ445+xDgkQIEBg9gICnNkbewUCBAiMWkCAM+rxao7AIAICnEEYHUKAAAECxQUEOMUXQPsECBCYVkCAM62g6wmMX0CAM/4Z65AAAQIEZi8gwJm9sVcgQIDAqAUEOKMer+YIDCIgwBmE0SEECBAgUFxAgFN8AbRPgACBaQUEONMKup7A+AUEOOOfsQ4JECBAYPYCApzZG3sFAgQIjFpAgDPq8WqOwCACApxBGB1CgAABAsUFBDjFF0D7BAgQmFbgypUrbWlpadpjXE+AwIgFBDgjHq7WCBAgQGBuAgKcuVF7IQIECIxT4Ic//GF74oknxtmcrggQGERAgDMIo0MIECBAoLiAAKf4AmifAAEC0wqcP3++Pfvss9Me43oCBEYsIMAZ8XC1RoAAAQJzExDgzI3aCxEgQGCcAhsbG21zc3OczemKAIFBBAQ4gzA6hAABAgSKCwhwii+A9gkQIDCtwGc/+9n2ox/9aNpjXE+AwEgFuhudu0/WSIerLQIECBCYq4AAZ67cXowAAQLjEzh27Fj7y1/+Mr7GdESAwCAC+/v77ejRo4Oc5RACBAgQIFBZQIBTefp6J0CAwEACvkp8IEjHEBihwIULF9q5c+dG2JmWCBAgQIDAfAUEOPP19moECBAYpcCvfvWrdvz48VH2pikCBKYT2Nracp+s6QhdTYAAAQIEJgICHItAgAABAlMLdDcx7m5m7EGAAIG3CnS/PnQhjgcBAgQIECAwnYAAZzo/VxMgQIBAa5PwxjdRWQUCBK4nsLy83Pb29uAQIECAAAECUwoIcKYEdDkBAgQIvCHgPjg2gQCB6wn4CnF7QYAAAQIEhhEQ4Azj6BQCBAiUFxDglF8BAAT+R2BnZ6etrKyQIUCAAAECBAYQEOAMgOgIAgQIEGjt4sWLbXV1FQUBAgTeFHADY8tAgAABAgSGExDgDGfpJAIECJQWcB+c0uPXPIHrCqyvr7fz58/TIUCAAAECBAYQEOAMgOgIAgQIEGjt5MmTbXd3FwUBAgTeFHD/G8tAgAABAgSGExDgDGfpJAIECJQXcB+c8isAgMCbAt2vB0tLS0QIECBAgACBgQQEOANBOoYAAQIEWnvhhRfaU089hYIAAQLN/W8sAQECBAgQGFZAgDOsp9MIECBQWmBtba1tb2+XNtA8AQJvCGxubk5CHA8CBAgQIEBgGAEBzjCOTiFAgACB1tqdd97Z/va3v7EgQIBAW15ebnt7eyQIECBAgACBgQQEOANBOoYAAQIE3hB47bXX2n333YeDAIHCAvv7++3o0aOFBbROgAABAgSGFxDgDG/qRAIECJQW+PSnP91++tOfljbQPIHqAj4+VX0D9E+AAAECsxAQ4MxC1ZkECBAoLtD96buvDy6+BNovLXDq1Km2u7tb2kDzBAgQIEBgaAEBztCiziNAgACByc1LNzY2SBAgUFDA14cXHLqWCRAgQGAuAgKcuTB7EQIECNQSePTRR9vPf/7zWk3rlgCBiYCvD7cIBAgQIEBgNgICnNm4OpUAAQLlBbo/hfcgQKCewPr6ejt//ny9xnVMgAABAgRmLCDAmTGw4wkQIFBV4IUXXmhPPfVU1fb1TaCsgPtflR29xgkQIEBgxgICnBkDO54AAQJVBc6cOdNeeumlqu3rm0BJgZ2dnbayslKyd00TIECAAIFZCwhwZi3sfAIECBQW+M1vftPuvffewgJaJ1BLwLdP1Zq3bgkQIEBgvgICnPl6ezUCBAiUEvAunFLj1mxxAe++Kb4A2idAgACBmQsIcGZO7AUIECBQW+CVV15pJ06cqI2gewIFBNy8uMCQtUiAAAECt1VAgHNb+b04AQIExi+wvLzcuj+Z9yBAYLwC3n0z3tnqjAABAgRyBAQ4ObNQCQECBEYr8O1vf7s9/fTTo+1PYwQqCxwcHLSlpaXKBHonQIAAAQJzERDgzIXZixAgQIDAlStX/CbPGhAYoYAbF49wqFoiQIAAgUgBAU7kWBRFgACB8Qn4KNX4ZqojAt1XhvuIpD0gQIAAAQLzERDgzMfZqxAgQIBAa+2hhx5qv/zlL1kQIDACAeHNCIaoBQIECBBYKAEBzkKNS7EECBBYfIEuwOmCHA8CBBZXwMemFnd2KidAgACBxRUQ4Czu7FROgACBhRW4ePFiW11dXdj6FU6gqkD3canNzc22u7tblUDfBAgQIEDgtgkIcG4bvRcmQIBAbQEhTu35637xBLrgZmtra/EKVzEBAgQIEBiJgABnJIPUBgECBBZR4Pnnn29f//rXF7F0NRMoI3D58uVJcONdN2VGrlECBAgQCBUQ4IQORlkECBCoIuDGxlUmrc9FE+hCm0uXLrW9vb1FK129BAgQIEBglAICnFGOVVMECBBYLIGTJ0+2b37zm+3EiROLVbhqCYxMoAttXnnlldZ9xNGDAAECBAgQyBIQ4GTNQzUECBAoLbCxsTG5QaoHAQKzF9jf32/nz59vBwcH7m0ze26vQIAAAQIEphYQ4ExN6AACBAgQGFKgezdO9y6A7muKPQgQGFbg17/+dXvppZda921S7mkzrK3TCBAgQIDArAUEOLMWdj4BAgQI9BI4c+bM5N04x48f73W9iwhUFujeVdOFNN39a67+vcCm8kbonQABAgTGICDAGcMU9UCAAIERC5w9e7atr6+3+++/f8Rdao3A4QW6QGZ7e3tyQff33cPXex/ezzMJECBAgMCiCghwFnVy6iZAgEAxgXe/+93ty1/+cnvkkUfaxz/+8WLda7eCQBfGdD9+8pOftD/84Q/typUrk3++NrCp4KBHAgQIECBA4PoCAhybQYAAAQILKXA10OmKP3Kk+8+ZB4EcgavvjHlrRW99p8x73vOe9s9//rP9+c9/zileJQQIECBAgECkgAAnciyKIkCAAAECBAgQIECAAAECBAj8R0CAYxsIECBAgAABAgQIECBAgAABAuECApzwASmPAAECBAgQIECAAAECBAgQICDAsQMECBAgQIAAAQIECBAgQIAAgXABAU74gJRHgAABAgQIECBAgAABAgQIEBDg2AECBAgQIECAAAECBAgQIECAQLiAACd8QMojQIAAAQIECBAgQIAAAQIECAhw7AABAgQIECBAgAABAgQIECBAIFxAgBM+IOURIECAAAECBAgQIECAAAECBAQ4doAAAQIECBAgQIAAAQIECBAgEC4gwAkfkPIIECBAgAABAgQIECBAgAABAgIcO0CAAAECBAgQIECAAAECBAgQCBcQ4IQPSHkECBAgQIAAAQIECBAgQIAAAQGOHSBAgAABAgQIECBAgAABAgQIhAsIcMIHpDwCBAgQIECAAAECBAgQIECAgADHDhAgQIAAAQIECBAgQIAAAQIEwgUEOOEDUh4BAgQIECBAgAABAgQIECBAQIBjBwgQIECAAAECBAgQIECAAAEC4QICnPABKY8AAQIECBAgQIAAAQIECBAgIMCxAwQIECBAgAABAgQIECBAgACBcAEBTviAlEeAAAECBAgQIECAAAECBAgQEODYAQIECBAgQIAAAQIECBAgQIBAuIAAJ3xAyiNAgAABAgQIECBAgAABAgQICHDsAAECBAgQIECAAAECBAgQIEAgXECAEz4g5REgQIAAAQIECBAgQIAAAQIEBDh2gAABAgQIECBAgAABAgQIECAQLiDACR+Q8ggQIECAAAECBAgQIECAAAECAhw7QIAAAQIECBAgQIAAAQIECBAIFxDghA9IeQQIECBAgAABAgQIECBAgAABAY4dIECAAAECBAgQIECAAAECBAiECwhwwgekPAIECBAgQIAAAQIECBAgQICAAMcOECBAgAABAgQIECBAgAABAgTCBQQ44QNSHgECBAgQIECAAAECBAgQIEBAgGMHCBAgQIAAAQIECBAgQIAAAQLhAgKc8AEpjwABAgQIECBAgAABAgQIECAgwLEDBAgQIECAAAECBAgQIECAAIFwAQFO+ICUR4AAAQIECBAgQIAAAQIECBAQ4NgBAgQIECBAgAABAgQIECBAgEC4gAAnfEDKI0CAAAECBAgQIECAAAECBAgIcOwAAQIECBAgQIAAAQIECBAgQCBcQIATPiDlESBAgAABAgQIECBAgAABAgQEOHaAAAECBAgQIECAAAECBAgQIBAuIMAJH5DyCBAgQIAAAQIECBAgQIAAAQICHDtAgAABAgQIECBAgAABAgQIEAgXEOCED0h5BAgQIECAAAECBAgQIECAAAEBjh0gQIAAAQIECBAgQIAAAQIECIQLCHDCB6Q8AgQIECBAgAABAgQIECBAgIAAxw4QIECAAAECBAgQIECAAAECBMIFBDjhA1IeAQIECBAgQIAAAQIECBAgQECAYwcIECBAgAABAgQIECBAgAABAuECApzwASmPAAECBAgQIECAAAECBAgQICDAsQMECBAgQIAAAQIECBAgQIAAgXABAU74gJRHgAABAgQIECBAgAABAgQIEBDg2AECBAgQIECAAAECBAgQIECAQLiAACd8QMojQIAAAQIECBAgQIAAAQIECAhw7AABAgQIECBAgAABAgQIECBAIFxAgBM+IOURIECAAAECBAgQIECAAAECBAQ4doAAAQIECBAgQIAAAQIECBAgEC4gwAkfkPIIECBAgAABAgQIECBAgAABAgIcO0CAAAECBAgQIECAAAECBAgQCBcQ4IQPSHkECBAgQIAAAQIECBAgQIAAAQGOHSBAgAABAgQIECBAgAABAgQIhAsIcMIHpDwCBAgQIECAAAECBAgQIECAgADHDhAgQIAAAQIECBAgQIAAAQIEwgUEOOEDUh4BAgQIECBAgAABAgQIECBAQIBjBwgQIECAAAECBAgQIECAAAEC4QICnPABKY8AAQIECBAgQIAAAQIECBAgIMCxAwQIECBAgAABAgQIECBAgACBcAEBTviAlEeAAAECBAgQIECAAAECBAgQEODYAQIECBAgQIAAAQIECBAgQIBAuIAAJ3xAyiNAgAABAgQIECBAgAABAgQICHDsAAECBAgQIECAAAECBAgQIEAgXECAEz4g5REgQIAAAQIECBAgQIAAAQIEBDh2gAABAgQIECBAgAABAgQIECAQLiDACR+Q8ggQIECAAAECBAgQIECAAAECAhw7QIAAAQIECBAgQIAAAQIECBAIFxDghA9IeQQIECBAgAABAgQIECBAgAABAY4dIECAAAECBAgQIECAAAECBAiECwhwwgekPAIECBAgQIAAAQIECBAgQICAAMcOECBAgAABAgQIECBAgAABAgTCBQQ44QNSHgECBAgQIECAAAECBAgQIEBAgGMHCBAgQIAAAQIECBAgQIAAAQLhAgKc8AEpjwABAgQIECBAgAABAgQIECAgwLEDBAgQIECAAAECBAgQIECAAIFwAQFO+ICUR4AAAQIECBAgQIAAAQIECBAQ4NgBAgQIECBAgAABAgQIECBAgEC4gAAnfEDKI0CAAAECBAgQIECAAAECBAgIcOwAAQIECBAgQIAAAQIECBAgQCBcQIATPiDlESBAgAABAgQIECBAgAABAgQEOHaAAAECBAgQIECAAAECBAgQIBAuIMAJH5DyCBAgQIAAAQIECBAgQIAAAQICHDtAgAABAgQIECBAgAABAgQIEAgXEOCED0h5BAgQIECAAAECBAgQIECAAAEBjh0gQIAAAQIECBAgQIAAAQIECIQLCHDCB6Q8AgQIECBAgAABAgQIECBAgIAAxw4QIECAAAECBAgQIECAAAECBMIFBDjhA1IeAQIECBAgQIAAAQIECBAgQECAYwcIECBAgAABAgQIECBAgAABAuECApzwASmPAAECBAgQIECAAAECBAgQICDAsQMECBAgQIAAAQIECBAgQIAAgXABAU74gJRHgAABAgQIECBAgAABAgQIEBDg2AECBAgQIECAAAECBAgQIECAQLiAACd8QMojQIAAAQIECBAgQIAAAQIECAhw7AABAgQIECBAgAABAgQIECBAIFxAgBM+IOURIECAAAECBAgQIECAAAECBAQ4doAAAQIECBAgQIAAAQIECBAgEC4gwAkfkPIIECBAgAABAgQIECBAgAABAgIcO0CAAAECBAgQIECAAAECBAgQCBcQ4IQPSHkECBAgQIAAAQIECBAgQIAAAQGOHSBAgAABAgQIECBAgAABAgQIhAsIcMIHpDwCBAgQIECAAAECBAgQIECAgADHDhAgQIAAAQIECBAgQIAAAQIEwgUEOOEDUh4BAgQIECBAgAABAgQIECBAQIBjBwgQIECAAAECBAgQIECAAAEC4QICnPABKY8AAQIECBAgQIAAAQIECBAgIMCxAwQIECBAgAABAgQIECBAgACBcAEBTviAlEeAAAECBAgQIECAAAECBAgQEODYAQIECBAgQIAAAQIECBAgQIBAuIAAJ3xAyiNAgAABAgQIECBAgAABAgQICHDsAAECBAgQIECAAAECBAgQIEAgXECAEz4g5REgQIAAAQIECBAgQIAAAQIEBDh2gAABAgQIECBAgAABAgQIECAQLiDACR+Q8ggQIECAAAECBAgQIECAAAECAhw7QIAAAQIECBAgQIAAAQIECBAIFxDghA9IeQQIECBAgAABAgQIECBAgAABAY4dIECAAAECBAgQIECAAAECBAiECwhwwgekPAIECBAgQIAAAQIECBAgQICAAMcOECBAgAABAgQIECBAgAABAgTCBQQ44QNSHgECBAgQIECAAAECBAgQIEBAgGMHCBAgQIAAAQIECBAgQIAAAQLhAgKc8AEpjwABAgQIECBAgAABAgQIECAgwLEDBAgQIECAAAECBAgQIECAAIFwAQFO+ICUR4AAAQIECBAgQIAAAQIECBAQ4NgBAgQIECBAgAABAgQIECBAgEC4gAAnfEDKI0CAAAECBAgQIECAAAECBAgIcOwAAQIECBAgQIAAAQIECBAgQCBcQIATPiDlESBAgAABAgQIECBAgAABAgQEOHaAAAECBAgQIECAAAECBAgQIBAuIMAJH5DyCBAgQIAAAQIECBAgQIAAAQICHDtAgAABAgQIECBAgAABAgQIEAgXEOCED0h5BAgQIECAAAECBAgQIECAAAEBjh0gQIAAAQIECBAgQIAAAQIECIQLCHDCB6Q8AgQIECBAgAABAgQIECBAgIAAxw4QIECAAAECBAgQIECAAAECBMIFBDjhA1IeAQIECBAgQIAAAQIECBAgQECAYwcIECBAgAABAgQIECBAgAABAuECApzwASmPAAECBAgQIECAAAECBAgQICDAsQMECBAgQIAAAQIECBAgQIAAgXABAU74gJRHgAABAgQIECBAgAABAgQIEBDg2AECBAgQIECAAAECBAgQIECAQLiAACd8QMojQIAAAQIECBAgQIAAAQIECAhw7AABAgQIECBAgAABAgQIECBAIFxAgBM+IOURIECAAAECBAgQIED6lhiEAAAQsklEQVSAAAECBAQ4doAAAQIECBAgQIAAAQIECBAgEC4gwAkfkPIIECBAgAABAgQIECBAgAABAgIcO0CAAAECBAgQIECAAAECBAgQCBcQ4IQPSHkECBAgQIAAAQIECBAgQIAAAQGOHSBAgAABAgQIECBAgAABAgQIhAsIcMIHpDwCBAgQIECAAAECBAgQIECAgADHDhAgQIAAAQIECBAgQIAAAQIEwgUEOOEDUh4BAgQIECBAgAABAgQIECBAQIBjBwgQIECAAAECBAgQIECAAAEC4QICnPABKY8AAQIECBAgQIAAAQIECBAgIMCxAwQIECBAgAABAgQIECBAgACBcAEBTviAlEeAAAECBAgQIECAAAECBAgQEODYAQIECBAgQIAAAQIECBAgQIBAuIAAJ3xAyiNAgAABAgQIECBAgAABAgQICHDsAAECBAgQIECAAAECBAgQIEAgXECAEz4g5REgQIAAAQIECBAgQIAAAQIEBDh2gAABAgQIECBAgAABAgQIECAQLiDACR+Q8ggQIECAAAECBAgQIECAAAECAhw7QIAAAQIECBAgQIAAAQIECBAIFxDghA9IeQQIECBAgAABAgQIECBAgAABAY4dIECAAAECBAgQIECAAAECBAiECwhwwgekPAIECBAgQIAAAQIECBAgQICAAMcOECBAgAABAgQIECBAgAABAgTCBQQ44QNSHgECBAgQIECAAAECBAgQIEBAgGMHCBAgQIAAAQIECBAgQIAAAQLhAgKc8AEpjwABAgQIECBAgAABAgQIECAgwLEDBAgQIECAAAECBAgQIECAAIFwAQFO+ICUR4AAAQIECBAgQIAAAQIECBAQ4NgBAgQIECBAgAABAgQIECBAgEC4gAAnfEDKI0CAAAECBAgQIECAAAECBAgIcOwAAQIECBAgQIAAAQIECBAgQCBcQIATPiDlESBAgAABAgQIECBAgAABAgQEOHaAAAECBAgQIECAAAECBAgQIBAuIMAJH5DyCBAgQIAAAQIECBAgQIAAAQICHDtAgAABAgQIECBAgAABAgQIEAgXEOCED0h5BAgQIECAAAECBAgQIECAAAEBjh0gQIAAAQIECBAgQIAAAQIECIQLCHDCB6Q8AgQIECBAgAABAgQIECBAgIAAxw4QIECAAAECBAgQIECAAAECBMIFBDjhA1IeAQIECBAgQIAAAQIECBAgQECAYwcIECBAgAABAgQIECBAgAABAuECApzwASmPAAECBAgQIECAAAECBAgQICDAsQMECBAgQIAAAQIECBAgQIAAgXABAU74gJRHgAABAgQIECBAgAABAgQIEBDg2AECBAgQIECAAAECBAgQIECAQLiAACd8QMojQIAAAQIECBAgQIAAAQIECAhw7AABAgQIECBAgAABAgQIECBAIFxAgBM+IOURIECAAAECBAgQIECAAAECBAQ4doAAAQIECBAgQIAAAQIECBAgEC4gwAkfkPIIECBAgAABAgQIECBAgAABAgIcO0CAAAECBAgQIECAAAECBAgQCBcQ4IQPSHkECBAgQIAAAQIECBAgQIAAAQGOHSBAgAABAgQIECBAgAABAgQIhAsIcMIHpDwCBAgQIECAAAECBAgQIECAgADHDhAgQIAAAQIECBAgQIAAAQIEwgUEOOEDUh4BAgQIECBAgAABAgQIECBAQIBjBwgQIECAAAECBAgQIECAAAEC4QICnPABKY8AAQIECBAgQIAAAQIECBAgIMCxAwQIECBAgAABAgQIECBAgACBcAEBTviAlEeAAAECBAgQIECAAAECBAgQEODYAQIECBAgQIAAAQIECBAgQIBAuIAAJ3xAyiNAgAABAgQIECBAgAABAgQICHDsAAECBAgQIECAAAECBAgQIEAgXECAEz4g5REgQIAAAQIECBAgQIAAAQIEBDh2gAABAgQIECBAgAABAgQIECAQLiDACR+Q8ggQIECAAAECBAgQIECAAAECAhw7QIAAAQIECBAgQIAAAQIECBAIFxDghA9IeQQIECBAgAABAgQIECBAgAABAY4dIECAAAECBAgQIECAAAECBAiECwhwwgekPAIECBAgQIAAAQIECBAgQICAAMcOECBAgAABAgQIECBAgAABAgTCBQQ44QNSHgECBAgQIECAAAECBAgQIEBAgGMHCBAgQIAAAQIECBAgQIAAAQLhAgKc8AEpjwABAgQIECBAgAABAgQIECAgwLEDBAgQIECAAAECBAgQIECAAIFwAQFO+ICUR4AAAQIECBAgQIAAAQIECBAQ4NgBAgQIECBAgAABAgQIECBAgEC4gAAnfEDKI0CAAAECBAgQIECAAAECBAgIcOwAAQIECBAgQIAAAQIECBAgQCBcQIATPiDlESBAgAABAgQIECBAgAABAgQEOHaAAAECBAgQIECAAAECBAgQIBAuIMAJH5DyCBAgQIAAAQIECBAgQIAAAQICHDtAgAABAgQIECBAgAABAgQIEAgXEOCED0h5BAgQIECAAAECBAgQIECAAAEBjh0gQIAAAQIECBAgQIAAAQIECIQLCHDCB6Q8AgQIECBAgAABAgQIECBAgIAAxw4QIECAAAECBAgQIECAAAECBMIFBDjhA1IeAQIECBAgQIAAAQIECBAgQECAYwcIECBAgAABAgQIECBAgAABAuECApzwASmPAAECBAgQIECAAAECBAgQICDAsQMECBAgQIAAAQIECBAgQIAAgXABAU74gJRHgAABAgQIECBAgAABAgQIEBDg2AECBAgQIECAAAECBAgQIECAQLiAACd8QMojQIAAAQIECBAgQIAAAQIECAhw7AABAgQIECBAgAABAgQIECBAIFxAgBM+IOURIECAAAECBAgQIECAAAECBAQ4doAAAQIECBAgQIAAAQIECBAgEC4gwAkfkPIIECBAgAABAgQIECBAgAABAgIcO0CAAAECBAgQIECAAAECBAgQCBcQ4IQPSHkECBAgQIAAAQIECBAgQIAAAQGOHSBAgAABAgQIECBAgAABAgQIhAsIcMIHpDwCBAgQIECAAAECBAgQIECAgADHDhAgQIAAAQIECBAgQIAAAQIEwgUEOOEDUh4BAgQIECBAgAABAgQIECBAQIBjBwgQIECAAAECBAgQIECAAAEC4QICnPABKY8AAQIECBAgQIAAAQIECBAgIMCxAwQIECBAgAABAgQIECBAgACBcAEBTviAlEeAAAECBAgQIECAAAECBAgQEODYAQIECBAgQIAAAQIECBAgQIBAuIAAJ3xAyiNAgAABAgQIECBAgAABAgQICHDsAAECBAgQIECAAAECBAgQIEAgXECAEz4g5REgQIAAAQIECBAgQIAAAQIEBDh2gAABAgQIECBAgAABAgQIECAQLiDACR+Q8ggQIECAAAECBAgQIECAAAECAhw7QIAAAQIECBAgQIAAAQIECBAIFxDghA9IeQQIECBAgAABAgQIECBAgAABAY4dIECAAAECBAgQIECAAAECBAiECwhwwgekPAIECBAgQIAAAQIECBAgQICAAMcOECBAgAABAgQIECBAgAABAgTCBQQ44QNSHgECBAgQIECAAAECBAgQIEBAgGMHCBAgQIAAAQIECBAgQIAAAQLhAgKc8AEpjwABAgQIECBAgAABAgQIECAgwLEDBAgQIECAAAECBAgQIECAAIFwAQFO+ICUR4AAAQIECBAgQIAAAQIECBAQ4NgBAgQIECBAgAABAgQIECBAgEC4gAAnfEDKI0CAAAECBAgQIECAAAECBAgIcOwAAQIECBAgQIAAAQIECBAgQCBcQIATPiDlESBAgAABAgQIECBAgAABAgQEOHaAAAECBAgQIECAAAECBAgQIBAuIMAJH5DyCBAgQIAAAQIECBAgQIAAAQICHDtAgAABAgQIECBAgAABAgQIEAgXEOCED0h5BAgQIECAAAECBAgQIECAAAEBjh0gQIAAAQIECBAgQIAAAQIECIQLCHDCB6Q8AgQIECBAgAABAgQIECBAgIAAxw4QIECAAAECBAgQIECAAAECBMIFBDjhA1IeAQIECBAgQIAAAQIECBAgQECAYwcIECBAgAABAgQIECBAgAABAuECApzwASmPAAECBAgQIECAAAECBAgQICDAsQMECBAgQIAAAQIECBAgQIAAgXABAU74gJRHgAABAgQIECBAgAABAgQIEBDg2AECBAgQIECAAAECBAgQIECAQLiAACd8QMojQIAAAQIECBAgQIAAAQIECAhw7AABAgQIECBAgAABAgQIECBAIFxAgBM+IOURIECAAAECBAgQIECAAAECBAQ4doAAAQIECBAgQIAAAQIECBAgEC4gwAkfkPIIECBAgAABAgQIECBAgAABAgIcO0CAAAECBAgQIECAAAECBAgQCBcQ4IQPSHkECBAgQIAAAQIECBAgQIAAAQGOHSBAgAABAgQIECBAgAABAgQIhAsIcMIHpDwCBAgQIECAAAECBAgQIECAgADHDhAgQIAAAQIECBAgQIAAAQIEwgUEOOEDUh4BAgQIECBAgAABAgQIECBAQIBjBwgQIECAAAECBAgQIECAAAEC4QICnPABKY8AAQIECBAgQIAAAQIECBAgIMCxAwQIECBAgAABAgQIECBAgACBcAEBTviAlEeAAAECBAgQIECAAAECBAgQEODYAQIECBAgQIAAAQIECBAgQIBAuIAAJ3xAyiNAgAABAgQIECBAgAABAgQICHDsAAECBAgQIECAAAECBAgQIEAgXECAEz4g5REgQIAAAQIECBAgQIAAAQIEBDh2gAABAgQIECBAgAABAgQIECAQLiDACR+Q8ggQIECAAAECBAgQIECAAAECAhw7QIAAAQIECBAgQIAAAQIECBAIFxDghA9IeQQIECBAgAABAgQIECBAgAABAY4dIECAAAECBAgQIECAAAECBAiECwhwwgekPAIECBAgQIAAAQIECBAgQICAAMcOECBAgAABAgQIECBAgAABAgTCBQQ44QNSHgECBAgQIECAAAECBAgQIEBAgGMHCBAgQIAAAQIECBAgQIAAAQLhAgKc8AEpjwABAgQIECBAgAABAgQIECAgwLEDBAgQIECAAAECBAgQIECAAIFwAQFO+ICUR4AAAQIECBAgQIAAAQIECBAQ4NgBAgQIECBAgAABAgQIECBAgEC4gAAnfEDKI0CAAAECBAgQIECAAAECBAgIcOwAAQIECBAgQIAAAQIECBAgQCBcQIATPiDlESBAgAABAgQIECBAgAABAgQEOHaAAAECBAgQIECAAAECBAgQIBAuIMAJH5DyCBAgQIAAAQIECBAgQIAAAQICHDtAgAABAgQIECBAgAABAgQIEAgXEOCED0h5BAgQIECAAAECBAgQIECAAAEBjh0gQIAAAQIECBAgQIAAAQIECIQLCHDCB6Q8AgQIECBAgAABAgQIECBAgIAAxw4QIECAAAECBAgQIECAAAECBMIFBDjhA1IeAQIECBAgQIAAAQIECBAgQECAYwcIECBAgAABAgQIECBAgAABAuECApzwASmPAAECBAgQIECAAAECBAgQICDAsQMECBAgQIAAAQIECBAgQIAAgXABAU74gJRHgAABAgQIECBAgAABAgQIEBDg2AECBAgQIECAAAECBAgQIECAQLiAACd8QMojQIAAAQIECBAgQIAAAQIECPwLOVmhiAyBfSsAAAAASUVORK5CYII="`

	simple := SimpleImg2ImgRequest{
		InitImages:     img2Img.InitImages,
		Prompt:         img2Img.Prompt,
		NegativePrompt: img2Img.NegativePrompt,
		Mask:           img2Img.Mask,
	}

	uuid := tools.GetUuid()
	input := service.SdInput{
		CustomApp:  "sdapitest",
		CustomData: "",
		CustomPath: "img2img/" + uuid + ".png",
		Sdapi:      enums.SdapiEnum.Img2img,
		Parameters: tools.GetJsonFromStruct(simple),
		TraceId:    tools.GetUuid(),
	}
	size := service.SdService.Push(input)
	if size == 0 {
		logger.Error("Txt2img推送失败")
		errmsg.Abort(c, errmsg.FAIL, "数据推送失败")
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code": code,
		"msg":  fmt.Sprintf("绘图中，当前排在第%d位", size),
	})

	//url := "http://117.187.188.4:7887/sdapi/v1/txt2img"
	////m := make(map[string]interface{}, 0)
	////m["prompt"] = "puppy dog"
	////m["negative_prompt"] = "wrong hands"
	////m["steps"] = 20
	////oReq.Payload = tools.GetJsonFromMap(m)
	//if s, err := Post(url, oReq.Payload); err != nil {
	//	logger.Error(err)
	//} else {
	//	logger.Info(s)
	//}
}

func GetImgFromUrl(url string) (string, error) {
	resp, err := http.Get(url)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	defer resp.Body.Close()

	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error(err)
		return "", err
	}

	// 转换为 base64
	str := base64.StdEncoding.EncodeToString(data)

	return str, nil
}

func ScanImg2img(o *Img2ImgRequest) {
	if o.Steps == 0 {
		o.Steps = 50
	}
	if o.DenoisingStrength == 0 {
		o.DenoisingStrength = 0.75
	}
	if o.MaskBlurX == 0 && o.MaskBlurY == 0 {
		o.MaskBlurX = 4
		o.MaskBlurY = 4
	}
	if o.Width == 0 && o.Height == 0 {
		o.Width = 512
		o.Height = 512
	}
	if o.Styles == nil {
		o.Styles = make([]string, 0)
	}
	if o.ScriptArgs == nil {
		o.ScriptArgs = make([]interface{}, 0)
	}

	if o.SamplerIndex == "" {
		o.SamplerIndex = "Euler"
	}

	o.SendImages = true
}
