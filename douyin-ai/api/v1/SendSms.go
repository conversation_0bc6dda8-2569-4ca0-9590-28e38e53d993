package v1

import (
	"center-ai/enums"
	"center-ai/middleware"
	"center-ai/model"
	"center-ai/service"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/myredis"
	"center-ai/utils/tools"
	"errors"
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
	"time"
)

type sendSmsReq struct {
	Token     string `json:"token"`
	Mobile    string `json:"mobile"`
	Mould     int    `json:"mould"`
	CodeId    string `json:"code_id"`
	CodeValue string `json:"code_value"`
}

func SendSms(c *gin.Context) {
	var code int
	var msg string

	var smsReq sendSmsReq

	er := c.ShouldBindJSON(&smsReq)
	if er != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		logger.Error("数据获取失败")
		errmsg.Abort(c, code, msg)
		return
	}

	if strings.Contains(smsReq.Mobile, "**") {
		tokenHeader := c.Request.Header.Get("Authorization")
		claims, err := middleware.GetClaimsByToken(tokenHeader)
		if claims == nil || err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		}
		var user model.User
		if err := user.GetByID(claims.UserId); err != nil {
			logger.Error("获取用户信息失败", claims.UserId)
			errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
			return
		}
		if user.Mobile == "" {
			logger.Error("未绑定手机号码，请先绑定手机号码")
			errmsg.Abort(c, errmsg.FAIL, "未绑定手机号码，请先绑定手机号码")
			return
		}
		smsReq.Mobile = user.Mobile
	}

	if !tools.IsMobile(smsReq.Mobile) {
		logger.Error("smsReq.Mobile：", smsReq.Mobile, "  token：", smsReq.Token)
		errmsg.Abort(c, errmsg.FAIL, "手机号码不正确")
		return
	}

	if len(smsReq.CodeId) > 0 {
		if len(smsReq.CodeValue) < 5 {
			logger.Error(errors.New("验证码不正确"), smsReq.CodeValue)
			errmsg.Abort(c, errmsg.FAIL, "验证码不正确")
			return
		}
	}

	//if !verifycode.VerifyCaptcha(smsReq.CodeId, smsReq.CodeValue) {
	//	errmsg.Abort(c, errmsg.FAIL, "验证码不正确")
	//	return
	//}

	smsCode := tools.CreateCaptcha(4)

	redisKey := getRedisKey(smsReq.Mould, smsReq.Mobile)
	if len(redisKey) == 0 {
		logger.Error(errors.New("参数不正确"))
		errmsg.Abort(c, errmsg.FAIL, "参数不正确")
		return
	}

	if err := myredis.Set(redisKey, smsCode, time.Minute*10); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "短信验证码生成失败")
		return
	}

	if err := myredis.Set(redisKey+":testcount", "0", time.Minute*10); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "短信验证码生成失败")
		return
	}

	go func() {
		if err := service.AliSms.SendSmsCode(smsReq.Mould, smsReq.Mobile, smsCode); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "短信验证码发送失败")
			return
		}
	}()

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  "验证码已发送",
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func getRedisKey(mould int, mobile string) string {
	if mould == enums.SmsMouldEnum.Reg {
		return enums.RedisKeyEnum.SmsReg + mobile
	} else if mould == enums.SmsMouldEnum.Login {
		return enums.RedisKeyEnum.SmsLogin + mobile
	} else if mould == enums.SmsMouldEnum.ModifyPassword {
		return enums.RedisKeyEnum.SmsModifyPassword + mobile
	} else {
		return ""
	}
}
