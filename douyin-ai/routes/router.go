package routes

import (
	"center-ai/api/manage"
	"center-ai/api/sd"
	v1 "center-ai/api/v1"
	"center-ai/middleware"
	"center-ai/utils/config"
	"center-ai/utils/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

func InitRouter() {
	r := gin.New()
	r.Use(middleware.Cors())
	r.StaticFile("www/index", "./www/index.html")
	r.StaticFile("www/draw", "./www/draw.html")
	//r.StaticFile("/index", "./www/index.htm")
	//r.Static("/", "/Users/<USER>/Downloads/h522")
	//r.Static("/", "/Users/<USER>/Desktop/zcloud/aigc-front/HomeDecoration/unpackage/dist/build/h5")

	routerSd := r.Group("sdapi")
	{
		routerSd.POST("txt2img", sd.Txt2img)
		routerSd.POST("img2img", sd.Img2img)
	}

	router := r.Group("api/v1")
	{
		router.GET("hello/:action", func(c *gin.Context) {

			//http://localhost:5002/api/v1/hello/img.png
			action := c.Param("action")
			if action == "img1.png" {
				c.Header("Content-Type", "application/octet-stream")
				c.Header("Content-Disposition", "attachment; filename=xiaz11.png")
				c.Header("Content-Transfer-Encoding", "binary")
				c.File("/Users/<USER>/Downloads/8e88f96fdcc92553e62e6f2415a23e42.png")
				return
			}
			if action == "img2.png" {
				c.Header("Content-Type", "application/octet-stream") //
				//c.Header("Content-Disposition", "attachment; filename=xiaz11.png")
				//c.Header("Content-Transfer-Encoding", "binary")
				c.File("/Users/<USER>/Downloads/15481496764171.png")
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"msg":   "ok",
				"value": action,
				"host":  c.Request.Host,
				"path":  c.Request.URL.Path,
			})
		})

		router.POST("getsmscode", v1.SendSms)
		router.POST("user/login", v1.Login)
		router.POST("user/loginsms", v1.LoginSms)

	}

	routerAuth := r.Group("api/v1")
	routerAuth.Use(middleware.JwtToken())
	{
		//routerAuth.POST("sys/restart_project", v1.SysApi.RestartProject)
		//routerAuth.POST("sys/publish_project", v1.SysApi.PublishProject)
		//routerAuth.POST("sys/publish_program", v1.SysApi.PublishProgram)
		//routerAuth.POST("sys/trans_upload", v1.SysApi.TransUpload)
		//routerAuth.POST("sys/trans_redis", v1.SysApi.TransRedis)
		//routerAuth.POST("sys/loar_redis", v1.SysApi.GetLoarRedis)
		//routerAuth.POST("sys/set_insider_user", v1.SysApi.SetInsiderUser)
		//routerAuth.POST("sys/reset_site_conf", v1.SysApi.ReSetSiteConf)
		//
		//routerAuth.POST("user/get_info", v1.UserApi.GetUserInfo)
		//routerAuth.POST("user/get_invite_qrcode", v1.UserApi.GetInviteQrCode)
		//
		//routerAuth.POST("user/change_password", v1.UserApi.ChangePassword)
		//routerAuth.POST("user/set_insider_state", v1.UserApi.SetInsiderUserState)

	}

	//noneAuthManage := r.Group("api/manage")
	//{
	//	noneAuthManage.POST("login", manage.Login)
	//	noneAuthManage.GET("get_image_by_path", manage.GetImgByPath)
	//}

	routerAuthManage := r.Group("api/manage")
	routerAuthManage.Use(middleware.JwtTokenCenter())
	{

		routerAuthManage.POST("room/add", manage.RoomApi.Add)
		routerAuthManage.POST("room/list", manage.RoomApi.List)
		routerAuthManage.POST("room/search", manage.RoomApi.Search)
		routerAuthManage.POST("message/list", manage.MessageApi.List)
		routerAuthManage.POST("statis/gen", manage.StatisticsApi.Gen)
		routerAuthManage.POST("gift/update", manage.GiftApi.Update)
		routerAuthManage.POST("gift/list", manage.GiftApi.List)
	}
	logger.Info("开启端口监听...", config.HttpPort)
	if err := r.Run(config.HttpPort); err != nil {
		logger.Error("端口监听失败", err)
	} else {
		logger.Info("端口监听中,port:", config.HttpPort)
	}
}
