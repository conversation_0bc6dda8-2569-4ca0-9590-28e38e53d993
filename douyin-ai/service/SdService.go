package service

import (
	"bytes"
	"center-ai/enums"
	"center-ai/utils/config"
	"center-ai/utils/logger"
	"center-ai/utils/myimg"
	"center-ai/utils/myredis"
	"center-ai/utils/tools"
	"errors"
	"io/ioutil"
	"net/http"
	"os"
	"path/filepath"
	"time"
	"unsafe"
)

type sd_ struct {
}

var SdService sd_

const (
	SdServer = "http://117.187.188.4:7887/"
)

type SdInput struct {
	CustomApp  string                 `json:"custom_app"`
	CustomData string                 `json:"custom_data"`
	CustomPath string                 `json:"custom_path"`
	Sdapi      string                 `json:"sdapi"`
	Parameters string                 `json:"parameters"`
	TraceId    string                 `json:"trace_id"`
	CreateAt   int64                  `json:"create_at"`
	Options    map[string]interface{} `json:"options"`
}

type SdOutput struct {
	CustomApp    string `json:"custom_app"`
	CustomData   string `json:"custom_data"`
	CustomMd5    string `json:"custom_md5"`
	CustomPath   string `json:"custom_path"`
	Sdapi        string `json:"sdapi"`
	Result       string `json:"result"`
	TraceId      string `json:"trace_id"`
	CreateAt     int64  `json:"create_at"`
	ExecuteAt    int64  `json:"execute_at"`
	ExecuteTimes int64  `json:"execute_times"`
}

func (d *sd_) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("SdService奔溃:", e)
		}
	}()
	logger.Info("SdService.Run 开始循环获取图片")
	for {
		value, err := d.Pop()

		if err != nil {
			logger.Error(value, err)
			continue
		}
		if value == "" {
			continue
		}
		logger.Info("接收到绘图json:", value)
		if err := d.Handle(value); err != nil {
			logger.Error(err)
		}
	}
}

func (d *sd_) Handle(value string) error {
	var sdInput SdInput
	if err := tools.GetStructFromJson(&sdInput, value); err != nil {
		logger.Error(err)
		return err
	}
	switch sdInput.Sdapi {
	case enums.SdapiEnum.Txt2img:
		logger.Info("txt2img")
		if err := d.Txt2img(sdInput); err != nil {
			logger.Error(err)
		}
	case enums.SdapiEnum.Img2img:
		logger.Info("img2img")
		if err := d.Img2img(sdInput); err != nil {
			logger.Error(err)
		}
	}
	return nil
}

func (d *sd_) Pop() (string, error) {
	value, err := myredis.BRPop(enums.AigcRedisKeyEnum.SdApiPush)
	return value, err
}

func (d *sd_) Push(input SdInput) int64 {
	input.CreateAt = time.Now().UnixMilli()
	json := tools.GetJsonFromStruct(input)
	size, err := myredis.LPush(enums.AigcRedisKeyEnum.SdApiPush, json)
	if err != nil {
		logger.Error(size, err)
		return size
	}
	return size
}

func (d *sd_) PushOut(input SdOutput) int64 {
	json := tools.GetJsonFromStruct(input)
	size, err := myredis.LPush(enums.AigcRedisKeyEnum.SdApiPush+":"+input.CustomApp, json)
	if err != nil {
		logger.Error(size, err)
		return size
	}
	return size
}

func (d *sd_) PushJson(json string) (int64, error) {
	size, err := myredis.LPush(enums.AigcRedisKeyEnum.SdApiPush, json)
	if err != nil {
		logger.Error(err)
		return size, err
	}
	return size, err
}

func (d *sd_) Txt2img(sdInput SdInput) error {
	url := SdServer + "sdapi/v1/txt2img"
	s := ""
	executeAt := time.Now().UnixMilli()
	if ss, err := Post(url, sdInput.Parameters); err != nil {
		logger.Error(err)
		return err
	} else {
		s = ss
	}
	executeTimes := time.Now().UnixMilli() - executeAt

	logger.Info(s)
	relativePath := sdInput.CustomApp + "/" + sdInput.CustomPath
	path := config.DiffusionFilePath + relativePath

	directory := filepath.Dir(path) // 获取目录路径
	// 创建目录，存在则不创建，不存在则创建
	if err := os.MkdirAll(directory, 0755); err != nil {
		logger.Error(err)
		return err
	}

	result := struct {
		ImagePath  string                 `json:"image_path"`
		Images     []string               `json:"images"`
		Parameters map[string]interface{} `json:"parameters"`
		Info       string                 `json:"info"`
	}{}
	if err := tools.GetStructFromJson(&result, s); err != nil {
		logger.Error(err)
		return err
	}
	imagePaths := make([]string, 0)
	for _, val := range result.Images {
		if err := myimg.Base64ToFile(val, path); err != nil {
			logger.Error(err)
			return err
		}
		imagePaths = append(imagePaths, relativePath)
	}
	result.ImagePath = relativePath
	result.Images = imagePaths

	outResult := make(map[string]interface{})
	outResult["out_image_path"] = relativePath

	if sdInput.Options != nil {
		if _, ok := sdInput.Options["need_info"]; ok {
			outResult["info"] = result.Info
		}
		if _, ok := sdInput.Options["need_parameters"]; ok {
			outResult["parameters"] = result.Parameters
		}
	}

	sdOutput := SdOutput{
		CustomApp:    sdInput.CustomApp,
		CustomData:   sdInput.CustomData,
		CustomPath:   sdInput.CustomPath,
		Sdapi:        sdInput.Sdapi,
		Result:       tools.GetJsonFromStruct(outResult),
		TraceId:      sdInput.TraceId,
		CreateAt:     sdInput.CreateAt,
		ExecuteAt:    executeAt,
		ExecuteTimes: executeTimes,
	}
	if size := d.PushOut(sdOutput); size == 0 {
		logger.Error("放入返回队列失败", sdInput.TraceId)
		return errors.New("放入返回队列失败")
	}
	return nil
}

func (d *sd_) Img2img(sdInput SdInput) error {
	url := SdServer + "sdapi/v1/img2img"
	s := ""
	executeAt := time.Now().UnixMilli()
	if ss, err := Post(url, sdInput.Parameters); err != nil {
		logger.Error(err)
		return err
	} else {
		s = ss
	}
	executeTimes := time.Now().UnixMilli() - executeAt

	logger.Info(s)
	relativePath := sdInput.CustomApp + "/" + sdInput.CustomPath
	path := config.DiffusionFilePath + relativePath

	directory := filepath.Dir(path) // 获取目录路径
	// 创建目录，存在则不创建，不存在则创建
	if err := os.MkdirAll(directory, 0755); err != nil {
		logger.Error(err)
		return err
	}

	result := struct {
		ImagePath  string                 `json:"image_path"`
		Images     []string               `json:"images"`
		Parameters map[string]interface{} `json:"parameters"`
		Info       string                 `json:"info"`
	}{}
	if err := tools.GetStructFromJson(&result, s); err != nil {
		logger.Error(err)
		return err
	}
	imagePaths := make([]string, 0)
	for _, val := range result.Images {
		if err := myimg.Base64ToFile(val, path); err != nil {
			logger.Error(err)
			return err
		}
		imagePaths = append(imagePaths, relativePath)
	}
	result.ImagePath = relativePath
	result.Images = imagePaths

	outResult := make(map[string]interface{})
	outResult["out_image_path"] = relativePath

	if sdInput.Options != nil {
		if _, ok := sdInput.Options["need_info"]; ok {
			outResult["info"] = result.Info
		}
		if _, ok := sdInput.Options["need_parameters"]; ok {
			outResult["parameters"] = result.Parameters
		}
	}

	sdOutput := SdOutput{
		CustomApp:    sdInput.CustomApp,
		CustomData:   sdInput.CustomData,
		CustomPath:   sdInput.CustomPath,
		Sdapi:        sdInput.Sdapi,
		Result:       tools.GetJsonFromStruct(outResult),
		TraceId:      sdInput.TraceId,
		CreateAt:     sdInput.CreateAt,
		ExecuteAt:    executeAt,
		ExecuteTimes: executeTimes,
	}
	if size := d.PushOut(sdOutput); size == 0 {
		logger.Error("放入返回队列失败", sdInput.TraceId)
		return errors.New("放入返回队列失败")
	}
	return nil
}

func Post(url string, payload string) (string, error) {
	authorization := "Basic eXhqOnl4ajEyMw=="
	req, err := http.NewRequest("POST", url, bytes.NewBuffer([]byte(payload)))
	if err != nil {
		logger.Error("NewRequest error:", err)
		return "", err
	}
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	req.Header.Set("Authorization", authorization)

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Error("Post Fatal error:", err)
		return "", err
	}

	defer res.Body.Close()

	content, err := ioutil.ReadAll(res.Body)
	if err != nil {
		logger.Error("Fatal error ", err)
		return "", err
	}

	str := (*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	return *str, nil
}
