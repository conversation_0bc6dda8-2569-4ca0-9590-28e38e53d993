package main

import (
	"fmt"
	"github.com/gorilla/websocket"
	"log"
)

// TestWebSocket
// @Description: 测试WebSocket脚本
// @param t
func main20() {

	url := "wss://webcast5-ws-web-lf.douyin.com/webcast/im/push/v2/?app_name=douyin_web&version_code=180800&webcast_sdk_version=1.0.8&update_version_code=1.0.8&compress=gzip&device_platform=web&cookie_enabled=true&screen_width=1792&screen_height=1120&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0%20(Macintosh;%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/*********%20Safari/537.36&browser_online=true&tz_name=Asia/Shanghai&cursor=r-1_d-1_u-1_h-1_t-1693297154806&internal_ext=internal_src:dim|wss_push_room_id:7272653376834276131|wss_push_did:7262176106497050112|dim_log_id:20230829161914F79202C3B1A684040BA5|first_req_ms:1693297154735|fetch_time:1693297154806|seq:1|wss_info:0-1693297154806-0-0|wrds_kvs:WebcastRoomStatsMessage-1693297150895261672_WebcastRoomStreamAdaptationMessage-1693297152457151976_LotteryInfoSyncData-1693297135462819026_WebcastRoomRankMessage-1693297144992754183&host=https://live.douyin.com&aid=6383&live_id=1&did_rule=3&endpoint=live_pc&support_wrds=1&user_unique_id=&im_path=/webcast/im/fetch/&identity=audience&room_id=7272653376834276131&heartbeatDuration=0&signature=RgyCTiUgP8h8Nt+6"
	c, res, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		log.Fatal("连接失败:", err)
	}
	log.Printf("响应:%s", fmt.Sprint(res))
	defer c.Close()
	done := make(chan struct{})
	err = c.WriteMessage(websocket.TextMessage, []byte("你好,我是FunTester"))
	if err != nil {
		fmt.Println(err)
	}
	go func() {
		defer close(done)
		for {
			_, message, err := c.ReadMessage()
			if err != nil {
				log.Fatal(err)
				break
			}
			log.Printf("收到消息: %s", message)

		}
	}()
	s := <-done
	fmt.Println(s)

}
