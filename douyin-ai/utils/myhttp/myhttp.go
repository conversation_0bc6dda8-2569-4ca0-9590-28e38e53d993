package myhttp

import (
	"bytes"
	"center-ai/utils/logger"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path"
	"unsafe"
)

func Txt2Img() {
	url := "https://c2f9ce50b6bbb639.gradio.app/sdapi/v1/txt2img"
	song := make(map[string]interface{})
	song["prompt"] = "puppy dog"
	song["steps"] = "5"
	song["batch_size"] = "3"
	Post(url, song)
}

func GetByte(rawUrl string, params url.Values) ([]byte, error) {
	/*params := url.Values{}
	params.Add("csdn", "@杰哥的技术杂货铺")
	params.Add("contactWay", "+jemooer")*/

	parseURL, err := url.Parse(rawUrl)
	if err != nil {
		log.Println("url 不正确")
		return nil, err
	}
	parseURL.RawQuery = params.Encode()
	urlPathWithParams := parseURL.String()
	res, err := http.Get(urlPathWithParams)
	if err != nil {
		log.Println("Post Fatal error:", err)
		return nil, err
	}
	defer res.Body.Close()

	content, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println("Fatal error ", err.Error())
		return nil, err
	}
	return content, err
}

func Get(rawUrl string, params url.Values) (string, error) {
	/*params := url.Values{}
	params.Add("csdn", "@杰哥的技术杂货铺")
	params.Add("contactWay", "+jemooer")*/

	parseURL, err := url.Parse(rawUrl)
	if err != nil {
		log.Println("err")
	}
	parseURL.RawQuery = params.Encode()
	urlPathWithParams := parseURL.String()
	res, err := http.Get(urlPathWithParams)
	if err != nil {
		log.Println("Post Fatal error:", err)
		return "", err
	}
	defer res.Body.Close()

	content, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println("Fatal error ", err.Error())
		return "", err
	}
	str := (*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	return *str, nil
}

func WechatDetectImg(url string, file string) bool {
	var bufReader bytes.Buffer
	//	"mime/multipart" 可以将上传文件封装

	mpWriter := multipart.NewWriter(&bufReader)
	//文件名无所谓
	//fileName := "detect"
	//字段名必须为media
	writer, err := mpWriter.CreateFormFile("media", path.Base(file))
	if err != nil {
		fmt.Println(err.Error())
		return false
	}

	bts, err := ioutil.ReadFile(file)
	if err != nil {
		fmt.Println(err)
	}

	reader := bytes.NewReader(bts)
	io.Copy(writer, reader)
	//关闭了才能把内容写入
	mpWriter.Close()

	client := http.DefaultClient
	destURL := url
	req, _ := http.NewRequest("POST", destURL, &bufReader)
	//从mpWriter中获取content-Type
	req.Header.Set("Content-Type", mpWriter.FormDataContentType())
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println(err.Error())
	}
	defer resp.Body.Close()
	vs := make(map[string]interface{})
	result, _ := ioutil.ReadAll(resp.Body)
	if err != nil {
		return false
	}
	err = json.Unmarshal(result, &vs)
	if err != nil {
		return false
	}
	//errcode 存在，且为0，返回通过
	if _, ok := vs["errcode"]; ok && vs["errcode"].(float64) == 0.0 {
		return true
	}

	return false

}

func UploadFile(url string, filePath string, fields map[string]string, headers map[string]string) (string, error) {
	// 打开要上传的文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 创建 multipart 请求体
	body := new(bytes.Buffer)
	writer := multipart.NewWriter(body)

	// 添加字段到请求体
	for key, value := range fields {
		_ = writer.WriteField(key, value)
	}

	// 添加文件到请求体
	part, err := writer.CreateFormFile("file", file.Name())
	if err != nil {
		return "", err
	}
	_, err = io.Copy(part, file)
	if err != nil {
		return "", err
	}

	// 结束请求体并设置 Content-Type
	err = writer.Close()
	if err != nil {
		return "", err
	}

	// 发送请求
	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return "", err
	}
	// 添加 Header 参数
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	result, err := ioutil.ReadAll(resp.Body)
	return string(result), err
}

func PostFile(url string, fieldName string, file string) ([]byte, error) {
	var bufReader bytes.Buffer
	//	"mime/multipart" 可以将上传文件封装

	mpWriter := multipart.NewWriter(&bufReader)
	//文件名无所谓
	//fileName := "detect"
	//字段名必须为media
	writer, err := mpWriter.CreateFormFile(fieldName, path.Base(file)) //media
	if err != nil {
		logger.Error(err)
		return nil, err
	}

	bts, err := ioutil.ReadFile(file)
	if err != nil {
		logger.Error(err)
		return nil, err
	}

	reader := bytes.NewReader(bts)
	io.Copy(writer, reader)
	//关闭了才能把内容写入
	mpWriter.Close()

	client := http.DefaultClient
	req, _ := http.NewRequest("POST", url, &bufReader)
	//从mpWriter中获取content-Type
	req.Header.Set("Content-Type", mpWriter.FormDataContentType())
	resp, err := client.Do(req)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	defer resp.Body.Close()
	result, err := ioutil.ReadAll(resp.Body)
	return result, err
}

func PostByte(url string, m map[string]interface{}) ([]byte, error) {

	bytesData, _ := json.Marshal(m)

	res, err := http.Post(url, "application/json;charset=utf-8", bytes.NewBuffer([]byte(bytesData)))
	if err != nil {
		log.Println("Post Fatal error:", err)
		return nil, err
	}

	defer res.Body.Close()

	content, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println("Fatal error ", err.Error())
		return nil, err
	}
	return content, nil
}

func Post(url string, m map[string]interface{}) (string, error) {

	bytesData, err := json.Marshal(m)
	if err != nil {
		logger.Error("json.Marshal(m) error:", err)
		return "", err
	}

	res, err2 := http.Post(url, "application/json;charset=utf-8", bytes.NewBuffer([]byte(bytesData)))
	if err2 != nil {
		logger.Error("Post Fatal error:", err2)
		return "", err2
	}

	defer res.Body.Close()

	content, err1 := ioutil.ReadAll(res.Body)
	if err1 != nil {
		logger.Error("Fatal error ", err1)
		return "", err1
	}

	//fmt.Println(string(content))
	str := (*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	//fmt.Println(*str)
	return *str, nil
}

func Post11() {
	song := make(map[string]string)
	song["mobile"] = "13067943278"
	song["username"] = "vben"
	song["password"] = "123456"
	bytesData, _ := json.Marshal(song)

	res, err := http.Post("http://ue5.zcloudai.cn:2580/api/reg",
		"application/json;charset=utf-8", bytes.NewBuffer([]byte(bytesData)))
	if err != nil {
		fmt.Println("Fatal error ", err.Error())
	}

	defer res.Body.Close()

	content, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println("Fatal error ", err.Error())
	}

	//fmt.Println(string(content))
	str := (*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	fmt.Println(*str)
}
