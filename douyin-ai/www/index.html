<!DOCTYPE html>
<html>
<body>

<h1>WebSocket Client Demo</h1>

<button type="button" onclick="startWebSocket()">Start WebSocket</button>
<button type="button" onclick="stopWebSocket()">Stop WebSocket</button>
<button type="button" onclick="sendWebSocket()">Send Message</button>
<button type="button" onclick="startDouyinWebSocket()">Start DouyinWebSocket3</button>


<p id="output"></p>

<script>
    var ws;
    var ii=0;
    function startWebSocket() {
        var url = "ws://localhost:8080/ws"; // 你的WebSocket服务器地址
        ws = new WebSocket(url);

        ws.onopen = function() {
            document.getElementById("output").innerHTML += "WebSocket connection opened.<br>";
        };

        ws.onmessage = function(evt) {
            document.getElementById("output").innerHTML += "Received message: " + evt.data + "<br>";
        };

        ws.onclose = function() {
            document.getElementById("output").innerHTML += "WebSocket connection closed.<br>";
        };
    }

    var wss;
    function startDouyinWebSocket() {
        var url = "wss://webcast5-ws-web-lq.douyin.com/webcast/im/push/v2/?app_name=douyin_web&version_code=180800&webcast_sdk_version=1.0.8&update_version_code=1.0.8&compress=gzip&device_platform=web&cookie_enabled=true&screen_width=1792&screen_height=1120&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0%20(Macintosh;%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/*********%20Safari/537.36&browser_online=true&tz_name=Asia/Shanghai&cursor=h-1_t-1693363268698_r-1_d-1_u-1&internal_ext=internal_src:dim|wss_push_room_id:7272914310383291194|wss_push_did:7262176106497050112|dim_log_id:2023083010410840B4BB2759FCE1A09706|first_req_ms:1693363268636|fetch_time:1693363268698|seq:1|wss_info:0-1693363268698-0-0|wrds_kvs:WebcastRoomRankMessage-1693363264391294568_WebcastRoomStatsMessage-1693363264329503641_WebcastRoomStreamAdaptationMessage-1693363265652717741&host=https://live.douyin.com&aid=6383&live_id=1&did_rule=3&endpoint=live_pc&support_wrds=1&user_unique_id=&im_path=/webcast/im/fetch/&identity=audience&room_id=7272914310383291194&heartbeatDuration=0&signature=R/IseWQVX+7uv3CD"; // 你的WebSocket服务器地址
        wss = new WebSocket(url);
        wss.binaryType = "arraybuffer";

        wss.onopen = function() {
            document.getElementById("output").innerHTML += "WebSocket connection opened.<br>";
        };

        wss.onmessage = function(evt) {
            console.log(evt.data)

            let hexString = buf2hex(evt.data);

            console.log("hexString：",hexString);

            //document.getElementById("output").innerHTML += "Received message: " + evt.data + "<br>";
            ws.send(hexString)
        };

        wss.onclose = function() {
            document.getElementById("output").innerHTML += "WebSocket connection closed.<br>";
        };
    }

    function stopWebSocket() {
        if(ws) {
            ws.close();
        }
    }

    function sendWebSocket() {
        if(ws) {
            ws.send("Hello, server!"+ii);
            ii++
        }
    }

    function bytesToHex(bytes) {
        return Array.from(bytes).map(function(byte) {
            return ('0' + (byte & 0xFF).toString(16)).slice(-2);
        }).join('');
    }

    function buf2hex(buffer) { // buffer is an ArrayBuffer
        return Array.prototype.map.call(new Uint8Array(buffer), x => ('00' + x.toString(16)).slice(-2)).join('');
    }
</script>

</body>
</html>
