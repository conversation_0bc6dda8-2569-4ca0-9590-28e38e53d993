# 项目名称
PROJECT_NAME := node-server

# 可执行文件输出路径
OUTPUT_DIR := ./target
SERVER_OUTPUT_BINARY := $(OUTPUT_DIR)/$(PROJECT_NAME)
SQUASH_OUTPUT_BINARY := $(OUTPUT_DIR)/squash-image
CGO=0
#TARGET_OS=$(shell uname -s | tr '[:upper:]' '[:lower:]')
#TARGET_ARCH=amd64
FLAGS=" -s -w "


# 自动检测操作系统
ifeq ($(OS),Windows_NT)
    TARGET_OS := windows
else
    UNAME_S := $(shell uname -s)
    ifeq ($(UNAME_S),Linux)
        TARGET_OS := linux
    else ifeq ($(UNAME_S),Darwin)
        TARGET_OS := darwin
    else
        $(error Unsupported operating system: $(UNAME_S))
    endif
endif

# 自动检测架构
ifeq ($(OS),Windows_NT)
    ifeq ($(PROCESSOR_ARCHITECTURE),AMD64)
        TARGET_ARCH := amd64
    else ifeq ($(PROCESSOR_ARCHITECTURE),ARM64)
        TARGET_ARCH := arm64
    else
        $(error Unsupported architecture on Windows: $(PROCESSOR_ARCHITECTURE))
    endif
else
    UNAME_M := $(shell uname -m)
    ifeq ($(UNAME_M),x86_64)
        TARGET_ARCH := amd64
    else ifeq ($(UNAME_M),aarch64)
        TARGET_ARCH := arm64
    else
        $(error Unsupported architecture: $(UNAME_M))
    endif
endif



# 构建squash工具
squash:
	@mkdir -p $(OUTPUT_DIR)
	CGO_ENABLED=$(CGO) GOOS=$(TARGET_OS) GOARCH=$(TARGET_ARCH) go build -ldflags $(FLAGS) -o $(SQUASH_OUTPUT_BINARY) ./cmd/squash-image/squash-image.go

# 构建server
server:
	@mkdir -p $(OUTPUT_DIR)
	@echo "TARGET_OS: $(TARGET_OS), TARGET_ARCH: $(TARGET_ARCH)"
	go mod tidy
	CGO_ENABLED=$(CGO) GOOS=$(TARGET_OS) GOARCH=$(TARGET_ARCH) go build -ldflags $(FLAGS) -o $(SERVER_OUTPUT_BINARY) ./cmd/node-server.go



# 清理生成的文件
clean:
	rm -rf $(OUTPUT_DIR)

all: clean squash server
# 定义默认目标
.DEFAULT_GOAL := server
