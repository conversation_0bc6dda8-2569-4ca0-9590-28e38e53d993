package main

import (
	"node-server/internal/common"
	"node-server/internal/common/logger"
	"node-server/internal/config"
	"node-server/internal/enums"
	"node-server/internal/middleware"
	"node-server/internal/router"
	"node-server/internal/service"
	"node-server/internal/service/relay"
	"os"
	"runtime"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
)

func main() {
	if config.Env == enums.EnvEnum.ONLINE {

		defer func() {
			if e := recover(); e != nil {
				logger.Error("奔溃:", e)
				stack := make([]byte, 4096)
				runtime.Stack(stack, false)
				logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			}
		}()

	}
	logger.Info("启动main")
	logger.Info("Node Server " + common.Version + " started")
	if os.Getenv("GIN_MODE") != "debug" {
		gin.SetMode(gin.ReleaseMode)
	}
	if common.DebugEnabled {
		logger.Info("running in debug mode")
	}
	// Initialize SQL Database
	//if err := model.InitDB(); err != nil {
	//	common.FatalLog("failed to initialize database: " + err.Error())
	//}
	//defer func() {
	//	err := model.CloseDB()
	//	if err != nil {
	//		common.FatalLog("failed to close database: " + err.Error())
	//	}
	//}()

	// Initialize Redis
	if err := common.InitRedisClient(); err != nil {
		logger.Error("failed to initialize Redis: " + err.Error())
	}

	err := relay.InitRelayRedisClient()
	if err != nil {
		logger.Error("Failed to initialize relay Redis client: %v\n", err)
		return
	}
	// Make sure to close the client when done
	defer relay.CloseRelayRedisClient()
	//relay.InitTokenEncoders()

	// Initialize options
	//model.InitOptionMap()

	//if common.RedisEnabled {
	//	// for compatibility with old versions
	//	common.MemoryCacheEnabled = true
	//}
	//if common.MemoryCacheEnabled {
	//	common.SysLog("memory cache enabled")
	//	common.SysError(fmt.Sprintf("sync frequency: %d seconds", common.SyncFrequency))
	//	model.InitChannelCache()
	//}
	//if common.MemoryCacheEnabled {
	//	go model.SyncOptions(common.SyncFrequency)
	//	go model.SyncChannelCache(common.SyncFrequency)
	//}

	//if len(config.DiffusionFilePath) < 10 {
	//	logger.Error("配置参数异常 common.Config.DiffusionFilePath：", config.DiffusionFilePath)
	//	return
	//}

	if len(config.MasterServer) < 10 {
		logger.Error("配置参数异常 common.Config.ReportServer：", config.MasterServer)
		return
	}

	//if err := service.CheckPrivateStorage(); err != nil {
	//	logger.Error("路径检测失败", err)
	//	return
	//}

	if service.NodeService.Virtuals == nil {
		if err := service.NodeService.Init(); err != nil {
			logger.Error("初始化调度信息失败", err)
			return
		}
	}
	service.RunTimer()

	//if os.Getenv("CHANNEL_UPDATE_FREQUENCY") != "" {
	//	frequency, err := strconv.Atoi(os.Getenv("CHANNEL_UPDATE_FREQUENCY"))
	//	if err != nil {
	//		common.FatalLog("failed to parse CHANNEL_UPDATE_FREQUENCY: " + err.Error())
	//	}
	//	go controller.AutomaticallyUpdateChannels(frequency)
	//}
	//if os.Getenv("CHANNEL_TEST_FREQUENCY") != "" {
	//	frequency, err := strconv.Atoi(os.Getenv("CHANNEL_TEST_FREQUENCY"))
	//	if err != nil {
	//		common.FatalLog("failed to parse CHANNEL_TEST_FREQUENCY: " + err.Error())
	//	}
	//	go controller.AutomaticallyTestChannels(frequency)
	//}
	//if os.Getenv("BATCH_UPDATE_ENABLED") == "true" {
	//	common.BatchUpdateEnabled = true
	//	common.SysLog("batch update enabled with interval " + strconv.Itoa(common.BatchUpdateInterval) + "s")
	//	model.InitBatchUpdater()
	//}
	//controller.InitTokenEncoders()

	// Initialize HTTP server
	server := gin.New()
	server.Use(gin.Recovery())
	// This will cause SSE not to work!!!
	//server.Use(gzip.Gzip(gzip.DefaultCompression))
	server.Use(middleware.RequestId())
	middleware.SetUpLogger(server)
	// Initialize session store
	store := cookie.NewStore([]byte(common.SessionSecret))
	server.Use(sessions.Sessions("session", store))

	router.SetApiRouter(server)

	logger.Info("开启端口监听..." + config.HttpPort)
	logger.Info("开启端口监听...", config.HttpPort)
	if err := server.Run(config.HttpPort); err != nil {
		logger.Error("failed to start HTTP server: " + err.Error())
	}
}
