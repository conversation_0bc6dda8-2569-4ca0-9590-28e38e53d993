package common

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"node-server/internal/common/logger"
	"node-server/internal/common/utils"
	"node-server/internal/config"
	"os"
	"time"
)

var RDB *redis.Client
var RedisEnabled = true

// InitRedisClient This function is called after init()
func InitRedisClient() (err error) {
	REDIS_CONN_STRING := os.Getenv("REDIS_CONN_STRING")
	if REDIS_CONN_STRING == "" {
		REDIS_CONN_STRING = config.REDIS_CONN_STRING
	}
	if REDIS_CONN_STRING == "" {
		RedisEnabled = false
		logger.Error("REDIS_CONN_STRING not set, Redis is not enabled")
		return errors.New("REDIS_CONN_STRING not set, Redis is not enabled")
	}
	//if os.Getenv("SYNC_FREQUENCY") == "" {
	//	RedisEnabled = false
	//	SysLog("SYNC_FREQUENCY not set, Redis is disabled")
	//	return nil
	//}
	logger.Info("Redis is enabled")
	opt, err := redis.ParseURL(REDIS_CONN_STRING)
	if err != nil {
		logger.Error("failed to parse Redis connection string: " + err.Error())
		return errors.New("failed to parse Redis connection string: " + err.Error())
	}
	RDB = redis.NewClient(opt)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err = RDB.Ping(ctx).Result()
	if err != nil {
		msg := fmt.Sprintf("Redis ping test failed: " + err.Error())
		logger.Error(msg)
		return errors.New(msg)
	}
	return err
}

func ParseRedisOption() *redis.Options {
	opt, err := redis.ParseURL(os.Getenv("REDIS_CONN_STRING"))
	if err != nil {
		msg := fmt.Sprintf("failed to parse Redis connection string: " + err.Error())
		logger.Error(msg)
	}
	return opt
}

func RedisIncr(key string) (int64, error) {
	ctx := context.Background()
	return RDB.Incr(ctx, key).Result()
}

func RedisSet(key string, value string, expiration time.Duration) error {
	ctx := context.Background()
	return RDB.Set(ctx, key, value, expiration).Err()
}

func RedisGet(key string) (string, error) {
	ctx := context.Background()
	return RDB.Get(ctx, key).Result()
}

func RedisDel(key string) error {
	ctx := context.Background()
	return RDB.Del(ctx, key).Err()
}

func RedisExists(key string) bool {
	ctx := context.Background()
	intCmd := RDB.Exists(ctx, key)
	if intCmd.Val() > 0 {
		return true
	}
	return false
}
func RedisScanKeys(pattern string) ([]string, error) {
	ctx := context.Background()
	var keys []string
	var cursor uint64 = 0
	for {
		var (
			scanKeys []string
			err      error
		)
		scanKeys, cursor, err = RDB.Scan(ctx, cursor, pattern, 10).Result()
		if err != nil {
			return nil, err
		}
		keys = append(keys, scanKeys...)
		if cursor == 0 {
			break
		}
	}
	return keys, nil
}

func RedisDecrease(key string, value int64) error {
	ctx := context.Background()
	return RDB.DecrBy(ctx, key, value).Err()
}

func RedisLSet(key string, index int64, values interface{}) error {
	ctx := context.Background()

	err := RDB.LSet(ctx, key, index, values).Err()
	if err == redis.Nil {
		return nil
	}
	return err
}

func RedisLRem(key string, index int64, values interface{}) error {
	ctx := context.Background()

	err := RDB.LRem(ctx, key, index, values).Err()
	if err == redis.Nil {
		return nil
	}
	return err
}

func RedisLPush(key string, values ...interface{}) (int64, error) {
	ctx := context.Background()

	result, err := RDB.LPush(ctx, key, values...).Result()
	if err == redis.Nil {
		return result, nil
	}
	return result, err
}

func RedisRPush(key string, values ...interface{}) (int64, error) {
	ctx := context.Background()

	result, err := RDB.RPush(ctx, key, values...).Result()
	if err == redis.Nil {
		return result, nil
	}
	return result, err
}

func RedisLLen(key string) (int64, error) {
	ctx := context.Background()
	length, err := RDB.LLen(ctx, key).Result()
	if err != nil {
		return 0, err
	}
	return length, nil
}

func RedisLIndex(key string, index int64) (string, error) { //// 读取列表中的数据 按索引
	ctx := context.Background()
	result, err := RDB.LIndex(ctx, key, index).Result()
	if err != nil {
		return "", err
	}
	return result, nil
}

func RedisLRange(key string, start, stop int64) ([]string, error) { //// 读取列表中的数据 RedisLRange("mylist", 0, -1)
	ctx := context.Background()

	result, err := RDB.LRange(ctx, key, start, stop).Result()
	if err != nil {
		return nil, err
	}
	return result, nil
}

func RedisZIncrBy(key string, member string, increment float64) (float64, error) {
	ctx := context.Background()
	newScore, err := RDB.ZIncrBy(ctx, key, increment, member).Result()
	if err == redis.Nil {
		return newScore, nil
	}
	return newScore, err
}

func RedisZAdd(key string, field string, score float64) (int64, error) { //如果成员已存在，则更新其分数。
	ctx := context.Background()
	result, err := RDB.ZAdd(ctx, key, &redis.Z{Score: score, Member: field}).Result()
	if err == redis.Nil {
		return result, nil
	}
	return result, err
}
func RedisZAddNX(key string, field string, score float64) (int64, error) { //只有当成员不存在时才执行添加操作，如果成员已存在，则不进行任何操作。
	ctx := context.Background()
	result, err := RDB.ZAddNX(ctx, key, &redis.Z{Score: score, Member: field}).Result()
	if err == redis.Nil {
		return result, nil
	}
	return result, err
}
func RedisZRem(key string, member string) (int64, error) {
	ctx := context.Background()
	result, err := RDB.ZRem(ctx, key, member).Result()
	if err == redis.Nil {
		// 成员不存在，也可以视情况处理
		return result, nil
	}
	return result, err
}

func RedisZScore(key string, field string) (float64, error) {
	ctx := context.Background()
	result, err := RDB.ZScore(ctx, key, field).Result()
	if err == redis.Nil {
		return result, nil
	}
	return result, err
}

func RedisZRangeWithScores(key string, count int64) redis.ZSliceCmd {
	ctx := context.Background()
	membersCmd := RDB.ZRangeWithScores(ctx, key, 0, count)
	return *membersCmd
}

func RedisZRangeByScore(key string, score int, count int64) []string {
	ctx := context.Background()
	minStr := utils.Int2String(score)

	membersCmd := RDB.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min:   minStr,
		Max:   "+inf",
		Count: count,
	})
	return membersCmd.Val()
}

func RedisZRangeByScoreWithScores(key string, score int, count int64) []redis.Z {
	ctx := context.Background()
	minStr := utils.Int2String(score)

	membersCmd := RDB.ZRangeByScoreWithScores(ctx, key, &redis.ZRangeBy{
		Min:   minStr,
		Max:   "+inf",
		Count: count,
	})
	return membersCmd.Val()
}

func RedisHSetIntField(key string, field uint, value interface{}) (int64, error) {
	ctx := context.Background()
	result, err := RDB.HSet(ctx, key, field, value).Result()
	if err == redis.Nil {
		return result, nil
	}
	return result, err
}

func RedisHGetIntField(key string, field uint) (string, error) {
	ctx := context.Background()
	value, err := RDB.HGet(ctx, key, fmt.Sprintf("%d", field)).Result()
	if err == redis.Nil {
		return value, nil
	}
	return value, err
}

func RedisHSet(key string, field string, value string) (int64, error) {
	ctx := context.Background()
	result, err := RDB.HSet(ctx, key, field, value).Result()
	if err == redis.Nil {
		return result, nil
	}
	return result, err
}

func RedisHGet(key string, field string) (string, error) {
	ctx := context.Background()
	value, err := RDB.HGet(ctx, key, field).Result()
	if err == redis.Nil {
		return value, nil
	}
	return value, err
}

func RedisHExists(key string, field string) (bool, error) {
	ctx := context.Background()
	exists, err := RDB.HExists(ctx, key, field).Result()
	return exists, err
}

func RedisHGetAll(key string) (map[string]string, error) {
	ctx := context.Background()
	result, err := RDB.HGetAll(ctx, key).Result()
	if err == redis.Nil {
		// Key不存在时返回空map和nil
		return make(map[string]string), nil
	}
	return result, err
}

func RedisHDel(key string, field string) (int64, error) {
	ctx := context.Background()
	value, err := RDB.HDel(ctx, key, field).Result()
	if err == redis.Nil {
		return value, nil
	}
	return value, err
}

func RedisLock(lockKey string, timeoutMillis int64, lockTTLMillis int64) bool {
	ctx := context.Background()
	// 获取锁
	if lockTTLMillis == 0 {
		lockTTLMillis = 1000 * 60
	}
	lockTimeout := time.Duration(lockTTLMillis) * time.Millisecond
	for tt := int64(0); tt < timeoutMillis; tt += 100 {
		lock, err := RDB.SetNX(ctx, lockKey, "locked", lockTimeout).Result()
		if err != nil {
			logger.Error(lockKey, " ", err)
		}
		if lock {
			return true
		}

		if tt+100+100 > timeoutMillis {
			isLock, timeD, errLock := RedisIsLocked(lockKey)
			if errLock == nil && isLock == false {
				logger.Info(lockKey, "锁已过期,强制删除")
				nums, er := RDB.Del(ctx, lockKey).Result()
				if er != nil {
					logger.Error(lockKey, " 强制删除锁失败", er, timeD.Milliseconds(), "  ", nums)
				}
			} else {
				logger.Error(errLock, " lockKey:", lockKey)
			}
		}

		time.Sleep(100 * time.Millisecond)
	}

	return false
}

func RedisIsLocked(key string) (bool, time.Duration, error) {
	ctx := context.Background()
	// 获取锁的过期时间
	ttl, err := RDB.TTL(ctx, key).Result()
	if err != nil {
		return false, 0, err
	}

	if ttl > 0 {
		// 锁还未过期，锁被锁住
		return true, ttl, nil
	}

	// 锁已过期，锁未被锁住
	return false, 0, nil
}

// 解锁
func RedisUnLock(key string) (int64, error) {
	ctx := context.Background()
	nums, err := RDB.Del(ctx, key).Result()
	if err != nil {
		logger.Error("解锁失败", key, " ", err)
		return nums, err
	}
	return nums, nil
}
