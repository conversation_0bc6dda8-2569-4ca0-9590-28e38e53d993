package enums

type dockerStateEnum_ struct {
	Unknow, NotExist, Created, Running, Exited string
}

var DockerStatusEnum = dockerStateEnum_{
	Unknow:   "unknow",   //未知
	NotExist: "notexist", //不存在
	Created:  "created",  //已创建
	Running:  "running",  //运行中
	Exited:   "exited",   //已退出
}

func (obj dockerStateEnum_) Name(v string) string {
	if val, ok := DockerStatusName[v]; ok {
		//存在
		return val
	}
	return ""
}

var DockerStatusName = map[string]string{
	"unknow":   "未知",
	"notexist": "不存在",
	"created":  "已创建",
	"running":  "运行中",
	"exited":   "已退出",
}
