package enums

type gpuStatusEnum_ struct {
	Unknow, Used, Free, Locked, Disable int
}

var GpuStatusEnum = gpuStatusEnum_{
	Unknow:  0, //未知
	Used:    1, //使用中
	Free:    2, //空闲中
	Locked:  3, //锁定中
	Disable: 4, //不可用
}

func (obj gpuStatusEnum_) Name(v int) string {
	if val, ok := GpuStatusName[v]; ok {
		//存在
		return val
	}
	return ""
}

var GpuStatusName = map[int]string{
	0: "未知",
	1: "使用中",
	2: "锁定中",
	3: "空闲中",
	4: "不可用",
}
