package enums

type imageStorageModeEnum_ struct {
	Registry, RegistryDisk, PrivateDisk int
}

var ImageStorageModeEnum = imageStorageModeEnum_{
	Registry:     1, //镜像仓库
	RegistryDisk: 2, //镜像磁盘仓库
	PrivateDisk:  3, //个人磁盘存储
}

func (obj imageStorageModeEnum_) Name(v int) string {
	if val, ok := ImageStorageModeName[v]; ok {
		//存在
		return val
	}
	return ""
}

var ImageStorageModeName = map[int]string{
	1: "镜像仓库",
	2: "镜像磁盘仓库",
	3: "个人磁盘存储",
}
