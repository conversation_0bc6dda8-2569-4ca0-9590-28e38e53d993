package enums

type instanceStatusEnum_ struct {
	Created, BootInProgress, Running, ShutdownInProgress, ShutdownComplete, Hidden int
}

var InstanceStatusEnum = instanceStatusEnum_{
	Created:            0, //已创建
	BootInProgress:     1, //开机中
	Running:            2, //运行中
	ShutdownInProgress: 3, //关机中
	ShutdownComplete:   4, //已关机
	Hidden:             9, //隐藏，前端表现为删除
}

func (obj instanceStatusEnum_) Name(v int) string {
	if val, ok := InstanceStatusName[v]; ok {
		//存在
		return val
	}
	return ""
}

var InstanceStatusName = map[int]string{
	0: "已创建",
	1: "开机中",
	2: "运行中",
	3: "关机中",
	4: "已关机",
	9: "删除",
}
