package enums

type queueActionEnum_ struct {
	InitVirtualBatch, InitVirtual, InitVirtualAndReport, CheckInstanceLive, InitVirtualAndCheckLive, LoopAppStart, ReportLocalImages string
}

var QueueActionEnum = queueActionEnum_{
	InitVirtualBatch:        "InitVirtualBatch",
	InitVirtual:             "InitVirtual",
	InitVirtualAndReport:    "InitVirtualAndReport",
	CheckInstanceLive:       "CheckInstanceLive",
	InitVirtualAndCheckLive: "InitVirtualAndCheckLive",
	LoopAppStart:            "LoopAppStart",
	ReportLocalImages:       "ReportLocalImages",
}
