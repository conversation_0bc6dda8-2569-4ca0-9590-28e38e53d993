package enums

type reportStateEnum_ struct {
	Progress, Success, Fail string
}

var ReportStateEnum = reportStateEnum_{
	Progress: "progress", //进行中
	Success:  "success",  //成功
	Fail:     "fail",     //失败
}

func (obj reportStateEnum_) Name(v string) string {
	if val, ok := reportStateName[v]; ok {
		//存在
		return val
	}
	return ""
}

var reportStateName = map[string]string{
	"progress": "进行中",
	"success":  "成功",
	"fail":     "失败",
}
