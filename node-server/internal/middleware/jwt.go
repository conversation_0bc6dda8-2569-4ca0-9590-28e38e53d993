package middleware

import (
	"errors"
	"node-server/internal/common/logger"
	"node-server/internal/common/utils"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
)

type Jwt struct {
	JwtKey []byte
}

type MyClaims struct {
	UserId      uint   `json:"user_id"`
	Mobile      string `json:"mobile"`
	Username    string `json:"username"`
	DisplayName string `json:"display_name"`
	Role        int    `json:"role"`
	Status      int    `json:"status"`
	AccessToken string `json:"access_token"`
	jwt.StandardClaims
}

type MyToken struct {
	UserId  uint `json:"user_id"`
	TokenId uint `json:"token_id"`
	jwt.StandardClaims
}

// 定义错误
var (
	TokenExpired     error  = errors.New("Token已过期,请重新登录")
	TokenNotValidYet error  = errors.New("Token无效,请重新登录")
	TokenMalformed   error  = errors.New("Token不正确,请重新登录")
	TokenInvalid     error  = errors.New("这不是一个token,请重新登录")
	TokenIssuer      string = "Cpn"
)

func (j *Jwt) SetToken(userId uint, userName string, mobile string, displayName string, role int, accessToken string) (string, error) {
	claims := MyClaims{
		UserId:      userId,
		Username:    userName,
		Mobile:      utils.FormatMobileStar(mobile),
		DisplayName: displayName,
		Role:        role,
		AccessToken: accessToken,
		StandardClaims: jwt.StandardClaims{
			NotBefore: time.Now().Unix() - 100,
			ExpiresAt: time.Now().Unix() + 31536000000,
			Issuer:    TokenIssuer,
		},
	}
	token, err := j.CreateToken(claims)
	if err != nil {
		return "", err
	}
	return TokenIssuer + " " + token, nil
}

// CreateToken 生成token
func (j *Jwt) CreateToken(claims MyClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.JwtKey)
}

// ParserToken 解析token
func (j *Jwt) ParserToken(tokenString string) (*MyClaims, error) {
	ary := strings.Split(tokenString, " ")
	if len(ary) >= 2 {
		tokenString = ary[1]
	}
	token, err := jwt.ParseWithClaims(tokenString, &MyClaims{}, func(token *jwt.Token) (interface{}, error) {
		return j.JwtKey, nil
	})

	if err != nil {
		logger.Error(err, "   tokenString:===", tokenString, "===")
		if ve, ok := err.(*jwt.ValidationError); ok {
			if ve.Errors&jwt.ValidationErrorMalformed != 0 {
				return nil, TokenMalformed
			} else if ve.Errors&jwt.ValidationErrorExpired != 0 {
				// Token is expired
				return nil, TokenExpired
			} else if ve.Errors&jwt.ValidationErrorNotValidYet != 0 {
				return nil, TokenNotValidYet
			} else {
				return nil, TokenInvalid
			}
		}
	}

	if token != nil {
		if claims, ok := token.Claims.(*MyClaims); ok && token.Valid {
			return claims, nil
		}
		logger.Error(TokenInvalid, token)
		return nil, TokenInvalid
	}
	logger.Error(TokenInvalid, token)
	return nil, TokenInvalid
}
