package middleware

import (
	"net/http"
	"node-server/internal/common/logger"
	"node-server/internal/config"

	"github.com/gin-gonic/gin"
)

func MasterAuth() func(c *gin.Context) {
	return func(c *gin.Context) {
		masterA<PERSON><PERSON>elper(c)
	}
}

func masterAuthHelper(c *gin.Context) {

	// Check access token
	//logger.Info("未找到session，开始生成session", c.Request.URL.Path)
	authorization := c.Request.Header.Get("Authorization")
	if authorization == "" {
		logger.Error("无权进行此操作，未登录且未提供token", c.Request.URL.Path)
		c.JSON(http.StatusOK, gin.H{
			"code": 2,
			"msg":  "无权进行此操作，未登录且未提供token",
		})
		c.Abort()
		return
	}

	if authorization != config.NodeToken && authorization != "ac4f334sddbfe701dda3783124ef6822a" {
		logger.Error("无权进行此操作，token验证失败", c.Request.URL.Path)
		c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
			"code": 2,
			"msg":  "无权进行此操作",
		})
		c.Abort()
		return
	}
	c.Next()
}
