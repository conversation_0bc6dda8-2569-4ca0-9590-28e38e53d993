package middleware

import (
	"context"
	"node-server/internal/common"
	"node-server/internal/common/utils"

	"github.com/gin-gonic/gin"
)

func RequestId() func(c *gin.Context) {
	return func(c *gin.Context) {
		id := utils.GetTimeString() + utils.GetRandomString(8)
		c.Set(common.RequestIdKey, id)
		ctx := context.WithValue(c.Request.Context(), common.RequestIdKey, id)
		c.Request = c.Request.WithContext(ctx)
		c.<PERSON><PERSON>(common.RequestIdKey, id)
		c.Next()
	}
}
