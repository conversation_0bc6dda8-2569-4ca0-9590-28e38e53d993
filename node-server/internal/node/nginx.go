package node

import (
	"net/http"
	"node-server/internal/common/logger"
	"node-server/internal/common/utils"
	"node-server/internal/service"

	"github.com/gin-gonic/gin"
)

type nginxApi_ struct {
}

var NginxApi nginxApi_

type nginxReq struct {
	Key          string `json:"key"`
	VirtualId    uint   `json:"virtual_id"`
	InstanceUuid string `json:"instance_uuid"`
	PodId        uint   `json:"pod_id"`
}

type removeNginxReq struct {
	VirtualId    uint   `json:"virtual_id"`
	InstanceUuid string `json:"instance_uuid"`
	PodId        uint   `json:"pod_id"`
}

func (obj nginxApi_) RemoveByInstance(c *gin.Context) {
	logger.Info("remove nginx by instance ...")
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.J<PERSON>(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq removeNginxReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if len(oReq.InstanceUuid) != 32 {
		msg = "参数错误"
		logger.Error(msg, "instanceUuid:", oReq.InstanceUuid)
		return
	}

	if str, removeKeys, errKeys, err := service.NginxRemoveInstance(oReq.VirtualId, oReq.PodId, oReq.InstanceUuid); err != nil {
		msg = "移除映射失败"
		logger.Error(msg, " ", err, " oReq:", utils.GetJsonFromStruct(oReq), "  str:", str)
		result["remove_keys"] = removeKeys
		result["err_keys"] = errKeys
		return
	} else {
		msg = str
		result["remove_keys"] = removeKeys
		result["err_keys"] = errKeys
		code = 0
	}
}

func (obj nginxApi_) List(c *gin.Context) {
	logger.Info("list nginx api ...")
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	if str, err := service.NginxList(); err != nil {
		msg = "映射列表失败"
		logger.Error(err, msg, "  str:", str)
		result["err"] = err.Error()
		return
	} else {
		result["list"] = str
		code = 0
	}
}

func (obj nginxApi_) Get(c *gin.Context) {
	logger.Info("get nginx ...")
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq nginxReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.Key == "" {
		msg = "参数为空"
		logger.Error(msg, "Key:", oReq.Key)
		return
	}

	if str, err := service.NginxGetByKey(oReq.Key); err != nil {
		msg = "映射列表失败"
		logger.Error(err, msg, "  str:", str, " key:", oReq.Key)
		result["err"] = err.Error()
		return
	} else {
		result["list"] = str
		code = 0
	}
}
