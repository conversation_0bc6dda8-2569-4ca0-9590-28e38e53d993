package node

import (
	"net/http"
	"node-server/internal/common/logger"
	"node-server/internal/config"
	"node-server/internal/service"
	"sync/atomic"

	"github.com/gin-gonic/gin"
)

type nodeApi_ struct {
}

var NodeApi nodeApi_

type actionReq struct {
	Action string `json:"action"`
	Key    string `json:"key"`
}

func (obj nodeApi_) Static(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	if totalVirtual, totalInstance, totalGpus, freeGpus, err := service.NodeService.Static(); err != nil {
		logger.Error(err)
		msg = "获取信息失败"
		return
	} else {
		result["node_id"] = config.NodeId
		result["total_virtual"] = totalVirtual
		result["total_instance"] = totalInstance
		result["total_gpus"] = totalGpus
		result["free_gpus"] = freeGpus
		code = 0
	}
}

func (obj nodeApi_) Detail(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	//result["cancel_funcs"] = DockerApi.CancelFuncs
	result["virtual_init_queue"] = service.VirtualInitQueue
	result["virtual_init_queue_size"] = service.VirtualInitQueue.Size()

	result["command_running_count"] = service.NodeService.CommandRunningCount

	tmpMap := make(map[string]interface{})
	service.NodeService.CommandRunning.Range(func(key, value interface{}) bool {
		tmpMap[key.(string)] = value
		return true // 继续遍历
	})
	result["command_running"] = tmpMap

	result["need_init_count"] = service.NodeService.NeedInitCount
	result["first_report"] = service.NodeService.FirstReport
	result["virtuals"] = service.NodeService.GetVirtuals()
	result["pods"] = service.NodeService.Pods
	result["gpu_models"] = service.NodeService.GetGpuModels()
	result["pause"] = service.NodeService.Pause
	code = 0
	return

}

func (obj nodeApi_) Action(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq actionReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.Action == "RemoveCommandRunning" {
		if oReq.Key == "" {
			msg = "key 为空"
			return
		}
		atomic.AddInt32(&service.NodeService.CommandRunningCount, -1)
		service.NodeService.CommandRunning.Delete(oReq.Key)
		msg = "移除成功"
		code = 0
		return
	} else {
		msg = "为设置的事务"
		return
	}
}

func (obj nodeApi_) RunningCommand(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	result["command_running_count"] = service.NodeService.CommandRunningCount
	result["command_running"] = service.NodeService.CommandRunning
	code = 0
	return

}
