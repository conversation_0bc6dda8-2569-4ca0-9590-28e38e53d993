package node

import (
	"context"
	"github.com/gin-gonic/gin"
	"net/http"
	"node-server/internal/common"
	"node-server/internal/common/logger"
	"node-server/internal/common/utils"
	"node-server/internal/service"
	"node-server/internal/structs"
)

type virtualApi_ struct {
}

var VirtualApi virtualApi_

type virtualActionReq struct {
	Action      string `json:"action"`
	VirtualId   uint   `json:"virtual_id"`
	StartupMark string `json:"startup_mark"`
	ContainerID string `json:"container_id"`
	KeepSeconds int64  `json:"keep_seconds"`
	OReq        bool   `json:"o_req"`
}

type virtualDetailReq struct {
	VirtualId uint `json:"virtual_id"`
	Status    int  `json:"status"`
}

type removeDockerReq struct {
	VirtualId   uint   `json:"virtual_id"`
	StartupMark string `json:"startup_mark"`
	DockerId    string `json:"docker_id"`
}

type removeImageReq struct {
	VirtualId   uint   `json:"virtual_id"` //指定虚拟机ID
	ImageSha256 string `json:"image_sha256"`
}

type pruneImageReq struct {
	VirtualId uint `json:"virtual_id"` //指定虚拟机ID
	WithA     bool `json:"with_a"`     //false删除悬空镜像   true删除悬空以及未被使用的镜像
}

type clearLocalImageReq struct {
	VirtualId uint `json:"virtual_id"` //指定虚拟机ID
	days      bool `json:"days"`       //清理几天前
}

func (obj virtualApi_) Action(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	result["v"] = common.Version
	defer func() {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq virtualActionReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.OReq {
		result["o_req"] = utils.GetJsonFromStruct(oReq)
	}
	if oReq.Action == "ResetInit" {
		if oReq.VirtualId <= 0 {
			msg = "参数错误"
			return
		}
		if _, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
			if service.NodeService.SetVirtualInitAt(oReq.VirtualId, 0) {
				msg = "初始化标记已重置，请稍后查看"
				code = 0
			} else {
				msg = "初始化标记重置失败"
				return
			}
		} else {
			msg = "虚拟机不存在"
			logger.Error(msg, oReq)
			return
		}
	} else if oReq.Action == "LockCacheShutdown" {
		if oReq.VirtualId <= 0 || oReq.StartupMark == "" {
			msg = "参数错误"
			return
		}
		if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
			ctx := context.Background()
			if err := virtual.SetCacheShutdownKeep(ctx, oReq.StartupMark, 1*60*60*24*300); err != nil {
				result["err"] = err
			} else {
				msg = "设置成功"
			}
			code = 0
			return
		} else {
			msg = "虚拟机不存在"
			logger.Error(msg, oReq)
			return
		}
	} else if oReq.Action == "ResetCacheShutdown" {
		if oReq.VirtualId <= 0 || oReq.StartupMark == "" {
			msg = "参数错误"
			return
		}
		if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
			ctx := context.Background()
			keepSeconds := common.CacheShutdownKeepSeconds
			if oReq.KeepSeconds > 0 {
				keepSeconds = oReq.KeepSeconds
			}
			if err := virtual.SetCacheShutdownKeep(ctx, oReq.StartupMark, keepSeconds); err != nil {
				result["err"] = err
			} else {
				msg = "设置成功"
			}
			code = 0
			return
		} else {
			msg = "虚拟机不存在"
			logger.Error(msg, oReq)
			return
		}
	} else if oReq.Action == "ContainerInfo" {
		if oReq.VirtualId <= 0 {
			msg = "VirtualId参数错误"
			return
		}
		if oReq.ContainerID == "" {
			msg = "ContainerID不能为空"
			return
		}
		if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
			defer virtual.CloseSsh("ContainerInfo")

			ctx := context.Background()
			containerInfo := structs.ContainerInfo{}
			if container, err := virtual.ContainerByIdUseApi(ctx, oReq.ContainerID); err != nil {
				result["err"] = err.Error()
				result["track"] = "lgx8r6n5tm2q"
				logger.Error(err)
			} else {
				logger.Infof("ContainerInfo: %+v", containerInfo)
				containerInfo.SizeRw = container.SizeRw
				containerInfo.SizeRootFs = container.SizeRootFs
				if stats, err := virtual.ContainerInfoUseApi(ctx, oReq.ContainerID); err != nil {
					result["err"] = err.Error()
					result["track"] = "pvk87vw8fexe"
					logger.Error(err)
				} else {
					containerInfo.CpuTotalUsage = stats.CPUStats.CPUUsage.TotalUsage
					containerInfo.MemoryUsage = stats.MemoryStats.Usage
					containerInfo.MemoryLimit = stats.MemoryStats.Limit
					code = 0
				}

				if image, err := virtual.ImageInspectWithRaw(ctx, container.ImageID); err != nil {
					result["err_image"] = err.Error()
					result["track"] = "hjwrswhsh7z4"
				} else {
					containerInfo.LayerCount = len(image.RootFS.Layers)
				}
			}
			result["container_info"] = containerInfo
			return
		} else {
			msg = "虚拟机不存在"
			logger.Error(msg, oReq)
			return
		}
	} else if oReq.Action == "ContainerFullInfo" {
		if oReq.VirtualId <= 0 {
			msg = "VirtualId参数错误"
			return
		}
		if oReq.ContainerID == "" && oReq.StartupMark == "" {
			msg = "ContainerID和StartupMark至少传一个"
			return
		}
		if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
			defer virtual.CloseSsh("ContainerInfo")

			ctx := context.Background()
			if oReq.ContainerID != "" {
				if container, err := virtual.ContainerByIdUseApi(ctx, oReq.ContainerID); err != nil {
					result["err"] = err.Error()
					result["track"] = "lgx8r6n5tm2q"
					logger.Error(err)
					return
				} else {
					result["container_id"] = oReq.ContainerID
					result["container"] = container
					code = 0
					return
				}
			} else if oReq.StartupMark != "" {
				if container, err := virtual.ContainerByStartupMarkUseApi(ctx, oReq.StartupMark); err != nil {
					logger.Error(err)
					return
				} else {
					result["startup_mark"] = oReq.StartupMark
					result["container"] = container
					code = 0
					return
				}
			}
			return
		} else {
			msg = "虚拟机不存在"
			logger.Error(msg, oReq)
			return
		}
	} else if oReq.Action == "MigrateLargeFile" {
		if oReq.VirtualId <= 0 || oReq.ContainerID == "" {
			msg = "参数错误"
			return
		}
		if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
			defer virtual.CloseSsh("ContainerInfo")

			ctx := context.Background()
			if container, err := virtual.ContainerByIdUseApi(ctx, oReq.ContainerID); err != nil {
				result["err"] = err.Error()
				logger.Error(err)
			} else {
				if container.State == "running" {
					if inspect, err := virtual.MigrateLargeFile(ctx, oReq.ContainerID); err != nil {
						msg = "迁移大文件失败"
						result["err"] = err.Error()
					} else {
						result["inspect"] = inspect
						result["before_size_rw"] = container.SizeRw
						result["before_size_root_fs"] = container.SizeRootFs
						code = 0
					}
				} else {
					msg = "容器不在运行状态"
				}
			}
			return
		} else {
			msg = "虚拟机不存在"
			logger.Error(msg, oReq)
			return
		}
	} else if oReq.Action == "ManualLockGpu" {
		if oReq.StartupMark == "" {
			msg = "请输入启动标识"
			return
		}
		if oReq.VirtualId <= 0 {
			msg = "参数错误"
			return
		}
		if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
			ctx := context.Background()
			if err := virtual.ManualLockGpu(ctx, oReq.StartupMark); err != nil {
				msg = err.Error()
				logger.Error(err)
			} else {
				msg = "添加成功"
				code = 0
			}
		} else {
			msg = "虚拟机不存在"
		}

	} else if oReq.Action == "ManualUnLockGpu" { //暂时不开发，只要将容器存储时间缩短就可以了
		if oReq.StartupMark == "" {
			msg = "请输入启动标识"
			return
		}
		if oReq.VirtualId <= 0 {
			msg = "参数错误"
			return
		}
		if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
			ctx := context.Background()
			if err := virtual.ManualUnLockGpu(ctx, oReq.StartupMark); err != nil {
				msg = err.Error()
				logger.Error(err)
			} else {
				msg = "移除成功"
				code = 0
			}
		} else {
			msg = "虚拟机不存在"
		}

	} else if oReq.Action == "ManualRemoveContainer" {
		if oReq.StartupMark == "" {
			msg = "请输入启动标识"
			return
		}
		if oReq.VirtualId <= 0 {
			msg = "参数错误"
			return
		}
		if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
			ctx := context.Background()
			if err := virtual.ManualUnLockGpu(ctx, oReq.StartupMark); err != nil {
				msg = err.Error()
				logger.Error(err)
			} else {
				msg = "移除成功"
				code = 0
			}
		} else {
			msg = "虚拟机不存在"
		}

	} else {
		msg = "未知操作"
	}

}

func (obj virtualApi_) Detail(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	result["from"] = "NodeMemory"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq virtualDetailReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
		result["virtual"] = virtual
		result["oreq"] = oReq
		if mm, err := virtual.GetLockGpus(); err != nil {
			result["lock_gpus"] = err
		} else {
			result["lock_gpus"] = mm
		}
		if mm, err := virtual.GetCacheShutdowns(); err != nil {
			result["cache_shutdowns"] = err
		} else {
			result["cache_shutdowns"] = mm
		}

		code = 0
		return
	} else {
		msg = "虚拟机不存在"
		logger.Error(msg, oReq)
		return
	}
}

func (obj virtualApi_) SetStatus(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	result["from"] = "NodeMemory"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq virtualDetailReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Status != 0 && oReq.Status != 1 && oReq.Status != 2 {
		msg = "状态值不在设置范围内"
		logger.Error(msg, utils.GetJsonFromStruct(oReq))
		return
	}

	if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
		virtual.Status = oReq.Status
		//service.NodeService.Virtuals[oReq.VirtualId] = virtual
		result["virtual"] = virtual
		msg = "状态在节点内存设置成功"
		code = 0
		return
	} else {
		msg = "虚拟机不存在"
		logger.Error(msg, oReq)
		return
	}
}

func (obj virtualApi_) RemoveContaner(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	result["from"] = "Ssh"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	var oReq removeImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.ImageSha256 == "" {
		msg = "镜像ID不能为空"
		return
	}

	if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
		ctx := context.Background()
		//if _, err := virtual.SshDial(); err != nil {
		//	logger.Error(err)
		//}
		defer virtual.CloseSsh("LocalImages")

		result["virtual_id"] = oReq.VirtualId
		result["image_sha256"] = oReq.ImageSha256
		if imageDeletes, err := virtual.DockerImageRemoveUseApi(ctx, oReq.ImageSha256); err != nil {
			msg = err.Error()
		} else {
			msg = "移除成功"
			result["image_deletes"] = imageDeletes
			code = 0
		}

		return
	} else {
		msg = "虚拟机不存在"
		logger.Error(msg, oReq)
		return
	}

}

func (obj virtualApi_) LocalImages(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	result["from"] = "Ssh"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	var oReq virtualDetailReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
		ctx := context.Background()
		if _, err := virtual.SshDial(); err != nil {
			logger.Error(err)
		}
		defer virtual.CloseSsh("LocalImages")

		result["virtual_id"] = oReq.VirtualId
		if space, err := virtual.DockerSpace(ctx); err != nil {
			result["space_err"] = err.Error()
		} else {
			result["docker_space"] = space
		}

		if images, err := virtual.LocalImages(ctx); err != nil {
			result["images_err"] = err.Error()
		} else {

			result["local_images"] = images
		}

		if allGpus, err := virtual.AllGpus(); err != nil {
			result["all_gpus_err"] = err.Error()
		} else {
			result["all_gpus"] = allGpus
		}
		code = 0
		return
	} else {
		msg = "虚拟机不存在"
		logger.Error(msg, oReq)
		return
	}

}

func (obj virtualApi_) RemoveImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	result["from"] = "Ssh"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	var oReq removeImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.ImageSha256 == "" {
		msg = "镜像ID不能为空"
		return
	}

	if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
		ctx := context.Background()
		//if _, err := virtual.SshDial(); err != nil {
		//	logger.Error(err)
		//}
		defer virtual.CloseSsh("LocalImages")

		result["virtual_id"] = oReq.VirtualId
		result["image_sha256"] = oReq.ImageSha256
		if imageDeletes, err := virtual.DockerImageRemoveUseApi(ctx, oReq.ImageSha256); err != nil {
			msg = err.Error()
		} else {
			msg = "移除成功"
			result["image_deletes"] = imageDeletes
			code = 0
		}

		return
	} else {
		msg = "虚拟机不存在"
		logger.Error(msg, oReq)
		return
	}

}

func (obj virtualApi_) PruneImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	result["from"] = "Ssh"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	var oReq pruneImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
		ctx := context.Background()
		if _, err := virtual.SshDial(); err != nil {
			logger.Error(err)
		}
		defer virtual.CloseSsh("LocalImages")

		result["virtual_id"] = oReq.VirtualId
		result["with_a"] = oReq.WithA
		if err := virtual.DockerImagesPrune(ctx, oReq.WithA); err != nil {
			msg = err.Error()
		} else {
			if oReq.WithA {
				msg = "清理成功"
			} else {
				if err := virtual.ClearLocalImage(ctx); err != nil {
					logger.Error(virtual.Host, "(", virtual.ID, ")清理本地镜像失败 err：", err)
					msg = "清理本地镜像失败"
				} else {
					msg = "清理本地镜像完成"
				}
			}
			if err := virtual.ReportLocalImages(ctx); err != nil {
				logger.Error(virtual.Host, "(", virtual.ID, ")上报本地镜像失败 err：", err)
				msg += ",上报本地镜像失败"
			} else {
				result["image_ids"] = virtual.ImageIds
				if virtualP, ok := service.NodeService.GetVirtualP(virtual.ID); ok {
					virtualP.SetImageIds(virtual.ImageIds)
					msg += ",上报本地镜像完成"
					code = 0
				} else {
					msg += ",上报本地镜像完成,设置失败"
				}
			}
		}
		return
	} else {
		msg = "虚拟机不存在"
		logger.Error(msg, oReq)
		return
	}
}

func (obj virtualApi_) ClearLocalImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	result["from"] = "Ssh"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	var oReq clearLocalImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
		ctx := context.Background()
		if _, err := virtual.SshDial(); err != nil {
			logger.Error(err)
		}
		defer virtual.CloseSsh("LocalImages")

		result["virtual_id"] = oReq.VirtualId

		if err := virtual.ClearLocalImage(ctx); err != nil {
			logger.Error(virtual.Host, "(", virtual.ID, ")清理本地镜像失败 err：", err)
			msg = "清理本地镜像失败"
		} else {
			msg = "清理本地镜像完成，请手动刷新镜像"
		}
		return
	} else {
		msg = "虚拟机不存在"
		logger.Error(msg, oReq)
		return
	}
}

func (obj virtualApi_) RefreshLocalImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	result["from"] = "Ssh"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	var oReq pruneImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
		ctx := context.Background()
		if _, err := virtual.SshDial(); err != nil {
			logger.Error(err)
		}
		defer virtual.CloseSsh("LocalImages")

		result["virtual_id"] = oReq.VirtualId

		if err := virtual.ReportLocalImages(ctx); err != nil {
			logger.Error(virtual.Host, "(", virtual.ID, ")上报本地镜像失败 err：", err)
			msg = "刷新失败,上报本地镜像失败"
		} else {
			result["image_ids"] = virtual.ImageIds
			if virtualP, ok := service.NodeService.GetVirtualP(virtual.ID); ok {
				virtualP.SetImageIds(virtual.ImageIds)
				msg = "刷新成功,上报本地镜像完成"
				code = 0
			} else {
				msg = "刷新成功,上报本地镜像完成,设置失败"
			}
		}
		return
	} else {
		msg = "虚拟机不存在"
		logger.Error(msg, oReq)
		return
	}
}

func (obj virtualApi_) RemoveDocker(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	result["from"] = "NodeMemory"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq removeDockerReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if b, err := service.NodeService.RmDockerFromMemory(nil, oReq.VirtualId, oReq.StartupMark, oReq.DockerId); err != nil {
		msg = "从内存移除Docker失败"
		logger.Error(msg, err, oReq)
		return
	} else {
		if b {
			code = 0
			msg = "从内存移除Docker成功"
			return
		} else {
			msg = "从内存移除Docker失败"
			return
		}
	}

}

func (obj virtualApi_) FreeGpus(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	result["from"] = "NodeMemory"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq virtualDetailReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if virtual, ok := service.NodeService.GetVirtual(oReq.VirtualId); ok {
		result["free_gpus"] = virtual.GpuFrees
		code = 0
		return
	} else {
		msg = "虚拟机不存在"
		logger.Error(msg, oReq)
		return
	}
}
