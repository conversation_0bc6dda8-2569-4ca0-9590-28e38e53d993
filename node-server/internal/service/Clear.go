package service

import (
	"context"
	"fmt"
	"node-server/internal/common"
	"node-server/internal/common/jsontime"
	"node-server/internal/common/logger"
	"node-server/internal/common/utils"
	"node-server/internal/config"
	"node-server/internal/enums"
	"node-server/internal/service/tasklog"
	"runtime"
	"strings"
	"sync"
	"time"

	"gorm.io/gorm"
)

// docker 存在 实例已经关机
type clear_ struct {
	dockerQueue *common.Queue
}

var Clear clear_

func init() {
	Clear.dockerQueue = &common.Queue{}
	Clear.dockerQueue.Cond = sync.NewCond(new(sync.Mutex))
}

func (d *clear_) ClearNginxMap() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	//item := Clear.dockerQueue.Dequeue()
	if str, err := nginxHandle("list", "", ""); err != nil {
		logger.Error(err)
		return err
	} else {
		lastUseMap := make(map[string]int64)
		keyMap := make(map[string]string)
		lines := strings.Split(str, "\n")
		logger.Info("NginxMap:", "共", len(lines), "条数据")
		for _, line := range lines {
			tmpAry := strings.Split(line, "  ")
			if len(tmpAry) != 2 {
				if line != "" {
					logger.Error("不规则的映射 line:", line)
				}
				continue
			} else {
				key := tmpAry[0]
				val := tmpAry[1]
				if strings.Compare(key, "") != 0 {
					tmpKey := key
					instKey := ""
					if len(tmpKey) == 34 {
						instKey = tmpKey[:32]
					} else if len(tmpKey) == 32 {
						instKey = tmpKey
					}
					if instKey == "" {
						logger.Error("instKey为空 line:", line)
						continue
					}
					if _, ok := keyMap[instKey]; !ok {
						keyMap[instKey] = val
					}
					if _, ok := lastUseMap[instKey]; !ok {
						lastUseMap[instKey] = 0
					}
				}
			}
		}
		logger.Info("NginxMap:", "有", len(keyMap), "个Key需要检测")
		for key, _ := range keyMap {
			logger.Info("Key:", key)
			trace := "NginxMap InstanceUuid:" + key
			instanceUuid := key

			lastUseUnix := int64(0)
			if _, ok := lastUseMap[instanceUuid]; ok {
				//存在
				lastUseUnix = lastUseMap[instanceUuid]
			}

			if liveState, rGinH, err := MasterService.InstanceLive(instanceUuid, lastUseUnix); err != nil {
				logger.Error(trace, err)
			} else {
				ctx := context.Background()
				if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupInstance, instanceUuid); err != nil {
					logger.Error(trace, err)
				} else {
					ctx = context.WithValue(ctx, "logkey", logKey)
					ctx = context.WithValue(ctx, "task", tasklog.TaskEnum.StartupMark)
				}

				if liveState == "remove" {
					tasklog.Save(ctx, "", "", fmt.Sprintf("需要手动移除Nginx liveState:%s %s ", liveState, trace), nil)
					logger.Info(trace, "NginxRemoveInstance ", fmt.Sprintf("需要手动移除Nginx liveState:%s", liveState))

					if rGinH != nil {
						virtualId := uint(0)
						podId := uint(0)
						if val, ok := rGinH.Result["virtual_id"]; ok {
							virtualId = uint(val.(float64))
						}
						if val, ok := rGinH.Result["pod_id"]; ok {
							podId = uint(val.(float64))
						}
						if virtualId > 0 && podId > 0 {
							logger.Info(trace, "NginxRemoveInstance开始 ", " virtualId:", virtualId, " podId:", podId)
							if msg, removeKeys, errKeys, err := NginxRemoveInstance(virtualId, podId, instanceUuid); err != nil {
								logger.Error(trace, "NginxRemoveInstance出错 ", err, msg, removeKeys, errKeys)
							} else {
								logger.Info(trace, "NginxRemoveInstance结束 ", msg, removeKeys, errKeys)
							}
						} else {
							logger.Error(trace, "NginxRemoveInstance rGinH:", utils.GetJsonFromStruct(*rGinH))
						}
					}
					//return nil
				} else {
					logger.Info(trace, " liveState:", liveState)
				}
			}
		}
		logger.Info("NginxMap end")
	}
	return nil
}

func (d *clear_) ClearLocalGpuLock() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	mVirtualKey := NodeService.GetVirtualIds()
	logger.Info("ClearLocalGpuLock 需要检测", len(mVirtualKey), "台虚拟机")
	for virtualId, _ := range mVirtualKey {
		trace := fmt.Sprintf("ClearLocalGpuLock virtualId:%d ", virtualId)
		if virtual, ok := NodeService.GetVirtual(virtualId); ok {
			if virtual.Inited() == false {
				logger.Info(trace, " 未初始化，继续下一个")
				continue
			}

			if virtual.TimeoutAt > 0 {
				logger.Info(trace, " 已超时，继续下一个")
				continue
			}

			ctx := context.Background()

			defer virtual.CloseSsh("ClearLocalGpuLock")

			gpuIndexMap := make(map[int]string)
			for _, docker := range virtual.Dockers {
				if len(docker.Gpus) == 0 {
					if ii := utils.String2Int(docker.MapPref); ii >= 30 {
						gpuIndexMap[ii] = docker.StartupMark
					}
				}
				for _, gpuIndex := range docker.Gpus {
					gpuIndexMap[gpuIndex] = docker.StartupMark
				}
			}
			//logger.Info(trace, " gpus111:", utils.GetJsonFromStruct(gpuIndexMap))

			if mm, err := virtual.GetLockGpus(); err != nil {
				logger.Error(err)
				return err
			} else {
				for gpuIndex, lockGpuItem := range mm {

					if lockGpuItem.LockedAt.After(time.Now().Add(time.Minute * -2)) {
						logger.Info(trace, "gpuIndex:", gpuIndex, " docker中不存在，但是移除时间未到")
						continue
					}

					if _, ok1 := gpuIndexMap[gpuIndex]; ok1 {
						//logger.Info(trace, "gpuIndex:", gpuIndex, " docker中存在不处理")
					} else {

						logger.Info(trace, "gpuIndex:", gpuIndex, " docker中不存在，检查启动情况 lockGpuItem:", utils.GetJsonFromStruct(lockGpuItem))
						commandKey := "RunDockerUserApi_" + lockGpuItem.StartupMark
						if _, ok2 := NodeService.CommandRunning.Load(commandKey); !ok2 {
							if bootIn, err := tasklog.BootIn(lockGpuItem.StartupMark); err != nil {
								logger.Error(trace, "tasklog.BootIn出错，不做处理", utils.GetJsonFromStruct(lockGpuItem))
								continue
							} else {
								if bootIn {
									logger.Info(trace, "正在启动中，不做处理", utils.GetJsonFromStruct(lockGpuItem))
									continue
								}
							}

							if container, err := virtual.ContainerByStartupMarkUseApi(ctx, lockGpuItem.StartupMark); err != nil {
								if err != common.ErrRecordNotFound {
									tmpMsg := "确认容器是否已经停止 获取容器信息失败"
									logger.Error(tmpMsg, lockGpuItem.StartupMark)
									continue
								} else {
									logger.Info("确认容器是否已经停止 容器不存在 ", lockGpuItem.StartupMark)
								}
							} else {
								if container.State == enums.DockerStatusEnum.Running {
									tmpMsg := "确认容器是否已经停止 未停止,不移除锁卡标记"
									logger.Info(tmpMsg, lockGpuItem.StartupMark)
									continue
								} else {
									tmpMsg := "确认容器是否已经停止 不在运行中,移除锁卡标记"
									logger.Info(tmpMsg, lockGpuItem.StartupMark, "  当前容器状态：", container.State)
								}
							}

							if err := virtual.UnLockGpus(lockGpuItem.StartupMark, "ClearLocalGpuLock"); err != nil {
								logger.Error(trace, "gpuIndex:", gpuIndex, " 检查启动情况不在启动中，需要移除 移除出错err:", err)
							} else {
								logger.Info(trace, "gpuIndex:", gpuIndex, " 检查启动情况不在启动中，需要移除 移除成功")
							}
						} else {
							logger.Info(trace, "gpuIndex:", gpuIndex, " docker中不存在，检查启动情况在启动中 lockGpuItem:", utils.GetJsonFromStruct(lockGpuItem))
						}
					}
				}
			}
		}
	}
	logger.Info("ClearLocalGpuLock 处理完成")
	return nil
}

func (d *clear_) ClearSpace() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	logger.Info("开始检查磁盘空间情况")

	now := time.Now()
	start := time.Date(now.Year(), now.Month(), now.Day(), 3, 1, 0, 0, now.Location())
	end := time.Date(now.Year(), now.Month(), now.Day(), 3, 16, 0, 0, now.Location())

	justClearPrivateImage := now.After(start) && now.Before(end)
	mVirtualKey := NodeService.GetVirtualIds()
	for virtualId, _ := range mVirtualKey {
		if virtual, ok := NodeService.GetVirtual(virtualId); ok {
			if virtual.Inited() == false {
				continue
			}
			if virtual.Status != 1 && virtual.Status != 2 {
				continue
			}
			if _, err := virtual.SshDial(); err != nil {
				continue
			}
			defer virtual.CloseSsh("DockerApiTest")

			ctx := context.Background()
			if err := virtual.ClearCacheShutdown(ctx); err != nil {
				logger.Error("virtual.ClearCacheShutdown err:", err)
			}

			if space, err := virtual.DockerSpace(ctx); err != nil {
				logger.Error(virtual.Host, "(", virtual.ID, ") 获取磁盘空间err:", err)
				if err == gorm.ErrRecordNotFound {
					logKey := "HandleCheckSpaceProgress_" + utils.Uint2String(virtualId)
					EmailService.AddNeedSend(logKey, virtual.Host+"("+utils.Uint2String(virtualId)+")虚拟机未检查到Docker磁盘，检查时间:"+jsontime.Now().String())
				}
			} else {
				if justClearPrivateImage && space.AvailG > 150 {
					ctx = context.WithValue(ctx, "task", "JustClearPrivateImage")
				} else {
					ctx = context.WithValue(ctx, "task", "ClearAllImage")
				}
				logger.Info(virtual.Host, "(", virtual.ID, ") 当前磁盘空间 space:", utils.GetJsonFromStruct(space), " justClearPrivateImage:", justClearPrivateImage)
				if space.AvailG <= 150 || justClearPrivateImage {

					sendMsg := ""
					if err := virtual.ClearLocalImage(ctx); err != nil {
						logger.Error("清除本地镜像出错 err:", err)
						sendMsg = virtual.Host + "(" + utils.Uint2String(virtualId) + ")虚拟机Docker剩余磁盘空间为" + space.Avail + "，已小于150G,清理出错 err:" + err.Error()
					} else {
						sendMsg = virtual.Host + "(" + utils.Uint2String(virtualId) + ")虚拟机Docker剩余磁盘空间为" + space.Avail + "，已小于150G,清理完成"

						if space1, err1 := virtual.DockerSpace(ctx); err1 != nil {
							logger.Error(err1)
						} else {
							sendMsg += " 当前可用空间为：" + space1.Avail
						}
					}
					logKey := "HandleCheckSpaceProgress_" + utils.Uint2String(virtualId)
					EmailService.AddNeedSend(logKey, sendMsg+" 检查时间:"+jsontime.Now().String())
				}
			}
			if err := virtual.ReportLocalImages(ctx); err != nil {
				logger.Error(virtual.Host, "(", virtual.ID, ") 清理空间后，上报本地镜像失败 err：", err)
			} else {
				if virtualP, ok := NodeService.GetVirtualP(virtual.ID); ok {
					virtualP.SetImageIds(virtual.ImageIds)
					logger.Info(virtual.Host, "(", virtual.ID, ") 清理空间后，上报本地镜像完成,设置成功")
				} else {
					logger.Error(virtual.Host, "(", virtual.ID, ") 清理空间后，上报本地镜像完成,设置失败 err：虚拟机不存在")
				}
			}
		}
	}
	logger.Info("开始检查磁盘空间情况检查完成")
	return nil
}

func (d *clear_) ClearLocalImage() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	logger.Info("开始检查磁盘空间情况")
	mVirtualKey := NodeService.GetVirtualIds()
	for virtualId, _ := range mVirtualKey {
		if virtual, ok := NodeService.GetVirtual(virtualId); ok {
			if space, err := virtual.DockerSpace(nil); err != nil {
				logger.Error(virtual.Host, "(", virtual.ID, ") 获取磁盘空间err:", err)
				if err == gorm.ErrRecordNotFound {
					logKey := "HandleCheckSpaceProgress_" + utils.Uint2String(virtualId)
					EmailService.AddNeedSend(logKey, virtual.Host+"("+utils.Uint2String(virtualId)+")虚拟机未检查到Docker磁盘，检查时间:"+jsontime.Now().String())
				}
			} else {
				logger.Info(virtual.Host, "(", virtual.ID, ") 当前磁盘空间 space:", utils.GetJsonFromStruct(space))
				if space.AvailG < 200 {
					logKey := "HandleCheckSpaceProgress_" + utils.Uint2String(virtualId)
					EmailService.AddNeedSend(logKey, virtual.Host+"("+utils.Uint2String(virtualId)+")虚拟机Docker剩余磁盘空间为"+space.Avail+"，已小于200G,需要处理 检查时间:"+jsontime.Now().String())
				}
			}
		}
	}
	logger.Info("开始检查磁盘空间情况检查完成")
	return nil
}

func (d *clear_) HandleInitVirtual() {
	defer func() {
		if e := recover(); e != nil {
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("奔溃panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	logger.Info("Start HandleInitVirtual")
	for _, virtual := range NodeService.GetVirtuals() {
		pre := fmt.Sprintf("HandleInitVirtual %s(%d)", virtual.Host, virtual.ID)
		if virtual.Status == 0 {
			logger.Info(pre, " 状态为未上线(", virtual.Status, ")不加入队列", "  VirtualInitQueue size:", VirtualInitQueue.Size())
			continue
		}
		//if virtual.Status == 2 {
		//	logger.Info(pre, " 状态为暂停(", virtual.Status, ")不加入队列", "  VirtualInitQueue size:", VirtualInitQueue.Size())
		//	continue
		//}
		//if virtual.TimeoutAt > 0 {
		//	logger.Info(pre, " 超时状态，不做实例检测 TimeoutAt:", virtual.TimeoutAt, "  VirtualInitQueue size:", VirtualInitQueue.Size())
		//	continue
		//}
		//if virtual.Inited() == false {
		//	logger.Info(pre, " 未初始化，不做实例检测 InitAt:", virtual.InitAt, "  VirtualInitQueue size:", VirtualInitQueue.Size())
		//	continue
		//}

		checkTime := time.Now().Add(time.Minute * -4)
		if virtual.LastInitTime.Time().Before(checkTime) { //最后初始化成功时间在5分钟之前 加入队列
			logger.Info(pre, " 状态为(", virtual.Status, ")加入初始化队列", "  VirtualInitQueue size:", VirtualInitQueue.Size())
			queueItem := QueueItem{Action: enums.QueueActionEnum.InitVirtualBatch, Data: virtual.ID}
			NodeService.NeedInitCount++
			if VirtualInitQueue.Enqueue(queueItem) == false {
				NodeService.NeedInitCount--
			}
		} else {
			logger.Info(pre, " 检测时间未到，不加入初始化队列 LastCheckTime：", virtual.LastInitTime, "   checkTime:", checkTime)
		}
	}
	logger.Info("HandleInitVirtual Complete")
}

func (d *clear_) HandleCheckInstanceLive() {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	for _, virtual := range NodeService.GetVirtuals() {
		pre := fmt.Sprintf("HandleCheckInstanceLive %s(%d)", virtual.Host, virtual.ID)
		if virtual.Status == 0 {
			logger.Info(pre, " 状态为未上线(", virtual.Status, ")不加入队列", "  VirtualInitQueue size:", VirtualInitQueue.Size())
			continue
		}
		if virtual.Status == 2 {
			logger.Info(pre, " 状态为暂停(", virtual.Status, ")不加入队列", "  VirtualInitQueue size:", VirtualInitQueue.Size())
			continue
		}
		if virtual.TimeoutAt > 0 {
			logger.Info(pre, " 超时状态，不做实例检测 TimeoutAt:", virtual.TimeoutAt, "  VirtualInitQueue size:", VirtualInitQueue.Size())
			continue
		}
		if virtual.Inited() == false {
			logger.Info(pre, " 未初始化，不做实例检测 InitAt:", virtual.InitAt, "  VirtualInitQueue size:", VirtualInitQueue.Size())
			continue
		}

		logger.Info(pre, " 状态为(", virtual.Status, ")加入实例状态检测队列", "  VirtualInitQueue size:", VirtualInitQueue.Size())
		queueItem := QueueItem{Action: enums.QueueActionEnum.CheckInstanceLive, Data: virtual.ID}
		VirtualInitQueue.Enqueue(queueItem)

		//checkTime := time.Now().Add(time.Second)
		//if virtual.LastCheckTime.Time().Before(checkTime) {
		//	logger.Info(pre, " 状态为(", virtual.Status, ")加入队列", "  VirtualInitQueue size:", VirtualInitQueue.Size())
		//	queueItem := QueueItem{Action: enums.QueueActionEnum.CheckInstanceLive, Data: virtual.ID}
		//	VirtualInitQueue.Enqueue(queueItem)
		//} else {
		//	logger.Info(pre, " 检测时间未到，不加入队列 LastCheckTime：", virtual.LastCheckTime, "   checkTime:", checkTime)
		//}
	}
}

func (o *clear_) ReportNodeInfo() bool {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	if ginH, err := MasterService.ReportNodeInfo(); err != nil {
		logger.Error(err)
		return false
	} else {
		if ginH == nil {
			return false
		}
		logger.Info("Node Info report result:", utils.GetJsonFromStruct(ginH))
		return true
	}
}

func (o *clear_) ReportNodeLive() bool {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	totalVirtual, totalInstance, totalGpus, freeGpus, _ := NodeService.Static()

	postUrl := "api/master/node/report_live"
	postData := make(map[string]interface{})
	postData["node_id"] = config.NodeId
	postData["total_virtual"] = totalVirtual
	postData["total_instance"] = totalInstance
	postData["total_gpus"] = totalGpus
	postData["free_gpus"] = freeGpus
	m, code := Post(postUrl, postData)
	if code == 0 {
		logger.Info("上报节点数据成功", postData, m)
		return true
	} else {
		logger.Error("上报节点数据失败", postData, m)
	}
	return false
}
