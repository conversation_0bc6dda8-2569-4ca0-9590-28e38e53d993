package service

import (
	"errors"
	"node-server/internal/common/logger"
	"node-server/internal/common/utils"
	"node-server/internal/config"
	"node-server/internal/enums"
	"node-server/internal/structs"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/gin-gonic/gin"
)

type master_ struct {
}

var MasterService master_

func (obj *master_) InstanceLiveByStartupMark(startupMark string) (string, int64, error) {
	postUrl := "api/master/info/instance_live"
	postData := make(map[string]interface{})
	postData["startup_mark"] = startupMark
	shutdownAt := int64(0)
	if ginH, err := PostMasterForGin(postUrl, postData); err != nil {
		logger.Error(err)
		return "", shutdownAt, err
	} else {
		logger.Debugf("InstanceLiveByStartupMark[%s] ginH: %v", startupMark, ginH)
		if rGinH, err := Result(ginH); err != nil {
			logger.Error(err)
			return "", shutdownAt, err
		} else {
			if rGinH.Code == 0 {

				if val, ok := rGinH.Result["shutdown_at"]; ok {
					if at, ok := val.(int64); ok {
						shutdownAt = at
					}
				}

				if val, ok := rGinH.Result["live_state"]; ok {
					return val.(string), shutdownAt, nil
				} else {
					return "", shutdownAt, nil
				}
			} else {
				return rGinH.Msg, shutdownAt, errors.New(rGinH.Msg)
			}
		}
	}
}

func (obj *master_) InstanceLive(instanceUuid string, lastUseUnix int64) (string, *GinH, error) {
	postUrl := "api/master/info/instance_live"
	postData := make(map[string]interface{})
	postData["instance_uuid"] = instanceUuid
	postData["last_use_unix"] = lastUseUnix
	if ginH, err := PostMasterForGin(postUrl, postData); err != nil {
		logger.Error(err)
		return "", nil, err
	} else {
		if rGinH, err := Result(ginH); err != nil {
			logger.Error(err)
			return "", &rGinH, err
		} else {
			if rGinH.Code == 0 {
				if val, ok := rGinH.Result["live_state"]; ok {
					return val.(string), &rGinH, nil
				} else {
					return "", &rGinH, nil
				}
			} else {
				return rGinH.Msg, &rGinH, errors.New(rGinH.Msg)
			}
		}
	}
}

func (obj *master_) StartupLive(startupMark string, lastUseUnix int64) (string, *GinH, error) {
	postUrl := "api/master/info/startup_live"
	postData := make(map[string]interface{})
	postData["startup_mark"] = startupMark
	postData["last_use_unix"] = lastUseUnix
	if ginH, err := PostMasterForGin(postUrl, postData); err != nil {
		logger.Error(err)
		return "", nil, err
	} else {
		if rGinH, err := Result(ginH); err != nil {
			logger.Error(err)
			return "", &rGinH, err
		} else {
			if rGinH.Code == 0 {
				if val, ok := rGinH.Result["live_state"]; ok {
					return val.(string), &rGinH, nil
				} else {
					return "", &rGinH, nil
				}
			} else {
				return rGinH.Msg, &rGinH, errors.New(rGinH.Msg)
			}
		}
	}
}

func (obj *master_) ReportStartupSuccess(virtualId uint, startupMark string) (gin.H, error) {

	//code 不为0不知道什么情况  code为0
	var resp structs.DockerDetailResp

	if config.Env == enums.EnvEnum.DEV {

		resp = structs.DockerDetailResp{
			ID:           "f03c5e1b3a70",
			PodId:        1,
			VirtualId:    virtualId,
			InstanceUuid: "555d42676cf7446398278c59311fb69d",
			StartupMark:  startupMark,
			Gpus:         []int{2},
			MapPref:      "",
			MapPorts:     []string{"88"},
			WebUrl:       "",
			ApiBase:      "",
			CreatedAt:    time.Now(),
		}

	} else {

		if virtual, ok := NodeService.GetVirtual(virtualId); ok {
			for _, docker := range virtual.GetDockers() {
				if docker.StartupMark == startupMark {
					resp = structs.DockerDetailResp{
						ID:           docker.ID,
						PodId:        docker.PodId,
						VirtualId:    docker.VirtualId,
						InstanceUuid: docker.InstanceUuid,
						StartupMark:  docker.StartupMark,
						Gpus:         docker.Gpus,
						MapPref:      docker.MapPref,
						MapPorts:     docker.MapPorts,
						WebUrl:       docker.WebUrl,
						ApiBase:      docker.ApiBase,
						CreatedAt:    docker.CreatedAt,
					}
					break
				}
			}
		}

	}

	if resp.ID == "" {
		msg := "Docker不存在"
		logger.Error(msg, virtualId, "   ", startupMark)
		return nil, errors.New(msg)
	}

	postUrl := "api/master/report/startup_success"
	postData := make(map[string]interface{})
	postData["docker"] = resp
	postData["startup_mark"] = startupMark
	return PostMasterForGin(postUrl, postData)
}

func (obj *master_) ReportStartupFail(virtualId uint, startupMark string, reason string) (gin.H, error) {
	var resp structs.DockerDetailResp
	if virtual, ok := NodeService.GetVirtual(virtualId); ok {
		for _, docker := range virtual.GetDockers() {
			if docker.StartupMark == startupMark {
				resp = structs.DockerDetailResp{
					ID:           docker.ID,
					PodId:        docker.PodId,
					VirtualId:    docker.VirtualId,
					InstanceUuid: docker.InstanceUuid,
					StartupMark:  docker.StartupMark,
					Gpus:         docker.Gpus,
					MapPref:      docker.MapPref,
					MapPorts:     docker.MapPorts,
					WebUrl:       docker.WebUrl,
					ApiBase:      docker.ApiBase,
					CreatedAt:    docker.CreatedAt,
				}
				break
			}
		}
	}

	postUrl := "api/master/report/startup_fail"
	postData := make(map[string]interface{})
	postData["startup_mark"] = startupMark
	postData["virtual_id"] = virtualId
	postData["docker"] = resp
	postData["reason"] = reason
	return PostMasterForGin(postUrl, postData)
}

func (obj *master_) ReportDockerCommit(startupMark string, imageId uint, reportState string) (gin.H, error) {
	postUrl := "api/master/report/docker_commit"
	postData := make(map[string]interface{})
	postData["image_id"] = imageId
	postData["report_state"] = reportState
	postData["startup_mark"] = startupMark
	return PostMasterForGin(postUrl, postData)
}

func (obj *master_) ReportDockerPush(startupMark string, imageId uint, reportState string, task string, reason, imageMeta string) (gin.H, error) {
	postUrl := "api/master/report/docker_push"
	postData := make(map[string]interface{})
	postData["image_id"] = imageId
	postData["report_state"] = reportState
	postData["startup_mark"] = startupMark
	postData["task"] = task
	postData["reason"] = reason
	postData["image_meta"] = imageMeta

	return PostMasterForGin(postUrl, postData)
}

func (obj *master_) ReportLocalImages(virtualId uint, images []DockerImageItem) (gin.H, error) {
	logger.Info("开始上报本地镜像 virtualId:", virtualId, "  images数量:", len(images))
	postUrl := "api/master/report/virtual_local_images"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["images"] = utils.GetJsonFromStruct(images)
	return PostMasterForGin(postUrl, postData)
}

func (obj *master_) ReportLocalNewImages(virtualId uint, imageIDs []uint) (gin.H, error) {
	logger.Info("开始上报本地新增镜像 virtualId:", virtualId, "  images数量:", len(imageIDs))
	postUrl := "api/master/report/virtual_local_new_images"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["image_ids"] = utils.GetJsonFromStruct(imageIDs)
	return PostMasterForGin(postUrl, postData)
}

func (obj *master_) ReportInspectImage(virtualId uint, imageId uint, layerCount int, imageInspect types.ImageInspect) (gin.H, error) {
	//logger.Info("开始上报本地新增镜像 virtualId:", virtualId, "  images数量:", len(imageIDs))
	postUrl := "api/master/report/virtual_inspect_image"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId
	postData["image_id"] = imageId
	postData["layer_count"] = layerCount
	//postData["image_inspect"] = imageInspect
	return PostMasterForGin(postUrl, postData)
}

func (obj *master_) ReportVirtualInfo(virtualId uint) (gin.H, error) {

	postUrl := "api/master/report/virtual_info"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId

	if o, ok := NodeService.GetVirtual(virtualId); ok {
		postData["gpu_frees"] = o.GpuFrees
		postData["total_gpus"] = len(o.GetGpus())
		postData["total_instance"] = len(o.GetDockers())
		postData["init_at"] = o.InitAt
		postData["last_init_time"] = o.LastInitTime
		postData["docker_at"] = o.DockerAt
		postData["timeout_at"] = o.TimeoutAt
	} else {
		err := errors.New("Virtual not in Map")
		logger.Error(err, "上报Virtual信息,virtualId:", virtualId)
		return nil, err
	}
	logger.Info("开始上报Virtual信息 virtual_info:", utils.GetJsonFromStruct(postData))
	return PostMasterForGin(postUrl, postData)
}

func (obj *master_) ReportVirtualTimeout(virtualId uint, timeout int64) (gin.H, error) {
	logger.Info("开始上报Virtual超时信息 virtualId:", virtualId)
	postUrl := "api/master/report/virtual_timeout"
	postData := make(map[string]interface{})
	postData["virtual_id"] = virtualId

	if _, ok := NodeService.GetVirtual(virtualId); ok {
		postData["timeout_at"] = timeout
	} else {
		err := errors.New("Virtual not in Map")
		logger.Error(err, virtualId)
		return nil, err
	}

	return PostMasterForGin(postUrl, postData)
}

func (obj *master_) ReportNodeInfo() (gin.H, error) {
	logger.Info("开始上报Node信息 nodeId:", config.NodeId)

	totalVirtual, totalInstance, totalGpus, freeGpus, err := NodeService.Static()
	if err != nil {
		logger.Error("统计节点信息出错 nodeId:", config.NodeId, "  err:", err)
		return nil, err
	}
	postUrl := "api/master/report/node_info"
	postData := make(map[string]interface{})
	postData["node_id"] = config.NodeId
	postData["total_virtual"] = totalVirtual
	postData["total_instance"] = totalInstance
	postData["total_gpus"] = totalGpus
	postData["free_gpus"] = freeGpus
	return PostMasterForGin(postUrl, postData)
}
