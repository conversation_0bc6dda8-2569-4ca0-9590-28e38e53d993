package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"node-server/internal/common"
	"node-server/internal/common/jsontime"
	"node-server/internal/common/logger"
	"node-server/internal/common/utils"
	"node-server/internal/config"
	"node-server/internal/enums"
	daemon_server "node-server/internal/service/daemon-server"
	"node-server/internal/service/tasklog"
	"node-server/internal/structs"
	"os"
	"path"
	"runtime"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"unsafe"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/image"
	"gorm.io/gorm"
)

type _node struct {
	CommandRunningCount int32
	//CommandRunning      map[string]structs.RunningCommand
	CommandRunning sync.Map
	CancelFuncs    sync.Map
	NeedInitCount  int
	FirstReport    bool
	Virtuals       map[uint]*Virtual
	//Pods           map[uint]PodItem
	Pods sync.Map
	//PodImages map[uint]PodImageItem
	PodImages sync.Map
	//GpuModels map[string]GpuModelItem
	GpuModels sync.Map
	Pause     bool
	mu        sync.RWMutex // 读写锁
}

var NodeService _node

var VirtualInitQueue *common.Queue

type QueueItem struct {
	Action string
	Data   interface{}
}

func (o *_node) GetGpuModels() map[string]GpuModelItem {
	result := make(map[string]GpuModelItem)
	// Range 操作：遍历所有键值对
	o.GpuModels.Range(func(key, value interface{}) bool {
		result[key.(string)] = value.(GpuModelItem)
		return true
	})
	return result
}

func (o *_node) GetVirtualP(virtualId uint) (*Virtual, bool) {
	o.mu.RLock()
	defer o.mu.RUnlock()
	if tmp, ok := o.Virtuals[virtualId]; ok {
		return tmp, true
	}
	return nil, false
}

func (o *_node) GetVirtual(virtualId uint) (Virtual, bool) {
	o.mu.RLock()
	defer o.mu.RUnlock()
	var virtual Virtual
	if tmp, ok := o.Virtuals[virtualId]; ok {
		return tmp.GetVirtual(), true
	}
	//logger.Error("GetVirtual未找到虚拟机 virtualId:", virtualId, "  Virtuals:", utils.GetJsonFromStruct(o.Virtuals))
	return virtual, false
}

func (o *_node) LenVirtuals() int {
	o.mu.RLock()
	defer o.mu.RUnlock()
	if NodeService.Virtuals == nil {
		return 0
	}
	return len(NodeService.Virtuals)
}

func (o *_node) GetVirtuals() map[uint]Virtual {
	o.mu.RLock()
	defer o.mu.RUnlock()
	virtuals := make(map[uint]Virtual)
	for _, virtual := range NodeService.Virtuals {
		virtuals[virtual.ID] = virtual.GetVirtual()
	}
	return virtuals
}

func (o *_node) GetVirtualIds() map[uint]uint {
	o.mu.RLock()
	defer o.mu.RUnlock()
	virtualIds := make(map[uint]uint)
	for key, _ := range NodeService.Virtuals {
		virtualIds[key] = key
	}
	return virtualIds
}

func (o *_node) GetVirtualDockers(virtualId uint) []DockerItem {
	o.mu.RLock()
	defer o.mu.RUnlock()
	dockers := make([]DockerItem, 0)
	if virtualP, ok := NodeService.Virtuals[virtualId]; ok {
		dockers = virtualP.GetDockers()
	}
	return dockers
}

func (o *_node) SetVirtual(virtual Virtual) bool {
	o.mu.Lock()
	defer o.mu.Unlock()
	if virtualP, ok := o.Virtuals[virtual.ID]; ok {
		virtualP.SetVirtualBaseInfo(virtual)
	} else {
		o.Virtuals[virtual.ID] = &virtual
	}
	return true
}

func (o *_node) SetVirtualTimeoutAt(virtualId uint, at int64) bool {
	o.mu.Lock()
	defer o.mu.Unlock()
	if virtual, ok := o.Virtuals[virtualId]; ok {
		virtual.TimeoutAt = at
		return true
	}
	return false
}

func (o *_node) SetVirtualStatus(virtualId uint, status int) bool {
	o.mu.Lock()
	defer o.mu.Unlock()
	if virtual, ok := o.Virtuals[virtualId]; ok {
		virtual.Status = status
		if virtual.Status == 0 {
			virtual.InitAt = 0
		}
		return true
	}
	return false
}

func (o *_node) SetVirtualInitAt(virtualId uint, at int64) bool {
	o.mu.Lock()
	defer o.mu.Unlock()
	if virtual, ok := o.Virtuals[virtualId]; ok {
		virtual.InitAt = at
		return true
	}
	return false
}

func (o *_node) SetVirtualImageIds(virtualId uint, imageIds string) bool {
	o.mu.Lock()
	defer o.mu.Unlock()
	if virtual, ok := o.Virtuals[virtualId]; ok {
		virtual.ImageIds = imageIds
		return true
	}
	return false
}

func (o *_node) SetDockerLastStateCheck(virtualId uint, startupMark string) bool {
	o.mu.Lock()
	defer o.mu.Unlock()
	if virtual, ok := o.Virtuals[virtualId]; ok {
		for i, docker := range virtual.Dockers {
			if docker.StartupMark == startupMark {
				now := jsontime.Now()
				virtual.Dockers[i].LastStateCheck = jsontime.Now()
				//o.Dockers[i].SshPort = 22
				logger.Info("设置LastStateCheck i:", i, " val:", now)
				return true
			}
		}
	}
	return false
}

func (d *_node) Init() error {
	VirtualInitQueue = &common.Queue{}
	VirtualInitQueue.Cond = sync.NewCond(new(sync.Mutex))
	if err := d.LoadGpuModels(""); err != nil {
		logger.Error(err)
		return err
	}

	if err := d.LoadVirtuals(); err != nil {
		logger.Error(err)
		return err
	}
	if err := d.LoadPods(0); err != nil {
		logger.Error(err)
		return err
	}
	if err := d.LoadPodImages(0); err != nil {
		logger.Error(err)
		return err
	}

	if err := d.StartInitVirtualsQueue(); err != nil {
		logger.Error(err)
	}

	return nil
}

func (d *_node) LoadPods(podId uint) error {
	mPod := make(map[uint]PodItem)
	postUrl := "api/master/node/pods"
	postData := make(map[string]interface{})
	postData["pod_id"] = podId
	mm, _ := Post(postUrl, postData)

	dataOk := false
	cacheFileName := "pods.txt"
	if mm != nil && podId == 0 {
		if _, ok := mm["result"]; ok {
			result := mm["result"].(map[string]interface{})
			if _, ok := result["items"]; ok {
				items := result["items"].([]interface{})
				if len(items) > 0 {
					dataOk = true
					if err := SaveToFileCache(cacheFileName, utils.GetJsonFromStruct(mm)); err != nil {
						logger.Error(err)
					}
				}
			}
		}
	}
	if dataOk == false && podId == 0 {
		if content, err := ReadFromFileCache(cacheFileName); err != nil {
			logger.Error(err)
			return err
		} else {
			mm = utils.GetMapFromJson(content)
		}
	}

	if mm != nil {
		if _, ok := mm["result"]; ok {
			result := mm["result"].(map[string]interface{})
			if _, ok := result["items"]; ok {
				items := result["items"].([]interface{})
				for _, tmp := range items {
					item := tmp.(map[string]interface{})
					pod := PodItem{
						ID:            uint(item["id"].(float64)),
						Uuid:          item["uuid"].(string),
						Title:         item["title"].(string),
						PodName:       item["pod_name"].(string),
						Category:      int(item["category"].(float64)),
						PortMaps:      item["port_maps"].(string),
						ImageName:     item["image_name"].(string),
						ImageTag:      item["image_tag"].(string),
						Command:       item["command"].(string),
						NeedGpus:      int(item["need_gpus"].(float64)),
						StartupElapse: uint(item["startup_elapse"].(float64)),
						DataFolder:    item["data_folder"].(string),
						VirtualIds:    item["virtual_ids"].(string),
						Status:        int(item["status"].(float64)),
					}

					if pod.PortMaps != "" {
						ary := utils.GetMapAryFromJson(pod.PortMaps)
						if ary == nil {
							ary = make([]map[string]interface{}, 0)
						}

						portMap := make(map[string]string)
						for i := 0; i < len(ary); i++ {
							if ary[i]["host_port"].(string) == "" {
								continue
							}
							if utils.String2Int(ary[i]["host_port"].(string)) == 0 {
								continue
							}
							if ary[i]["container_port"].(string) == "" {
								continue
							}
							if utils.String2Int(ary[i]["container_port"].(string)) == 0 {
								continue
							}
							portMap[ary[i]["host_port"].(string)] = ary[i]["container_port"].(string)
						}
						pod.PortMap = portMap
					}
					mPod[pod.ID] = pod
					d.Pods.Store(podId, pod)
				}
				//if podId == 0 {
				//	d.Pods = mPod
				//} else {
				//	if _, ok := mPod[podId]; ok {
				//		//d.Pods[podId] = mPod[podId]
				//		d.Pods.Store(podId, mPod[podId])
				//		logger.Info("Pod载入 podId:", podId)
				//	} else {
				//		logger.Error("Pod未载入 podId:", podId)
				//		return errors.New("pod不存在")
				//	}
				//}
				return nil
			}
		}
	}
	err := errors.New("获取Pod数据失败")
	logger.Error(err)
	return err
}

func (d *_node) LoadPodImages(imageId uint) error {
	//mPodImage := make(map[uint]PodImageItem)
	postUrl := "api/master/node/pod_images"
	postData := make(map[string]interface{})
	postData["image_id"] = imageId
	mm, _ := Post(postUrl, postData)

	dataOk := false
	cacheFileName := "pod_images.txt"
	if mm != nil && imageId == 0 {
		if _, ok := mm["result"]; ok {
			result := mm["result"].(map[string]interface{})
			if _, ok := result["items"]; ok {
				items := result["items"].([]interface{})
				if len(items) > 0 {
					dataOk = true
					if err := SaveToFileCache(cacheFileName, utils.GetJsonFromStruct(mm)); err != nil {
						logger.Error(err)
					}
				}
			}
		}
	}
	if dataOk == false && imageId == 0 {
		if content, err := ReadFromFileCache(cacheFileName); err != nil {
			logger.Error(err)
			return err
		} else {
			mm = utils.GetMapFromJson(content)
		}
	}

	if mm != nil {
		if _, ok := mm["result"]; ok {
			result := mm["result"].(map[string]interface{})
			if _, ok := result["items"]; ok {
				items := result["items"].([]interface{})
				for _, tmp := range items {
					var podImage PodImageItem
					if err := utils.GetStructFromMap(&podImage, tmp.(map[string]interface{})); err != nil {
						logger.Error(err)
						continue
					}
					if podImage.ID == 0 {
						continue
					}
					//mPodImage[podImage.ID] = podImage
					d.PodImages.Store(podImage.ID, podImage)
				}
				//if imageId == 0 {
				//	d.PodImages = mPodImage
				//} else {
				//	if _, ok := mPodImage[imageId]; ok {
				//		d.PodImages[imageId] = mPodImage[imageId]
				//	} else {
				//		return errors.New("podImage不存在")
				//	}
				//}
				return nil
			}
		}
	}
	err := errors.New("获取PodImage数据失败")
	logger.Error(err)
	return err
}

func (d *_node) LoadGpuModels(gpuModelName string) error {
	//mGpuModel := make(map[string]GpuModelItem)
	postUrl := "api/master/node/gpu_models"
	postData := make(map[string]interface{})
	postData["gpu_model_name"] = gpuModelName
	mm, _ := Post(postUrl, postData)

	dataOk := false
	cacheFileName := "gpumodels.txt"
	if mm != nil && gpuModelName == "" {
		if _, ok := mm["result"]; ok {
			result := mm["result"].(map[string]interface{})
			if _, ok := result["items"]; ok {
				items := result["items"].([]interface{})
				if len(items) > 0 {
					dataOk = true
					if err := SaveToFileCache(cacheFileName, utils.GetJsonFromStruct(mm)); err != nil {
						logger.Error(err)
					}
				}
			}
		}
	}
	if dataOk == false && gpuModelName == "" {
		if content, err := ReadFromFileCache(cacheFileName); err != nil {
			logger.Error(err)
			return err
		} else {
			mm = utils.GetMapFromJson(content)
		}
	}

	if mm != nil {
		if _, ok := mm["result"]; ok {
			result := mm["result"].(map[string]interface{})
			if _, ok := result["items"]; ok {
				items := result["items"].([]interface{})
				for _, tmp := range items {
					item := tmp.(map[string]interface{})
					gpuModel := GpuModelItem{
						ID:      uint(item["id"].(float64)),
						Uuid:    item["uuid"].(string),
						GpuName: item["gpu_name"].(string),
						MemoryG: int(item["memory_g"].(float64)),
						MemoryM: int(item["memory_m"].(float64)),
						Status:  int(item["status"].(float64)),
					}
					//mGpuModel[gpuModel.GpuName] = gpuModel
					d.GpuModels.Store(gpuModel.GpuName, gpuModel)
				}
				//if gpuModelName == "" {
				//	d.GpuModels = mGpuModel
				//} else {
				//	if _, ok := mGpuModel[gpuModelName]; ok {
				//		d.GpuModels[gpuModelName] = mGpuModel[gpuModelName]
				//	} else {
				//		return errors.New("gpu型号不存在")
				//	}
				//}
				return nil
			}
		}
	}
	err := errors.New("获取GpuModel数据失败")
	logger.Error(err)
	return err
}

func (d *_node) LoadVirtuals() error {
	defer func() {
		if e := recover(); e != nil {
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	if d.LenVirtuals() == 0 {
		d.Virtuals = make(map[uint]*Virtual)
	}
	pre := "LoadVirtuals_" + utils.GetUUID() + " "

	mVirtual := make(map[uint]Virtual)
	postUrl := "api/master/node/virtuals"
	postData := make(map[string]interface{})
	mm, _ := Post(postUrl, postData)

	dataOk := false
	cacheFileName := "virtuals.txt"
	if mm != nil {
		if _, ok := mm["result"]; ok {
			result := mm["result"].(map[string]interface{})
			if _, ok := result["items"]; ok {
				//items := result["items"].([]interface{})
				//if len(items) > 0 {
				dataOk = true
				if err := SaveToFileCache(cacheFileName, utils.GetJsonFromStruct(mm)); err != nil {
					logger.Error(err)
				}
				//}
			}
		}
	}
	if dataOk {
		logger.Info(pre, "从服务端获取Virtual列表 dataOk:", dataOk)
	} else {
		logger.Info(pre, "从服务端获取Virtual列表 dataOk:", dataOk, " mm:", utils.GetJsonFromStruct(mm))
	}

	if dataOk == false {
		if content, err := ReadFromFileCache(cacheFileName); err != nil {
			logger.Error(err)
			return err
		} else {
			mm = utils.GetMapFromJson(content)
		}
	}

	logger.Info(pre, "开始装载 dataOk:", dataOk)
	if mm != nil {
		if _, ok := mm["result"]; ok {
			result := mm["result"].(map[string]interface{})
			if _, ok := result["items"]; ok {
				items := result["items"].([]interface{})
				for _, tmp := range items {
					item := tmp.(map[string]interface{})
					hostPort := fmt.Sprintf("%s:%d", item["host"], int(item["port"].(float64)))
					virtualNode := Virtual{
						ID:           uint(item["id"].(float64)),
						HostPort:     hostPort,
						Host:         item["host"].(string),
						Port:         int(item["port"].(float64)),
						SshUser:      item["ssh_user"].(string),
						SshPassword:  item["ssh_password"].(string),
						GpuModelId:   uint(item["gpu_model_id"].(float64)),
						ImageIds:     item["image_ids"].(string),
						PodIds:       item["pod_ids"].(string),
						Status:       int(item["status"].(float64)),
						Gpus:         make([]GpuItem, 0),
						Dockers:      make([]DockerItem, 0),
						DaemonClient: daemon_server.GetDaemonServerClient(item["host"].(string)),
					}
					if _, ok1 := item["container_cpus"]; ok1 {
						virtualNode.ContainerCpus = item["container_cpus"].(float64)
					}
					if _, ok1 := item["container_mem"]; ok1 {
						virtualNode.ContainerMem = item["container_mem"].(string)
					}
					mVirtual[virtualNode.ID] = virtualNode
				}
			}
		}
	}

	for _, item := range mVirtual {
		if !d.SetVirtual(item) {
			logger.Error("d.SetVirtual 失败 virtual:", utils.GetJsonFromStruct(item))
		}

		//virtual := item
		//if tmp, ok := d.Virtuals[virtual.ID]; ok {
		//	tmp.HostPort = virtual.HostPort
		//	tmp.Host = virtual.Host
		//	tmp.Port = virtual.Port
		//	tmp.SshUser = virtual.SshUser
		//	tmp.SshPassword = virtual.SshPassword
		//	tmp.GpuModelId = virtual.GpuModelId
		//	tmp.PodIds = virtual.PodIds
		//	tmp.Status = virtual.Status
		//} else {
		//	d.Virtuals[virtual.ID] = &virtual
		//}
	}

	logger.Info(pre, "将不在列表的Virtual状态设置为0 len(mVirtual):", len(mVirtual))
	for _, virtual := range d.GetVirtuals() {
		if dataOk { //只有是从服务器重新载入的做软移除处理
			if _, ok := mVirtual[virtual.ID]; !ok {
				logger.Infof("虚拟机%s(%d)不在载入队列，状态置为0", virtual.Host, virtual.ID)
				//virtual.Status = 0
				d.SetVirtualStatus(virtual.ID, 0)
			}
		}
		if d.FirstReport == false {
			queueItem := QueueItem{Action: enums.QueueActionEnum.InitVirtualBatch, Data: virtual.ID}
			d.NeedInitCount++
			if VirtualInitQueue.Enqueue(queueItem) == false {
				d.NeedInitCount--
			}
			logger.Info(pre, "加入初始化队列VirtualId:", virtual.ID, " Host:", virtual.Host, "VirtualInitQueue len:", VirtualInitQueue.Size())
		}
	}
	logger.Info(pre, "检查需要移除的virtual3")
	if d.LenVirtuals() == 0 {
		if dataOk {
			return nil
		}
		err := errors.New("载入Virtual数据失败")
		logger.Error(err)
		return err
	} else {
		return nil
	}
}

func (d *_node) StartInitVirtualsQueue() error {

	worker := func(i int) {
		defer func() {
			if e := recover(); e != nil {
				logger.Error("StartInitVirtualsQueue奔溃:", e)
				stack := make([]byte, 4096)
				runtime.Stack(stack, false)
				logger.Error("StartInitVirtualsQueue panicked:", e, "\nStack Trace:\n", string(stack))
			}
		}()
		time.Sleep(time.Second * time.Duration(i))
		logger.Info(i, "号线程 StartInitVirtualsQueue启动")
		for {
			item := VirtualInitQueue.Dequeue()
			if item != nil {
				queueItem := item.(QueueItem)
				logger.Info(i, "号携程开始执行queueItem：", utils.GetJsonFromStruct(queueItem))
				if queueItem.Action == enums.QueueActionEnum.InitVirtualBatch {
					if err := d.InitVirtualThread(queueItem.Data.(uint)); err != nil {
						logger.Error(err)
					}
				} else if queueItem.Action == enums.QueueActionEnum.InitVirtual {
					if err := d.InitVirtual(queueItem.Data.(uint), false); err != nil {
						logger.Error(err)
					}
				} else if queueItem.Action == enums.QueueActionEnum.InitVirtualAndReport {
					if err := d.InitVirtual(queueItem.Data.(uint), true); err != nil {
						logger.Error(err)
					}
				} else if queueItem.Action == enums.QueueActionEnum.CheckInstanceLive {
					if err := d.CheckInstanceLiveThread(queueItem.Data.(uint)); err != nil {
						logger.Error(err, "   queueItem:", utils.GetJsonFromStruct(queueItem))
					}
				} else if queueItem.Action == enums.QueueActionEnum.ReportLocalImages {
					if err := d.ReportLocalImagesThread(queueItem.Data.(uint)); err != nil {
						logger.Error(err, "   queueItem:", utils.GetJsonFromStruct(queueItem))
					}
				}
				logger.Info(i, "号携程执行完成queueItem：", utils.GetJsonFromStruct(queueItem))
			}
			if VirtualInitQueue.Size() == 0 {
				logger.Info(i, "号携程开始进入等待")
				VirtualInitQueue.Cond.L.Lock()
				logger.Info(i, "号携程开始等待")
				VirtualInitQueue.Cond.Wait()
				logger.Info(i, "号携程等待开始解锁")
				VirtualInitQueue.Cond.L.Unlock()
				logger.Info(i, "号携程等待解锁完成")
				//logger.Info(i, "队列空了，延迟5秒等待")
				//time.Sleep(time.Second * 5)
			}
		}
		logger.Info(i, "号线程 StartInitVirtualsQueue已经退出")
	}
	go worker(1)

	go worker(2)

	go worker(3)
	return nil
}

func (d *_node) InitVirtualThread(virtualId uint) error {
	funUuid := ""
	defer func() {
		if e := recover(); e != nil {
			logger.Error(funUuid, " InitVirtualThread奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error(funUuid, " panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	funUuid = "Fun_" + utils.GetUUID()

	logger.Info(funUuid, " InitVirtualThread VirtualId:", virtualId)

	defer func() {
		d.NeedInitCount--
		if d.NeedInitCount == 0 {
			msg := "开始上报节点信息"
			if d.FirstReport == false {
				d.FirstReport = true
				d.ContinueLoopAppStart()
				msg = "第一次上报节点信息"
			}
			suc := Clear.ReportNodeInfo()
			logger.Info(funUuid, msg+" suc:", suc)
		}
		logger.Info(funUuid, " InitVirtualThread Complete VirtualId:", virtualId, " NeedInitCount:", d.NeedInitCount)
	}()

	return d.InitVirtual(virtualId, true)
}

func (d *_node) InitVirtual(virtualId uint, report bool) error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("InitVirtual奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	logger.Infof("开始初始化虚拟机 VirtualId:%d", virtualId)

	command := fmt.Sprintf("InitVirtualThread")
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	NodeService.CommandRunning.Store(commandKey, structs.RunningCommand{VirtualId: virtualId, VirtualHost: "", Command: command, StartTime: jsontime.Now()})
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	if virtual, ok := d.GetVirtual(virtualId); ok {
		NodeService.CommandRunning.Store(commandKey, structs.RunningCommand{VirtualId: virtualId, VirtualHost: virtual.Host, Command: command, StartTime: jsontime.Now()})
		if report {
			defer func() {
				go func() {
					if virtual.Report() == false {
						logger.Error(virtual.HostPort, "初始化状态:", virtual.Inited(), " Virtual信息上报失败")
					} else {
						logger.Info(virtual.HostPort, "初始化状态:", virtual.Inited(), " Virtual信息上报成功")
					}
				}()
			}()
		}
		if virtual.Status == 1 || virtual.Status == 2 {
			logger.Info("开始初始化", virtual.Host)

			if virtual.Inited() == false || virtual.TimeoutAt > 0 {

				if _, err := virtual.SshDial(); err != nil {
					if virtual.TimeoutAt > 0 {
						logger.Info("开始初始化", virtual.Host, " 超时：", virtual.TimeoutAt)
						if virtualP, ok := d.GetVirtualP(virtual.ID); ok {
							virtualP.SetTimeoutAt(virtual.TimeoutAt)
							logger.Info("开始初始化", virtual.Host, " 超时设置成功：", virtual.TimeoutAt)
						} else {
							logger.Info("开始初始化", virtual.Host, " 超时未设置：", virtual.TimeoutAt)
						}
					}
					logger.Error("初始化", virtual.Host, " Ssh链接失败err:", err)
					return err
				} else {
					logger.Info("开始初始化", virtual.Host, "Ssh链接成功")
				}
			}
			defer func() {
				virtual.CloseSsh("InitVirtualThread")
				virtual.SshClient = nil
			}()

			if virtual.TimeoutAt > 0 {
				logger.Info("开始初始化", virtual.Host, " 超时：", virtual.TimeoutAt)
				if virtualP, ok := d.GetVirtualP(virtual.ID); ok {
					virtualP.SetTimeoutAt(virtual.TimeoutAt)
					logger.Info("开始初始化", virtual.Host, " 超时设置成功：", virtual.TimeoutAt)
				} else {
					logger.Info("开始初始化", virtual.Host, " 超时未设置：", virtual.TimeoutAt)
				}
			}

			if err := virtual.Init(false); err != nil {
				logger.Error("初始化失败 host:", virtual.Host, err)
				return err
			} else {
				if virtualP, ok := d.GetVirtualP(virtualId); ok {
					virtualP.SetVirtual(virtual)
					logger.Info("初始化成功 host:", virtual.Host)
				} else {
					logger.Info("初始化失败，未找到VirtualId:", virtualId, " host:", virtual.Host)
				}
			}
		} else {
			logger.Info("不初始化", virtual.Host, "   Status:", virtual.Status)
		}
	}
	return nil
}

func (d *_node) Static() (int, int, int, int, error) {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("Static奔溃:", e)
		}
	}()

	totalVirtual := d.LenVirtuals()
	totalInstance := 0
	totalGpus := 0
	freeGpus := 0
	for _, virtual := range d.GetVirtuals() {
		totalInstance += len(virtual.GetDockers())
		totalGpus += len(virtual.GetGpus())
		freeGpus += virtual.GpuFrees
	}
	return totalVirtual, totalInstance, totalGpus, freeGpus, nil
}

func (d *_node) CheckInstanceLiveThread(virtualId uint) error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("CheckLiveThread奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("CheckLiveThread panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	command := fmt.Sprintf("CheckLiveThread virtualId:%d", virtualId)
	commandKey := utils.GetUUID()
	if commandKey == "" {
		logger.Error("commandKey为空 command：", command)
	}
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	NodeService.CommandRunning.Store(commandKey, structs.RunningCommand{VirtualId: virtualId, VirtualHost: "", Command: command, StartTime: jsontime.Now()})
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	if virtual, ok := d.GetVirtual(virtualId); ok {
		if virtual.Status == 0 {
			logger.Info("下线状态，不做实例检测 host:", virtual.Host, " Status:", virtual.Status)
			return nil
		}

		//if virtual.Status == 2 {
		//	logger.Info("暂停状态，不做实例检测 host:", virtual.Host, " Status:", virtual.Status)
		//	return nil
		//}

		if virtual.TimeoutAt > 0 {
			logger.Info("超时状态，不做实例检测 host:", virtual.Host, " Status:", virtual.Status)
			return nil
		}

		if virtual.Inited() == false {
			logger.Info("未初始化，不做实例检测 host:", virtual.Host, " Status:", virtual.Status)
			return nil
		}

		//if virtual.Inited() == false {
		//	err := errors.New("Virtual 未初始化")
		//	logger.Error("err:", err, " virtualId:", virtual.ID, "   host:", virtual.Host)
		//	return err
		//}

		var removeDockers []DockerItem
		for i := 0; i < len(virtual.Dockers); i++ {
			docker := virtual.Dockers[i]
			trace := fmt.Sprintf("开始检测存活状态 实例:%s  startupMark:%s  dockerId:%s ", docker.InstanceUuid, docker.StartupMark, docker.ID)
			logger.Info(trace)
			if docker.CreatedAt.After(time.Now().Add(-time.Minute)) {
				logger.Info(trace, "未到检测时间")
				continue
			}

			if liveState, _, err := MasterService.InstanceLiveByStartupMark(docker.StartupMark); err != nil {
				logger.Error(trace, err)
			} else {
				ctx := context.Background()
				if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, docker.StartupMark); err != nil {
					logger.Error(trace, err)
				} else {
					ctx = context.WithValue(ctx, "logkey", logKey)
					//ctx = context.WithValue(ctx, "task", tasklog.TaskEnum.StartupMark)
				}

				logger.Info(trace, " liveState:", liveState)
				if liveState == "remove" {
					tasklog.Save(ctx, "", "", fmt.Sprintf("需要手动移除Docker liveState:%s %s ", liveState, trace), nil)
					removeDockers = append(removeDockers, docker)
				}
				{
					if virtualP, ok := d.GetVirtualP(docker.VirtualId); ok {
						b := virtualP.SetDockerLastStateCheck(docker.StartupMark)
						logger.Info("设置LastStateCheck startupMark:", docker.StartupMark, "b:", b, "   virtualId:", virtualId, " i:", i)
					}
				}
			}
		}

		if len(removeDockers) > 0 {
			go func() {
				body := "容器存在但是实例已关闭\n"
				body += "检查时间：" + jsontime.Now().String() + "\n"
				for _, docker := range removeDockers {
					body += fmt.Sprintf(" 虚拟机:%s(%d) 实例:%s 启动标记:%s 容器ID:%s \n", virtual.Host, virtual.ID, docker.InstanceUuid, docker.StartupMark, docker.ID)
				}
				logKey := "CheckInstanceLive_VirtualId_" + utils.Uint2String(virtualId)
				EmailService.AddNeedSend(logKey, body)
			}()

			if _, err := d.RmStopDockers(nil, virtualId, removeDockers); err != nil {
				logger.Error(fmt.Sprintf("检测移除Docker失败 virtualId:%d dockerIds:%d err:", virtualId, len(removeDockers)), err)
			} else {
				logger.Info(fmt.Sprintf("检测移除Docker成功 virtualId:%d dockerIds:%d", virtualId, len(removeDockers)))
			}
		}
		return nil
	} else {
		err := errors.New("Virtual 不存在")
		logger.Error("err:", err, " virtualId:", virtualId)
		return err
	}
}

func (d *_node) ReportLocalImagesThread(virtualId uint) error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("ReportLocalImagesThread奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("ReportLocalImagesThread panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()

	command := fmt.Sprintf("ReportLocalImagesThread virtualId:%d", virtualId)
	commandKey := utils.GetUUID()
	if commandKey == "" {
		logger.Error("commandKey为空 command：", command)
	}
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	NodeService.CommandRunning.Store(commandKey, structs.RunningCommand{VirtualId: virtualId, VirtualHost: "", Command: command, StartTime: jsontime.Now()})
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	if virtual, ok := d.GetVirtual(virtualId); ok {
		ctx := context.Background()
		//if _, err := virtual.SshDial(); err != nil {
		//	logger.Error(err)
		//}
		defer virtual.CloseSsh("ReportLocalImagesThread")
		if err := virtual.ReportLocalImages(ctx); err != nil {
			logger.Error(virtual.Host, "(", virtual.ID, ")上报本地镜像失败 err：", err)
			return err
		} else {
			if virtualP, ok := d.GetVirtualP(virtual.ID); ok {
				virtualP.SetImageIds(virtual.ImageIds)
				return nil
			} else {
				msg := "刷新成功,上报本地镜像完成,设置失败"
				logger.Error(msg, virtual.Host, "(", virtual.ID, ")")
				return errors.New(msg)
			}
		}
	} else {
		msg := "虚拟机不存在"
		logger.Error(msg, virtual.Host, "(", virtual.ID, ")")
		return errors.New(msg)
	}
}

func (d *_node) Startup(ctx context.Context, instanceUuid string, podId uint, imageId uint, virtualId uint, gpuModelId uint, needGpus int, userPath string, parm structs.StartupParm) (uint, string, error) {
	//instKey := "inst:" + instanceUuid
	logger.Info("startup instance %s ...", instanceUuid)
	startupMark := utils.GetUUID()
	trace := fmt.Sprintf("生成启动标记startupMark:%s podId:%d virtualId:%d gpuModelId:%d needGpus:%d userPath:%s ", startupMark, podId, virtualId, gpuModelId, needGpus, userPath)
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", trace, nil)

	cancelKey := ""
	ctxStartupMark, cancel := context.WithCancel(context.Background())
	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, startupMark); err != nil {
		logger.Error(err)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("startupMark err:%s", err.Error()), nil)
	} else {
		cancelKey = logKey
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("startupMark LogKey:%s", logKey), nil)
		ctxStartupMark = context.WithValue(ctxStartupMark, "logkey", logKey)
		ctxStartupMark = context.WithValue(ctxStartupMark, "task", tasklog.TaskEnum.StartupMark)
		ctxStartupMark = context.WithValue(ctxStartupMark, "startup_mark", startupMark)
	}

	elapse := 120
	var pod PodItem
	if podValue, ok := d.Pods.Load(podId); !ok {
		err := errors.New("Pod不存在")
		logger.Error(trace, err)
		return 0, "", err
	} else {
		pod = podValue.(PodItem)
		elapse = int(pod.StartupElapse)
	}
	if elapse < 120 {
		elapse = 120
	}
	if lockedVirtualId, msg, err := d.PickOneVirtualAndLockGpu(ctxStartupMark, instanceUuid, startupMark, virtualId, pod, imageId, gpuModelId, needGpus, parm); err != nil {
		logger.Error(trace, err, msg)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "查找可用虚拟机失败", fmt.Sprintf("查找可用虚拟机失败 msg:%s  err:%s", msg, err.Error()), nil)
		return 0, startupMark, err
	} else {
		go func() {
			d.CancelFuncs.Store(cancelKey, cancel)
			defer d.CancelFuncs.Delete(cancelKey)
			tasklog.Save(ctxStartupMark, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("开始启动容器 ctxStartupMark:%s", utils.GetJsonFromStruct(ctxStartupMark)), nil)
			startUnix := time.Now().Unix()
			if docker, err := d.RunDocker(ctxStartupMark, instanceUuid, startupMark, podId, imageId, lockedVirtualId, gpuModelId, needGpus, userPath, parm); err != nil {
				logger.Error(trace, msg, err)
				errMsg := err.Error()
				ctxLog := context.Background()

				if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, startupMark); err != nil {
					logger.Error(err)
				} else {
					ctxLog = context.WithValue(ctxLog, "logkey", logKey)
					tasklog.Save(ctxLog, "", "", fmt.Sprintf("容器启动失败处理逻辑 startupMark:%s", startupMark), nil)
				}

				reason := tasklog.ScanErrReasonByCtx(ctxLog)
				ginH, err1 := MasterService.ReportStartupFail(virtualId, startupMark, reason) //上报容器启动失败
				if err1 != nil {
					logger.Error(err, "ginH:", utils.GetJsonFromStruct(ginH))
					errMsg += " " + err1.Error()
				}

				if reason != "" {
					reason = "(" + reason + ")"
				}
				tasklog.Save(ctxLog, tasklog.NormalTaskStateEnum.Fail, "容器启动失败"+reason, fmt.Sprintf("容器启动失败上报结果: %s %s", utils.GetJsonFromStruct(ginH), errMsg), nil)
				elapseSeconds := time.Now().Unix() - startUnix
				//if !strings.Contains(reason, "主动取消") || elapseSeconds > 60 {
				//	elapseStr := "耗时：" + utils.Seconds2Time(elapseSeconds)
				//	body := tasklog.StartupMarkContent(startupMark)
				//	emailReq := EmailReq{
				//		Subject: "容器启动失败" + reason + elapseStr + " startupMark:" + startupMark,
				//		Content: time.Now().Format(jsontime.TimeFormat) + " " + body,
				//	}
				//	EmailService.SendWarn(emailReq)
				//}
				dockerId := ""
				virtualHost := ""
				if docker != nil {
					dockerId = docker.ID
					virtualHost = docker.VirtualHost
				}

				if !strings.Contains(reason, "主动取消") || elapseSeconds > 60 {
					if virtualHost == "" {
						if tmpVirtual, ok := d.GetVirtual(lockedVirtualId); ok {
							virtualHost = tmpVirtual.Host
						}
					}

					elapseStr := utils.Seconds2Time(elapseSeconds)

					// 准备容器启动失败邮件数据
					failureData := ContainerFailureEmailData{
						FailureType:  "容器启动失败",
						PodTitle:     pod.Title,
						AlarmTime:    time.Now().Format(jsontime.TimeFormat),
						StartupMark:  startupMark,
						InstanceUuid: instanceUuid,
						DockerID:     dockerId,
						NodeID:       config.NodeId,
						VirtualID:    lockedVirtualId,
						VirtualHost:  virtualHost,
						PodID:        pod.ID,
						ImageID:      imageId,
						ElapsedTime:  elapseStr,
						StartupLog:   tasklog.StartupMarkContentFormatted(startupMark),
					}

					// 生成HTML格式的邮件内容
					htmlContent, htmlErr := EmailService.generateContainerFailureEmailHTML(failureData)
					if htmlErr != nil {
						logger.Error("生成容器启动失败HTML邮件内容失败:", htmlErr)
					} else {
						// 发送HTML格式邮件
						emailReq := EmailReq{
							From:        "",
							To:          config.AlarmEmailAddr,
							Subject:     fmt.Sprintf("容器启动失败%s，耗时%s startupMark:%s ip:%s", reason, elapseStr, startupMark, utils.GetLocalIP()),
							Content:     htmlContent,
							ContentType: common.EmailContentTypeHtml,
						}
						if sendErr := EmailService.SendFromServiceWitContentType(emailReq); sendErr != nil {
							logger.Error("发送HTML格式容器启动失败邮件失败:", sendErr)
						}
					}
				}

				tasklog.Save(ctxLog, tasklog.NormalTaskStateEnum.Progress, "容器启动失败"+reason, fmt.Sprintf("err:%s", errMsg), nil)
				if b, err := NodeService.RmStopDocker(ctxLog, lockedVirtualId, startupMark, dockerId); err != nil {
					logger.Error(trace, "移除停止容器出错 err:", err)
					tasklog.Save(ctxLog, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("容器启动失败 移除停止docker出错 startupMark：%s virtualId：%d dockerId：%s err:%s", startupMark, virtualId, dockerId, err.Error()), nil)
				} else {
					logger.Info(trace, "移除停止容器完成 b:", b)
					if b {
						tasklog.Save(ctxLog, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("容器启动失败 移除停止docker成功 startupMark：%s virtualId：%d dockerId：%s", startupMark, virtualId, dockerId), nil)
					} else {
						tasklog.Save(ctxLog, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("容器启动失败 移除停止docker失败 startupMark：%s virtualId：%d dockerId：%s", startupMark, virtualId, dockerId), nil)
					}
				}
				tasklog.Save(ctxLog, tasklog.NormalTaskStateEnum.Progress, "容器启动失败"+reason, "容器启动失败逻辑执行完成", nil)
			} else {
				d.LoopAppStart(ctxStartupMark, docker.StartupMark, docker.VirtualId)
			}
		}()
		return lockedVirtualId, startupMark, nil
	}
}

func (d *_node) ContinueLoopAppStart() {
	var appStartLoopItem AppStartLoopItem
	if arr, err := appStartLoopItem.ListAll(); err != nil {
		logger.Error(err)
	} else {
		logger.Info(len(arr), "条循环检测App启动")
		for _, tmp := range arr {
			logger.Info("开始继续循环检测App启动", tmp.StartupMark, "    ", tmp.VirtualId)

			ctxStartupMark := context.Background()
			if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, tmp.StartupMark); err != nil {
				logger.Error(err)
			} else {
				ctxStartupMark = context.WithValue(ctxStartupMark, "logkey", logKey)
				ctxStartupMark = context.WithValue(ctxStartupMark, "task", tasklog.TaskEnum.StartupMark)
			}
			d.LoopAppStart(ctxStartupMark, tmp.StartupMark, tmp.VirtualId)
		}
	}
}

func (d *_node) LoopAppStart(ctx context.Context, startupMark string, virtualId uint) {
	logger.Info("loop app start %s ... ", startupMark)
	go func() {
		defer func() {
			var appStartLoopItem AppStartLoopItem
			logger.Info("移除 appStartLoopItem ", startupMark)
			err := appStartLoopItem.RemoveAppStartLoop(startupMark)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("RemoveAppStartLoop err:%s", utils.GetJsonFromStruct(err)), nil)
		}()
		trace := fmt.Sprintf("LoopAppStart startupMark:%s  virtualId:%d", startupMark, virtualId)
		logger.Info(trace)
		logger.Info("find docker ...")
		if docker, err := d.FindDocker(virtualId, startupMark); err != nil {
			logger.Info("err in FindDocker[%s:%s]: %v", virtualId, startupMark, err)
			logger.Error(trace, err)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("DockerItem未找到startupMark:"+startupMark+" err:%s", err.Error()), nil)
		} else {
			logger.Info("already find docker ")
			podTitle := ""
			if podValue, ok := d.Pods.Load(docker.PodId); ok {
				podTitle = podValue.(PodItem).Title
			}
			logger.Info("already load pod")
			elapse := 150
			attachMsg := ""
			var appStartLoopItem AppStartLoopItem
			logger.Info("GetAppStartLoop ...")
			if err := appStartLoopItem.GetAppStartLoop(&appStartLoopItem, docker.StartupMark); err == nil {
				elapse = appStartLoopItem.LeaveCount
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在启动应用", fmt.Sprintf("从GetAppStartLoop 获取到检测次数:%d", elapse), nil)
			} else {
				if podValue, ok := NodeService.Pods.Load(docker.PodId); ok {
					pod := podValue.(PodItem)
					elapse = int(pod.StartupElapse / 2)
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在启动应用", fmt.Sprintf("从Pod 获取到检测次数:%d", elapse), nil)
				}
			}
			logger.Info("elapse: ", elapse)
			if elapse < 60 {
				elapse = 60
			}
			instanceUuid := docker.InstanceUuid
			responseSuccess := false
			lastI := 0
			elapseSeconds := int64(0)
			//checkUrl := GetCheckUrl(*docker)
			checkUrls := GetCheckUrls(*docker)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在启动应用", fmt.Sprintf("开始检测WebUrls:%s 最多检测%d次", utils.GetJsonFromStruct(checkUrls), elapse), nil)
			for i := 0; i < elapse; i++ {

				abortRedisKey := enums.RedisKeyEnum.LockKey + "StartupAbort:" + docker.StartupMark
				if abortTime, _ := common.RedisGet(abortRedisKey); abortTime != "" {
					attachMsg = "(主动取消)"
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在停止启动", abortRedisKey+" context canceled abortTime:"+abortTime, nil)
					break
				}
				appStartLoopItem.SaveAppStartLoop(docker.VirtualId, docker.StartupMark, elapse-i-1)

				if len(checkUrls) == 0 {
					logger.Error(trace, "checkUrls 为空，不做检测")
					time.Sleep(time.Second)
					responseSuccess = true
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在启动应用", "checkUrls 为空，不做检测", nil)
					break
				}
				logger.Info("find add nginx instance url: %+v", checkUrls)
				for _, checkUrl := range checkUrls {
					if resp, err := utils.Get(checkUrl, nil, nil); err != nil {
						//logger.Error(trace, "i:", i, " ", msg, err)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在启动应用", fmt.Sprintf("第%d次检查实例地址%s，还在启动中", i, checkUrl), nil)
					} else {
						responseSuccess = true
						logger.Info(trace, "第", i, "次checkUrl请求成功", resp.Status)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在启动应用", fmt.Sprintf("第%d次检查实例地址%s成功 resp.status:%s", i, checkUrl, resp.Status), nil)
						break
					}
				}
				if responseSuccess {
					break
				}

				lastI++
				time.Sleep(time.Second * 2)
				elapseSeconds += 2
			}
			logger.Info("start add nginx instance ...")
			if responseSuccess {
				for i := 0; i < 3; i++ {
					if str, err := NginxAddInstance(virtualId, instanceUuid); err != nil {
						logger.Error(trace, str, err)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在启动应用", fmt.Sprintf("第%d次映射地址失败 str：%s err：%s  lockedVirtualId：%d startupMark:%s instanceUuid:%s ", i, str, err, virtualId, startupMark, instanceUuid), nil)

					} else {
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在启动应用", fmt.Sprintf("第%d次映射地址完成 %s", i, str), nil)
						break
					}
					time.Sleep(time.Second * 2)
				}

				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在启动应用", fmt.Sprintf("共检查了%d次", lastI), nil)
				logger.Info(trace, "开始上报结果")
				ginH, err := MasterService.ReportStartupSuccess(virtualId, startupMark)
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Success, "实例启动成功", fmt.Sprintf("启动成功上报结果: %s %s", utils.GetJsonFromStruct(ginH), utils.GetJsonFromStruct(err)), nil)
				logger.Info(trace, "启动成功上报结果:", ginH, "   ", err)
			} else {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, "应用启动失败"+attachMsg, fmt.Sprintf("共检查了%d次", lastI), nil)
				trace += fmt.Sprintf("%s(%d)应用启动失败 开始移除dockerId: %s", docker.VirtualHost, docker.VirtualId, docker.ID)
				logger.Error(trace)

				{
					ginH, err := MasterService.ReportStartupFail(virtualId, startupMark, "应用启动失败"+attachMsg)
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, "应用启动失败"+attachMsg, fmt.Sprintf("应用启动失败上报结果: %s %s", utils.GetJsonFromStruct(ginH), utils.GetJsonFromStruct(err)), nil)
				}

				ctxStartupMark := context.Background()
				if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, startupMark); err != nil {
					logger.Error(err)
				} else {
					ctxStartupMark = context.WithValue(ctxStartupMark, "logkey", logKey)
				}

				if b, err := d.RmStopDocker(ctxStartupMark, virtualId, startupMark, docker.ID); err != nil {
					logger.Error(trace, " 移除停止出错 err:", err)
					tasklog.Save(ctxStartupMark, tasklog.NormalTaskStateEnum.Progress, "应用启动失败"+attachMsg, fmt.Sprintf("应用启动失败 移除停止docker出错 startupMark：%s virtualId：%d dockerId：%s err:%s", startupMark, virtualId, docker.ID, err.Error()), nil)
				} else {
					logger.Error(trace, "应用启动失败 b:", b)
					if b {
						tasklog.Save(ctxStartupMark, tasklog.NormalTaskStateEnum.Progress, "应用启动失败"+attachMsg, fmt.Sprintf("应用启动失败 移除停止docker成功 startupMark：%s virtualId：%d dockerId：%s", startupMark, virtualId, docker.ID), nil)
					} else {
						tasklog.Save(ctxStartupMark, tasklog.NormalTaskStateEnum.Progress, "应用启动失败"+attachMsg, fmt.Sprintf("应用启动失败 移除停止docker失败 startupMark：%s virtualId：%d dockerId：%s", startupMark, virtualId, docker.ID), nil)
					}
				}
				if !strings.Contains(attachMsg, "主动取消") || elapseSeconds > 25 {
					elapseStr := utils.Seconds2Time(elapseSeconds)

					// 准备应用启动失败邮件数据
					failureData := ContainerFailureEmailData{
						FailureType:  "应用启动失败",
						PodTitle:     podTitle,
						AlarmTime:    time.Now().Format(jsontime.TimeFormat),
						StartupMark:  docker.StartupMark,
						InstanceUuid: docker.InstanceUuid,
						DockerID:     docker.ID,
						NodeID:       config.NodeId,
						VirtualID:    docker.VirtualId,
						VirtualHost:  docker.VirtualHost,
						PodID:        docker.PodId,
						ImageID:      docker.ImageId,
						ElapsedTime:  elapseStr,
						StartupLog:   tasklog.StartupMarkContentFormatted(startupMark),
					}

					// 生成HTML格式的邮件内容
					htmlContent, htmlErr := EmailService.generateContainerFailureEmailHTML(failureData)
					if htmlErr != nil {
						logger.Error("生成应用启动失败HTML邮件内容失败:", htmlErr)
					} else {
						// 发送HTML格式邮件
						emailReq := EmailReq{
							From:        "",
							To:          config.AlarmEmailAddr,
							Subject:     fmt.Sprintf("应用启动失败%s，耗时%s startupMark:%s ip:%s", attachMsg, elapseStr, startupMark, utils.GetLocalIP()),
							Content:     htmlContent,
							ContentType: common.EmailContentTypeHtml,
						}
						if sendErr := EmailService.SendFromServiceWitContentType(emailReq); sendErr != nil {
							logger.Error("发送HTML格式应用启动失败邮件失败:", sendErr)
						}
					}
				}
				//SendEmailPro("应用启动失败 startupMark:"+startupMark, body, 0)
				tasklog.Save(ctxStartupMark, tasklog.NormalTaskStateEnum.Fail, "应用启动失败"+attachMsg, fmt.Sprintf("应用启动失败 startupMark：%s virtualId：%d dockerId：%s", startupMark, virtualId, docker.ID), nil)
			}
		}
	}()
}

type pickVirutal struct {
	VirtualId uint   `json:"virtual_id"`
	Host      string `json:"host"`
	Score     int    `json:"score"`
}

func (d *_node) PickOneVirtualAndLockGpu(ctx context.Context, instanceUuid string, startupMark string, virtualId uint, pod PodItem, imageId uint, gpuModelId uint, needGpus int, parm structs.StartupParm) (uint, string, error) {
	//instKey := "inst:" + instanceUuid
	pre := fmt.Sprintf("instanceUuid:%s startupMark:%s nodeId:%d, virtualId:%d imageId:%d gpuModelId:%d  needGpus:%d", instanceUuid, startupMark, config.NodeId, virtualId, imageId, gpuModelId, needGpus)
	tasklog.Save(ctx, "", "正在查找可用资源", "开始查找可用显卡 "+pre, nil)
	lockKey := enums.RedisKeyEnum.LockKey + "PickOneVirtualByFreeGpu:node" + utils.Uint2String(config.NodeId)
	if common.RedisLock(lockKey, 1000*10, 1000*15) {
		defer common.RedisUnLock(lockKey)

		tasklog.Save(ctx, "", "正在查找可用资源", "开始查找可用显卡 ", nil)
		aryPickVirtual := make([]pickVirutal, 0)
		if virtualId > 0 {
			//nums = append(nums, virtualId)
			if virtual, ok := d.GetVirtual(virtualId); ok {
				aryPickVirtual = append(aryPickVirtual, pickVirutal{VirtualId: virtualId, Host: virtual.Host, Score: 0})
			} else {
				tasklog.Save(ctx, "", "", "指定虚拟机不在内存中，不可用 ", nil)
				return 0, "指定虚拟机不可用", errors.New("指定虚拟机不在内存中，不可用")
			}
		} else {
			for tmpVirtualId, virtual := range d.GetVirtuals() {
				//nums = append(nums, tmpVirtualId)
				score := utils.GenerateRandomInt(1, 9999)
				if strings.Contains(virtual.ImageIds, fmt.Sprintf("|%d|", imageId)) {
					//logger.Info(pre, fmt.Sprintf("镜像检查到 host:%s  imageId:%s  imageIds:%s", virtual.Host, fmt.Sprintf("|%d|", imageId), virtual.ImageIds))
					score += 10000
				} else {
					if imageValue, ok := d.PodImages.Load(imageId); ok {
						podImage := imageValue.(PodImageItem)
						if podImage.ParentId > 0 && strings.Contains(virtual.ImageIds, fmt.Sprintf("|%d|", podImage.ParentId)) {
							score += 9000
						}
					}
					//logger.Info(pre, fmt.Sprintf("镜像未检查到 host:%s  imageId:%s  imageIds:%s", virtual.Host, fmt.Sprintf("|%d|", imageId), virtual.ImageIds))
				}

				//if parm.InstanceType == enums.InstanceTypeEnum.Kol && virtual.PodIds == "" {
				//	tmp := fmt.Sprintf("虚拟机：%s(%d) Kol实例 PodIds为空%s，不加入队列 Score：%d", virtual.Host, virtual.ID, virtual.PodIds, score)
				//	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)
				//	continue
				//}

				if virtual.PodIds != "" {
					if strings.Contains(virtual.PodIds, "|kol|") { //kol专用
						if parm.InstanceType != enums.InstanceTypeEnum.Kol {
							continue
						}
					} else {
						checkStr := fmt.Sprintf("|%d|", pod.ID)
						if !strings.Contains(virtual.PodIds, checkStr) {
							tmp := fmt.Sprintf("虚拟机：%s(%d) PodId:%d 不在指定Pod中%s，不加入队列 Score：%d", virtual.Host, virtual.ID, pod.ID, virtual.PodIds, score)
							tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)
							continue
						}
					}
				}

				if pod.VirtualIds != "" {
					checkStr := fmt.Sprintf("|%d|", tmpVirtualId)
					if !strings.Contains(pod.VirtualIds, checkStr) {
						tmp := fmt.Sprintf("虚拟机：%s(%d) PodId:%d 不在指定Virtual中%s，不加入队列 Score：%d", virtual.Host, virtual.ID, pod.ID, pod.VirtualIds, score)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)
						continue
					}
				}

				aryPickVirtual = append(aryPickVirtual, pickVirutal{VirtualId: tmpVirtualId, Host: virtual.Host, Score: score})

			}
		}

		sort.Slice(aryPickVirtual, func(i, j int) bool {
			return aryPickVirtual[i].Score > aryPickVirtual[j].Score
		})

		l := len(aryPickVirtual)
		logger.Info(pre, "有", l, "个虚拟机可以检测：", utils.GetJsonFromStruct(aryPickVirtual))
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", pre+fmt.Sprintf(" 有%d个虚拟机可以检测 %s", l, utils.GetJsonFromStruct(aryPickVirtual)), nil)

		var lockedGpuIndexs = make([]int, 0)
		//now := time.Now()
		lockedVirtualId := uint(0)
		for j := 0; j < l; j++ {
			tmpVirtualId := aryPickVirtual[j].VirtualId
			score := aryPickVirtual[j].Score
			logger.Info(pre, "开始检测虚拟机：", aryPickVirtual[j].Host, "(", tmpVirtualId, ")")
			if virtual, ok := d.GetVirtual(tmpVirtualId); ok {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("开始检测虚拟机：%s(%d) Score：%d", virtual.Host, tmpVirtualId, score), nil)

				if needGpus > 0 && virtual.GpuModelId != gpuModelId {
					tmp := fmt.Sprintf("开始检测虚拟机：%s(%d) Gpus类型不匹配，继续下一个 Score：%d", virtual.Host, virtual.ID, score)
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)

					logger.Info(pre, tmp)
					continue
				}
				if !virtual.Inited() {
					tmp := fmt.Sprintf("开始检测虚拟机：%s(%d) 虚拟机未初始化，继续下一个 Score：%d", virtual.Host, virtual.ID, score)
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)
					logger.Info(pre, tmp)
					continue
				}
				if virtual.TimeoutAt > 0 {
					tmp := fmt.Sprintf("开始检测虚拟机：%s(%d) 虚拟机链接超时，继续下一个  Score：%d", virtual.Host, virtual.ID, score)
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)
					logger.Info(pre, tmp)
					continue
				}
				if virtual.Status != 1 {
					if virtualId > 0 && virtual.Status == 2 { //指定虚拟机暂停状态也能继续

					} else {
						tmp := fmt.Sprintf("开始检测虚拟机：%s(%d)虚拟机状态不可用，当前状态为%d，继续下一个 Score：%d", virtual.Host, virtual.ID, virtual.Status, score)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)
						logger.Info(pre, tmp)
						continue
					}
				}
				if needGpus == 0 {
					if len(virtual.Dockers) >= 16 {
						tmp := fmt.Sprintf("开始检测虚拟机：%s(%d) Docker数量过载needGpus:%d Docker数量:%d，继续下一个 Score：%d", virtual.Host, virtual.ID, needGpus, len(virtual.Dockers), score)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)
						logger.Info(pre, tmp)
						continue
					}
				} else if virtual.GpuFrees < needGpus {

					tmp := fmt.Sprintf("开始检测虚拟机：%s(%d) 可用Gpu数量不足needGpus:%d FreeGpus:%d，继续下一个 Score：%d", virtual.Host, virtual.ID, needGpus, virtual.GpuFrees, score)
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)
					logger.Info(pre, tmp)
					//StartupLog.Save(startupMark, fmt.Sprintf("正在查找可用节点"), fmt.Sprintf("Gpus数量不足：%d，virtualId:%d  %s", virtual.GpuFrees, virtual.ID, virtual.HostPort))
					continue
				}

				tmp := fmt.Sprintf("已找到符合条件的虚拟机：%s(%d) needGpus:%d FreeGpus:%d Score：%d", virtual.Host, virtual.ID, needGpus, virtual.GpuFrees, score)
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)
				logger.Info(pre, tmp)
				if needGpus == 0 {
					if err := virtual.LockGpus(ctx, startupMark, needGpus); err != nil {
						tmp = "无卡启动 锁定失败 err:" + err.Error()
						logger.Error(tmp)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)
						continue
					} else {
						tmp = "无卡启动，锁定成功"
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "已找到可用资源", tmp, nil)
					}
					lockedVirtualId = tmpVirtualId
				} else {
					if err := virtual.LockGpus(ctx, startupMark, needGpus); err != nil {
						tmp = " 锁定Gpu失败 err:" + err.Error()
						logger.Error(tmp)
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)
						continue
					} else {
						if virtualP, ok := d.GetVirtualP(virtual.ID); !ok {
							logger.Error(pre, " virtualP获取失败 virtualId:", virtual.ID)
						} else {
							if err := virtualP.RefreshFreeGpus(); err != nil {
								logger.Error(pre, " virtualP.RefreshFreeGpus virtualId:", virtual.ID, " err:", err)
							}
							if virtual.Report() == false {
								logger.Error(pre, " virtual.Report() virtualId:", virtual.ID)
							}
						}
						tmp = fmt.Sprintf("启动，锁定Gpu成功 lockedVirtualId:%d", tmpVirtualId)
						lockedVirtualId = tmpVirtualId
						tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "已找到可用资源", tmp, nil)
					}
				}
				logger.Info(pre, "开始检测虚拟机：", tmpVirtualId, " 锁定的虚拟机：", lockedVirtualId, "  锁定的Gpus:", lockedGpuIndexs)
				if lockedVirtualId > 0 {
					break
				}
			} else {
				//StartupLog.Save(instKey, "", fmt.Sprintf("虚拟机不在内存中：%s(%d) Score：%d", "", tmpVirtualId, score))
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("虚拟机不在内存中：%s(%d) Score：%d", aryPickVirtual[j].Host, tmpVirtualId, score), nil)
				logger.Info(pre, "虚拟机：", tmpVirtualId, "不在内存中")
			}
		}

		if lockedVirtualId <= 0 {
			err := errors.New("未找到空闲的虚拟机")
			logger.Error(pre, err)

			tmp := fmt.Sprintf("未找到符合条件的虚拟机：%s", pre)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)

			return lockedVirtualId, "未找到空闲的虚拟机", err
		}

		if needGpus == 0 {
			//锁定序号
			return lockedVirtualId, "", nil
		}

		if needGpus > 0 {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("锁定的虚拟机 lockedVirtualId：%d startupMark:%s", lockedVirtualId, startupMark), nil)
			return lockedVirtualId, "", nil
		}
		return lockedVirtualId, "", nil
	} else {
		tmp := fmt.Sprintf("%s Redis锁等待超时", pre)
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", tmp, nil)
		return 0, "等待超时，请稍后尝试", errors.New("等待超时，请稍后尝试")
	}
}

func (d *_node) RunDocker(ctx context.Context, instanceUuid string, startupMark string, podId uint, imageId uint, lockedVirtualId uint, gpuModelId uint, needGpus int, userPath string, parm structs.StartupParm) (*DockerItem, error) {
	code := 1
	dockerId := ""
	result := make(map[string]interface{})
	defer func() {
		//if code != 0 && dockerId != "" {
		//	//virtual.RmDocker(dockerId)
		//	if _, err := d.RmDocker(ctx, lockedVirtualId, dockerId); err != nil {
		//		logger.Error(err, "  startupMark:", startupMark)
		//	}
		//}
		//if code != 0 {
		//	d.UnlockGpuByStartupMark(lockedVirtualId, startupMark)
		//}
		//if err := virtual.CloseSsh(); err != nil {
		//	logger.Error(err)
		//}

	}()

	var pod PodItem
	if podValue, ok := d.Pods.Load(podId); !ok {
		//if _, ok := NodeService.Pods[podId]; !ok {
		logger.Error("Pod不存在", podId)
		return nil, errors.New("Pod不存在")
	} else {
		pod = podValue.(PodItem)
	}

	if imageId > 0 {
		if imageValue, ok := NodeService.PodImages.Load(imageId); !ok {
			//if _, ok := NodeService.PodImages[imageId]; !ok {
			logger.Error("Pod镜像不存在 imageId：", imageId)
			return nil, errors.New("Pod镜像不存在")
		} else {
			//pod.PodImage = NodeService.PodImages[imageId]
			pod.PodImage = imageValue.(PodImageItem)
		}
	}

	var virtual Virtual
	if tmpVirtual, ok := d.GetVirtual(lockedVirtualId); !ok {
		logger.Error("Virtual不存在 lockedVirtualId:", lockedVirtualId)
		return nil, errors.New("Virtual不存在")
	} else {
		virtual = tmpVirtual
	}

	if _, err := virtual.SshDial(); err != nil {
		logger.Error(err)
		return nil, err
	}
	defer func() {
		virtual.CloseSsh("RunDocker")
		//virtual.SshClient = nil
		//d.Virtuals[virtual.ID] = virtual
	}()

	if pod.Command == "" {

		if output, err := virtual.RunDockerUserApi(ctx, instanceUuid, startupMark, pod, needGpus, userPath, parm); err != nil {
			logger.Error(err)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "err:"+err.Error(), nil)
			return nil, err
		} else {
			result["docker_id"] = output
			dockerId = output[:12]
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "容器启动完成 dockerId："+dockerId+"  output:"+output, nil)
		}

	} else {

		if imageId > 0 && pod.PodImage.ImageType == enums.ImageTypeEnum.Private {

			if output, err := virtual.RunDockerUserApi(ctx, instanceUuid, startupMark, pod, needGpus, userPath, parm); err != nil {
				logger.Error(err)
				return nil, err
			} else {
				result["docker_id"] = output
				dockerId = output[:12]
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "容器启动完成 dockerId："+dockerId+"  output:"+output, nil)
			}

		} else {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", fmt.Sprintf("启动容器 非标准Pod podId：%d", pod.ID), nil)
			if output, err := virtual.RunDocker(ctx, pod, needGpus, instanceUuid, startupMark, userPath, parm); err != nil {
				logger.Error(err)
				return nil, err
			} else {
				result["docker_id"] = output
				dockerId = output[:12]
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", "容器启动完成 dockerId："+dockerId+"  output:"+output, nil)
			}
		}
	}
	logger.Info("启动完成，开始初始化重新载入数据", virtual.Host, "(", virtual.ID, ")", "  startupMark:", startupMark, " dockerId:", dockerId)
	if err := d.InitVirtual(virtual.ID, true); err != nil {
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", " 虚拟机初始化失败 err："+err.Error(), nil)
		logger.Error(err)
		return nil, err
	} else {
		if imageId > 0 && !strings.Contains(virtual.ImageIds, fmt.Sprintf("|%d|", imageId)) {
			if virtual.ImageIds == "" {
				virtual.ImageIds = fmt.Sprintf("|%d|", imageId)
			} else {
				virtual.ImageIds += fmt.Sprintf("%d|", imageId)
			}
			go func() {
				imageIds := []uint{imageId}
				if ginH, err := MasterService.ReportLocalNewImages(virtual.ID, imageIds); err != nil {
					logger.Error("上报本地新增镜像失败 ", err)
				} else {
					logger.Info("上报本地新增镜像成功", utils.GetJsonFromStruct(ginH))
				}
			}()
			logger.Info("添加新增ImageId:", imageId, "    imageIds:", virtual.ImageIds)
		}

		for _, docker := range d.GetVirtualDockers(virtual.ID) {
			if docker.ID == dockerId {
				//if !virtual.Report() {   d.InitVirtual已经设置了上报
				//	err := errors.New("虚拟机信息上报失败")
				//	logger.Error(err)
				//	return nil, err
				//}
				code = 0
				logger.Info("载入完成，并且找到DockerId", virtual.Host, "(", virtual.ID, ")", "  startupMark:", startupMark, " dockerId:", dockerId, " code:", code)
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动完成", "容器ID："+dockerId, nil)
				return &docker, nil
			}
		}
	}

	err := errors.New("虚拟机中未找到dockerId为" + dockerId + "的docker")
	logger.Error("err:", err, " host:", virtual.Host, "(", virtual.ID, ")", "  startupMark:", startupMark, " dockerId:", dockerId)
	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动中", " err："+err.Error(), nil)
	return nil, err
}

func (d *_node) FindDocker(virtualId uint, startupMark string) (*DockerItem, error) {
	trace := fmt.Sprintf("FindDocker %d %s ", virtualId, startupMark)
	if virtual, ok := d.GetVirtual(virtualId); ok {
		//if (virtual.Status == 1 || virtual.Status == 2) && !virtual.Inited() {
		//	err := errors.New("虚拟机还未初始化，请稍后操作")
		//	logger.Error(trace, err)
		//	return nil, err
		//}

		for _, docker := range virtual.GetDockers() {
			if docker.StartupMark == startupMark {
				return &docker, nil
			}
		}
		//logger.Info(trace, "Docker不存在 virtual:", utils.GetJsonFromStruct(virtual))
		logger.Info(trace, "Docker不存在 virtual:", virtualId, " startupMark:", startupMark)
		return nil, gorm.ErrRecordNotFound
	} else {
		err := errors.New("虚拟机不存在")
		logger.Error(trace, err)
		return nil, err
	}
}

func (d *_node) FindContainer(ctx context.Context, virtualId uint, startupMark string) (*DockerItem, error) { //DockerItem信息不完整，请注意使用
	trace := fmt.Sprintf("FindContainer %d %s ", virtualId, startupMark)
	if virtual, ok := d.GetVirtual(virtualId); ok {
		defer virtual.CloseSsh("FindContainer")
		if container, err := virtual.ContainerByStartupMarkUseApi(ctx, startupMark); err != nil {
			logger.Error(err)
			return nil, err
		} else {
			//container.Labels["startupMark"] = startupMark
			dockerItem := &DockerItem{
				ID:           container.ID,
				StartupMark:  startupMark,
				VirtualId:    virtualId,
				VirtualHost:  virtual.Host,
				MapPref:      container.Labels["map_pref"],
				InstanceUuid: container.Labels["instance_uuid"],
			}
			if utils.String2Uint(container.Labels["image_id"]) > 0 {
				dockerItem.ImageId = utils.String2Uint(container.Labels["image_id"])
			}
			if utils.String2Int(container.Labels["image_type"]) > 0 {
				dockerItem.ImageType = utils.String2Int(container.Labels["image_type"])
			}

			if utils.String2Int(container.Labels["pod_category"]) > 0 {
				dockerItem.PodCategory = utils.String2Int(container.Labels["pod_category"])
			}
			if utils.String2Uint(container.Labels["pod_id"]) > 0 {
				dockerItem.PodId = utils.String2Uint(container.Labels["pod_id"])
			}
			if container.Labels["gpus"] != "" {
				gpus := make([]int, 0)
				ary1 := strings.Split(container.Labels["gpus"], "_")
				for _, v := range ary1 {
					if v == "" {
						continue
					}
					gpus = append(gpus, utils.String2Int(v))
				}
				dockerItem.Gpus = gpus
			}
			if container.Labels["startup_parm"] != "" {
				var tmp structs.StartupParm
				if err1 := utils.GetStructFromJson(&tmp, container.Labels["startup_parm"]); err1 != nil {
					logger.Error(err1, startupMark, container.Labels["startup_parm"])
				} else {
					dockerItem.StartupParm = tmp
				}
			}
			if container.Labels["map_ports"] != "" {
				mapPorts := make([]string, 0)
				ary1 := strings.Split(container.Labels["map_ports"], "_")
				for _, v := range ary1 {
					mapPorts = append(mapPorts, v)
				}
				dockerItem.MapPorts = mapPorts
			}

			return dockerItem, nil
		}
	} else {
		err := errors.New("虚拟机不存在")
		logger.Error(trace, err)
		return nil, err
	}
}

func (d *_node) RmDockerFromMemory(ctx context.Context, virtualId uint, startupMark string, dockerId string) (bool, error) {
	trace := fmt.Sprintf("RmDockerFromMemory %d %s ", virtualId, dockerId)
	if virtualId == 0 {
		return true, nil
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		finded := false
		ary := make([]DockerItem, 0)
		for _, docker := range virtual.Dockers {

			if startupMark != "" && docker.StartupMark == startupMark {
				finded = true
				continue
			} else if dockerId != "" && docker.ID == dockerId {
				finded = true
				continue
			}
			ary = append(ary, docker)
		}
		if finded {
			virtual.Dockers = ary
			//d.Virtuals[virtualId] = virtual
			return true, nil
		}
		logger.Error(trace, "Docker在内存中不存在")
		return true, nil

	} else {
		err := errors.New("虚拟机内存中不存在")
		logger.Error(trace, err)
		return true, nil
	}
}

func (d *_node) RmDocker(ctx context.Context, virtualId uint, dockerId string) (bool, error) {
	trace := fmt.Sprintf("RmDocker %d %s ", virtualId, dockerId)
	if virtualId == 0 {
		return true, nil
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		for _, docker := range virtual.Dockers {
			if docker.ID == dockerId {

				if _, err := virtual.SshDial(); err != nil {
					logger.Error(trace, err)
					return false, err
				}
				defer virtual.CloseSsh("RmDocker")

				tasklog.Save(ctx, "", "", trace, nil)
				//if err := virtual.RmDockerUseApi(ctx, dockerId); err != nil {

				/*
					ports := make([]string, 0)
					if ary, err := virtual.PortDocker(ctx, dockerId); err != nil {
						logger.Error(err, "dockerId:", dockerId)
						tasklog.Save(ctx, "", "", "获取需要移除的端口出错 err:"+err.Error(), nil)
					} else {
						ports = ary
						tasklog.Save(ctx, "", "", "获取需要移除的端口:"+utils.GetJsonFromStruct(ports), nil)
						if len(ports) == 0 {
							if ary1, err := virtual.PortDockerItem(ctx, dockerId); err != nil {
								logger.Error(err)
								tasklog.Save(ctx, "", "", "从内存获取需要移除的端口出错:"+utils.GetJsonFromStruct(ports)+"  err:"+err.Error(), nil)
							} else {
								ports = ary1
								tasklog.Save(ctx, "", "", "从内存获取需要移除的端口:"+utils.GetJsonFromStruct(ports), nil)
							}
						}
					}*/

				if err := virtual.RmDocker(ctx, dockerId); err != nil {
					logger.Error(trace, err, "   ")
					return false, err
				} else {
					/*
						if err := virtual.ClearBindPort(ctx, ports); err != nil {
							logger.Error(err, " host:", docker.VirtualHost, "  startupMark:", docker.StartupMark, "   dockerId:", docker.ID, "   ports:", ports)
							tasklog.Save(ctx, "", "", "清除端口失败 err:"+err.Error(), nil)
						} else {
							tasklog.Save(ctx, "", "", "清除端口完成 ports:"+utils.GetJsonFromStruct(ports), nil)
						}*/

					if err := virtual.UnLockGpus(docker.StartupMark, "RmDocker"); err != nil {
						logger.Error(err, " host:", docker.VirtualHost, "  startupMark:", docker.StartupMark, "   dockerId:", docker.ID)
						tasklog.Save(ctx, "", "", "UnLockGpus失败 err:"+err.Error(), nil)
					} else {
						tasklog.Save(ctx, "", "", "UnLockGpus完成", nil)
					}

					if err := d.InitVirtual(virtual.ID, true); err != nil {
						logger.Error(err, " host:", docker.VirtualHost, "  startupMark:", docker.StartupMark, "   dockerId:", docker.ID)
						tasklog.Save(ctx, "", "移除Docker", "virtual.Init出错 err:"+err.Error(), nil)
					} else {
						logger.Info("移除Docker host:", docker.VirtualHost, "  startupMark:", docker.StartupMark, "   dockerId:", docker.ID)
						tasklog.Save(ctx, "", "移除Docker", "virtual.Init完成", nil)
					}

					//queueItem := QueueItem{Action: enums.QueueActionEnum.InitVirtual, Data: virtual.ID}
					//VirtualInitQueue.Enqueue(queueItem)
					return true, nil
				}
			}
		}
		return true, nil

	} else {
		err := errors.New("虚拟机缓存中不存在")
		logger.Error(trace, err)
		return true, nil
	}
}

func (d *_node) RmStopDockerTest(ctx context.Context, virtualId uint, startupMark string, dockerId string, test int) (bool, error) {

	for testI := 0; testI < test; testI++ {
		if b, err := d.RmStopDocker(ctx, virtualId, startupMark, dockerId); err != nil {
			if testI >= test {
				return b, err
			}
		} else {
			return b, err
		}
		time.Sleep(time.Second)
	}
	return false, errors.New("RmStopDockerTest")
}

func (d *_node) RmStopDocker111(ctx context.Context, virtualId uint, startupMark string, dockerId string) (bool, error) {
	trace := fmt.Sprintf("RmStopDocker %d %s ", virtualId, dockerId)
	if virtualId == 0 {
		return true, nil
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		for _, docker := range virtual.Dockers {
			if docker.ID == dockerId {

				//if _, err := virtual.SshDial(); err != nil {
				//	logger.Error(trace, err)
				//	return false, err
				//}
				defer virtual.CloseSsh("RmStopDocker")

				tasklog.Save(ctx, "", "", trace, nil)
				if err := virtual.StopDockerUseApi(ctx, dockerId); err != nil {
					logger.Error(trace, err, "   ")
					return false, err
				} else {

					tmpMsg := ""
					time.Sleep(time.Second)
					if container, err := virtual.ContainerByStartupMarkUseApi(ctx, docker.StartupMark); err != nil {
						tmpMsg = "确认容器是否停止成功 获取容器信息失败"
						logger.Error(tmpMsg, docker.StartupMark)
					} else {
						if container.State == enums.DockerStatusEnum.Running {
							tmpMsg = "确认容器是否停止成功 停止失败"
							logger.Error(tmpMsg, docker.StartupMark)
						} else {
							tmpMsg = "确认容器是否停止成功 已停止"
							logger.Info(tmpMsg, docker.StartupMark)

							checkPort := GetCheckPort(docker)
							if checkPort != "" {
								time.Sleep(time.Second)
								if container1, err1 := virtual.ContainerByPublishUseApi(ctx, checkPort); err1 != nil {
									if err1 == common.ErrRecordNotFound {
										tmpMsg = " 通过端口" + checkPort + "检测 端口已释放:" + err1.Error()
										logger.Info(tmpMsg, docker.StartupMark)
									} else {
										tmpMsg = " 通过端口" + checkPort + "检测 err:" + err1.Error()
										logger.Error(tmpMsg, docker.StartupMark)
									}
								} else {
									tmpMsg = " 通过端口" + checkPort + "检测 还被占用中 State:" + container1.State
									logger.Error(tmpMsg, docker.StartupMark)
									tasklog.Save(ctx, "", "", tmpMsg, nil)
									return false, errors.New("容器端口" + checkPort + "未释放")
								}
							}
						}
					}
					tasklog.Save(ctx, "", "", "开始UnLockGpus CacheShutdown "+tmpMsg, nil)

					if err := virtual.UnLockGpus(docker.StartupMark, "RmStopDocker"); err != nil {
						logger.Error(err, " host:", docker.VirtualHost, "  startupMark:", docker.StartupMark, "   dockerId:", docker.ID)
						tasklog.Save(ctx, "", "", "UnLockGpus失败 err:"+err.Error(), nil)
					} else {
						tasklog.Save(ctx, "", "", "UnLockGpus完成", nil)
					}

					if err := virtual.CacheShutdown(ctx, docker.StartupMark, docker.ID); err != nil {
						logger.Error("virtual.CacheShutdown err:", err, "  startupMark:", docker.StartupMark)
						tasklog.Save(ctx, "", "", "CacheShutdown失败 err:"+err.Error(), nil)
					} else {
						tasklog.Save(ctx, "", "", "CacheShutdown完成", nil)
					}

					//if err := d.InitVirtual(virtual.ID, true); err != nil {
					//	logger.Error(err, " host:", docker.VirtualHost, "  startupMark:", docker.StartupMark, "   dockerId:", docker.ID)
					//	tasklog.Save(ctx, "", "移除Docker", "virtual.Init出错 err:"+err.Error(), nil)
					//} else {
					//	logger.Info("移除Docker host:", docker.VirtualHost, "  startupMark:", docker.StartupMark, "   dockerId:", docker.ID)
					//	tasklog.Save(ctx, "", "移除Docker", "virtual.Init完成", nil)
					//}

					queueItem := QueueItem{Action: enums.QueueActionEnum.InitVirtualAndReport, Data: virtual.ID}
					VirtualInitQueue.Enqueue(queueItem)
					return true, nil
				}
			}
		}
		return true, nil

	} else {
		err := errors.New("虚拟机缓存中不存在")
		logger.Error(trace, err)
		return true, nil
	}
}

func (d *_node) RmStopDocker(ctx context.Context, virtualId uint, startupMark string, dockerId string) (bool, error) {
	trace := fmt.Sprintf("NodeRmStopDocker virtualId:%d startupMark:%s dockerId:%s", virtualId, startupMark, dockerId)
	logger.Info(ctx, "     ", trace)
	tasklog.Save(ctx, "", "", trace, nil)
	if virtualId == 0 {
		return false, errors.New("虚拟机ID为空")
	}
	if startupMark == "" {
		return false, errors.New("启动标记为空")
	}
	tasklog.Save(ctx, "", "", "开始尝试停止容器，解锁卡，放入历史容器缓存", nil)
	if virtual, ok := d.GetVirtual(virtualId); ok {
		defer virtual.CloseSsh("NodeRmStopDocker")
		if err := virtual.RmStopDocker(ctx, startupMark, dockerId); err != nil {
			tasklog.Save(ctx, "", "", trace+err.Error(), nil)
			return true, err
		} else {
			queueItem := QueueItem{Action: enums.QueueActionEnum.InitVirtualAndReport, Data: virtual.ID}
			VirtualInitQueue.Enqueue(queueItem)
			tasklog.Save(ctx, "", "", "加入InitVirtualAndReport队列", nil)
			return true, nil
		}
	} else {
		err := errors.New("虚拟机缓存中不存在")
		logger.Error(trace, err)
		tasklog.Save(ctx, "", "", err.Error(), nil)
		return false, err
	}
}

func (d *_node) RmStopDockers(ctx context.Context, virtualId uint, dockers []DockerItem) (bool, error) {
	trace := fmt.Sprintf("RmStopDockers %d %d ", virtualId, len(dockers))
	logger.Info(trace)
	if virtualId == 0 {
		return true, nil
	}
	if len(dockers) == 0 {
		return true, nil
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		defer virtual.CloseSsh("RmStopDockers")
		dockerIds := ""
		for _, docker := range dockers {
			trace1 := fmt.Sprintf(" RmStopDockers virtual:%s(%d) dockerId:%s startupMarK:%s instanceUuId:%s ", docker.VirtualHost, virtualId, docker.ID, docker.StartupMark, docker.InstanceUuid)
			ctxStartupMark := context.Background()
			if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, docker.StartupMark); err != nil {
				logger.Error(err)
			} else {
				ctxStartupMark = context.WithValue(ctxStartupMark, "logkey", logKey)
			}
			dockerId := docker.ID
			dockerIds += "," + dockerId
			logger.Info(trace1, "Start RmStopDockers")

			if err := virtual.RmStopDocker(ctxStartupMark, docker.StartupMark, dockerId); err != nil {
				logger.Error(trace1, err)
				tasklog.Save(ctxStartupMark, "", "", trace1+" err:"+err.Error(), nil)
			} else {
				tasklog.Save(ctxStartupMark, "", "", trace1+" 成功", nil)
			}
		}
		if err := d.InitVirtual(virtualId, true); err != nil {
			logger.Error(err, " host:", virtual.Host, "   dockerIds:", dockerIds)
			tasklog.Save(ctx, "", "批量停止Docker", "virtual.Init出错 err:"+err.Error(), nil)
		} else {
			logger.Info("批量移除Docker", " host:", virtual.Host, "   dockerIds:", dockerIds)
			tasklog.Save(ctx, "", "批量停止Docker", "virtual.Init完成", nil)
		}
		return true, nil
	} else {
		err := errors.New("虚拟机缓存中不存在")
		logger.Error(trace, err)
		return true, nil
	}
}

func (d *_node) RmStopDockers1111(ctx context.Context, virtualId uint, dockers []DockerItem) (bool, error) {
	trace := fmt.Sprintf("RmStopDockers %d %d ", virtualId, len(dockers))
	logger.Info(trace)
	if virtualId == 0 {
		return true, nil
	}
	if len(dockers) == 0 {
		return true, nil
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		//if _, err := virtual.SshDial(); err != nil {
		//	logger.Error(trace, err)
		//	return false, err
		//}
		defer virtual.CloseSsh("RmStopDockers")
		dockerIds := ""
		for _, docker := range dockers {
			trace1 := fmt.Sprintf(" RmStopDockers virtual:%s(%d) dockerId:%s startupMarK:%s instanceUuId:%s ", docker.VirtualHost, virtualId, docker.ID, docker.StartupMark, docker.InstanceUuid)
			ctxStartupMark := context.Background()
			if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, docker.StartupMark); err != nil {
				logger.Error(err)
			} else {
				ctxStartupMark = context.WithValue(ctxStartupMark, "logkey", logKey)
			}
			dockerId := docker.ID
			dockerIds += "," + dockerId
			logger.Info(trace1, "Start RmStopDockers")

			if err := virtual.StopDockerUseApi(ctxStartupMark, dockerId); err != nil {
				logger.Error(trace1, " err:", err)
				return false, err
			} else {
				if err := virtual.UnLockGpus(docker.StartupMark, "RmStopDockers"); err != nil {
					logger.Error(trace1, " err:", err, " host:", docker.VirtualHost, "  startupMark:", docker.StartupMark, "   dockerId:", docker.ID)
				} else {
					logger.Info(trace1, " Success UnLockGpus virtualId:", virtualId, "  dockerId:", dockerId, "  startupMark：", docker.StartupMark)
				}
				if err := virtual.CacheShutdown(ctx, docker.StartupMark, docker.ID); err != nil {
					logger.Error("virtual.CacheShutdown err:", err, "  startupMark:", docker.StartupMark)
					tasklog.Save(ctxStartupMark, "", "", "CacheShutdown失败 err:"+err.Error(), nil)
				} else {
					tasklog.Save(ctxStartupMark, "", "", "CacheShutdown完成", nil)
				}
			}
		}
		if err := d.InitVirtual(virtualId, true); err != nil {
			logger.Error(err, " host:", virtual.Host, "   dockerIds:", dockerIds)
			tasklog.Save(ctx, "", "批量停止Docker", "virtual.Init出错 err:"+err.Error(), nil)
		} else {
			logger.Info("批量移除Docker", " host:", virtual.Host, "   dockerIds:", dockerIds)
			tasklog.Save(ctx, "", "批量停止Docker", "virtual.Init完成", nil)
		}
		return true, nil
	} else {
		err := errors.New("虚拟机缓存中不存在")
		logger.Error(trace, err)
		return true, nil
	}
}

func (d *_node) RmDockers(ctx context.Context, virtualId uint, dockers []DockerItem) (bool, error) {
	trace := fmt.Sprintf("RmDockers %d %d ", virtualId, len(dockers))
	logger.Info(trace)
	if virtualId == 0 {
		return true, nil
	}
	if len(dockers) == 0 {
		return true, nil
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		//if _, err := virtual.SshDial(); err != nil {
		//	logger.Error(trace, err)
		//	return false, err
		//}
		if _, err := virtual.SshDial(); err != nil {
			logger.Error(trace, err)
			return false, err
		}
		defer virtual.CloseSsh("RmDockers")
		dockerIds := ""
		for _, docker := range dockers {
			trace1 := fmt.Sprintf(" RmDockers virtual:%s(%d) dockerId:%s startupMarK:%s instanceUuId:%s ", docker.VirtualHost, virtualId, docker.ID, docker.StartupMark, docker.InstanceUuid)
			ctxStartupMark := context.Background()
			if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, docker.StartupMark); err != nil {
				logger.Error(err)
			} else {
				ctxStartupMark = context.WithValue(ctxStartupMark, "logkey", logKey)
			}
			dockerId := docker.ID
			dockerIds += "," + dockerId
			logger.Info(trace1, "Start RmDocker")

			if err := virtual.RmDocker(ctxStartupMark, dockerId); err != nil {
				logger.Error(trace1, " err:", err)
				return false, err
			} else {
				/*
					if err := virtual.ClearBindPort(ctxStartupMark, ports); err != nil {
						logger.Error(trace1, " err:", err, " host:", docker.VirtualHost, "  startupMark:", docker.StartupMark, "   dockerId:", docker.ID, "   ports:", ports)
					}*/
				logger.Info(trace1, " Success RmDocker virtualId:", virtualId, "  dockerId:", dockerId, "  startupMark：", docker.StartupMark)
				if err := virtual.UnLockGpus(docker.StartupMark, "RmDockers"); err != nil {
					logger.Error(trace1, " err:", err, " host:", docker.VirtualHost, "  startupMark:", docker.StartupMark, "   dockerId:", docker.ID)
				} else {
					logger.Info(trace1, " Success UnLockGpus virtualId:", virtualId, "  dockerId:", dockerId, "  startupMark：", docker.StartupMark)
				}
			}
		}
		if err := d.InitVirtual(virtualId, true); err != nil {
			logger.Error(err, " host:", virtual.Host, "   dockerIds:", dockerIds)
			tasklog.Save(ctx, "", "批量移除Docker", "virtual.Init出错 err:"+err.Error(), nil)
		} else {
			logger.Info("批量移除Docker", " host:", virtual.Host, "   dockerIds:", dockerIds)
			tasklog.Save(ctx, "", "批量移除Docker", "virtual.Init完成", nil)
		}
		return true, nil
	} else {
		err := errors.New("虚拟机缓存中不存在")
		logger.Error(trace, err)
		return true, nil
	}
}

func (d *_node) StopDocker(virtualId uint, dockerId string) (bool, string, error) {
	trace := fmt.Sprintf("StopDocker %d %s ", virtualId, dockerId)
	msg := ""
	if virtualId == 0 {
		msg = "未指定虚拟机"
		return false, msg, errors.New(msg)
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		if virtual.DaemonClient == nil {
			s := fmt.Sprintf("daemon server[%s:%s] not initialized", virtual.Host, virtual.ID)
			return false, s, errors.New(s)
		}

		err := daemon_server.StopContainer(virtual.DaemonClient, dockerId)
		if err != nil {
			msg = fmt.Sprintf("Failed to stop container[%s]: %v\n", dockerId, err)
			logger.Error(trace, msg)
			return false, msg, errors.New(msg)
		}

		queueItem := QueueItem{Action: enums.QueueActionEnum.InitVirtual, Data: virtual.ID}
		VirtualInitQueue.Enqueue(queueItem)
		return true, "停止成功", nil
	} else {
		msg := "虚拟机不存在"
		err := errors.New(msg)
		logger.Error(trace, err)
		return false, msg, err
	}
}

func (d *_node) StartDocker(virtualId uint, dockerId string) (bool, string, error) {
	trace := fmt.Sprintf("StartDocker %d %s ", virtualId, dockerId)
	msg := ""
	if virtualId == 0 {
		msg = "未指定虚拟机"
		return false, msg, errors.New(msg)
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		err := daemon_server.StartContainer(virtual.DaemonClient, dockerId)
		if err != nil {
			msg := fmt.Sprintf("Failed to start container[%s]: %v\n", dockerId, err)
			logger.Error(trace, msg, "   ")
			return false, msg, errors.New(msg)
		} else {
			queueItem := QueueItem{Action: enums.QueueActionEnum.InitVirtual, Data: virtual.ID}
			VirtualInitQueue.Enqueue(queueItem)
			return true, "Docker启动成功", nil
		}
	} else {
		msg := "虚拟机不存在"
		err := errors.New(msg)
		logger.Error(trace, err)
		return false, msg, err
	}
}

func (d *_node) ReStartDocker(virtualId uint, dockerId string) (bool, string, error) {
	trace := fmt.Sprintf("ReStartDocker %d %s ", virtualId, dockerId)
	msg := ""
	if virtualId == 0 {
		msg = "未指定虚拟机"
		return false, msg, errors.New(msg)
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		err := daemon_server.RestartContainer(virtual.DaemonClient, dockerId, daemon_server.DaemonServerTimeoutSecond)
		if err != nil {
			msg := fmt.Sprintf("Failed to restart container[%s]: %v\n", dockerId, err)
			logger.Error(trace, msg, "   ")
			return false, msg, errors.New(msg)
		} else {
			queueItem := QueueItem{Action: enums.QueueActionEnum.InitVirtual, Data: virtual.ID}
			VirtualInitQueue.Enqueue(queueItem)
			return true, "重启成功", nil
		}
	} else {
		msg := "虚拟机不存在"
		err := errors.New(msg)
		logger.Error(trace, err)
		return false, msg, err
	}
}

func (d *_node) LogsDocker(virtualId uint, dockerId string) (bool, string, error) {
	trace := fmt.Sprintf("LogsDocker %d %s ", virtualId, dockerId)
	msg := ""
	if virtualId == 0 {
		msg = "未指定虚拟机"
		return false, msg, errors.New(msg)
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		if _, err := virtual.SshDial(); err != nil {
			logger.Error(trace, err)
			msg = "连接虚拟机失败"
			return false, msg, errors.New(msg)
		}
		defer virtual.CloseSsh("LogsDocker")

		if msg1, err := virtual.LogsDocker(dockerId); err != nil {
			logger.Error(trace, err, "   ", msg1)
			return false, msg1, err
		} else {
			return true, msg1, nil
		}
	} else {
		msg = "虚拟机不存在"
		err := errors.New(msg)
		logger.Error(trace, err)
		return false, msg, err
	}
}

func (d *_node) DockerImages123(virtualId uint) error {
	trace := fmt.Sprintf("DockerImages virtualId:%d ", virtualId)
	msg := ""
	if virtualId == 0 {
		msg = "未指定虚拟机"
		return errors.New(msg)
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		if _, err := virtual.SshDial(); err != nil {
			logger.Error(trace, err)
			msg = "连接虚拟机失败"
			return errors.New(msg)
		}
		defer virtual.CloseSsh("DockerImages")

		if images, err := virtual.DockerImages(""); err != nil {
			logger.Error(trace, err, "获取本地镜像失败   ", msg)
			return err
		} else {
			if ginH, err := MasterService.ReportLocalImages(virtualId, images); err != nil {
				logger.Error(err)
				return err
			} else {
				logger.Info("上报本地镜像成功", utils.GetJsonFromStruct(ginH))
				return nil
			}
		}
	} else {
		msg := "虚拟机不存在"
		err := errors.New(msg)
		logger.Error(trace, err)
		return err
	}
}

func waitDockerfileImage(ctx context.Context, img *PodImageItem) error {
	if img.ImageType == enums.ImageTypeEnum.CCM && img.ImageTag == "" {
		for i := 0; ; i++ {
			m, err := PostMasterForGin("api/v1/node/ccm/dockerfile/"+img.ImageName, nil)
			if err != nil {
				return err
			}

			tag, ok := m["tag"].(string)
			if !ok {
				return errors.New("等待算力市场镜像失败：" + img.ImageName)
			}

			if tag != "" {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "算力市场镜像已准备就绪", "ImageName："+img.ImageName, nil)
				img.ImageTag = tag
				break
			}

			if i > 180 { //1h
				return errors.New("等待算力市场镜像超时：" + img.ImageName)
			}
			if i%5 == 0 { //100s
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "等待算力市场镜像准备就绪", "ImageName："+img.ImageName, nil)
			}
			time.Sleep(20 * time.Second)
		}
	}
	return nil
}

func (d *_node) PullImages(ctx context.Context, virtualId uint, imageId uint) error {
	trace := fmt.Sprintf("PullImages virtualId:%d imageId:%d", virtualId, imageId)
	msg := ""
	if virtualId == 0 {
		msg = "未指定虚拟机"
		return errors.New(msg)
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		defer virtual.CloseSsh("PullImages")

		if imageValue, ok := d.PodImages.Load(imageId); !ok {
			//if podImage, ok := d.PodImages[imageId]; !ok {
			msg = "镜像ID不存在"
			return errors.New(msg)
		} else {
			podImage := imageValue.(PodImageItem)
			if err := waitDockerfileImage(ctx, &podImage); err != nil {
				return err
			}

			hubImagePath := GetHubImagePath(podImage.ImageType, podImage.ImageName, podImage.ImageTag)
			if hubImagePath == "" {
				msg = "镜像路径为空"
				logger.Error(msg, "podImage:", utils.GetJsonFromStruct(podImage))
			}
			if err := virtual.PullImageUserApi(ctx, hubImagePath, podImage); err != nil {
				logger.Error(trace, err, "获取本地镜像失败   ", msg)
				return err
			} else {
				return nil
			}
		}
	} else {
		msg := "虚拟机不存在"
		err := errors.New(msg)
		logger.Error(trace, err)
		return err
	}
}

func (d *_node) CommitDockerImage(ctx context.Context, docker DockerItem, newImageId uint, userDataPath string) error {
	reportState := enums.ReportStateEnum.Fail
	virtualId := docker.VirtualId
	dockerId := docker.ID
	trace := fmt.Sprintf("CommitDockerImage %d %s %d", virtualId, dockerId, newImageId)
	msg := ""

	defer func() {
		//StartupLog.Save(logKey, "开始上报镜像提交结果", "reportState:"+reportState+"  startupMark:"+docker.StartupMark)
		tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Progress, "开始上报镜像提交结果", "reportState:"+reportState+"  startupMark:"+docker.StartupMark, nil)
		if ginH, err := MasterService.ReportDockerCommit(docker.StartupMark, newImageId, reportState); err != nil {
			logger.Error("镜像提交上报失败", err, "   imageId:", newImageId, "  reportState:", reportState)
			//StartupLog.Save(logKey, "镜像提交结果上报失败", utils.GetJsonFromStruct(ginH))
			tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Progress, "镜像提交结果上报失败", utils.GetJsonFromStruct(ginH), nil)
		} else {
			logger.Info("镜像提交上报完成", ginH, "   imageId:", newImageId, "  reportState:", reportState)
			//StartupLog.Save(logKey, "镜像提交结果上报完成", utils.GetJsonFromStruct(ginH))
			tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Progress, "镜像提交结果上报完成", utils.GetJsonFromStruct(ginH), nil)
		}
	}()

	if virtualId == 0 {
		msg = "未指定虚拟机"
		return errors.New(msg)
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		if _, err := virtual.SshDial(); err != nil {
			logger.Error(trace, err)
			msg = "连接虚拟机失败"
			return errors.New(msg)
		}
		defer virtual.CloseSsh("CommitDockerImage")

		hubImagePath := ""
		if imageValue, ok := NodeService.PodImages.Load(newImageId); !ok {
			//if _, ok := NodeService.PodImages[newImageId]; !ok {
			msg = "镜像ID不存在"
			logger.Error(msg, newImageId)
			return errors.New(msg)
		} else {
			//podImage := NodeService.PodImages[newImageId]
			podImage := imageValue.(PodImageItem)
			sourcePath := ""
			if podImage.ImageType == enums.ImageTypeEnum.Base {
				sourcePath = "hub.suanyun.cn/suanyun-dev/nvidia"
			} else if podImage.ImageType == enums.ImageTypeEnum.Public {
				sourcePath = "hub.suanyun.cn/public"
			} else if podImage.ImageType == enums.ImageTypeEnum.Private {
				sourcePath = "hub.suanyun.cn/private"
			}

			if docker.StartupParm.InstanceType == enums.InstanceTypeEnum.Kol {
				sourcePath = "hub.suanyun.cn/public"
			}

			if sourcePath != "" {
				hubImagePath = fmt.Sprintf("%s/%s:%s", sourcePath, podImage.ImageName, podImage.ImageTag)
			}
		}
		if hubImagePath == "" {
			msg = "生成镜像Hub路径失败"
			logger.Error(msg)
			return errors.New(msg)
		}

		//StartupLog.Save(logKey, "开始提交镜像", fmt.Sprintf("saveImagePath:%s", saveImagePath))
		tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Progress, "开始提交镜像", fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)
		if b, msg, err := virtual.CommitDocker(nil, dockerId, hubImagePath); err != nil {
			//StartupLog.Save(logKey, "开始提交镜像", fmt.Sprintf("saveImagePath:%s", saveImagePath))
			tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Fail, "提交镜像失败", fmt.Sprintf("msg:%s  err:%s", msg, err.Error()), nil)
			logger.Error("   ", trace, err, "   ", b, "DockerId:", dockerId, "   saveImagePath:", hubImagePath, "  newImageId:", newImageId)
			return err
		}
		reportState = enums.ReportStateEnum.Success
		tasklog.Save(ctx, tasklog.CommitDockerTaskStateEnum.Success, "提交成功，开始查询本地镜像大小", fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)

		imageSize := int64(0)
		if images, err := virtual.DockerImagesUseApi(ctx, hubImagePath); err != nil {
			msg = "查询本地镜像失败"
			tasklog.Save(ctx, "", msg, fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)
			logger.Error(msg, err, "获取本地镜像失败  virtualId: ", virtual.ID, "  hubImagePath:", hubImagePath)
			return err
		} else {
			if len(images) == 0 {
				msg = "查询本地镜像为空"
				tasklog.Save(ctx, "", msg, fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)

				logger.Error(msg, "查询本地镜像失败  virtualId: ", virtual.ID, "  hubImagePath:", hubImagePath)
				return errors.New(msg)
			}
			imageSize = images[0].SizeB
			//if images[0].SizeG > 500 {
			//	msg = "镜像大小超过500GB，不能推送到仓库"
			//	tasklog.Save(ctx, "", msg, fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)
			//	logger.Error(msg, "  virtualId: ", virtual.ID, "  hubImagePath:", hubImagePath)
			//} else
			{
				msg = fmt.Sprintf("镜像大小为%s，可以推送到仓库", images[0].Size)
				tasklog.Save(ctx, "", msg, fmt.Sprintf("hubImagePath:%s  size:%d", hubImagePath, imageSize), nil)
			}
			//用户数据目录
			if userDataPath != "" {
				userBasePath := path.Join(config.PrivateStorage, userDataPath)
				if _, err := os.Stat(userBasePath); os.IsNotExist(err) {
					logger.Info("用户存储路径不存在 开始创建存储路径", userBasePath)
					if err := os.MkdirAll(userBasePath, os.ModePerm); err != nil {
						msg := "创建用户文件夹出错"
						logger.Error(msg, err)
						return err
					}
				}
				if _, err := os.Stat(userBasePath); err != nil {
					logger.Error(err)
					return err
				}
			}

			//App数据目录
			if userDataPath != "" {
				privateImagePath := path.Join(config.PrivateStorage, userDataPath, "container_images")

				if _, err := os.Stat(privateImagePath); os.IsNotExist(err) {
					logger.Info("个人镜像存储路径不存在 开始创建存储路径", privateImagePath)
					if err := os.MkdirAll(privateImagePath, os.ModePerm); err != nil {
						msg := "创建个人镜像存储文件夹出错"
						logger.Error(msg, err, privateImagePath)
						return err
					}
				}
				if _, err := os.Stat(privateImagePath); err != nil {
					logger.Error(err)
					return err
				}
				tmpAry := strings.Split(hubImagePath, "/")
				if len(tmpAry) < 2 {
					err := errors.New("hubImagePath 不正确")
					logger.Error(err)
					return err
				}
				jarName := tmpAry[len(tmpAry)-1]
				privateImageSavePath := path.Join(privateImagePath, jarName+".tar")
				//if err := virtual.SaveImage(ctx, hubImagePath, imageSize, privateImageSavePath); err != nil {
				//	logger.Error(err)
				//	return err
				//}
				if err := virtual.SaveImageUseApi(ctx, hubImagePath, privateImageSavePath); err != nil {
					logger.Error(err)
					return err
				}
			}

			return nil
		}
	} else {
		msg := "虚拟机不存在"
		err := errors.New(msg)
		logger.Error(trace, err)
		return err
	}
}

func (d *_node) SaveDockerImage(ctx context.Context, docker DockerItem, newImage PodImageItem, storageMode int, userDataPath string, imageMeta *string) (err error) {
	reportState := enums.ReportStateEnum.Fail
	newImageId := newImage.ID
	if newImageId <= 0 {
		err = errors.New("镜像ID不正确")
		logger.Error(err)
		return
	}
	if newImage.ImageType == enums.ImageTypeEnum.Public {
		if docker.StartupParm.InstanceType != enums.InstanceTypeEnum.Kol {
			err = errors.New("镜像类型和实例不匹配")
			logger.Error(err, newImageId, " ", docker.StartupParm.InstanceType, " ", docker.StartupMark)
			return
		}
	}

	virtualId := docker.VirtualId
	dockerId := docker.ID
	trace := fmt.Sprintf("SaveDockerImage %s(%d) %s %d ", docker.VirtualHost, virtualId, dockerId, newImageId)
	msg := ""

	task := ""
	if ctx != nil {
		if tmpTask, ok := ctx.Value("task").(string); ok {
			task = tmpTask
		}
	}
	showObjName := "镜像"
	if task == tasklog.TaskEnum.SaveInstanceImage {
		showObjName = "实例数据"
	}

	defer func() {
		reason := ""
		logMsg := "推送成功"
		if reportState == enums.ReportStateEnum.Fail {
			reason = tasklog.ScanErrReasonByCtx(ctx)
			logMsg = "推送失败(" + reason + ")"
		}
		tasklog.Save(ctx, "", "", " 开始上报镜像保存结果 reportState:"+reportState+" reason:"+reason+" task:"+task+" startupMark:"+docker.StartupMark, nil)

		if ginH, err1 := MasterService.ReportDockerPush(docker.StartupMark, newImageId, reportState, task, reason, *imageMeta); err1 != nil {
			err = err1
			logger.Error("Push上报失败", err, "   imageId:", newImageId, "  reportState:", reportState)
			logMsg += "(上报失败)"
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, showObjName+"保存失败(上报失败)", logMsg+" 镜像保存结果上报失败 reportState:"+reportState+"  startupMark:"+docker.StartupMark, nil)

		} else {
			logger.Info("Push上报完成", ginH, "   imageId:", newImageId, "  reportState:", reportState)

			if result, err2 := Result(ginH); err2 != nil {
				err = err2
				logMsg += "(上报失败)"
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, showObjName+"保存失败(上报失败)", logMsg+" 镜像保存结果上报失败 reportState:"+reportState+"  startupMark:"+docker.StartupMark+" ginH:"+utils.GetJsonFromStruct(ginH), nil)
				logger.Error(err)
			} else {
				if result.Code == 0 {
					showMsg := showObjName + "保存失败"
					state := tasklog.NormalTaskStateEnum.Fail
					if reportState == enums.ReportStateEnum.Success {
						showMsg = showObjName + "保存成功"
						state = tasklog.NormalTaskStateEnum.Success
					} else {
						if reason != "" {
							showMsg += "(" + reason + ")"
						}
					}
					logMsg += "(上报成功)"
					tasklog.Save(ctx, state, showMsg, logMsg+" 镜像保存结果上报成功 reportState:"+reportState+"  startupMark:"+docker.StartupMark+" ginH:"+utils.GetJsonFromStruct(ginH), nil)
				} else {
					if err == nil {
						err = errors.New("上报镜像保存结果失败")
					}
					logMsg += "(上报失败)"
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, showObjName+"保存失败(上报失败)", logMsg+" 镜像保存结果上报失败 reportState:"+reportState+"  startupMark:"+docker.StartupMark+" ginH:"+utils.GetJsonFromStruct(ginH), nil)
				}
			}

		}
	}()

	if virtualId == 0 {
		msg = "未指定虚拟机"
		return errors.New(msg)
	}
	if virtual, ok := d.GetVirtual(virtualId); ok {
		//if _, err = virtual.SshDial(); err != nil {
		//	msg = "Ssh连接失败"
		//	logger.Error(trace, msg, err)
		//	return errors.New(msg)
		//}
		defer virtual.CloseSsh("SaveDockerImage")

		hubImagePath := ""
		if imageValue, ok := NodeService.PodImages.Load(newImageId); !ok {
			//if _, ok := NodeService.PodImages[newImageId]; !ok {
			msg = "镜像ID不存在"
			logger.Error(msg, newImageId)
			return errors.New(msg)
		} else {
			podImage := imageValue.(PodImageItem)
			hubImagePath = GetHubImagePath(podImage.ImageType, podImage.ImageName, podImage.ImageTag)
		}
		if hubImagePath == "" {
			msg = "生成镜像路径失败"
			logger.Error(msg)
			return errors.New(msg)
		}

		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始保存"+showObjName, fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)

		var container types.Container
		if tmp, err := virtual.ContainerByStartupMarkUseApi(ctx, docker.StartupMark); err != nil {
			msg = "获取容器信息失败"
			logger.Error(msg, docker.StartupMark)
			return errors.New(msg)
		} else {
			container = tmp
		}

		if container.State == "running" {
			//tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在迁移大文件", fmt.Sprintf("dockerId:%s", dockerId), nil)
			//if inspect, err := virtual.MigrateLargeFile(ctx, dockerId); err != nil {
			//	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, "迁移大文件失败", fmt.Sprintf("迁移大文件失败 dockerId:%s err:%v", dockerId, err), nil)
			//	return err
			//} else {
			//	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "迁移大文件完成", fmt.Sprintf("dockerId:%s %s", dockerId, utils.GetJsonFromStruct(inspect)), nil)
			//}
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在停止容器", fmt.Sprintf("dockerId:%s", dockerId), nil)
			if err := virtual.StopDockerUseApi(ctx, dockerId); err != nil {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, "容器停止失败", fmt.Sprintf("容器停止失败 dockerId:%s err:%v", dockerId, err), nil)
				return err
			} else {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器已停止，开始提交", fmt.Sprintf("dockerId:%s", dockerId), nil)
			}
		}
		{

			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在检查容器大小", fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)

			if containers, err := virtual.ContainerListUseApi(ctx, dockerId, docker.StartupMark); err != nil {
				logger.Error(err)
			} else {
				if len(containers) == 1 {
					container = containers[0]
				} else {
					logger.Error("容器不存在 startupMark:", docker.StartupMark, " len:", len(containers))
				}
			}

			if container.ID != "" {
				sizeRw := fmt.Sprintf("%fMB", float64(container.SizeRw)/1024/1024)
				sizeRootFs := fmt.Sprintf("%fGB", float64(container.SizeRootFs)/1024/1024/1024)

				msg = fmt.Sprintf("容器更改的大小为%d字节 %s，容器的总大小为%d字节 %s", container.SizeRw, sizeRw, container.SizeRootFs, sizeRootFs)
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, msg, fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)
			}

		}

		/*
			if task == tasklog.TaskEnum.SaveImageAndShutdown || task == tasklog.TaskEnum.SaveImage {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在停止容器", fmt.Sprintf("dockerId:%s", dockerId), nil)
				if err := virtual.StopDockerUseApi(ctx, dockerId); err != nil {
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, "容器停止失败", fmt.Sprintf("容器停止失败 dockerId:%s err:%s", dockerId, err.Error()), nil)
					return err
				} else {
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器已停止，开始提交", fmt.Sprintf("dockerId:%s", dockerId), nil)
				}

				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器已停止，开始检查容器大小", fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)

				if containers, err := virtual.ContainerListUseApi(ctx, dockerId, docker.StartupMark); err != nil {
					logger.Error(err)
				} else {
					if len(containers) == 1 {
						container = containers[0]
					} else {
						logger.Error("容器不存在 startupMark:", docker.StartupMark, " len:", len(containers))
					}
				}

				if container.ID != "" {
					sizeRw := fmt.Sprintf("%fMB", float64(container.SizeRw)/1024/1024)
					sizeRootFs := fmt.Sprintf("%fGB", float64(container.SizeRootFs)/1024/1024/1024)

					msg = fmt.Sprintf("容器更改的大小为%d字节 %s，容器的总大小为%d字节 %s", container.SizeRw, sizeRw, container.SizeRootFs, sizeRootFs)
					tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, msg, fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)
				}

				//if task == tasklog.TaskEnum.SaveImage {
				//	defer func() {
				//		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在启动容器", fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)
				//		if err = virtual.StartDockerUserApi(ctx, dockerId); err != nil {
				//			logger.Error(err)
				//			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动失败", fmt.Sprintf("dockerId:%s", dockerId), nil)
				//		} else {
				//			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器启动成功", fmt.Sprintf("dockerId:%s", dockerId), nil)
				//		}
				//	}()
				//}
			}*/

		//tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "开始提交容器", fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)
		if err = virtual.CommitDockerUseApi(ctx, dockerId, hubImagePath, newImageId, container.SizeRw); err != nil {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, "提交容器失败", fmt.Sprintf("msg:%s  err:%s", "容器提交失败", err.Error()), nil)
			logger.Error("   ", trace, err, "   ", "DockerId:", dockerId, "   hubImagePath:", hubImagePath, "  newImageId:", newImageId)
			return err
		}
		tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "容器提交成功，开始查询本地镜像大小", fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)

		imageSize := int64(0)
		if images, err1 := virtual.DockerImagesUseApi(ctx, hubImagePath); err1 != nil {
			err = err1
			msg = "查询本地镜像失败"

			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, msg, fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)

			logger.Error(msg, err, "获取本地镜像失败  virtualId: ", virtual.ID, "  hubImagePath:", hubImagePath)
			return err
		} else {
			if len(images) == 0 {
				msg = "查询本地镜像为空"

				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, msg, fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)

				logger.Error(msg, "获取本地镜像失败  virtualId: ", virtual.ID, "  hubImagePath:", hubImagePath)
				return errors.New(msg)
			}
			imageSize = images[0].SizeB
			//if images[0].SizeG > 500 {
			//	msg = "镜像大小超过500GB，不能推送到仓库"
			//
			//	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Fail, msg, fmt.Sprintf("hubImagePath:%s", hubImagePath), nil)
			//
			//	logger.Error(msg, "  virtualId: ", virtual.ID, "  hubImagePath:", hubImagePath)
			//	return errors.New(msg)
			//} else
			{
				msg = fmt.Sprintf(showObjName+"大小为%s，可以推送到仓库", images[0].Size)

				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, msg, fmt.Sprintf("hubImagePath:%s size:%d", hubImagePath, imageSize), nil)

			}
		}

		if storageMode == enums.ImageStorageModeEnum.Registry {
			var im struct {
				types.ImageInspect
				History []image.HistoryResponseItem `json:"History"`
			}
			im.ImageInspect, err = daemon_server.InspectImageByName(virtual.DaemonClient, hubImagePath)
			if err != nil {
				logger.Error("获取镜像信息失败", newImageId, hubImagePath, err)
			}
			im.History, err = daemon_server.GetImageHistoryByName(virtual.DaemonClient, hubImagePath)
			if err != nil {
				logger.Error("获取镜像历史失败", newImageId, hubImagePath, err)
			}
			// {
			// 	b, err := json.Marshal(im)
			// 	if err == nil {
			// 		err = os.WriteFile("/mnt/pod-data/docker-image-meta/"+strconv.FormatUint(uint64(newImageId), 10)+".json", b, 0644)
			// 	}
			// 	if err != nil {
			// 		logger.Error("保存镜像元数据失败", newImageId, hubImagePath, err)
			// 	}
			// }
			if b, err := json.Marshal(map[string]any{
				"Id":           im.ID,
				"Size":         im.Size,
				"LayerCount":   len(im.RootFS.Layers),
				"HistoryCount": len(im.History),
			}); err != nil {
				logger.Error("报存镜像元数据失败", newImageId, hubImagePath, err)
			} else {
				*imageMeta = unsafe.String(unsafe.SliceData(b), len(b))
			}

			if err = virtual.PushDockerUseApi(ctx, hubImagePath, newImageId, imageSize); err != nil {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, showObjName+"推送失败", fmt.Sprintf("msg:%s  err:%s", msg, err.Error()), nil)
				logger.Error(trace, err, "   ", err)
				return err
			} else {

				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, showObjName+"推送成功", fmt.Sprintf("hubImagePath:%s imageSize:%d", hubImagePath, imageSize), nil)
				reportState = enums.ReportStateEnum.Success

				if newImageId > 0 && !strings.Contains(virtual.ImageIds, fmt.Sprintf("|%d|", newImageId)) {
					if virtual.ImageIds == "" {
						virtual.ImageIds = fmt.Sprintf("|%d|", newImageId)
					} else {
						virtual.ImageIds += fmt.Sprintf("%d|", newImageId)
					}
					logger.Info("添加新增ImageId:", newImageId, "    imageIds:", virtual.ImageIds)
					//virtualP.ImageIds = virtual.ImageIds
					d.SetVirtualImageIds(virtual.ID, virtual.ImageIds)
				}

				return nil
			}
		} else if storageMode == enums.ImageStorageModeEnum.PrivateDisk {

			if userDataPath == "" {
				err = errors.New("用户存储路径为空")
				logger.Error(err)
				return err
			}

			userBasePath := path.Join(config.PrivateStorage, userDataPath)
			if _, err = os.Stat(userBasePath); os.IsNotExist(err) {
				logger.Info("用户存储路径不存在 开始创建存储路径", userBasePath)
				if err = os.MkdirAll(userBasePath, os.ModePerm); err != nil {
					msg = "创建用户文件夹出错"
					logger.Error(msg, err)
					return err
				}
			}
			if _, err = os.Stat(userBasePath); err != nil {
				logger.Error(err)
				return err
			}

			//个人镜像数据目录
			privateImagePath := path.Join(config.PrivateStorage, userDataPath, "container_images")

			if _, err = os.Stat(privateImagePath); os.IsNotExist(err) {
				logger.Info("个人镜像存储路径不存在 开始创建存储路径", privateImagePath)
				if err := os.MkdirAll(privateImagePath, os.ModePerm); err != nil {
					msg = "创建个人镜像存储文件夹出错"
					logger.Error(msg, err, privateImagePath)
					return err
				}
			}
			if _, err = os.Stat(privateImagePath); err != nil {
				logger.Error(err)
				return err
			}
			tmpAry := strings.Split(hubImagePath, "/")
			if len(tmpAry) < 2 {
				err = errors.New("hubImagePath 不正确")
				logger.Error(err)
				return err
			}
			jarName := tmpAry[len(tmpAry)-1]
			privateImageSavePath := path.Join(privateImagePath, jarName+".tar")

			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "virtual.SaveImageUseApi开始 privateImageSavePath："+privateImageSavePath, nil)

			err = SquashImage(ctx, nil, hubImagePath, privateImageSavePath)
			if err != nil {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "合并镜像层失败", fmt.Sprintf("hubImagePath:%s privateImageSavePath:%s error:%v", hubImagePath, privateImageSavePath, err), nil)
				logger.Error("squash image %s to %s failed: %v", hubImagePath, privateImageSavePath, err)

				err = virtual.SaveImageUseApi(ctx, hubImagePath, privateImageSavePath)
			} else {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "合并镜像层成功", fmt.Sprintf("hubImagePath:%s privateImageSavePath:%s", hubImagePath, privateImageSavePath), nil)
			}

			if err != nil {
				logger.Error(err)
				return err
			} else {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像已保存到个人存储", fmt.Sprintf("hubImagePath:%s imageSize:%d", hubImagePath, imageSize), nil)
				reportState = enums.ReportStateEnum.Success
				//return nil
			}
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "virtual.SaveImageUseApi结束 privateImageSavePath："+privateImageSavePath, nil)

			//privateImageSavePath = strings.Replace(privateImageSavePath, "/root/suanyun-user/0/4a5c08f09d37/container_images", "/tmp", -1)
			//tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "virtual.SaveImage开始 privateImageSavePath："+privateImageSavePath, nil)
			//if err := virtual.SaveImage(ctx, hubImagePath, imageSize, privateImageSavePath); err != nil {
			//	logger.Error(err)
			//	return err
			//} else {
			//	tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "镜像已保存到个人存储", fmt.Sprintf("hubImagePath:%s imageSize:%d", hubImagePath, imageSize), nil)
			//	reportState = enums.ReportStateEnum.Success
			//	//return nil
			//}
			//tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "", "virtual.SaveImage结束 privateImageSavePath："+privateImageSavePath, nil)
			return nil
		} else {
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "未处理的存储类型", fmt.Sprintf("hubImagePath:%s imageSize:%d", hubImagePath, imageSize), nil)
			err = errors.New("未处理的存储类型")
			logger.Error(err)
			return err
		}
	} else {
		err = errors.New("虚拟机不存在")
		logger.Error(trace, err)
		return err
	}
}

func (o *_node) UnlockGpuByStartupMark(lockVirtualId uint, startupMark string) bool {
	if virtual, ok := o.GetVirtual(lockVirtualId); ok {
		free := 0
		for i := 0; i < len(virtual.Gpus); i++ {
			if virtual.Gpus[i].Status == enums.GpuStatusEnum.Free {
				free++
				continue
			}
			if virtual.Gpus[i].StartupMark == startupMark && virtual.Gpus[i].Status == enums.GpuStatusEnum.Locked {
				virtual.Gpus[i].Status = enums.GpuStatusEnum.Free
				free++
				continue
			}
		}
		virtual.GpuFrees = free
		//o.Virtuals[lockVirtualId] = virtual
		virtual.Report()
	}
	return true
}
