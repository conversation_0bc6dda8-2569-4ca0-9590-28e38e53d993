package service

import (
	"io"
	"time"
)

type ProgressReader struct {
	io.Reader
	StartAt      int64     `json:"start_at,omitempty"`
	CompletedAt  int64     `json:"completed_at,omitempty"`
	Current      int64     `json:"current,omitempty"`
	Total        int64     `json:"total,omitempty"`
	Progress     float64   `json:"progress,omitempty"`
	LastTime     time.Time `json:"last_time,omitempty"`
	progressFunc func(current, total int64)
}

func (p *ProgressReader) Read(b []byte) (int, error) {
	n, err := p.Reader.Read(b)
	p.Current += int64(n)
	if time.Since(p.LastTime).Seconds() >= 1 {
		p.LastTime = time.Now()
		if p.progressFunc != nil {
			p.progressFunc(p.Current, p.Total)
		}
	}
	if p.Current >= p.Total && p.CompletedAt == 0 {
		p.CompletedAt = time.Now().Unix()
	}
	return n, err
}
