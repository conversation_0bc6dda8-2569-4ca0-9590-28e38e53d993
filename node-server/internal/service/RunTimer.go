package service

import (
	"node-server/internal/common/logger"
	"node-server/internal/config"
	"runtime"
	"time"
)

var RunPause bool //服务是否暂停
var RunTask [1]bool

func RunTimer() {
	//go func() {
	//	time.Sleep(time.Second * 10)
	//	RestartProject13()
	//}()

	go func() {
		defer func() {
			if e := recover(); e != nil {
				logger.Error("奔溃:", e)
				stack := make([]byte, 4096)
				runtime.Stack(stack, false)
				logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			}
		}()
		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()
		for range ticker.C {
			logger.Info("开始执行Clear.HandleReloadVirtual业务逻辑")
			Clear.ClearLocalGpuLock()
			NodeService.LoadVirtuals()
			Clear.HandleInitVirtual()

			for i := 0; i < 10; i++ {
				if NodeService.NeedInitCount <= 0 {
					logger.Info("定点初始化全部完成，跳出循环")
					break
				}
				logger.Info("延迟2秒等待定点初始化完成")
				time.Sleep(time.Second * 2)
			}
			Clear.HandleCheckInstanceLive()
			if Clear.ReportNodeInfo() {
				logger.Info("定点上报节点信息成功")
			} else {
				logger.Error("定点上报节点信息失败")
			}
			//Clear.ClearNginxMap()

			logger.Info("Clear.HandleReloadVirtual业务逻辑执行结束")
		}
	}()

	if config.ClearNginx {
		go func() {
			defer func() {
				if e := recover(); e != nil {
					logger.Error("奔溃:", e)
					stack := make([]byte, 4096)
					runtime.Stack(stack, false)
					logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
				}
			}()
			ticker := time.NewTicker(15 * time.Minute)
			defer ticker.Stop()
			for range ticker.C {
				logger.Info("开始执行Clear.ClearNginxMap业务逻辑")

				Clear.ClearNginxMap()

				logger.Info("Clear.ClearNginxMap业务逻辑执行结束")
			}
		}()
	}

	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()
		for range ticker.C {
			logger.Info("开始执行EmailService.ClearNeedSend业务逻辑")
			EmailService.ClearNeedSend()
			logger.Info("EmailService.ClearNeedSend业务逻辑执行结束")
		}
	}()
	go func() {
		ticker := time.NewTicker(15 * time.Minute)
		defer ticker.Stop()
		for range ticker.C {
			logger.Info("开始执行ClearSpace业务逻辑(15分钟执行一次)")
			Clear.ClearSpace()
			logger.Info("ClearSpace业务逻辑执行结束")
		}
	}()

	//if config.Bundle == "sd.cyuai.aigc" {
	//
	//	go func() {
	//		ticker := time.NewTicker(1 * time.Second)
	//		defer ticker.Stop()
	//		for range ticker.C {
	//			logger.Info("开始执行SdService业务逻辑")
	//			SdService.Run()
	//			logger.Info("SdService业务逻辑出错,延迟10秒")
	//		}
	//	}()
	//}
	//go func() {
	//	ticker := time.NewTicker(1 * time.Second)
	//	defer ticker.Stop()
	//	for range ticker.C {
	//		//logger.Info("开始执行SchedService业务逻辑")
	//		SchedService.Run()
	//		//logger.Info("SchedService业务逻辑出错,延迟10秒")
	//	}
	//}()
}
