package service

import (
	"context"
	"errors"

	"node-server/internal/squash"

	"github.com/docker/docker/client"
)

const squashDisabled = true

func SquashImage(ctx context.Context, c *client.Client, fromImage, savePath string) error {
	if squashDisabled {
		return errors.New("squash image disabled")
	}
	s := squash.Squash{
		Client:    c,
		FromImage: fromImage,
		Tag:       fromImage,
		SavePath:  savePath,
		To1G:      true,
	}
	return s.Do(ctx)
}
