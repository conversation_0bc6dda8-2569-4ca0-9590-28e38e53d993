package service

import (
	"errors"
	"fmt"
	"node-server/internal/common"
	"node-server/internal/common/logger"
	"node-server/internal/common/utils"
	"node-server/internal/enums"
	"strings"
	"time"
)

//type appStartLoop{
//	VirtualId uint
//}

type AppStartLoopItem struct {
	VirtualId   uint      `json:"virtual_id"`
	StartupMark string    `json:"startup_mark"`
	LeaveCount  int       `json:"leave_count"`
	CreateAt    time.Time `json:"create_at"`
	SaveAt      time.Time `json:"save_at"`
}

func (d *AppStartLoopItem) SaveAppStartLoop(virtualId uint, statupMark string, leaveCount int) error {
	item := AppStartLoopItem{
		VirtualId:   virtualId,
		StartupMark: statupMark,
		LeaveCount:  leaveCount,
		CreateAt:    time.Now(),
		SaveAt:      time.Now(),
	}
	var tmp AppStartLoopItem
	if err := d.GetAppStartLoop(&tmp, statupMark); err == nil {
		item.CreateAt = tmp.CreateAt
	}

	json := utils.GetJsonFromStruct(item)
	if json == "" {
		return errors.New("序列化为空")
	}
	field := fmt.Sprintf("AppStartLoop_%s", statupMark)
	if _, err := common.RedisHSet(enums.RedisKeyEnum.CpnSchedTask, field, json); err != nil {
		logger.Error(err)
		return err
	} else {
		return nil
	}
}

func (d *AppStartLoopItem) GetAppStartLoop(dest interface{}, statupMark string) error {
	field := fmt.Sprintf("AppStartLoop_%s", statupMark)
	if json, err := common.RedisHGet(enums.RedisKeyEnum.CpnSchedTask, field); err != nil {
		logger.Error(err)
		return err
	} else {
		if json == "" {
			return errors.New("string is empty")
		}
		if err := utils.GetStructFromJson(dest, json); err != nil {
			logger.Error(err)
			return err
		}
		return nil
	}
}

func (d *AppStartLoopItem) RemoveAppStartLoop(statupMark string) error {
	field := fmt.Sprintf("AppStartLoop_%s", statupMark)
	if _, err := common.RedisHDel(enums.RedisKeyEnum.CpnSchedTask, field); err != nil {
		logger.Error(err)
		return err
	} else {
		return nil
	}
}

func (d *AppStartLoopItem) ListAll() ([]AppStartLoopItem, error) {
	arr := make([]AppStartLoopItem, 0)
	if mm, err := common.RedisHGetAll(enums.RedisKeyEnum.CpnSchedTask); err != nil {
		logger.Error(err)
		return arr, err
	} else {
		for field, value := range mm {
			if !strings.HasPrefix(field, "AppStartLoop_") {
				continue
			}

			var tmp AppStartLoopItem
			if err := utils.GetStructFromJson(&tmp, value); err != nil {

			} else {
				if tmp.StartupMark != "" {
					arr = append(arr, tmp)
					if tmp.LeaveCount <= 0 {
						d.RemoveAppStartLoop(tmp.StartupMark)
					}
				}
			}
		}
	}
	return arr, nil
}

type CommandRunItem struct {
	VirtualId   uint                   `json:"virtual_id"`
	StartupMark string                 `json:"startup_mark"`
	Data        map[string]interface{} `json:"data"`
	CreateAt    time.Time              `json:"create_at"`
	SaveAt      time.Time              `json:"save_at"`
}
