package service

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"node-server/internal/common"
	"node-server/internal/common/logger"
	"node-server/internal/common/utils"
	"node-server/internal/config"
	"node-server/internal/enums"
	"node-server/internal/structs"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strings"
	"time"
	"unsafe"

	"github.com/docker/docker/api/types"

	"github.com/gin-gonic/gin"
)

type GinH struct {
	Code   int                    `json:"code"`
	Msg    string                 `json:"msg"`
	Result map[string]interface{} `json:"result"`
}

func PostMasterForGin(postUrl string, postData map[string]interface{}) (gin.H, error) {
	bytesData, _ := json.Marshal(postData)
	if !strings.HasPrefix(postUrl, "http") {
		postUrl = config.MasterServer + postUrl
	}
	//if common.Config.Env == enums.EnvEnum.ONLINE {
	//	postUrl = strings.Replace(postUrl, "6001/", "6002/", -1)
	//}
	//logger.Info("PostUrl：", postUrl, " PostData:", utils.GetJsonFromStruct(postData))
	req, err := http.NewRequest("POST", postUrl, bytes.NewBuffer(bytesData))
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, err
	}
	// 设置请求头
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	req.Header.Set("Authorization", config.NodeToken) // 设置其他自定义的请求头

	// 发送请求
	client := http.DefaultClient
	res, err := client.Do(req)
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, err
	}
	defer res.Body.Close()

	content, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, err
	}
	str := *(*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	//logger.Info("PostUrl:", postUrl, " Result：", str)
	str = strings.TrimSpace(str)
	var ginH gin.H
	if strings.HasPrefix(str, "{") {
		if err = json.Unmarshal([]byte(str), &ginH); err != nil {
			logger.Error(err, "PostUrl:", postUrl, " Result：", str)
			return nil, err
		}
		return ginH, nil
	} else {
		logger.Error("不是Json格式 PostUrl:", postUrl, " Result：===|", str, "|===")
		return nil, errors.New("不是Json格式")
	}
}

func Post(postUrl string, postData map[string]interface{}) (map[string]interface{}, int) {
	code := 1
	bytesData, _ := json.Marshal(postData)

	if !strings.HasPrefix(postUrl, "http") {
		postUrl = config.MasterServer + postUrl
	}
	//if common.Config.Env == enums.EnvEnum.ONLINE {
	//	postUrl = strings.Replace(postUrl, "6001/", "6002/", -1)
	//}
	logger.Info("PostUrl：", postUrl, " PostData:", utils.GetJsonFromStruct(postData))
	req, err := http.NewRequest("POST", postUrl, bytes.NewBuffer(bytesData))
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, code
	}
	// 设置请求头
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	req.Header.Set("Authorization", config.NodeToken) // 设置其他自定义的请求头

	// 发送请求
	client := http.DefaultClient
	res, err := client.Do(req)
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, code
	}
	defer res.Body.Close()

	content, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Error(err, postUrl, postData)
		return nil, code
	}
	str := *(*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	//logger.Info("PostUrl:", postUrl, " Result：", str)
	str = strings.TrimSpace(str)
	if len(str) >= 2 && str[0] == '{' && str[len(str)-1] == '}' {
		m := utils.GetMapFromJson(str)
		if m == nil {
			logger.Error("将返回的内容格式化成Map出错", postUrl, postData, str)
			return nil, code
		}
		code = 0
		return m, code
	} else {
		logger.Error("返回的内容不是json格式", postUrl, postData, str)
		return nil, code
	}
}

func ResultGinH(mm gin.H) (GinH, error) {
	var ginH GinH

	success := 0
	if _, ok := mm["code"]; ok {
		ginH.Code = int(mm["code"].(float64))
		success += 1
	}

	if _, ok := mm["msg"]; ok {
		ginH.Msg = mm["msg"].(string)
		success += 2
	}
	if success <= 2 {
		return ginH, errors.New("不是标准的Result格式")
	}

	if _, ok := mm["result"]; ok {
		ginH.Result = mm["result"].(map[string]interface{})
	}
	return ginH, nil
}

func Result(mm map[string]interface{}) (GinH, error) {
	var ginH GinH
	if mm == nil {
		return ginH, errors.New("mm is nil")
	}

	success := 0
	if _, ok := mm["code"]; ok {
		ginH.Code = int(mm["code"].(float64))
		success += 1
	}

	if _, ok := mm["msg"]; ok {
		ginH.Msg = mm["msg"].(string)
		success += 2
	}
	if success <= 2 {
		return ginH, errors.New("不是标准的Result格式")
	}

	if _, ok := mm["result"]; ok {
		ginH.Result = mm["result"].(map[string]interface{})
	}
	return ginH, nil
}

func SaveToFileCache(fileName string, content string) error {
	relativePath := "./cache/" + fileName
	absolutePath, err := filepath.Abs(relativePath)
	if err != nil {
		logger.Error(err)
		return err
	}
	dir := filepath.Dir(absolutePath)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		if err := os.MkdirAll(dir, os.ModePerm); err != nil {
			logger.Error(err)
			return err
		}
	} else if err != nil {
		logger.Error(err)
		return err
	}
	if err := os.WriteFile(relativePath, []byte(content), 0644); err != nil {
		logger.Error("无法写入文件:", err)
		return err
	}
	return nil
}

func ReadFromFileCache(fileName string) (string, error) {
	relativePath := "./cache/" + fileName
	logger.Info("从缓存获取数据", relativePath)
	absolutePath, err := filepath.Abs(relativePath)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	// 读取文件内容
	// 读取文件内容
	content, err := os.ReadFile(absolutePath)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	// 将内容转换为字符串
	str := string(content)
	return str, nil
}

func GetMapPorts(input string) []string {
	//if modelHash == "" {
	//	return ""
	//}

	//input = `docker run -p 1{==={GpuIndex}===}022:22 -p 1{==={GpuIndex}===}800:8000 -v /root/aigc-models/llms`
	// 创建正则表达式匹配模式
	//re := regexp.MustCompile(fmt.Sprintf(`0(\d+):\d+,`, input))
	re := regexp.MustCompile(`1\{===\{GpuIndex\}===\}(\d+):\d+`)
	// 查找匹配的字符串
	matches := re.FindAllStringSubmatch(input, -1)
	ary := make([]string, 0)
	for _, match := range matches {
		if len(match) >= 2 {

			num := utils.String2Int(match[1])
			if num > 0 {
				ary = append(ary, utils.Int2String(num))
			}
		}
	}
	return ary
}

func GetImagePath(command string) string {
	//if modelHash == "" {
	//	return ""
	//}

	//input = `docker run -p 1{==={GpuIndex}===}022:22 -p 1{==={GpuIndex}===}800:8000 -v /root/aigc-models/llms`
	// 创建正则表达式匹配模式
	//re := regexp.MustCompile(fmt.Sprintf(`0(\d+):\d+,`, input))
	//re := regexp.MustCompile(`1\{===\{GpuIndex\}===\}(\d+):\d+`)
	//re := regexp.MustCompile(`-d .*?:([^ ]*)`)
	re := regexp.MustCompile(`hub\.suanyun\.cn/.+:([^ ]+)`)
	// 查找匹配的字符串
	matches := re.FindAllStringSubmatch(command, -1)
	if matches == nil || len(matches) == 0 {
		re = regexp.MustCompile(`192\.168\.200\.5/.+:([^ ]+)`)
		matches = re.FindAllStringSubmatch(command, -1)
	}

	for _, match := range matches {
		if len(match) >= 2 {
			return match[0]
		}
	}
	return ""
}

func GetImageSha256(output string) string {
	//v3: digest: sha256:1a9822d498ad0b5b107d2bb2d77a4b6bae8a416b00091571dbb1a0e0432074fe size: 3893
	re := regexp.MustCompile(`sha256:([^ ]+)`)
	// 查找匹配的字符串
	matches := re.FindAllStringSubmatch(output, -1)
	for _, match := range matches {
		if len(match) >= 2 && len(match[1]) == 64 {
			return match[1]
		}
	}
	return ""
}

func GetMapUrl(docker DockerItem, port string) string {
	if docker.MapPref == "" {
		if len(docker.Gpus) > 0 {
			return fmt.Sprintf("http://%s:1%d0%s", docker.VirtualHost, docker.Gpus[0], port)
		} else {
			logger.Error("docker.Gpus为空 startupMark:", docker.StartupMark)
			return ""
		}
	} else {
		return fmt.Sprintf("http://%s:%s0%s", docker.VirtualHost, docker.MapPref, port)
	}
}

func GetCheckUrls(docker DockerItem) []string {
	var aryUrl = make([]string, 0)
	has89 := false
	if docker.StartupParm.InstanceType == enums.InstanceTypeEnum.Kol {
		// fmt.Sprintf("http://%s:1%d0%s", docker.VirtualHost, docker.Gpus[0], "89")
		checkUrl := GetMapUrl(docker, "89")
		aryUrl = append(aryUrl, checkUrl)
		has89 = true
	}
	for _, tmp := range docker.MapPorts {
		if tmp == "89" && has89 {
			continue
		}
		checkUrl := GetMapUrl(docker, tmp)
		aryUrl = append(aryUrl, checkUrl)
	}
	return aryUrl
}

func GetCheckPort(docker DockerItem) string {
	if docker.MapPref == "" {
		return ""
	}
	for _, tmp := range docker.MapPorts {
		if tmp == "89" {
			return docker.MapPref + "0" + tmp
		}
	}
	for _, tmp := range docker.MapPorts {
		if tmp == "87" {
			return docker.MapPref + "0" + tmp
		}
	}
	for _, tmp := range docker.MapPorts {
		return docker.MapPref + "0" + tmp
	}
	return ""
}

func Container2DockerItem(virtualId uint, virtualHost string, container types.Container) DockerItem {

	{
		mLabel := container.Labels
		docker := DockerItem{
			ID:          container.ID[:12],
			VirtualId:   virtualId,
			VirtualHost: virtualHost,
			State:       container.State,
			Gpus:        make([]int, 0),
			MapPorts:    make([]string, 0),
		}

		if val, ok := mLabel["instance_uuid"]; ok {
			docker.InstanceUuid = val
		}
		if val, ok := mLabel["startup_mark"]; ok {
			docker.StartupMark = val
		}
		if val, ok := mLabel["startup_parm"]; ok {
			if val != "" {
				var startupParm structs.StartupParm
				if err := utils.GetStructFromJson(&startupParm, val); err != nil {
					logger.Error(err, " val:", val)
				} else {
					docker.StartupParm = startupParm
				}
			}
		}
		if val, ok := mLabel["gpus"]; ok {
			gpus := make([]int, 0)
			if val != "" {
				ary1 := strings.Split(val, "_")
				for _, v := range ary1 {
					if v == "" {
						continue
					}
					gpus = append(gpus, utils.String2Int(v))
				}
			}
			docker.Gpus = gpus
		}
		if val, ok := mLabel["map_pref"]; ok {
			docker.MapPref = val
		}
		if val, ok := mLabel["map_ports"]; ok {
			mapPorts := make([]string, 0)
			if val != "" {
				ary1 := strings.Split(val, "_")
				for _, v := range ary1 {
					mapPorts = append(mapPorts, v)
				}
				docker.MapPorts = mapPorts
			}
		}
		if val, ok := mLabel["pod_id"]; ok {
			docker.PodId = utils.String2Uint(val)
		}
		if val, ok := mLabel["pod_category"]; ok {
			docker.PodCategory = utils.String2Int(val)
		}
		if _, ok := mLabel["pod_name"]; ok {

		}
		if val, ok := mLabel["image_id"]; ok {
			docker.ImageId = utils.String2Uint(val)
		}
		if val, ok := mLabel["image_type"]; ok {
			docker.ImageType = utils.String2Int(val)
		}
		//if val, ok := mLabel["host"]; ok {
		//	docker.VirtualHost = val
		//}
		//if val, ok := mLabel["host_port"]; ok {
		//	//docker.SshPort = utils.String2Int(val)
		//}
		//if val, ok := mLabel["ssh_port"]; ok {
		//	docker.SshPort = utils.String2Int(val)
		//}
		if container.Created > 0 {
			t := time.Unix(container.Created, 0)
			docker.CreatedAt = t.In(time.Local)
		}

		if docker.StartupMark != "" {
			//if old, ok := mOldDocker[docker.StartupMark]; ok {
			//	//logger.Info("重新设置LastStateCheck startupMark:", startupMark, "  val:", old.LastStateCheck)
			//	docker.LastStateCheck = old.LastStateCheck
			//}

			for _, tmp := range docker.MapPorts {
				if tmp == "88" {
					docker.WebUrl = GetMapUrl(docker, "88")
				} else if tmp == "33" {
					docker.ApiBase = GetMapUrl(docker, "33")
				}
			}

			//if exists, err := o.ExistsCacheShutdown(docker.StartupMark); err != nil {
			//	logger.Error("o.ExistsCacheShutdown err:", err, " startupMark:", docker.StartupMark)
			//	return aryDocker, err
			//} else {
			//	if exists {
			//		aryCacheDocker = append(aryCacheDocker, docker)
			//		continue
			//	}
			//	aryDocker = append(aryDocker, docker)
			//}
		}
		return docker
	}
}

func GetHubImagePath(imageType int, imageName string, imageTag string) string {
	if imageType == enums.ImageTypeEnum.CCM {
		if imageTag == "" {
			return ""
		}
		return "*************/chenyu/public/base-env:" + imageTag
	}

	sourcePath := ""
	if imageType == enums.ImageTypeEnum.Base {
		sourcePath = "hub.suanyun.cn/chenyu/suanyun-dev"
	} else if imageType == enums.ImageTypeEnum.Public {
		sourcePath = "hub.suanyun.cn/chenyu/public"
	} else if imageType == enums.ImageTypeEnum.Private || imageType == enums.ImageTypeEnum.PrivateInstance {
		sourcePath = "hub.suanyun.cn/chenyu/private"
	}
	sourcePath = strings.Replace(sourcePath, "hub.suanyun.cn", common.HubServerAddress, -1)
	if sourcePath != "" {
		imageStr := fmt.Sprintf("%s/%s:%s", sourcePath, imageName, imageTag)
		return imageStr
	}
	return ""
}

func GetPrivateImageSavePath(hubImagePath string, userDataPath string) (privateImageSavePath string, err error) {
	defer func() {
		if err != nil {
			privateImageSavePath = ""
			logger.Error(err)
		}
	}()
	if userDataPath == "" {
		err = errors.New("用户存储路径为空")
		logger.Error(err)
		return
	}

	userBasePath := path.Join(config.PrivateStorage, userDataPath)
	if _, err = os.Stat(userBasePath); err != nil {
		logger.Error(err)
		return
	}

	//个人镜像数据目录
	privateImagePath := path.Join(config.PrivateStorage, userDataPath, "container_images")
	if _, err = os.Stat(privateImagePath); err != nil {
		logger.Error(err)
		return
	}
	tmpAry := strings.Split(hubImagePath, "/")
	if len(tmpAry) < 2 {
		err = errors.New("hubImagePath 不正确")
		logger.Error(err)
		return
	}
	jarName := tmpAry[len(tmpAry)-1]
	privateImageSavePath = path.Join(privateImagePath, jarName+".tar")
	if _, err = os.Stat(privateImageSavePath); err != nil {
		logger.Error(err)
		return
	}
	return
}
