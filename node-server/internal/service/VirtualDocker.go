package service

import (
	"context"
	"errors"
	"node-server/internal/common/logger"
	daemon_server "node-server/internal/service/daemon-server"
	"strings"

	"github.com/docker/docker/api/types"
)

func (o *Virtual) ImageInspectWithRaw(ctx context.Context, imageId string) (types.ImageInspect, error) {

	if len(imageId) < 12 {
		return types.ImageInspect{}, errors.New("镜像ID长度不足")
	}
	if !strings.HasPrefix(imageId, "sha256:") {
		imageId = "sha256:" + imageId
	}
	if imageInspect, err := daemon_server.InspectImage(o.DaemonClient, imageId); err != nil {
		logger.Error(err)
		return imageInspect, err
	} else {
		//sha256:65fedce8f488f46bddf303bf86c1c8123d9927111b6021c5cec071899bdf3f84

		if !strings.HasPrefix(imageInspect.ID, imageId) {
			err := errors.New("镜像ID不一致")
			logger.Error(err, " imageId:", imageId, "  imageInspect.ID:", imageInspect.ID)
			return imageInspect, err
		}
		return imageInspect, nil
	}
}
