package service

import (
	"context"
	"fmt"
	"node-server/internal/common/utils"
	"testing"
)

func TestDockerImages(t *testing.T) {

	ctx := context.Background()
	virtual := Virtual{
		ID:   55,
		Host: "***************",
	}
	virtual.NewDockerClient()
	hubImagePath := ""
	if images, err := virtual.DockerImagesUseApi(ctx, hubImagePath); err != nil {
		fmt.Println(err)
	} else {
		fmt.Println(utils.GetJsonFromStruct(images))
	}

}
