package daemon_server

import (
	"context"
	"daemon-server/sdk/docker"
	"daemon-server/sdk/system"
	"daemon-server/sdk/taskflow"
	"errors"
	"fmt"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/filters"
	"github.com/docker/docker/api/types/image"
	"node-server/internal/common/logger"
	"node-server/internal/config"
	"time"
)

const (
	DaemonServerShortTimeoutSecond       = 30
	DaemonServerTimeoutSecond            = 300
	DaemonServerPollIntervalSecond       = 2
	DaemonServerMiddleTimeoutSecond      = 30 * 60
	DaemonServerMiddlePollIntervalSecond = 5
	DaemonServerLongTimeoutSecond        = 120 * 60
	DaemonServerLongPollIntervalSecond   = 10
)

func WaitLongTaskComplete(ctx context.Context, task *taskflow.Task) (*taskflow.TaskInfo, error) {
	logger.Infof("Waiting for task[%s] to complete...", task.ID)
	taskInfo, err := task.Wait(ctx, DaemonServerLongPollIntervalSecond*time.Second, DaemonServerLongTimeoutSecond*time.Second)
	if err != nil {
		msg := fmt.Sprintf("Failed to wait for task[%s]: %v\n", task.ID, err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	fmt.Printf("Task completed with status: %s\n", taskInfo.Status)
	if taskInfo.Error != "" {
		msg := fmt.Sprintf("Failed to complete task[%s]: %v\n", task.ID, taskInfo.Error)
		logger.Error(msg)
		return taskInfo, errors.New(msg)
	}
	return taskInfo, nil
}

func WaitTaskComplete(ctx context.Context, task *taskflow.Task) (*taskflow.TaskInfo, error) {
	logger.Infof("Waiting for task[%s] to complete...", task.ID)
	taskInfo, err := task.Wait(ctx, DaemonServerPollIntervalSecond*time.Second, DaemonServerTimeoutSecond*time.Second)
	if err != nil {
		msg := fmt.Sprintf("Failed to wait for task[%s]: %v\n", task.ID, err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	fmt.Printf("Task completed with status: %s\n", taskInfo.Status)
	if taskInfo.Error != "" {
		msg := fmt.Sprintf("Failed to complete task[%s]: %v\n", task.ID, taskInfo.Error)
		logger.Error(msg)
		return taskInfo, errors.New(msg)
	}
	return taskInfo, nil
}

func GetDaemonServerClient(host string) *docker.DockerClient {
	if host != "" {
		return docker.NewDockerClient(
			fmt.Sprintf("http://%s:%d", host, config.DaemonServerPort),
			docker.WithTimeout(DaemonServerTimeoutSecond*time.Second),
		)
	} else {
		msg := fmt.Sprintf("daemon[%s] host is empty, cannot init daemon server client", host)
		logger.Error(msg)
		return nil
	}
}

func StopContainer(client *docker.DockerClient, containerId string) error {
	ctx := context.Background()

	stopTask, err := client.AsyncContainer.Stop(ctx, containerId, nil)
	if err != nil {
		msg := fmt.Sprintf("Failed to create stop container[%s] task: %v\n", containerId, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	fmt.Printf("Container stop task started with ID: %s\n", stopTask.ID)
	_, err = WaitTaskComplete(ctx, stopTask)
	if err != nil {
		msg := fmt.Sprintf("Stop container[%s] err: %v\n", containerId, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	return nil
}

func RemoveContainer(client *docker.DockerClient, containerId string, force bool, removeVols bool) error {
	ctx := context.Background()
	task, err := client.AsyncContainer.Remove(ctx, containerId, force, removeVols)
	if err != nil {
		msg := fmt.Sprintf("Failed to create remove container[%s] task: %v\n", containerId, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	fmt.Printf("Container remove task started with ID: %s\n", task.ID)
	_, err = WaitTaskComplete(ctx, task)
	if err != nil {
		msg := fmt.Sprintf("Remove container[%s] err: %v\n", containerId, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	return nil
}

func RemoveImage(client *docker.DockerClient, imageId string, force bool, pruneChildren bool) ([]image.DeleteResponse, error) {
	ctx := context.Background()
	task, err := client.AsyncImage.Remove(ctx, imageId, force, pruneChildren)
	if err != nil {
		msg := fmt.Sprintf("Failed to create remove image[%s] task: %v\n", imageId, err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	fmt.Printf("Image remove task started with ID: %s\n", task.ID)
	taskInfo, err := WaitTaskComplete(ctx, task)
	if err != nil {
		msg := fmt.Sprintf("Failed to remove image[%s]: %v\n", imageId, err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	if taskInfo.Error != "" {
		msg := fmt.Sprintf("Failed to remove image[%s]: %v\n", imageId, taskInfo.Error)
		logger.Error(msg)
		return nil, errors.New(msg)
	}

	return ConvertTaskResult2DeleteImageResponses(taskInfo.Result)
}

func PullImage(client *docker.DockerClient, imageName string, registryToken string) error {
	ctx := context.Background()
	task, err := client.AsyncImage.Pull(ctx, imageName, registryToken)
	if err != nil {
		msg := fmt.Sprintf("Failed to create pull image[%s] task: %v\n", imageName, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	fmt.Printf("image pull task started with ID: %s\n", task.ID)
	info, err := task.Wait(ctx, DaemonServerLongPollIntervalSecond*time.Minute, DaemonServerLongTimeoutSecond*time.Minute)
	if err != nil {
		msg := fmt.Sprintf("Pull image[%s] err: %v\n", imageName, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	if info.Error != "" {
		msg := fmt.Sprintf("Pull image[%s] err: %v\n", imageName, info.Error)
		logger.Error(msg)
		return errors.New(msg)
	}
	return nil
}

func PullImageReturnTask(client *docker.DockerClient, imageName string, registryToken string) (*taskflow.Task, error) {
	ctx := context.Background()
	task, err := client.AsyncImage.Pull(ctx, imageName, registryToken)
	if err != nil {
		msg := fmt.Sprintf("Failed to create pull image[%s] task: %v\n", imageName, err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	return task, nil
}

// PushImage pushes an image to a registry
func PushImage(client *docker.DockerClient, imageName string, auth string) error {
	ctx := context.Background()
	task, err := client.AsyncImage.Push(ctx, imageName, auth)
	if err != nil {
		msg := fmt.Sprintf("Failed to create push image[%s] task: %v\n", imageName, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	fmt.Printf("Image push task started with ID: %s\n", task.ID)
	info, err := task.Wait(ctx, DaemonServerLongPollIntervalSecond*time.Minute, DaemonServerLongTimeoutSecond*time.Minute)
	if err != nil {
		msg := fmt.Sprintf("Push image[%s] err: %v\n", imageName, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	if info.Error != "" {
		msg := fmt.Sprintf("Push image[%s] err: %v\n", imageName, info.Error)
		logger.Error(msg)
		return errors.New(msg)
	}
	return nil
}

// PushImageReturnTask pushes an image to a registry
func PushImageReturnTask(client *docker.DockerClient, imageName string, auth string) (*taskflow.Task, error) {
	ctx := context.Background()
	task, err := client.AsyncImage.Push(ctx, imageName, auth)
	if err != nil {
		msg := fmt.Sprintf("Failed to create push image[%s] task: %v\n", imageName, err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	return task, nil
}

// StartContainer starts a container
func StartContainer(client *docker.DockerClient, containerId string) error {
	ctx := context.Background()
	task, err := client.AsyncContainer.Start(ctx, containerId)
	if err != nil {
		msg := fmt.Sprintf("Failed to create start container[%s] task: %v\n", containerId, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	fmt.Printf("Container start task started with ID: %s\n", task.ID)
	_, err = WaitTaskComplete(ctx, task)
	if err != nil {
		msg := fmt.Sprintf("Start container[%s] err: %v\n", containerId, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	return nil
}

// RestartContainer restarts a container
func RestartContainer(client *docker.DockerClient, containerId string, timeout int) error {
	ctx := context.Background()
	task, err := client.AsyncContainer.Restart(ctx, containerId, &timeout)
	if err != nil {
		msg := fmt.Sprintf("Failed to create  restart container[%s] task: %v\n", containerId, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	fmt.Printf("Container restart task started with ID: %s\n", task.ID)
	_, err = WaitTaskComplete(ctx, task)
	if err != nil {
		msg := fmt.Sprintf("Restart container[%s] err: %v\n", containerId, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	return nil
}

// CommitContainer commits a container to an image
func CommitContainer(client *docker.DockerClient, containerId string, options container.CommitOptions) (string, error) {
	ctx := context.Background()
	task, err := client.AsyncContainer.Commit(ctx, containerId, options)
	if err != nil {
		msg := fmt.Sprintf("Failed to create commit container[%s] task: %v\n", containerId, err)
		logger.Error(msg)
		return "", errors.New(msg)
	}
	fmt.Printf("Container commit task started with ID: %s\n", task.ID)
	taskInfo, err := WaitLongTaskComplete(ctx, task)
	if err != nil {
		msg := fmt.Sprintf("Commit container[%s] err: %v\n", containerId, err)
		logger.Error(msg)
		return "", errors.New(msg)
	}

	// Extract image ID from task result
	if taskInfo.Result == nil {
		return "", errors.New("commit task completed but no image ID returned")
	}
	result := taskInfo.Result.(map[string]interface{})
	imageId, ok := result["image_id"]
	if !ok {
		return "", errors.New("commit task completed but result is not a valid image ID")
	}

	return imageId.(string), nil
}

// ListContainers lists all containers
func ListContainers(client *docker.DockerClient, all bool) ([]types.Container, error) {
	ctx := context.Background()
	containers, err := client.Container.List(ctx, all)
	if err != nil {
		msg := fmt.Sprintf("Failed to list containers: %v\n", err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	return containers, nil
}

// ListContainersByFilter lists containers with specified filters
func ListContainersByFilter(client *docker.DockerClient, options container.ListOptions) ([]types.Container, error) {
	ctx := context.Background()
	containers, err := client.Container.ListWithFilters(ctx, options)
	if err != nil {
		msg := fmt.Sprintf("Failed to list containers with filters: %v\n", err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	return containers, nil
}

// ListImages lists all images
func ListImages(client *docker.DockerClient, options image.ListOptions) ([]image.Summary, error) {
	ctx := context.Background()
	images, err := client.Image.List(ctx, options)
	if err != nil {
		msg := fmt.Sprintf("Failed to list images: %v\n", err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	return images, nil
}

// InspectContainer gets detailed information about a container
func InspectContainer(client *docker.DockerClient, containerId string) (*docker.EnhancedContainerDetails, error) {
	ctx := context.Background()
	containerInfo, err := client.Container.InspectDetailed(ctx, containerId)
	if err != nil {
		msg := fmt.Sprintf("Failed to inspect container[%s]: %v\n", containerId, err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	return containerInfo, nil
}

// InspectImage gets detailed information about an image
func InspectImage(client *docker.DockerClient, imageId string) (image.InspectResponse, error) {
	ctx := context.Background()
	imageInfo, err := client.Image.InspectDetailed(ctx, imageId)
	if err != nil {
		msg := fmt.Sprintf("Failed to inspect image[%s]: %v\n", imageId, err)
		logger.Error(msg)
		return image.InspectResponse{}, errors.New(msg)
	}
	return *imageInfo, nil
}

// InspectImageByName gets detailed information about an image
func InspectImageByName(client *docker.DockerClient, imageName string) (image.InspectResponse, error) {
	ctx := context.Background()
	images, err := client.Image.List(ctx, image.ListOptions{
		All:     true,
		Filters: filters.NewArgs(filters.Arg("reference", imageName)),
	})
	if err != nil {
		msg := fmt.Sprintf("Failed to list images: %v\n", err)
		logger.Error(msg)
		return image.InspectResponse{}, errors.New(msg)
	}
	if len(images) == 0 {
		msg := fmt.Sprintf("image[%s] not found", imageName)
		logger.Error(msg)
		return image.InspectResponse{}, errors.New(msg)
	}
	return InspectImage(client, images[0].ID)
}

// PruneImages removes unused images
func PruneImages(client *docker.DockerClient, all bool) (image.PruneReport, error) {
	ctx := context.Background()
	task, err := client.AsyncImage.Prune(ctx, all)
	if err != nil {
		msg := fmt.Sprintf("Failed to create prune images task: %v\n", err)
		logger.Error(msg)
		return image.PruneReport{}, errors.New(msg)
	}
	fmt.Printf("Image prune task started with ID: %s\n", task.ID)
	taskInfo, err := WaitTaskComplete(ctx, task)
	if err != nil {
		msg := fmt.Sprintf("Prune images err: %v\n", err)
		logger.Error(msg)
		return image.PruneReport{}, errors.New(msg)
	}
	if taskInfo.Error != "" {
		msg := fmt.Sprintf("Prune images err: %v\n", taskInfo.Error)
		logger.Error(msg)
		return image.PruneReport{}, errors.New(msg)
	}
	return ConvertTaskResult2ImagePruneReport(taskInfo.Result)
}

func ExecContainerCommand(client *docker.DockerClient, containerId string, options container.ExecOptions) (*container.ExecInspect, error) {
	ctx := context.Background()
	task, err := client.AsyncContainer.Exec(ctx, containerId, options)
	if err != nil {
		msg := fmt.Sprintf("Failed to execute command in container[%s]: %v\n", containerId, err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	fmt.Printf("Container exec task started with ID: %s\n", task.ID)
	taskInfo, err := WaitTaskComplete(ctx, task)
	if err != nil {
		msg := fmt.Sprintf("Exec container[%s] err: %v\n", containerId, err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	if taskInfo.Error != "" {
		msg := fmt.Sprintf("Exec container[%s] command err: %v\n", containerId, taskInfo.Error)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	return ConvertTaskResult2ContainerExecInspect(taskInfo.Result)
}

func ExecCommand(dockerClient *docker.DockerClient, command string, args []string, timeout int) (string, int, error) {
	// Create a system client
	client := system.NewSystemClient(dockerClient.BaseClient.BaseURL)

	// Create a command request
	req := system.ExecuteCommandRequest{
		Command: command,
		Args:    args,
		Timeout: timeout,
	}

	// Execute command and wait for result
	ctx := context.Background()
	logger.Debugf("Execute command: %s %s", command, args)
	taskInfo, err := client.ExecuteCommandAndWait(ctx, req, 1, timeout)
	if err != nil {
		return "", 0, err
	}

	// Get command result
	result, err := client.GetCommandResult(ctx, taskInfo.ID)
	logger.Debugf("Command result: %s, err: %v", result, err)
	if err != nil {
		return "", 0, err
	}

	return result.Output, result.ExitCode, nil
}

func LoadImage(dockerClient *docker.DockerClient, imageSavePath string) error {
	ctx := context.Background()

	task, err := dockerClient.AsyncImage.Load(ctx, imageSavePath)
	if err != nil {
		logger.Error(err)
		return err
	}

	info, err := task.Wait(ctx, DaemonServerLongPollIntervalSecond*time.Minute, DaemonServerLongTimeoutSecond*time.Minute)
	if err != nil {
		msg := fmt.Sprintf("Load images[%s] err: %v\n", imageSavePath, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	if info.Error != "" {
		msg := fmt.Sprintf("Load images[%s] err: %v\n", imageSavePath, info.Error)
		logger.Error(msg)
		return errors.New(msg)
	}
	return nil
}

func GetImageHistory(dockerClient *docker.DockerClient, imageId string) ([]image.HistoryResponseItem, error) {
	ctx := context.Background()
	history, err := dockerClient.Image.History(ctx, imageId)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	return history, nil
}

func GetImageHistoryByName(client *docker.DockerClient, imageName string) ([]image.HistoryResponseItem, error) {
	ctx := context.Background()
	images, err := client.Image.List(ctx, image.ListOptions{
		All:     true,
		Filters: filters.NewArgs(filters.Arg("reference", imageName)),
	})
	if err != nil {
		msg := fmt.Sprintf("Failed to list images: %v\n", err)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	if len(images) == 0 {
		msg := fmt.Sprintf("image[%s] not found", imageName)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	return GetImageHistory(client, images[0].ID)
}

func ExecCommandWithRealtimeOutput(dockerClient *docker.DockerClient, command string, args []string, timeout int) (*taskflow.Task, error) {
	// Create a system client
	client := system.NewSystemClient(dockerClient.BaseClient.BaseURL)

	// Create a command request
	req := system.ExecuteCommandRequest{
		Command: command,
		Args:    args,
		Timeout: timeout,
	}

	// Execute command and wait for result
	ctx := context.Background()
	task, err := client.ExecuteCommandWithRealTimeOutput(ctx, req)
	if err != nil {
		return nil, err
	}
	return task, nil
}

func GetTaskStreamOutput(task *taskflow.Task, offset int, isStdout bool) ([]string, error) {
	ctx := context.Background()
	return task.GetTaskOutputByOffset(ctx, offset, isStdout)
}

func StatsContainer(client *docker.DockerClient, containerId string) (container.StatsResponse, error) {
	ctx := context.Background()
	stats, err := client.Container.GetContainerStats(ctx, containerId, false)
	if err != nil {
		logger.Error(err)
		return container.StatsResponse{}, err
	}
	return *stats, nil
}

func SaveImage(client *docker.DockerClient, imageId string, savePath string) error {
	ctx := context.Background()

	task, err := client.AsyncImage.Save(ctx, imageId, savePath)
	if err != nil {
		logger.Error(err)
		return err
	}
	info, err := task.Wait(ctx, DaemonServerLongPollIntervalSecond*time.Minute, DaemonServerLongTimeoutSecond*time.Minute)
	if err != nil {
		msg := fmt.Sprintf("Save images[%s] err: %v\n", imageId, err)
		logger.Error(msg)
		return errors.New(msg)
	}
	if info.Error != "" {
		msg := fmt.Sprintf("Save images[%s] err: %v\n", imageId, info.Error)
		logger.Error(msg)
		return errors.New(msg)
	}
	return nil
}
