package daemon_server

import (
	daemondocker "daemon-server/sdk/docker"
	"encoding/json"
	"fmt"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/image"
	"strconv"
	"strings"
)

// ConvertDaemonDocker2Container 将 daemondocker.EnhancedContainerDetails 转换为 types.Container
func ConvertDaemonDocker2Container(info daemondocker.EnhancedContainerDetails) types.Container {
	// 初始化结果对象
	result := types.Container{
		ID:                      info.ID,
		Names:                   []string{info.Name},
		Image:                   info.Image,
		ImageID:                 info.ImageID,
		ImageManifestDescriptor: nil,
		Command:                 info.Command,
		Created:                 info.Created.Unix(),
		SizeRw:                  info.SizeRw,
		SizeRootFs:              info.SizeRootFs,
		State:                   info.State,
		Status:                  info.Status,
		HostConfig: struct {
			NetworkMode string            `json:",omitempty"`
			Annotations map[string]string `json:",omitempty"`
		}{},
	}

	// 安全地设置 Labels
	if info.Labels != nil {
		result.Labels = info.Labels
	}

	// 安全地设置 Ports
	// 注意: 这里可能需要转换类型，因为 daemondocker.EnhancedContainerDetails 中的 Ports 类型可能与 types.Container 中的不同
	if info.Ports != nil {
		// 将 map[string][]PortBinding 转换为 []types.Port
		ports := []types.Port{}
		for portStr, bindings := range info.Ports {
			for _, binding := range bindings {
				// 解析端口号
				portParts := strings.Split(portStr, "/")
				portNum, _ := strconv.ParseUint(portParts[0], 10, 16)
				portType := "tcp"
				if len(portParts) > 1 {
					portType = portParts[1]
				}

				hostPort, _ := strconv.ParseUint(binding.HostPort, 10, 16)

				ports = append(ports, types.Port{
					IP:          binding.HostIP,
					PrivatePort: uint16(portNum),
					PublicPort:  uint16(hostPort),
					Type:        portType,
				})
			}
		}
		result.Ports = ports
	}

	// 安全地设置 Mounts
	if info.Mounts != nil {
		result.Mounts = info.Mounts
	}

	// 安全地设置 HostConfig.NetworkMode
	if info.HostConfig != nil {
		// 如果 HostConfig 是 interface{} 类型，需要进行类型断言
		hostConfig, ok := info.HostConfig.(*container.HostConfig)
		if ok && hostConfig != nil {
			result.HostConfig.NetworkMode = string(hostConfig.NetworkMode)
		} else {
			// 如果不是期望的类型，尝试使用 NetworkMode 字段
			result.HostConfig.NetworkMode = info.NetworkMode
		}
	} else if info.NetworkMode != "" {
		// 如果 HostConfig 为 nil，但有 NetworkMode 字段
		result.HostConfig.NetworkMode = info.NetworkMode
	}

	// 安全地设置 NetworkSettings
	if info.NetworkSettings != nil {
		// 如果 NetworkSettings 是 interface{} 类型，需要进行类型断言
		netSettings, ok := info.NetworkSettings.(*types.NetworkSettings)
		if ok && netSettings != nil && netSettings.Networks != nil {
			result.NetworkSettings = &types.SummaryNetworkSettings{
				Networks: netSettings.Networks,
			}
		}
	}

	return result
}

func ConvertTaskResult2DeleteImageResponses(result interface{}) ([]image.DeleteResponse, error) {
	var res map[string]interface{}
	err := json.Unmarshal([]byte(result.(string)), &res)
	if err != nil {
		return nil, err
	}

	responses := res["responses"].([]image.DeleteResponse)
	return responses, nil
}

func ConvertTaskResult2ImagePruneReport(result interface{}) (image.PruneReport, error) {
	var res image.PruneReport
	err := json.Unmarshal([]byte(result.(string)), &res)
	return res, err
}

func ConvertTaskResult2ContainerExecInspect(result interface{}) (*container.ExecInspect, error) {
	resp := result.(map[string]interface{})
	inspect, ok := resp["resp"].(container.ExecInspect)
	if !ok {
		return nil, fmt.Errorf("ConvertTaskResult2ContainerExecInspect invalid key: resp")
	}
	return &inspect, nil
}
