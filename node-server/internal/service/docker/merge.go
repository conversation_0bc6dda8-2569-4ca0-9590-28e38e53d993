package docker

import (
	"archive/tar"
	"context"
	"crypto/sha256"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"node-server/internal/common/logger"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/docker/docker/client"
	dfg "github.com/ozankasikci/dockerfile-generator"
)

func MergeLayer() error {

	/**
	名词解释
		源镜像：需要进行合并的镜像，比如客户保存多次的镜像
		基础镜像： 需要基于此镜像进行加一层形成新的镜像的镜像，比如comfyui-base:20241010-1
		输出目录:  合并层得到的层文件所在的目录
	整体逻辑：
		基于基础镜像使用Dockerfile实现
			FROM 基础镜像
			COPY 输出目录 /
			CMD bash /root/start.sh
		执行docker build -t 新镜像名 Dockerfile文件位置

	源镜像处理逻辑：
		1.把镜像打包成tar文件
		2.解压tar文件
		3.计算所有镜像层的layer.tar的MD5和layer.tar文件所在位置
		4.docker inspect 拿到源镜像和基础镜像的差异（需要进行合并的镜像层）
		5.按照顺序依次解压layer.tar文件，解压过程中要处理被删除掉的文件
			删除文件在镜像层中的逻辑：
				1.当前目录存在.wh..wh..opq文件，删除当前目录
				2.当前目录存在.wh.{文件名}，删除当前目录的{文件名}
		6.得到一个最终的镜像层的变更文件
	**/

	sourceImage := "10.20.103.240/chenyu/public/380fa0b820cc446486323550d477c0d8:v3.1"
	baseImage := "10.20.103.240/chenyu/public/380fa0b820cc446486323550d477c0d8:v0.1"
	tempSourceImageExtracted := "/temp/" // 合并层得到的文件

	instructions := []dfg.Instruction{
		dfg.From{
			Image: baseImage, As: "final",
		},
	}

	//拉取并解压文件
	println("开始处理源镜像")
	err, imageLayerMap := dealSourceImage(sourceImage)
	if err != nil {
		logger.Error(err)
		return err
	}

	// 查询需要处理的layer
	println("开始查询需要处理的layer")
	err, needDealLayer := queryLayers(sourceImage, baseImage)
	if err != nil {
		logger.Error(err)
		return err
	}
	println("开始查询需要处理的layer:需要处理" + strconv.Itoa(len(needDealLayer)) + "层")
	// 合并需要处理的layer

	println("开始合并需要处理的layer")
	for _, layerSha256 := range needDealLayer {
		layerTarFile := imageLayerMap[layerSha256]
		println("开始处理layer:" + layerSha256 + ",镜像文件:" + layerTarFile)
		println("开始处理layer:解压tar文件")
		err := extractTar(layerTarFile, tempSourceImageExtracted)
		if err != nil {
			logger.Error(err)
			return err
		}
		println("开始处理layer:处理删除文件")
		err = filepath.WalkDir(tempSourceImageExtracted, func(path string, d fs.DirEntry, err error) error {
			if err != nil {
				logger.Error(err)
				return err
			}
			// 只处理文件
			if d.Type().IsRegular() && strings.HasPrefix(filepath.Base(path), ".wh") {
				baseName := filepath.Base(path)
				println("发现需要删除文件:" + baseName)
				// 处理 .wh..wh..opq 表示覆盖删除当前文件夹内容
				if baseName == ".wh..wh..opq" {
					println("发现文件.wh..wh..opq，删除文件夹:" + path)
					dir := filepath.Dir(path)
					err := os.RemoveAll(dir)
					if err != nil {
						logger.Error(err)
						return fmt.Errorf("failed to remove opaque directory %s: %v", dir, err)
					}
					return nil
				}

				// 处理单个文件或目录删除
				// .wh.filename 表示文件删除
				relativePath := baseName[4:] // 移除 `.wh.` 前缀
				println("删除文件:" + relativePath)
				deletionPath := filepath.Join(tempSourceImageExtracted, relativePath)
				err := os.RemoveAll(deletionPath)
				if err != nil {
					logger.Error(err)
					return fmt.Errorf("failed to remove %s: %v", deletionPath, err)
				}

			}
			return nil
		})
	}
	instructions = append(instructions, dfg.CopyCommand{
		Sources:     []string{tempSourceImageExtracted},
		Destination: "/",
	})
	instructions = append(instructions, dfg.Cmd{
		Params: []string{"bash", "/root/start.sh"},
	})
	data := &dfg.DockerfileData{
		Stages: []dfg.Stage{
			// Stage 2 - Final Image
			instructions,
		},
	}
	tmpl := dfg.NewDockerfileTemplate(data)
	buff := strings.Builder{}
	tmpl.Render(&buff)
	println(buff.String())
	return nil
}

func queryLayers(sourceImage string, baseImage string) (err error, layers []string) {
	println("开始查询需要处理的layer:获取docker连接")
	cli, err := newClient()
	if err != nil {
		logger.Error(err)
		return err, layers
	}
	defer cli.Close()

	println("开始查询需要处理的layer:获取源镜像信息")
	sourceImageInspect, _, err := cli.ImageInspectWithRaw(context.Background(), sourceImage)
	if err != nil {
		logger.Error(err)
		return err, layers
	}
	println("开始查询需要处理的layer:获取基础镜像信息")
	baseImageInspect, _, err := cli.ImageInspectWithRaw(context.Background(), baseImage)
	if err != nil {
		logger.Error(err)
		return err, layers
	}
	sourceLayers := sourceImageInspect.RootFS.Layers
	baseLayers := baseImageInspect.RootFS.Layers

	println("开始查询需要处理的layer:比较镜像层")
	println("基础镜像层数：" + strconv.Itoa(len(baseLayers)))
	println("源镜像层数：" + strconv.Itoa(len(sourceLayers)))

	for _, layer := range baseLayers {
		if !contains(layer, sourceLayers) {
			err = errors.New("镜像合并失败，基础镜像包含源镜像不存在的层")
			return err, layers
		}
	}
	return nil, sourceLayers[len(baseLayers):]
}

func contains(item string, list []string) bool {
	for _, v := range list {
		if v == item {
			return true
		}
	}
	return false
}

func dealSourceImage(sourceImage string) (err error, imageLayerMap map[string]string) {
	imageLayerMap = make(map[string]string)
	println("开始处理源镜像-获取docker连接")
	cli, err := newClient()
	if err != nil {
		logger.Error(err)
		return err, imageLayerMap
	}
	defer cli.Close()

	tempSourceImage := "temp_source_image.tar"
	tempSourceImageExtracted := "temp_source_image"
	// 创建临时文件
	println("开始处理源镜像-创建临时文件temp_source_image.tar")
	tarFile, err := os.Create(tempSourceImage)
	if err != nil {
		logger.Error(err)
		return err, imageLayerMap
	}

	println("开始处理源镜像-拉取保存镜像")
	saveReader, err := cli.ImageSave(context.Background(), []string{sourceImage})
	if err != nil {
		logger.Error(err)
		return err, imageLayerMap
	}
	defer saveReader.Close()
	// 将镜像内容写入 tar 文件
	println("开始处理源镜像-写入tar文件")
	if _, err = io.Copy(tarFile, saveReader); err != nil {
		logger.Error(err)
		return err, imageLayerMap
	}
	//解压tar
	println("开始处理源镜像-解压tar文件")
	err = extractTar(tempSourceImage, tempSourceImageExtracted)
	if err != nil {
		logger.Error(err)
		return err, imageLayerMap
	}

	// 获取image的文件路径与layer的关联关系
	println("开始处理源镜像-获取layer文件")
	err = filepath.WalkDir(tempSourceImageExtracted, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			logger.Error(err)
			return err
		}
		// 只处理文件
		if d.Type().IsRegular() && filepath.Base(path) == "layer.tar" {
			// 确保文件在子目录中
			if relPath, err := filepath.Rel(tempSourceImageExtracted, path); err == nil && len(relPath) > 0 {
				if sha256 := calculateSHA256(path); sha256 != "" {
					sha256sum := "sha256:" + sha256
					imageLayerMap[sha256sum] = path
				} else {
					err = errors.New("calculateSHA256 is empty ")
					logger.Error(err, " path:", path)
					return err
				}
			}
		}
		return nil
	})
	print("处理源镜像完成：共计镜像层数:" + strconv.Itoa(len(imageLayerMap)))
	return err, imageLayerMap
}

func newClient() (*client.Client, error) {
	// Docker API 地址配置
	dockerHost := "tcp://127.0.01:2375"
	dockerHost = "tcp://***************"
	//创建 Docker 客户端，使用指定的 API 地址
	return client.NewClientWithOpts(client.WithHost(dockerHost), client.WithVersion("1.43"))
}

// extractTar 用于解压 tar.gz 文件
func extractTar(src string, dest string) error {
	// 创建目标目录（如果它不存在）
	if err := os.MkdirAll(dest, os.ModePerm); err != nil {
		logger.Error(err)
		return fmt.Errorf("failed to create directory: %v", err)
	}
	// 打开 .tar 文件
	file, err := os.Open(src)
	if err != nil {
		logger.Error(err)
		return fmt.Errorf("failed to open tar file: %v", err)
	}
	defer file.Close()

	// 创建 tar reader
	tarReader := tar.NewReader(file)

	// 循环读取 tar 文件中的每一层
	for {
		// 读取 tar 文件的头部信息
		header, err := tarReader.Next()
		if err == io.EOF {
			// 到达文件末尾
			break
		}
		if err != nil {
			logger.Error(err)
			return fmt.Errorf("failed to read tar header: %v", err)
		}

		// 根据 tar header 的名字创建目标文件的路径
		targetPath := filepath.Join(dest, header.Name)

		// 创建目标目录（如果它不存在）
		if header.Typeflag == tar.TypeDir {
			if err := os.MkdirAll(targetPath, os.ModePerm); err != nil {
				logger.Error(err)
				return fmt.Errorf("failed to create directory: %v", err)
			}
			continue
		}

		// 创建目标文件
		file, err := os.Create(targetPath)
		if err != nil {
			logger.Error(err)
			return fmt.Errorf("failed to create file: %v", err)
		}

		// 将 tar 文件中的内容写入目标文件
		if _, err := io.Copy(file, tarReader); err != nil {
			logger.Error(err)
			file.Close()
			return fmt.Errorf("failed to extract file: %v", err)
		}
		file.Close()

		// 设置文件的权限和时间戳等属性
		if err := os.Chmod(targetPath, os.FileMode(header.Mode)); err != nil {
			logger.Error(err)
			return fmt.Errorf("failed to set file permissions: %v", err)
		}
	}

	return nil
}

func calculateSHA256(filePath string) string {
	file, err := os.Open(filePath)
	if err != nil {
		logger.Error(err)
		return ""
		//log.Fatalf("Failed to open file: %s", err)
	}
	defer file.Close()

	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		logger.Error(err)
		return ""
		//log.Fatalf("Failed to calculate hash: %s", err)
	}

	return fmt.Sprintf("%x", hash.Sum(nil))
}
