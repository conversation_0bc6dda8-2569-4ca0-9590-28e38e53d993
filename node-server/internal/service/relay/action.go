package relay

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"node-server/internal/common/logger"
	"node-server/internal/config"
	"os"
	"sync"
	"time"
)

// RelayRedisClient holds the Redis client for relay service
var (
	relayRDB  *redis.Client
	relayOnce sync.Once
	ctx       = context.Background()
	KeyPrefix = "relay:mapping:"
)

// InitRelayRedisClient initializes a separate Redis client for relay service
func InitRelayRedisClient() error {
	var err error
	relayOnce.Do(func() {
		RelayRedisConnString := os.Getenv("RELAY_REDIS_CONN_STRING")
		if RelayRedisConnString == "" {
			RelayRedisConnString = config.RELAY_REDIS_CONN_STRING
		}
		if RelayRedisConnString == "" {
			logger.Error("the RELAY_REDIS_CONN_STRING in config is not set, Redis is not enabled")
			return
		}
		logger.Info("relay redis config is enable")

		// Parse Redis connection string
		opt, parseErr := redis.ParseURL(RelayRedisConnString)
		if parseErr != nil {
			err = parseErr
			logger.Error(err.Error())
			return
		}
		logger.Info("relay redis client init ...")
		// Create new Redis client
		relayRDB = redis.NewClient(opt)
		if relayRDB == nil {
			err = errors.New("failed to create relay Redis client")
			logger.Error(err.Error())
			return
		}
		// Test connection
		ctxTimeout, cancel := context.WithTimeout(ctx, 5*time.Second)
		defer cancel()

		_, pingErr := relayRDB.Ping(ctxTimeout).Result()
		if pingErr != nil {
			err = pingErr
			logger.Error("ping relay redis server err: " + err.Error())
			return
		}
		logger.Info("relay redis client init complete")
	})

	return err
}

// getRedisClient returns the relay Redis client, initializing it if necessary
func getRedisClient() (*redis.Client, error) {
	if relayRDB == nil {
		if err := InitRelayRedisClient(); err != nil {
			return nil, err
		}
	}
	return relayRDB, nil
}

// HandleMapping handles different CRUD operations for key-value mappings in Redis
func HandleMapping(action string, key string, value string) (string, error) {
	if action == "add" {
		if KeyExists(key) {
			err := updateMapping(key, value)
			return "", err
		} else {
			err := addMapping(key, value)
			return "", err
		}
	}
	if action == "remove" {
		err := removeMapping(key)
		return "", err
	}
	if action == "get" {
		str, err := getMapping(key)
		if err != nil {
			return "", err
		} else {
			return str, nil
		}
	}
	if action == "update" {
		if KeyExists(key) {
			err := updateMapping(key, value)
			return "", err
		} else {
			err := addMapping(key, value)
			return "", err
		}
	}
	if action == "list" {
		str, err := ListMappings()
		return str, err
	}
	return "", nil
}

// updateMapping updates an existing key-value mapping in Redis
// If the key doesn't exist, it will be created
func updateMapping(key string, value string) error {
	rdb, err := getRedisClient()
	if err != nil {
		return err
	}

	// Set with no expiration (0)
	return rdb.Set(ctx, KeyPrefix+key, value, 0).Err()
}

// getMapping retrieves a value by key from Redis
func getMapping(key string) (string, error) {
	rdb, err := getRedisClient()
	if err != nil {
		return "", err
	}

	value, err := rdb.Get(ctx, KeyPrefix+key).Result()
	if err == redis.Nil {
		// Key does not exist
		return "", nil
	}
	return value, err
}

// removeMapping deletes a key-value mapping from Redis
func removeMapping(key string) error {
	rdb, err := getRedisClient()
	if err != nil {
		return err
	}

	return rdb.Del(ctx, KeyPrefix+key).Err()
}

// addMapping adds a new key-value mapping to Redis
// If the key already exists, it will return an error
func addMapping(key string, value string) error {
	rdb, err := getRedisClient()
	if err != nil {
		return err
	}

	// Check if key already exists
	exists, err := rdb.Exists(ctx, KeyPrefix+key).Result()
	if err != nil {
		return err
	}

	if exists > 0 {
		return redis.Nil // Using redis.Nil to indicate key already exists
	}

	// Set with no expiration (0)
	return rdb.Set(ctx, KeyPrefix+key, value, 0).Err()
}

// SetMappingWithExpiration adds or updates a key-value mapping with an expiration time
func SetMappingWithExpiration(key string, value string, expiration time.Duration) error {
	rdb, err := getRedisClient()
	if err != nil {
		return err
	}

	return rdb.Set(ctx, KeyPrefix+key, value, expiration).Err()
}

// ListMappings returns all keys matching the relay mapping pattern
func ListMappings() (string, error) {
	rdb, err := getRedisClient()
	if err != nil {
		return "", err
	}

	// Get all keys with the prefix
	var keys []string
	var cursor uint64 = 0
	for {
		var scanKeys []string
		var err error
		scanKeys, cursor, err = rdb.Scan(ctx, cursor, KeyPrefix+"*", 10).Result()
		if err != nil {
			return "", err
		}
		keys = append(keys, scanKeys...)
		if cursor == 0 {
			break
		}
	}

	result := ""
	for _, fullKey := range keys {
		// Extract the key without prefix
		key := fullKey[len(KeyPrefix):]

		// Get the value
		value, err := rdb.Get(ctx, fullKey).Result()
		if err != nil && err != redis.Nil {
			return "", err
		}

		if !errors.Is(err, redis.Nil) {
			result += fmt.Sprintf("%s  %s\n", key, value)
		}
	}

	return result, nil
}

// KeyExists checks if a key exists in Redis
func KeyExists(key string) bool {
	rdb, err := getRedisClient()
	if err != nil {
		return false
	}

	exists, err := rdb.Exists(ctx, KeyPrefix+key).Result()
	if err != nil {
		return false
	}

	return exists > 0
}

// CloseRelayRedisClient closes the Redis client connection
func CloseRelayRedisClient() error {
	if relayRDB != nil {
		return relayRDB.Close()
	}
	return nil
}
