package relay

import (
	"os"
	"testing"
	"time"
)

// TestRedisOperations tests the Redis CRUD operations
// Note: This test requires a running Redis instance
func TestRedisOperations(t *testing.T) {
	// Set Redis connection string for testing
	// You can override this with your test Redis instance
	os.Setenv("RELAY_REDIS_CONN_STRING", "redis://localhost:6379/0")

	// Initialize Redis client
	err := InitRelayRedisClient()
	if err != nil {
		t.Fatalf("Failed to initialize Redis client: %v", err)
	}
	// Make sure to close the client when the test is done
	defer CloseRelayRedisClient()

	// Test key and value
	testKey := "test_key"
	testValue := "test_value"
	updatedValue := "updated_value"

	// Clean up any existing test key
	_ = removeMapping(testKey)

	// Test addMapping
	err = addMapping(testKey, testValue)
	if err != nil {
		t.<PERSON>rrorf("addMapping failed: %v", err)
	}

	// Test getMapping
	value, err := getMapping(testKey)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("getMapping failed: %v", err)
	}
	if value != testValue {
		t.<PERSON><PERSON>rf("getMapping returned wrong value: got %v, want %v", value, testValue)
	}

	// Test updateMapping
	err = updateMapping(testKey, updatedValue)
	if err != nil {
		t.Errorf("updateMapping failed: %v", err)
	}

	// Verify update
	value, err = getMapping(testKey)
	if err != nil {
		t.Errorf("getMapping after update failed: %v", err)
	}
	if value != updatedValue {
		t.Errorf("getMapping after update returned wrong value: got %v, want %v", value, updatedValue)
	}

	// Test KeyExists
	exists := KeyExists(testKey)
	if !exists {
		t.Errorf("KeyExists returned false for existing key")
	}

	// Test SetMappingWithExpiration
	expKey := "exp_test_key"
	err = SetMappingWithExpiration(expKey, testValue, 1*time.Second)
	if err != nil {
		t.Errorf("SetMappingWithExpiration failed: %v", err)
	}

	// Verify expiration key exists
	exists = KeyExists(expKey)
	if !exists {
		t.Errorf("KeyExists returned false for expiration key")
	}

	// Wait for expiration
	time.Sleep(2 * time.Second)

	// Verify key expired
	exists = KeyExists(expKey)
	if exists {
		t.Errorf("Key did not expire as expected")
	}

	// Test removeMapping
	err = removeMapping(testKey)
	if err != nil {
		t.Errorf("removeMapping failed: %v", err)
	}

	// Verify removal
	exists = KeyExists(testKey)
	if exists {
		t.Errorf("Key still exists after removal")
	}

	// Test GetAllMappings
	// First, add a few test mappings
	testKeys := []string{"test_key1", "test_key2", "test_key3"}
	testValues := []string{"value1", "value2", "value3"}

	for i, key := range testKeys {
		err = addMapping(key, testValues[i])
		if err != nil {
			t.Errorf("Failed to add test mapping %s: %v", key, err)
		}
	}

	// Get all mappings
	_, err = ListMappings()
	if err != nil {
		t.Errorf("GetAllMappings failed: %v", err)
	}

	// Clean up test keys
	for _, key := range testKeys {
		_ = removeMapping(key)
	}
}
