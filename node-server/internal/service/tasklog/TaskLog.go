package tasklog

import (
	"context"
	"encoding/json"
	"fmt"
	"node-server/internal/common"
	"node-server/internal/common/jsontime"
	"node-server/internal/common/logger"
	"node-server/internal/common/utils"
	"node-server/internal/enums"
	"strings"
	"time"

	"gorm.io/gorm"
)

var RedisKey = enums.RedisKeyEnum.CpnSchedTaskLog

type TaskItem struct {
	State string `json:"state"`
	Done  bool   `json:"done"`
}

type TaskLogItem struct {
	Msg     string            `json:"msg"`               //对外显示的消息
	State   string            `json:"state,omitempty"`   //当前的状态
	Done    bool              `json:"done,omitempty"`    //任务是否已结束
	Log     string            `json:"log,omitempty"`     //日志内容
	Data    interface{}       `json:"data,omitempty"`    //日志数据
	Percent float64           `json:"percent,omitempty"` //任务进度
	Time    jsontime.JsonTime `json:"time"`              //日志添加时间
}

type TaskProgress struct {
	Status      string  `json:"status,omitempty"`
	CoverStatus string  `json:"cover_status,omitempty"`
	Current     int64   `json:"current,omitempty"`
	Total       int64   `json:"total,omitempty"`
	Percent     float64 `json:"percent,omitempty"`
}

func GenLogKey(task string, key string) (string, error) {
	if task == TaskEnum.CommitDocker || task == TaskEnum.SaveImage || task == TaskEnum.SaveInstanceImage || task == TaskEnum.SaveImageAndShutdown {
		return fmt.Sprintf("SaveImage_%s", key), nil
	}
	if task == TaskEnum.PullImage {
		return fmt.Sprintf("PullImage_%s", key), nil
	}

	return fmt.Sprintf("%s_%s", task, key), nil
}

func GetLogKey(ctx context.Context) string {
	if ctx != nil {
		if tmpLogKey, ok := ctx.Value("logkey").(string); ok {
			return tmpLogKey
		}
	}
	return ""
}

func Save(ctx context.Context, state string, showMsg string, logTxt string, data interface{}) {
	if ctx != nil {
		if tmpLogKey, ok := ctx.Value("logkey").(string); ok {
			save(tmpLogKey, state, showMsg, logTxt, data)
		}
	}
}

func save(logKey string, state string, showMsg string, logTxt string, data interface{}) {
	if logKey == "" {
		logger.Error("有参数为空 logKey:", logKey)
		return
	}
	listKey := RedisKey + logKey

	lastState := ""
	lastShowMsg := ""
	percent := float64(0)
	done := false
	coverIndex := int64(-1)
	coverValue := ""
	if lastValue, length, err := LastValue(logKey); err != nil {
		if err != gorm.ErrEmptySlice {
			logger.Error(err, "key:", logKey)
		}
	} else {
		var lastLog TaskLogItem
		if err := utils.GetStructFromJson(&lastLog, lastValue); err != nil {
			logger.Error(err, " logKey:", logKey, " str:", lastValue)
		} else {

		}
		lastState = lastLog.State
		lastShowMsg = lastLog.Msg
		coverStatus := ""
		percent = lastLog.Percent
		if lastLog.Data != nil {
			var lastProgress TaskProgress
			json := utils.GetJsonFromStruct(lastLog.Data)
			if err := utils.GetStructFromJson(&lastProgress, json); err == nil {
				coverStatus = lastProgress.CoverStatus
				if lastProgress.Status != "" {
					percent = lastProgress.Percent
				}
			}
		}

		if coverStatus != "" && data != nil {
			var curProgress TaskProgress
			json := utils.GetJsonFromStruct(data)
			if err := utils.GetStructFromJson(&curProgress, json); err == nil {
				if strings.Contains(coverStatus, curProgress.Status) {
					coverIndex = length - 1
					coverValue = lastValue
				}
				if curProgress.Status != "" {
					percent = curProgress.Percent
				}
			}
		}
	}

	if showMsg == "" {
		showMsg = lastShowMsg
	}
	if state == "" {
		state = lastState
	}

	if state == NormalTaskStateEnum.Fail || state == NormalTaskStateEnum.Success {
		done = true
	}

	log := TaskLogItem{
		Msg:     showMsg,
		Log:     logTxt,
		State:   state,
		Done:    done,
		Data:    data,
		Percent: percent,
		Time:    jsontime.Now(),
	}
	txt := utils.GetJsonFromStruct(log)
	if coverIndex >= 0 && coverValue != "" {
		if err := common.RedisLRem(listKey, -1, coverValue); err != nil {
			logger.Error("Error removing item:", err)
			return
		}
	}
	if _, err := common.RedisRPush(listKey, txt); err != nil {
		logger.Error(err)
	}
}

func List(logKey string) ([]string, error) {
	listKey := RedisKey + logKey
	return common.RedisLRange(listKey, 0, -1)
}

func Last(logKey string) (TaskLogItem, int64, error) {
	var taskLog TaskLogItem
	listKey := RedisKey + logKey
	// 获取列表的长度
	length, err := common.RedisLLen(listKey)
	if err != nil {
		logger.Error(err)
		return taskLog, length, err
	}
	// 如果列表为空，直接返回空字符串
	if length == 0 {
		return taskLog, length, gorm.ErrEmptySlice
	}

	if str, err := common.RedisLIndex(listKey, length-1); err != nil {
		logger.Error(err)
		return taskLog, length, err
	} else {
		if str == "" {
			return taskLog, length, gorm.ErrEmptySlice
		}
		//logger.Info("last:", str)
		if err := utils.GetStructFromJson(&taskLog, str); err != nil {
			logger.Error(err, " logKey:", logKey, " str:", str)
			return taskLog, length, err
		} else {
			//logger.Info("lastitem:", utils.GetJsonFromStruct(taskLog))
			return taskLog, length, nil
		}
	}
}

func LastValue(logKey string) (string, int64, error) {
	var lastValue string
	listKey := RedisKey + logKey
	// 获取列表的长度
	length, err := common.RedisLLen(listKey)
	if err != nil {
		logger.Error(err)
		return lastValue, length, err
	}
	// 如果列表为空，直接返回空字符串
	if length == 0 {
		return lastValue, length, gorm.ErrEmptySlice
	}

	if str, err := common.RedisLIndex(listKey, length-1); err != nil {
		logger.Error(err)
		return lastValue, length, err
	} else {
		if str == "" {
			return lastValue, length, gorm.ErrEmptySlice
		}
		return str, length, nil
	}
}

func BootIn(startupMark string) (bool, error) {
	logKey := startupMark
	if !strings.HasPrefix(logKey, TaskEnum.StartupMark) {
		if tmpKey, err := GenLogKey(TaskEnum.StartupMark, startupMark); err != nil {
			logger.Error(err)
			return false, err
		} else {
			logKey = tmpKey
		}
	}
	if outTime, err := OutTime(logKey, 60*3); err != nil {
		return outTime, err
	} else {
		return !outTime, nil
	}
}

func ScanErrReasonByCtx(ctx context.Context) string {
	logKey := GetLogKey(ctx)
	if logKey == "" {
		return ""
	}
	list, err := List(logKey)
	if err != nil {
		logger.Error("logKey:", logKey, " err:", err)
		return ""
	}
	return ScanErrReason(list)
}

func ScanErrReason(list []string) string {
	mReason := make(map[string]string, 0)
	mReason["port is already allocated"] = "端口已被分配"
	mReason["context canceled"] = "主动取消"
	mReason["unknown: artifact"] = "镜像不存在"
	mReason["failed to connect to `host=postgresql"] = "链接镜像仓库失败"
	mReason["500 Internal Server Error"] = "500内部服务器错误，请重试"
	mReason["max depth exceeded"] = "镜像层数已达上线"
	mReason["no space left on device"] = "磁盘空间不足"
	mReason["invalid containerPort"] = "无效的容器端口" //stderr：docker: invalid containerPort: 123456.
	mReason["invalid reference format"] = "无效的镜像"
	mReason["Unable to find image"] = "未找到镜像"
	mReason["tag does not exist"] = "镜像标签不存在"
	mReason["Cannot connect to the Docker daemon"] = "链接Docker失败" //Cannot connect to the Docker daemon at tcp://***************:2375. Is the docker daemon running?
	for i := len(list) - 1; i >= 0; i-- {
		for key, value := range mReason {
			if strings.Contains(list[i], key) {
				return value
			}
		}
	}
	return ""
}

func OutTime(logKey string, outSeconds int64) (bool, error) {
	//logger.Info(logKey, "outSeconds:", outSeconds)
	if item, _, err := Last(logKey); err != nil {
		if err == gorm.ErrEmptySlice {
			return true, nil
		}
		logger.Info(logKey, "err:", err)
		return false, err
	} else {
		duration := time.Now().Sub(item.Time.Time())
		// 获取秒数（整数）
		seconds := int64(duration.Seconds())
		//logger.Info(logKey, "outSeconds:", outSeconds, "  seconds:", seconds)
		if seconds > outSeconds {
			return true, nil
		} else {
			return false, nil
		}
	}
}

func DoneFive(logKey string) (bool, error) {
	outSeconds := int64(300)
	if item, _, err := Last(logKey); err != nil {
		if err == gorm.ErrEmptySlice {
			return true, nil
		}
		logger.Info(logKey, "err:", err)
		return false, err
	} else {
		if item.Done {
			return true, nil
		}
		duration := time.Now().Sub(item.Time.Time())
		// 获取秒数（整数）
		seconds := int64(duration.Seconds())
		//logger.Info(logKey, "outSeconds:", outSeconds, "  seconds:", seconds)
		if seconds > outSeconds {
			return true, nil
		} else {

			return false, nil
		}
	}
}

func Delete(logKey string) error {
	listKey := RedisKey + logKey
	return common.RedisDel(listKey)
}

type taskEnum_ struct {
	StartupInstance, StartupMark, CommitDocker, PullImage, ShutdownAndDestroy, SaveImage, SaveInstanceImage, SaveImageAndDestroy, SaveImageAndShutdown, PushDocker string
}

var TaskEnum = taskEnum_{
	StartupInstance:      "StartupInstance",      //实例启动
	StartupMark:          "StartupMark",          //实例启动
	CommitDocker:         "CommitDocker",         //保存Docker到本地
	PullImage:            "PullImage",            //拉取镜像
	ShutdownAndDestroy:   "ShutdownAndDestroy",   //关闭并销毁实例
	SaveImage:            "SaveImage",            //保存镜像
	SaveInstanceImage:    "SaveInstanceImage",    //保存实例镜像(关机后将实例推送到仓库)
	SaveImageAndDestroy:  "SaveImageAndDestroy",  //保存镜像并销毁
	SaveImageAndShutdown: "SaveImageAndShutdown", //保存镜像并且关闭
	PushDocker:           "PushDocker",           //推送Docker到仓库
}

func NewTask(task string, state string) *TaskItem {
	// 创建 StartupInstTask 实例并设置默认值
	switch task {
	case TaskEnum.CommitDocker:
		tmp := TaskItem{
			State: state,
			Done:  CommitDockerTaskStateEnum.Done(state),
		}
		return &tmp
	case TaskEnum.SaveImageAndShutdown:
		tmp := TaskItem{
			State: state,
			Done:  SaveImageAndShutdownStateEnum.Done(state),
		}
		return &tmp
	}
	return nil
}

type commitDockerTaskStateEnum_ struct {
	Progress, Success, Fail string
}

func (obj commitDockerTaskStateEnum_) Done(v string) bool {
	if v == obj.Fail || v == obj.Success {
		return true
	}
	return false
}

var CommitDockerTaskStateEnum = commitDockerTaskStateEnum_{
	Progress: "progress", //进行中
	Success:  "success",  //成功
	Fail:     "fail",     //失败
}

type saveImageAndShutdownStateEnum_ struct {
	Progress, Success, Fail string
}

func (obj saveImageAndShutdownStateEnum_) Done(v string) bool {
	if v == obj.Fail || v == obj.Success {
		return true
	}
	return false
}

var SaveImageAndShutdownStateEnum = saveImageAndShutdownStateEnum_{
	Progress: "progress", //进行中
	Success:  "success",  //成功
	Fail:     "fail",     //失败
}

type normalTaskStateEnum_ struct {
	Progress, Success, Fail string
}

var NormalTaskStateEnum = normalTaskStateEnum_{
	Progress: "progress", //进行中
	Success:  "success",  //成功
	Fail:     "fail",     //失败
}

func StartupMarkContent(startupMark string) string {
	if logKey, err := GenLogKey(TaskEnum.StartupMark, startupMark); err != nil {
		logger.Error(err, startupMark)
		return err.Error()
	} else {
		if ary, err := List(logKey); err != nil {
			logger.Error(err)
			return err.Error()
		} else {
			return strings.Join(ary, "\n")
		}
	}
}

func StartupMarkContentFormatted(startupMark string) string {
	type LogFormat struct {
		Msg   string
		State string
		Log   string
		Time  string
	}
	if logKey, err := GenLogKey(TaskEnum.StartupMark, startupMark); err != nil {
		logger.Error(err, startupMark)
		return err.Error()
	} else {
		if ary, err := List(logKey); err != nil {
			logger.Error(err)
			return err.Error()
		} else {
			var logs []string
			for _, item := range ary {
				var log LogFormat
				err = json.Unmarshal([]byte(item), &log)
				if err != nil {
					logger.Error("格式化日志失败，使用原格式, err: ", err)
					logs = append(logs, item)
					continue
				}
				logFormatted, _ := json.MarshalIndent(log, "", " ")
				logs = append(logs, string(logFormatted))
			}
			return strings.Join(logs, "\n")
		}
	}
}
