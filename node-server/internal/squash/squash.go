package squash

import (
	"archive/tar"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"os"
	"path"
	"path/filepath"
	"slices"
	"strings"
	"time"

	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/client"
	dockerspec "github.com/docker/docker/image"
	"github.com/docker/docker/layer"
	"github.com/docker/docker/pkg/archive"
	"github.com/opencontainers/go-digest"
	ocispec "github.com/opencontainers/image-spec/specs-go/v1"
)

func PathExists(name string) bool {
	_, err := os.Stat(name)
	if os.IsNotExist(err) {
		return false
	}
	MustNoError(err)
	return true
}

func DecodeJSON(name string, v any) error {
	f, err := os.Open(name)
	if err != nil {
		return err
	}
	defer f.Close()
	return json.NewDecoder(f).Decode(v)
}

func SaveFile(name string, r io.Reader) error {
	f, err := os.OpenFile(name, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0644)
	if err != nil {
		return err
	}
	_, err = io.Copy(f, r)
	if e := f.Close(); err == nil {
		return e
	}
	return err
}

func SplitImage(image string) (string, string) {
	i := strings.LastIndexByte(image, ':')
	if i != -1 && strings.IndexByte(image[i+1:], '/') == -1 {
		return image[:i], image[i+1:]
	}
	return image, "latest"
}

func recover2error(err error) error {
	if v := recover(); v != nil {
		if e, ok := v.(error); ok && e != nil {
			err = e
		} else {
			err = fmt.Errorf("%v", v)
		}
	}
	return err
}

type UploadResult struct {
	Error       string `json:"error"`
	ErrorDetail struct {
		Message string `json:"message"`
	} `json:"errorDetail"`
	Stream string `json:"stream"`
}

func (squash Squash) UploadImage(parent context.Context, squashDir string) (err error) {
	for i := 1; i <= 3; i++ {
		var success bool
		success, err = squash.uploadImage(parent, squashDir)
		if success || err == nil {
			return
		}
		log.Printf("upload image failed, retry %d: %s\n", i, err)
		time.Sleep(10 * time.Second)
	}
	return
}

func (squash Squash) uploadImage(parent context.Context, squashDir string) (success bool, err error) {
	rc, err := archive.Tar(squashDir, archive.Uncompressed)
	if err != nil {
		return
	}
	defer rc.Close()

	ctx, cancel := context.WithTimeout(parent, 8*time.Hour)
	defer cancel()

	res, err := squash.ImageLoad(ctx, rc, client.ImageLoadWithQuiet(true))
	if err != nil {
		return
	}
	defer res.Body.Close()

	for i := 0; ; i++ {
		var result UploadResult
		err = json.NewDecoder(res.Body).Decode(&result)
		if err == io.EOF && i > 0 {
			return true, nil
		} else if err != nil {
			return
		}
		if result.Error != "" {
			return true, errors.New(result.Error)
		}
		log.Println("upload image stream:", result.Stream)
	}
}

func (squash Squash) DownloadImage(parent context.Context, imageID, imageDir string) (err error) {
	for i := 1; i <= 3; i++ {
		err = squash.downloadImage(parent, imageID, imageDir)
		if err == nil {
			break
		}
		log.Printf("download image failed, retry %d: %s\n", i, err)
		time.Sleep(10 * time.Second)
	}
	return
}

func (squash Squash) downloadImage(parent context.Context, imageID, imageDir string) error {
	ctx, cancel := context.WithTimeout(parent, 8*time.Hour)
	defer cancel()

	rc, err := squash.ImageSave(ctx, []string{imageID})
	if err != nil {
		return err
	}
	defer rc.Close()

	return archive.Untar(rc, imageDir, nil)
}

type Squash struct {
	*client.Client
	Tag       string
	FromImage string
	SavePath  string
	ToHistory string
	ToLayer   int
	To1G      bool //TODO
}

func (squash Squash) save(a []TarFile, squashDir string) digest.Digest {
	f, err := os.OpenFile(filepath.Join(squashDir, legacyLayerFileName), os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0644)
	MustNoError(err)
	defer f.Close()

	d := digest.Canonical.Digester()
	mw := io.MultiWriter(f, d.Hash())
	w := tar.NewWriter(mw)

	m := make(map[Tar][]string)
	for _, f := range a {
		m[f.Tar] = append(m[f.Tar], f.Name)
	}
	for t, a := range m {
		slices.Sort(a)
		for h, r := range t.Files() {
			if _, ok := slices.BinarySearch(a, h.Name); ok {
				MustNoError(w.WriteHeader(h))
				_, err := io.Copy(w, r)
				MustNoError(err)
			}
		}
		MustNoError(w.Flush())
	}
	MustNoError(f.Close())

	return d.Digest()
}

func (squash Squash) Do(parent context.Context) (err error) {
	defer func() {
		err = recover2error(err)
	}()

	ctx, cancel := context.WithTimeout(parent, time.Minute)
	defer cancel()

	squash.NegotiateAPIVersion(ctx)

	image, _, err := squash.ImageInspectWithRaw(ctx, squash.FromImage)
	MustNoError(err)

	history, err := squash.ImageHistory(ctx, image.ID)
	MustNoError(err)

	var toLayer int
	if squash.To1G {
		for i, h := range history {
			if h.Size >= 1024*1024*1024 {
				toLayer = len(history) - i
				break
			}
		}
	} else {
		toLayer = squash.ToLayer
		if squash.ToHistory != "" && toLayer != 0 {
			return fmt.Errorf("either squash to image or squash to layer")
		} else if squash.ToHistory != "" {
			toImage, _, err := squash.ImageInspectWithRaw(ctx, squash.ToHistory)
			MustNoError(err)
			toLayer = -1
			for i, h := range history {
				if h.ID != "<missing>" && h.ID == toImage.ID {
					toLayer = i
					break
				}
			}
			if toLayer == -1 {
				return fmt.Errorf("squash to image %s not found", squash.ToHistory)
			}
			toLayer++
		} else if toLayer != 0 {
			if toLayer < 0 {
				toLayer += len(history)
			}
			toLayer--
		}
	}
	if toLayer < 0 || toLayer >= len(history)-1 {
		return fmt.Errorf("squash %s %d layers to %d meaningless", squash.FromImage, len(history), squash.ToLayer)
	} else {
		log.Printf("squash %s %d layers to %d\n", squash.FromImage, len(history), toLayer+1)
	}

	imageDir, err := os.MkdirTemp("", "docker-image-")
	MustNoError(err)
	defer os.RemoveAll(imageDir)
	log.Println(imageDir, image.ID)

	MustNoError(squash.DownloadImage(parent, image.ID, imageDir))

	var manifest []manifestItem
	MustNoError(DecodeJSON(filepath.Join(imageDir, manifestFileName), &manifest))

	var config dockerspec.Image
	MustNoError(DecodeJSON(filepath.Join(imageDir, manifest[0].Config), &config))
	Must(len(config.History) == len(history))

	toNonEmptyLayer := 0
	for i := 0; i < toLayer; i++ {
		if !config.History[i].EmptyLayer {
			toNonEmptyLayer++
		}
	}

	var index ocispec.Index
	if PathExists(filepath.Join(imageDir, ocispec.ImageIndexFile)) {
		MustNoError(DecodeJSON(filepath.Join(imageDir, ocispec.ImageIndexFile), &index))
		Must(index.SchemaVersion != 0)

		var layout any
		MustNoError(DecodeJSON(filepath.Join(imageDir, ocispec.ImageLayoutFile), &layout))

		var m ocispec.Manifest
		MustNoError(DecodeJSON(filepath.Join(imageDir, ocispec.ImageBlobsDir,
			index.Manifests[0].Digest.Algorithm().String(), index.Manifests[0].Digest.Encoded()), &m))

		var c ocispec.Image
		MustNoError(DecodeJSON(filepath.Join(imageDir, ocispec.ImageBlobsDir,
			m.Config.Digest.Algorithm().String(), m.Config.Digest.Encoded()), &c))
	}

	getLayerId := func(s string) string {
		if index.SchemaVersion == 0 {
			Must(strings.HasSuffix(s, "/"+legacyLayerFileName))
			i := strings.IndexByte(s, '/')
			return s[:i]
		} else {
			Must(strings.HasPrefix(s, ocispec.ImageBlobsDir+"/"))
			i := strings.LastIndexByte(s, '/')
			return s[i+1:]
		}
	}

	var squashImage, imageName, imageTag string
	if squash.Tag != "" {
		imageName, imageTag = SplitImage(squash.Tag)
		squashImage = imageName + ":" + imageTag
	}

	squashDir, err := os.MkdirTemp("", "squash-image-")
	MustNoError(err)
	defer os.RemoveAll(squashDir)

	if squashImage != "" {
		log.Println(squashDir, squashImage)
	} else {
		log.Println(squashDir, strings.Join(manifest[0].RepoTags, " "))
	}

	squashFiles := SquashLayer(manifest[0].Layers, toNonEmptyLayer, imageDir)
	squashDigest := squash.save(squashFiles, squashDir)

	now := time.Now()

	config.Container = ""
	config.ContainerConfig = container.Config{}
	if config.Config != nil {
		config.Config.Image = ""
		config.Config.Hostname = ""
	}

	config.Created = &now
	config.History = config.History[:toLayer+1]
	config.History[toLayer] = ocispec.History{
		Comment: "squashed",
		Author:  "CHEN Xianren",
		Created: &now,
	}

	Must(len(config.RootFS.DiffIDs) == len(manifest[0].Layers))
	config.RootFS.DiffIDs = config.RootFS.DiffIDs[:toNonEmptyLayer+1]
	config.RootFS.DiffIDs[toNonEmptyLayer] = layer.DiffID(squashDigest)

	chainID := digest.Digest(layer.CreateChainID(config.RootFS.DiffIDs))
	layerImage := dockerspec.V1Image{
		ID:           chainID.Encoded(),
		Config:       config.Config,
		OS:           config.OS,
		Architecture: config.Architecture,
		Comment:      "squashed",
		Author:       "CHEN Xianren",
		Created:      &now,
	}
	if toNonEmptyLayer > 0 {
		layerImage.Parent = getLayerId(manifest[0].Layers[toNonEmptyLayer-1])
	}

	layerImageData, err := json.Marshal(layerImage)
	MustNoError(err)
	layerImageDigest := digest.FromBytes(layerImageData)
	layerImage.ID = layerImageDigest.Encoded()
	layerImageData, err = json.Marshal(layerImage)
	MustNoError(err)

	MustNoError(os.MkdirAll(filepath.Join(squashDir, layerImage.ID), 0755))
	MustNoError(os.Rename(filepath.Join(squashDir, legacyLayerFileName), filepath.Join(squashDir, layerImage.ID, legacyLayerFileName)))
	MustNoError(os.WriteFile(filepath.Join(squashDir, layerImage.ID, legacyConfigFileName), layerImageData, 0644))
	MustNoError(os.WriteFile(filepath.Join(squashDir, layerImage.ID, "VERSION"), []byte("1.0"), 0644))

	configData, err := json.Marshal(config)
	MustNoError(err)

	squashManifest := manifestItem{
		Config: digest.FromBytes(configData).Encoded() + ".json",
		Layers: make([]string, toNonEmptyLayer+1),
	}

	if squashImage != "" {
		squashManifest.RepoTags = []string{squashImage}
	} else {
		squashManifest.RepoTags = manifest[0].RepoTags
	}

	MustNoError(os.WriteFile(filepath.Join(squashDir, squashManifest.Config), configData, 0644))

	copy(squashManifest.Layers, manifest[0].Layers[:toNonEmptyLayer])
	squashManifest.Layers[toNonEmptyLayer] = path.Join(layerImage.ID, legacyLayerFileName)

	squashManifestData, err := json.Marshal([]manifestItem{squashManifest})
	MustNoError(err)
	MustNoError(os.WriteFile(filepath.Join(squashDir, manifestFileName), squashManifestData, 0644))

	if squashImage != "" {
		repositoriesData, err := json.Marshal(map[string]map[string]string{imageName: {imageTag: layerImage.ID}})
		MustNoError(err)
		MustNoError(os.WriteFile(filepath.Join(squashDir, legacyRepositoriesFileName), repositoriesData, 0644))
	} else if PathExists(filepath.Join(imageDir, legacyRepositoriesFileName)) {
		MustNoError(os.Rename(filepath.Join(imageDir, legacyRepositoriesFileName), filepath.Join(squashDir, legacyRepositoriesFileName)))
	}

	for _, layer := range manifest[0].Layers[:toNonEmptyLayer] {
		if index.SchemaVersion == 0 {
			id := getLayerId(layer)
			MustNoError(os.Rename(filepath.Join(imageDir, id), filepath.Join(squashDir, id)))
		} else {
			MustNoError(os.MkdirAll(filepath.Join(squashDir, path.Dir(layer)), 0755))
			MustNoError(os.Rename(filepath.Join(imageDir, layer), filepath.Join(squashDir, layer)))
		}
	}

	if squash.SavePath == "" {
		MustNoError(squash.UploadImage(parent, squashDir))
	} else {
		rc, err := archive.Tar(squashDir, archive.Uncompressed)
		MustNoError(err)
		defer rc.Close()
		MustNoError(SaveFile(squash.SavePath, rc))
	}

	return nil
}
