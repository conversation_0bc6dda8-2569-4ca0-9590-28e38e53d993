package squash

import (
	"archive/tar"
	"cmp"
	"io"
	"iter"
	"log"
	"maps"
	"os"
	"path"
	"path/filepath"
	"slices"
	"strings"

	"github.com/docker/distribution"
	"github.com/docker/docker/image"
	"github.com/docker/docker/layer"
	"github.com/docker/docker/pkg/archive"
)

func Must(ok bool) {
	if !ok {
		panic(ok)
	}
}

func MustNoError(err error) {
	if err != nil {
		panic(err)
	}
}

type TarFile struct {
	Tar
	*tar.Header
	Level    uint32
	Whiteout uint32 //1: prefix, 2: dir
}

type Tar struct {
	*os.File
}

func MustOpenTar(name string) Tar {
	f, err := os.Open(name)
	MustNoError(err)
	return Tar{f}
}

func (t Tar) SeekStart() {
	ret, err := t.Seek(0, io.SeekStart)
	Must(err == nil && ret == 0)
}

func (t Tar) Files() iter.Seq2[*tar.Header, *tar.Reader] {
	return func(f func(*tar.Header, *tar.Reader) bool) {
		t.SeekStart()
		r := tar.NewReader(t)
		for {
			h, err := r.Next()
			if err == io.EOF {
				break
			}
			MustNoError(err)
			if !f(h, r) {
				return
			}
		}
	}
}

func CleanName(s string) string {
	return filepath.ToSlash(filepath.Clean(s))
}

func (t Tar) whiteouts(level int) (outs, files map[string]TarFile) {
	for h := range t.Files() {
		name := CleanName(h.Name)
		name = strings.TrimPrefix(name, "/")

		if name == "" || name == "." || name == ".." || strings.HasPrefix(name, "../") {
			log.Println("SHOULD NOT ignore name: ", h.Name)
			continue
		}

		baseName := path.Base(name)
		tarFile := TarFile{Tar: t, Header: h, Level: uint32(level)}
		if baseName == archive.WhiteoutOpaqueDir {
			tarFile.Whiteout = 2
			name = path.Dir(name)
		} else if strings.HasPrefix(baseName, archive.WhiteoutPrefix) {
			tarFile.Whiteout = 1
			name = path.Join(path.Dir(name), strings.TrimPrefix(baseName, archive.WhiteoutPrefix))
		}
		if tarFile.Whiteout == 0 {
			if files == nil {
				files = make(map[string]TarFile)
			}
			files[name] = tarFile
		} else {
			if outs == nil {
				outs = make(map[string]TarFile)
			}
			outs[name] = tarFile //
		}
	}
	return
}

func mergeFiles(a, b map[string]TarFile) map[string]TarFile {
	if a == nil {
		return b
	} else {
		for k, v := range b {
			a[k] = v
		}
		return a
	}
}

func SquashLayer(layers []string, i int, imageDir string) []TarFile {
	var outs, files map[string]TarFile
	for j := i; j < len(layers); j++ {
		om, fm := MustOpenTar(filepath.Join(imageDir, layers[j])).whiteouts(j)
		outs = mergeFiles(outs, om)
		files = mergeFiles(files, fm)
	}

	if files == nil {
		files = outs
	} else {
		for k, v := range outs {
			var y int

			for s, f := range files {
				dir := f.Typeflag == tar.TypeDir

				if s == k {
					if v.Level < f.Level && !(v.Whiteout == 2 && dir) {
						y++
					} else if v.Level > f.Level {
						delete(files, s)
					}
				} else if strings.HasPrefix(s, k+"/") {
					if v.Level > f.Level {
						delete(files, s)
					}
				} else if strings.HasPrefix(k, s+"/") {
					if v.Level < f.Level && !dir {
						y++
					}
				}
			}

			if y == 0 && i > 0 {
				//TODO: real needs
				files[k] = v
			}
		}
	}

	for _, name := range slices.Sorted(maps.Keys(files)) {
		if f, ok := files[name]; ok && f.Typeflag != tar.TypeDir {
			for k, v := range files {
				if v.Level < f.Level && strings.HasPrefix(k, name+"/") {
					delete(files, k)
				}
			}
		}
	}

	return slices.SortedFunc(maps.Values(files), func(a, b TarFile) int {
		return cmp.Compare(a.Name, b.Name)
	})
}

const (
	manifestFileName           = "manifest.json"
	legacyLayerFileName        = "layer.tar"
	legacyConfigFileName       = "json"
	legacyRepositoriesFileName = "repositories"
)

type manifestItem struct {
	Config       string
	RepoTags     []string
	Layers       []string
	Parent       image.ID                                 `json:",omitempty"`
	LayerSources map[layer.DiffID]distribution.Descriptor `json:",omitempty"`
}
