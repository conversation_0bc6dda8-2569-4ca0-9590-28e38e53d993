package structs

import "time"

type DockerDetailResp struct {
	ID           string                 `json:"id"`
	InstanceUuid string                 `json:"instance_uuid"`
	StartupMark  string                 `json:"startup_mark"`
	Name         string                 `json:"-"`
	PodId        uint                   `json:"pod_id"`
	PodCategory  int                    `json:"pod_category"`
	PodName      string                 `json:"pod_name"`
	VirtualId    uint                   `json:"virtual_id"`
	VirtualHost  string                 `json:"virtual_host"` //在哪台虚拟机上
	VirtualPort  int                    `json:"virtual_port"`
	SshPort      int                    `json:"ssh_port"` //ssh操作docker的宿主机端口
	Gpus         []int                  `json:"gpus"`     //该docker启用的Gpu序列
	MapPref      string                 `json:"map_pref"`
	MapPorts     []string               `json:"map_ports"`
	CreatedAt    time.Time              `json:"created_at"`
	WebUrl       string                 `json:"web_url"`
	ApiBase      string                 `json:"api_base"`
	Info         map[string]interface{} `json:"-"`
	State        string                 `json:"state"`
}
