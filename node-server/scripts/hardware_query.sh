#!/bin/bash
# 检查并安装必要的工具
check_and_install() {
    local command_name=$1
    local package_name=${2:-$1}  # 如果未提供包名，使用命令名
    
    if ! command -v $command_name &> /dev/null; then
        echo "命令 $command_name 未找到，正在尝试安装..."
        
        # 检测包管理器
        if command -v apt-get &> /dev/null; then
            sudo apt-get update -qq && sudo apt-get install -y $package_name
        elif command -v yum &> /dev/null; then
            sudo yum install -y $package_name
        elif command -v dnf &> /dev/null; then
            sudo dnf install -y $package_name
        elif command -v zypper &> /dev/null; then
            sudo zypper install -y $package_name
        elif command -v pacman &> /dev/null; then
            sudo pacman -S --noconfirm $package_name
        else
            echo "无法安装 $package_name：未找到支持的包管理器"
            return 1
        fi
        
        # 验证安装
        if ! command -v $command_name &> /dev/null; then
            echo "安装 $package_name 失败"
            return 1
        else
            echo "$package_name 安装成功"
        fi
    fi
    return 0
}

# 检查并安装必要的工具
check_and_install jq
check_and_install lscpu "util-linux"
check_and_install lsblk "util-linux"
check_and_install lspci "pciutils"
check_and_install dmidecode
check_and_install ethtool
check_and_install hdparm
# 创建输出文件
output_file="/root/hardware-info.json"

# 初始化JSON结构
echo '{
  "hostname": "'$(hostname)'",
  "server": {
    "model": "",
    "manufacturer": ""
  },
  "cpu": {
    "vendor": "",
    "model": "",
    "physical_count": 0,
    "cores_per_cpu": 0,
    "total_cores": 0,
    "total_threads": 0
  },
  "memory": {
    "total_size": "",
    "installed_count": 0,
    "modules": []
  },
  "gpus": [],
  "disks": [],
  "network": {
    "physical_count": 0,
    "interfaces": []
  }
}' > $output_file

# 获取服务器型号/制造商
if [ -f /sys/class/dmi/id/product_name ]; then
    model=$(cat /sys/class/dmi/id/product_name)
    manufacturer=$(cat /sys/class/dmi/id/sys_vendor 2>/dev/null || echo "Unknown")
else
    model=$(dmidecode -t system 2>/dev/null | grep "Product Name" | cut -d: -f2 | xargs || echo "Unknown")
    manufacturer=$(dmidecode -t system 2>/dev/null | grep "Manufacturer" | cut -d: -f2 | xargs || echo "Unknown")
fi

# 更新服务器信息
tmp_file=$(mktemp)
jq --arg model "$model" --arg manufacturer "$manufacturer" \
   '.server.model = $model | .server.manufacturer = $manufacturer' $output_file > $tmp_file
mv $tmp_file $output_file

# 获取CPU信息
cpu_vendor=$(lscpu | grep "Vendor ID" | cut -d: -f2 | xargs)
cpu_model=$(lscpu | grep "Model name" | cut -d: -f2 | xargs)
cpu_sockets=$(lscpu | grep "Socket(s)" | grep -o "[0-9]*")
cpu_cores=$(lscpu | grep "Core(s) per socket" | grep -o "[0-9]*")
total_cores_no_ht=$((cpu_sockets * cpu_cores))
total_threads=$(lscpu | grep "CPU(s):" | head -1 | grep -o "[0-9]*")

# 更新CPU信息
tmp_file=$(mktemp)
jq --arg vendor "$cpu_vendor" --arg model "$cpu_model" \
   --argjson sockets "$cpu_sockets" --argjson cores "$cpu_cores" \
   --argjson total_cores "$total_cores_no_ht" --argjson total_threads "$total_threads" \
   '.cpu.vendor = $vendor | .cpu.model = $model | .cpu.physical_count = $sockets | .cpu.cores_per_cpu = $cores | .cpu.total_cores = $total_cores | .cpu.total_threads = $total_threads' $output_file > $tmp_file
mv $tmp_file $output_file

# 获取内存信息
total_memory=$(free -h | grep "Mem:" | awk '{print $2}')

# 更新内存总大小
tmp_file=$(mktemp)
jq --arg total "$total_memory" '.memory.total_size = $total' $output_file > $tmp_file
mv $tmp_file $output_file

# 获取内存模块详细信息
if command -v dmidecode &> /dev/null; then
    # 获取内存模块数量
    installed_modules=$(sudo dmidecode -t memory | grep -A5 "Memory Device" | grep "Size:" | grep -v "No Module Installed" | wc -l)

    # 更新内存模块数量
    tmp_file=$(mktemp)
    jq --argjson count "$installed_modules" '.memory.installed_count = $count' $output_file > $tmp_file
    mv $tmp_file $output_file

    # 获取每个内存模块的详细信息
    mem_blocks=$(sudo dmidecode -t memory | grep -n "Memory Device" | cut -d: -f1)
    prev_line=0
    module_index=0

    for line in $mem_blocks; do
        # 获取内存模块段
        if [ $prev_line -ne 0 ]; then
            module_info=$(sudo dmidecode -t memory | sed -n "${prev_line},${line}p")
        else
            next_line=$(echo "$mem_blocks" | sed -n '2p')
            module_info=$(sudo dmidecode -t memory | sed -n "${line},${next_line}p")
        fi

        # 检查模块是否安装
        if echo "$module_info" | grep -q "Size: No Module Installed"; then
            prev_line=$line
            continue
        fi

        # 提取模块信息
        size=$(echo "$module_info" | grep "Size:" | cut -d: -f2 | xargs || echo "Unknown")
        type=$(echo "$module_info" | grep "Type:" | cut -d: -f2 | xargs || echo "Unknown")
        speed=$(echo "$module_info" | grep "Speed:" | cut -d: -f2 | xargs || echo "Unknown")
        manufacturer=$(echo "$module_info" | grep "Manufacturer:" | cut -d: -f2 | xargs || echo "Unknown")
        part_number=$(echo "$module_info" | grep "Part Number:" | cut -d: -f2 | xargs || echo "Unknown")

        # 跳过未安装的模块
        if [ "$size" = "No Module Installed" ] || [ "$size" = "Unknown" ]; then
            prev_line=$line
            continue
        fi

        # 添加模块信息到JSON
        tmp_file=$(mktemp)
        jq --arg size "$size" --arg type "$type" --arg speed "$speed" \
           --arg manufacturer "$manufacturer" --arg part_number "$part_number" \
           '.memory.modules += [{"size": $size, "type": $type, "speed": $speed, "manufacturer": $manufacturer, "part_number": $part_number}]' $output_file > $tmp_file
        mv $tmp_file $output_file

        module_index=$((module_index + 1))
        prev_line=$line
    done
else
    echo "dmidecode not available, cannot get detailed memory information"
fi

# 获取GPU信息
nvidia_gpus=$(nvidia-smi --query-gpu=name,memory.total,uuid --format=csv,noheader 2>/dev/null)
if [ ! -z "$nvidia_gpus" ]; then
    while IFS=',' read -r name memory uuid; do
        # 添加GPU信息到JSON
        tmp_file=$(mktemp)
        jq --arg name "$(echo $name | xargs)" --arg memory "$(echo $memory | xargs)" \
           --arg uuid "$(echo $uuid | xargs)" \
           '.gpus += [{"model": $name, "memory": $memory, "uuid": $uuid}]' $output_file > $tmp_file
        mv $tmp_file $output_file
    done <<< "$nvidia_gpus"
else
    # 替代方法获取GPU信息
    gpu_info=$(lspci | grep -i "vga\|3d\|display")
    while read -r line; do
        # 提取GPU型号
        model=$(echo "$line" | cut -d: -f3 | xargs)
        # 添加GPU信息到JSON
        tmp_file=$(mktemp)
        jq --arg model "$model" '.gpus += [{"model": $model, "memory": "Unknown", "serial": "Unknown", "uuid": "Unknown"}]' $output_file > $tmp_file
        mv $tmp_file $output_file
    done <<< "$gpu_info"
fi

# 获取磁盘信息
disk_info=$(lsblk -d -o name,model,size,serial,type,rota | grep -v "^loop" | grep -v "^sr" | sed '1d')
while read -r line; do
    if [ -z "$line" ]; then
        continue
    fi

    # 提取磁盘信息 - 使用更复杂的提取方式处理包含空格的字段
    name=$(echo "$line" | awk '{print $1}')

    # 处理有空格的model字段
    # 通过剔除已知固定位置的其他字段来提取model
    size_pattern="[0-9]+(\.[0-9]+)[KMGTPEi]*"
    # 找到size字段的位置
    size_pos=$(echo "$line" | grep -bo -E "$size_pattern" | head -1 | cut -d: -f1)
    # 提取model (从第一个字段后到size字段前)
    if [ ! -z "$size_pos" ]; then
        # 字段开始位置 (name字段后的第一个空格后)
        start_pos=$(echo "$line" | grep -bo -E "^$name[[:space:]]+" | head -1 | awk -F: '{print $1+length("'"$name"'")+1}')
        # 提取model
        model=$(echo "$line" | cut -c $start_pos-$((size_pos-1)) | xargs)
    else
        # 如果找不到size位置，使用简单方法
        model=$(echo "$line" | awk '{print $2}')
    fi

    # 提取其他字段
    size=$(echo "$line" | grep -o -E "$size_pattern")

    # 获取serial字段 (通常是size后面的字段)
    serial_pos=$(($(echo "$line" | grep -bo -E "$size_pattern" | head -1 | cut -d: -f1)+${#size}+1))
    remaining=$(echo "$line" | cut -c $serial_pos-)
    serial=$(echo "$remaining" | awk '{print $1}')

    # 获取type和rota字段
    type=$(echo "$remaining" | awk '{print $2}')
    rota=$(echo "$remaining" | awk '{print $3}')

    # 确定磁盘类型 (SSD vs HDD)
    if [ "$rota" = "0" ]; then
        disk_type="SSD"
    elif [ "$rota" = "1" ]; then
        disk_type="HDD"
    else
        disk_type="Unknown"
    fi

    # 我们已经从lsblk获取了serial，这里无需再次获取
    # 但如果serial为空，我们可以尝试获取
    if [ -z "$serial" ] || [ "$serial" = "Unknown" ]; then
        if [[ $name == nvme* ]]; then
            # 对于NVMe设备
            serial=$(nvme id-ctrl /dev/$name 2>/dev/null | grep "sn" | awk '{print $3}' || echo "Unknown")
        else
            # 对于SATA/SAS设备
            serial=$(sudo hdparm -I /dev/$name 2>/dev/null | grep "Serial Number" | awk '{print $3}' || echo "Unknown")
        fi
    fi

    # 添加磁盘信息到JSON
    tmp_file=$(mktemp)
    jq --arg name "$name" --arg model "$model" --arg size "$size" \
       --arg type "$disk_type" --arg serial "$serial" \
       '.disks += [{"name": $name, "model": $model, "size": $size, "type": $type, "serial": $serial}]' $output_file > $tmp_file
    mv $tmp_file $output_file
done <<< "$disk_info"

# 获取网卡信息
# 网卡型号
nic_info=$(lspci | grep -i ethernet)
nic_count=$(echo "$nic_info" | wc -l)

# 更新网卡数量
tmp_file=$(mktemp)
jq --argjson count "$nic_count" '.network.physical_count = $count' $output_file > $tmp_file
mv $tmp_file $output_file

# 获取网卡接口信息
interfaces=$(ip -o link show | awk -F': ' '{print $2}' | grep -v "lo\|docker\|veth")
for nic in $interfaces; do
    # 获取MAC地址
    mac=$(cat /sys/class/net/$nic/address 2>/dev/null || echo "Unknown")

    # 获取速率
    if [ -e /sys/class/net/$nic/speed ]; then
        speed=$(cat /sys/class/net/$nic/speed 2>/dev/null || echo "Unknown")
        if [ "$speed" != "Unknown" ]; then
            if [ $speed -ge 1000 ]; then
                speed_text="$((speed / 1000)) Gbps"
            else
                speed_text="$speed Mbps"
            fi
        else
            speed_text="Unknown"
        fi
    else
        speed_text=$(ethtool $nic 2>/dev/null | grep "Speed" | awk '{print $2}' || echo "Unknown")
    fi

    # 获取IP地址
    ip_addr=$(ip addr show $nic 2>/dev/null | grep 'inet ' | awk '{print $2}' | cut -d/ -f1 || echo "Unknown")

    # 添加网卡信息到JSON
    tmp_file=$(mktemp)
    jq --arg name "$nic" --arg mac "$mac" --arg speed "$speed_text" --arg ip "$ip_addr" \
       '.network.interfaces += [{"name": $name, "mac": $mac, "speed": $speed, "ip": $ip}]' $output_file > $tmp_file
    mv $tmp_file $output_file
done

echo "Hardware information has been saved to $output_file in JSON format."