server {
    listen      443 ssl;
    http2 on;
    server_name *.gz13.chenyu.cn;

    # 指定证书和私钥
    ssl_certificate     /etc/dynproxy/certs/gz13.chenyu.cn.pem;
    ssl_certificate_key /etc/dynproxy/certs/gz13.chenyu.cn.key;

    # 推荐的SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # SSL Session配置
    ssl_session_timeout 1d;
    ssl_session_tickets off;

    proxy_buffering off;
    proxy_request_buffering off;
    client_header_buffer_size 32k;
    large_client_header_buffers 8 32k;
    client_max_body_size 800m;

    # 定义自定义错误页面
    error_page 502 /node_suanyun502.html;

    # 配置 502 错误页面的路径, 注意这个路径需要和openresty映射的目录协同
    location = /node_suanyun502.html {
        root /usr/local/openresty/lualib/cy;
        internal;
    }

    location / {
        # 启用支持websocket连接的配置
        proxy_http_version  1.1;  #1.1版本才支持wss
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_connect_timeout 60; #配置等待服务器响应时间
        proxy_read_timeout 600;
        proxy_send_timeout 600;
        proxy_redirect off;

        add_header Cache-Control no-cache;
        set $redirected_url "";
        proxy_set_header Host            $host;
        proxy_set_header X-Real-IP       $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Origin "$scheme://$host";

        set_by_lua_block $redirected_url {
            local a = 'http://***************:12088'
            return a
        }

        access_by_lua_block{
            local resolver = require "entity_url_resolver"
            local host = ngx.var.host

            local url, err = resolver.resolve_url(host)
            if url then
                ngx.var.redirected_url = url
            else
                ngx.var.redirected_url = ""
                ngx.header.content_type = "text/plain; charset=utf-8"
                ngx.status = 404
                ngx.say("未获取到Pod地址,请尝试重启Pod.")
                ngx.flush(true)
                ngx.exit(ngx.HTTP_OK)
            end
        }

        if ($redirected_url = "") {
            return 404 "No URL provided";
        }

        if ($redirected_url ~ "^http") {
            proxy_pass $redirected_url;
        }

        if ($redirected_url !~ "^http") {
            return 404 "Invalid URL format";
        }

        error_log  /dev/stdout;
    }
}
