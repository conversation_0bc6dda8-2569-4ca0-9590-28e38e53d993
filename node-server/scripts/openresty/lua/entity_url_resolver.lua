-- entity_url_resolver.lua
-- 处理实体UUID到URL的解析和缓存

local _M = {}

-- 配置参数
_M.config = {
    memory_cache_expire = 30,     -- 内存缓存过期时间（秒）
    redis_host = "*************",
    redis_port = 6379,
    redis_password = "5ab46e545ac38e",    -- Redis密码
    redis_key_prefix = "relay:mapping:",
    host_pattern = "(.+).gz13.chenyu.cn",
}

-- 从域名中提取实体UUID
function _M.extract_entity_uuid(host)
    local result = string.match(host, _M.config.host_pattern)

    -- 处理特殊域名
    if result == "admin" then
        return nil, "http://127.0.0.1:6002"
    end

    -- 验证UUID有效性
    if result and string.len(result) >= 16 then
        return result
    end

    return nil
end

-- 从内存缓存中获取URL
function _M.get_from_memory(entity_uuid)
    local cache = ngx.shared.entity_url_cache
    local url = cache:get(entity_uuid)

    if url then
        local lastChar = string.sub(url, -1)
        if lastChar == '/' then
            url = string.sub(url, 1, -2)
        end

        return url
    end

    return nil
end

-- 从Redis中获取URL
function _M.get_from_redis(entity_uuid)
    local redis = require "resty.redis"
    local red = redis:new()
    red:set_timeout(1000) -- 1秒超时

    -- 连接到Redis
    local ok, err = red:connect(_M.config.redis_host, _M.config.redis_port)
    if not ok then
        ngx.log(ngx.ERR, "Failed to connect to Redis: ", err)
        return nil, "无法连接到Redis"
    end

    -- Redis认证
    if _M.config.redis_password and _M.config.redis_password ~= "" then
        local res, err = red:auth(_M.config.redis_password)
        if not res then
            ngx.log(ngx.ERR, "Failed to authenticate with Redis: ", err)
            return nil, "Redis认证失败"
        end
    end

    local redis_key = _M.config.redis_key_prefix .. entity_uuid
    ngx.log(ngx.DEBUG, "get host from redis by ", redis_key)
    local url, err = red:get(redis_key)
    ngx.log(ngx.DEBUG, "get value from redis by ", redis_key, " : ", url)
    -- 将连接放回连接池
    red:set_keepalive(10000, 100)

    if url and url ~= ngx.null then
        -- 将URL存储到内存缓存中
        local cache = ngx.shared.entity_url_cache
        cache:set(entity_uuid, url, _M.config.memory_cache_expire)

        -- 规范化URL（去掉末尾的斜杠）
        local lastChar = string.sub(url, -1)
        if lastChar == '/' then
            url = string.sub(url, 1, -2)
        end

        return url
    end

    return nil
end


-- 主函数：解析实体UUID并获取对应的URL
function _M.resolve_url(host)
    local entity_uuid, special_url = _M.extract_entity_uuid(host)
    ngx.log(ngx.DEBUG, "parse url result: ", entity_uuid, special_url)
    if special_url then
        return special_url
    end

    if not entity_uuid then
        return nil, "无效的实体UUID"
    end

    local url = _M.get_from_memory(entity_uuid)
    if url then
        return url
    end
    ngx.log(ngx.DEBUG, "uuid mapping host from mem: ", url)
    -- 2. 从Redis中查找
    local url, err = _M.get_from_redis(entity_uuid)
    ngx.log(ngx.DEBUG, "uuid mapping host from redis: ", url)
    if url then
        return url
    else
        return nil, err or "未获取到实体URL，请检查配置"
    end
end

return _M
