<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>启动中</title>
</head>
<style>
    * {
        margin: 0;
        padding: 0;
    }

    .main {
        width: 100%;
        height: 100vh;
        padding: 10px;
        box-sizing: border-box;

        display: flex;
        flex-wrap: wrap;
        align-items: center;
        align-content: center;
        justify-content: center;
    }

    /* 图片 */
    .svg {
        max-width: 100%;
    }

    .svg svg {
        width: 100%
    }

    /* 文字 */
    .content {
        margin: 30px 0;
        font-family: Source Han Sans SC, Source Han Sans SC;
        font-weight: 500;
        font-size: 30px;
        color: #000000;
        letter-spacing: 3px;
    }

    .content .button {
        margin-top: 30px;
        padding: 5px 30px;
        box-sizing: border-box;
        border-radius: 5px;
        border: 1px solid #000000;
        background: white;
        font-size: 20px;
        float: right;
        cursor: pointer;
    }

    .content .button:hover {
        background: #00000010;
    }
</style>

<body>
    <div class="main">
        <div class="svg">
            <svg width="462" height="272" viewBox="0 0 462 272" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="Group 12285">
                    <g id="Group">
                        <g id="Group_2">
                            <path id="Vector" fill-rule="evenodd" clip-rule="evenodd"
                                d="M461.999 207.589L461.897 209.645L461.537 211.752L460.92 213.808L460.098 215.915L458.967 218.022L457.579 220.181L455.78 222.494L453.622 224.858L451.052 227.222L448.071 229.689L444.627 232.156L440.362 234.88L435.479 237.656L429.928 240.431L423.658 243.258L417.49 245.725L410.706 248.192L403.305 250.608L395.184 253.023L378.738 257.238L360.389 261.041L341.475 264.227L321.123 266.951L307.4 268.391L293.163 269.624L278.515 270.601L263.867 271.269L248.962 271.68L233.8 271.834L218.639 271.68L203.734 271.269L189.086 270.601L174.438 269.624L160.201 268.391L146.478 266.951L126.126 264.227L107.212 261.041L88.8633 257.238L72.4166 253.023L64.296 250.608L56.8949 248.192L50.1106 245.725L43.9431 243.258L37.6728 240.431L32.122 237.656L27.2393 234.88L22.9735 232.156L19.5299 229.689L16.5489 227.222L13.9791 224.858L11.8205 222.494L10.0216 220.181L8.63394 218.022L7.50322 215.915L6.68088 213.808L6.06413 211.752L5.70435 209.645L5.60156 207.589L5.70435 205.533L6.06413 203.477L6.68088 201.37L7.50322 199.263L8.63394 197.156L10.0216 194.997L11.8205 192.684L13.9791 190.32L16.5489 187.956L19.5299 185.489L22.9735 183.022L27.2393 180.298L32.122 177.522L37.6728 174.747L43.9431 171.92L50.1106 169.453L56.8949 166.986L64.296 164.57L72.4166 162.155L88.8633 157.94L107.212 154.137L126.126 150.95L146.478 148.226L160.201 146.787L174.438 145.554L189.086 144.577L203.734 143.909L218.639 143.498L233.8 143.344L248.962 143.498L263.867 143.909L278.515 144.577L293.163 145.554L307.4 146.787L321.123 148.226L341.475 150.95L360.389 154.137L378.738 157.94L395.184 162.155L403.305 164.57L410.706 166.986L417.49 169.453L423.658 171.92L429.928 174.747L435.479 177.522L440.362 180.298L444.627 183.022L448.071 185.489L451.052 187.956L453.622 190.32L455.78 192.684L457.579 194.997L458.967 197.156L460.098 199.263L460.92 201.37L461.537 203.477L461.897 205.533L461.999 207.589Z"
                                fill="#FDFAF1" />
                        </g>
                        <path id="&#232;&#183;&#175;&#229;&#190;&#132;-4" fill-rule="evenodd" clip-rule="evenodd"
                            d="M15.1105 177.266L0 197.259L5.60218 216.995L135.737 220.902L208.925 222.855L244.851 223.677L258.985 223.78L269.162 223.626L282.987 223.06L291.982 222.084L296.196 221.364L299.948 220.439L303.34 219.411L306.629 218.126L309.765 216.636L312.694 214.94L312.848 214.323L312.591 213.655L310.279 210.622L304.625 204.712L292.65 193.764L279.338 182.766L271.269 176.752L267.98 174.594L266.746 173.977L264.125 173.052L259.499 171.767L251.893 169.917L240.38 167.45L223.779 164.16L210.93 177.266H15.1105Z"
                            fill="url(#paint0_linear_7905_649)" />
                    </g>
                    <g id="Group_3">
                        <path id="&#231;&#159;&#169;&#229;&#189;&#162;" fill-rule="evenodd" clip-rule="evenodd"
                            d="M152.391 18.0351L153.162 17.1614L154.087 16.5446L155.166 16.1848L156.297 16.082L157.376 16.2876L158.456 16.7502L170.534 24.6138L171.407 25.3847L172.024 26.3099L172.435 27.3892L172.487 28.4685L172.333 29.5992L171.819 30.6785L171.048 31.5523L170.122 32.169L169.043 32.5288L167.912 32.6316L166.833 32.426L165.754 31.9634L153.676 24.0998L152.802 23.3289L152.185 22.4038L151.774 21.3244L151.723 20.2451L151.877 19.1144L152.391 18.0351Z"
                            fill="url(#paint1_linear_7905_649)" />
                    </g>
                    <g id="Group_4">
                        <path id="&#231;&#159;&#169;&#229;&#189;&#162;&#229;&#164;&#135;&#228;&#187;&#189;-2"
                            fill-rule="evenodd" clip-rule="evenodd"
                            d="M192.94 0L194.07 0.30838L195.047 0.873725L195.869 1.64467L196.435 2.62121L196.743 3.70051V4.88263L195.355 16.087L195.047 17.2691L194.533 18.1942L193.711 19.0166L192.785 19.5819L191.706 19.8903H190.524L189.342 19.5819L188.365 19.068L187.594 18.2456L187.029 17.3205L186.721 16.2412V15.0591L188.057 3.80331L188.365 2.67259L188.931 1.69607L189.702 0.925124L190.678 0.359764L191.758 0.0513993L192.94 0Z"
                            fill="url(#paint2_linear_7905_649)" />
                    </g>
                    <g id="Group_5">
                        <path id="&#231;&#159;&#169;&#229;&#189;&#162;&#229;&#164;&#135;&#228;&#187;&#189;"
                            fill-rule="evenodd" clip-rule="evenodd"
                            d="M171.663 5.29543L172.793 5.08984L173.924 5.14124L175.003 5.50101L175.929 6.11777L176.648 6.94011L177.213 8.01943L179.372 14.3411L179.578 15.4719L179.526 16.6026L179.166 17.6819L178.55 18.607L177.727 19.3266L176.648 19.8919L175.517 20.0975L174.387 20.0461L173.307 19.6863L172.382 19.0696L171.663 18.2473L171.097 17.1679L168.939 10.8462L168.682 9.7155L168.784 8.58478L169.144 7.50547L169.761 6.58033L170.583 5.80939L171.663 5.29543Z"
                            fill="url(#paint3_linear_7905_649)" />
                    </g>
                    <g id="Group_6">
                        <g id="Group_7">
                            <path id="Vector_2" fill-rule="evenodd" clip-rule="evenodd"
                                d="M250.762 192.941L250.608 195.87L250.197 198.646L249.529 201.267L248.552 203.837L247.37 206.304L245.982 208.616L244.389 210.775L242.539 212.728L240.586 214.578L238.427 216.172L236.114 217.559L233.647 218.741L231.077 219.718L228.456 220.386L225.681 220.797L222.751 220.951L219.822 220.797L217.046 220.386L214.425 219.718L211.855 218.741L209.388 217.559L207.075 216.172L204.917 214.578L202.964 212.728L201.113 210.775L199.52 208.616L198.132 206.304L196.95 203.837L195.974 201.267L195.306 198.646L194.894 195.87L194.74 192.941L194.894 190.011L195.306 187.236L195.974 184.614L196.95 182.045L198.132 179.578L199.52 177.265L201.113 175.106L202.964 173.153L204.917 171.303L207.075 169.71L209.388 168.322L211.855 167.14L214.425 166.163L217.046 165.495L219.822 165.084L222.751 164.93L225.681 165.084L228.456 165.495L231.077 166.163L233.647 167.14L236.114 168.322L238.427 169.71L240.586 171.303L242.539 173.153L244.389 175.106L245.982 177.265L247.37 179.578L248.552 182.045L249.529 184.614L250.197 187.236L250.608 190.011L250.762 192.941Z"
                                fill="url(#paint4_linear_7905_649)" />
                        </g>
                        <path id="Vector_3" fill-rule="evenodd" clip-rule="evenodd"
                            d="M250.762 192.941L250.608 195.87L250.197 198.646L249.529 201.267L248.552 203.837L247.37 206.304L245.982 208.616L244.389 210.775L242.539 212.728L240.586 214.578L238.427 216.172L236.114 217.559L233.647 218.741L231.077 219.718L228.456 220.386L225.681 220.797L222.751 220.951L219.822 220.797L217.046 220.386L214.425 219.718L211.855 218.741L209.388 217.559L207.075 216.172L204.917 214.578L202.964 212.728L201.113 210.775L199.52 208.616L198.132 206.304L196.95 203.837L195.974 201.267L195.306 198.646L194.894 195.87L194.74 192.941L194.894 190.011L195.306 187.236L195.974 184.614L196.95 182.045L198.132 179.578L199.52 177.265L201.113 175.106L202.964 173.153L204.917 171.303L207.075 169.71L209.388 168.322L211.855 167.14L214.425 166.163L217.046 165.495L219.822 165.084L222.751 164.93L225.681 165.084L228.456 165.495L231.077 166.163L233.647 167.14L236.114 168.322L238.427 169.71L240.586 171.303L242.539 173.153L244.389 175.106L245.982 177.265L247.37 179.578L248.552 182.045L249.529 184.614L250.197 187.236L250.608 190.011L250.762 192.941Z"
                            fill="url(#paint5_linear_7905_649)" />
                    </g>
                    <g id="Group_8">
                        <path
                            id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;&#229;&#164;&#135;&#228;&#187;&#189;"
                            fill-rule="evenodd" clip-rule="evenodd"
                            d="M313.157 34.4278L313.774 36.8948L313.98 39.259L313.877 41.6746L313.466 43.9875L312.797 46.1975L311.821 48.3561L310.639 50.3092L309.148 52.1595L307.452 53.7527L305.551 55.1918L303.392 56.3225L301.079 57.1963L298.612 57.813L296.248 58.0186L293.832 57.9158L291.519 57.5046L289.309 56.8365L287.151 55.86L285.198 54.6779L283.347 53.1874L281.754 51.4913L280.315 49.5896L279.184 47.431L278.311 45.1182L277.694 42.6512L277.488 40.2869L277.591 37.8713L278.002 35.5585L278.67 33.3485L279.647 31.1898L280.829 29.2368L282.32 27.3865L284.016 25.7932L285.917 24.3541L288.076 23.2234L290.389 22.3497L292.856 21.7329L295.22 21.5273L297.636 21.6301L299.948 22.0413L302.158 22.7094L304.317 23.686L306.27 24.8681L308.12 26.3586L309.714 28.0547L311.153 29.9563L312.283 32.115L313.157 34.4278Z"
                            fill="url(#paint6_linear_7905_649)" />
                    </g>
                    <g id="Group_9">
                        <path id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;" fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M314.752 212.422L314.186 213.399L313.415 214.272L312.387 215.043L311.103 215.763L309.458 216.38L307.453 216.894L305.038 217.253L302.211 217.459L298.87 217.51L294.502 217.253L289.568 216.739L283.811 215.866L272.041 213.399L265.257 211.651L251.534 207.54L236.681 202.348L221.159 196.13L205.895 189.242L191.812 182.201L179.323 175.16L173.412 171.511L163.39 164.726L158.816 161.232L154.961 158.045L151.774 155.064L149.462 152.648L147.663 150.438L146.275 148.485L145.247 146.686L144.528 145.042L144.116 143.654L144.014 142.369L144.116 141.187L144.425 140.108L144.99 139.131L145.761 138.257L146.738 137.435L148.074 136.716L149.719 136.099L151.723 135.585L154.087 135.225L156.966 135.02H160.306L164.624 135.225L169.609 135.79L175.314 136.664L187.135 139.131L193.868 140.827L207.591 144.939L222.496 150.181L238.017 156.4L253.282 163.236L267.364 170.329L279.854 177.319L285.764 180.968L295.786 187.752L300.361 191.247L304.215 194.485L307.351 197.414L309.715 199.881L311.514 202.04L312.901 204.045L313.929 205.843L314.649 207.437L315.009 208.876L315.163 210.161L315.06 211.343L314.752 212.422Z"
                            fill="url(#paint7_linear_7905_649)" />
                    </g>
                    <g id="Group_10">
                        <path id="&#232;&#183;&#175;&#229;&#190;&#132;" fill-rule="evenodd" clip-rule="evenodd"
                            d="M161.023 135.843L186.567 75.6067L188.572 71.238L190.73 67.1263L192.992 63.323L195.407 59.6739L197.926 56.3331L200.701 53.0437L203.579 50.0114L206.56 47.1846L209.644 44.6148L212.831 42.1992L216.223 39.9891L219.615 37.9847L223.161 36.2372L226.759 34.6439L230.408 33.2562L235.137 31.7657L239.917 30.635L244.799 29.8127L249.785 29.2987L254.77 29.1445L259.807 29.2473L264.895 29.6585L270.035 30.3781L275.123 31.4574L280.16 32.7937L285.248 34.4383L290.336 36.4428L295.116 38.6014L299.639 41.017L303.905 43.5355L307.914 46.3109L311.717 49.189L315.366 52.3756L318.758 55.7163L321.893 59.1599L324.772 62.809L327.393 66.5609L329.757 70.5184L331.864 74.5787L333.663 78.7932L335.205 83.1105L336.438 87.6334L337.415 92.1562L338.032 96.7819L338.34 101.51L338.392 106.393L338.083 111.43L337.569 115.49L336.85 119.653L335.873 123.868L334.64 128.185L333.098 132.554L331.35 137.076L305.806 197.261L295.938 195.822L286.379 194.075L277.127 192.122L268.082 189.912L259.293 187.496L250.761 184.875L242.435 181.997L234.366 178.913L226.502 175.572L217.559 171.46L208.873 167.092L200.393 162.518L192.118 157.635L184.049 152.547L176.185 147.253L168.476 141.651L161.023 135.843Z"
                            fill="url(#paint8_linear_7905_649)" />
                    </g>
                    <path
                        id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;&#229;&#164;&#135;&#228;&#187;&#189;-4"
                        fill-rule="evenodd" clip-rule="evenodd"
                        d="M418.314 192.939L418.159 194.378L417.8 195.714L417.234 196.948L416.412 198.079L415.487 199.004L414.356 199.826L413.123 200.392L411.786 200.751L410.347 200.905L408.908 200.751L407.572 200.392L406.338 199.826L405.208 199.004L404.283 198.079L403.46 196.948L402.895 195.714L402.535 194.378L402.381 192.939L402.535 191.5L402.895 190.164L403.46 188.93L404.283 187.799L405.208 186.874L406.338 186.052L407.572 185.487L408.908 185.127L410.347 184.973L411.786 185.127L413.123 185.487L414.356 186.052L415.487 186.874L416.412 187.799L417.234 188.93L417.8 190.164L418.159 191.5L418.314 192.939Z"
                        fill="url(#paint9_radial_7905_649)" />
                </g>
                <defs>
                    <linearGradient id="paint0_linear_7905_649" x1="22.0358" y1="193.065" x2="178.994" y2="222.537"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#DDAF3A" stop-opacity="0" />
                        <stop offset="1" stop-color="#FFB941" stop-opacity="0.846864" />
                    </linearGradient>
                    <linearGradient id="paint1_linear_7905_649" x1="166.72" y1="16.082" x2="165.62" y2="36.3285"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#F7DF76" />
                        <stop offset="1" stop-color="#EEB765" />
                    </linearGradient>
                    <linearGradient id="paint2_linear_7905_649" x1="194.698" y1="1.74931e-07" x2="190.426" y2="23.7232"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#F7DF76" />
                        <stop offset="1" stop-color="#EEB765" />
                    </linearGradient>
                    <linearGradient id="paint3_linear_7905_649" x1="179.579" y1="5.08984" x2="175.786" y2="23.0489"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#F7DF76" />
                        <stop offset="1" stop-color="#EEB765" />
                    </linearGradient>
                    <linearGradient id="paint4_linear_7905_649" x1="194.74" y1="164.93" x2="253.591" y2="220.951"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#3154EF" />
                        <stop offset="0.775833" stop-color="#769FFF" />
                        <stop offset="1" stop-color="#C9ECFF" />
                    </linearGradient>
                    <linearGradient id="paint5_linear_7905_649" x1="250.762" y1="197.367" x2="194.74" y2="197.97"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FFEDD9" />
                        <stop offset="0.173769" stop-color="#FFF4DD" />
                        <stop offset="1" stop-color="#FFAA5F" />
                    </linearGradient>
                    <linearGradient id="paint6_linear_7905_649" x1="277.488" y1="21.5273" x2="315.823" y2="58.0186"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#F58E08" />
                        <stop offset="0.55" stop-color="#FFDF51" />
                        <stop offset="1" stop-color="#FFF8DE" />
                    </linearGradient>
                    <linearGradient id="paint7_linear_7905_649" x1="144.014" y1="174.019" x2="322.229" y2="193.183"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#EF8731" />
                        <stop offset="0.775833" stop-color="#FFB876" />
                        <stop offset="1" stop-color="#FFEEC9" />
                    </linearGradient>
                    <linearGradient id="paint8_linear_7905_649" x1="338.392" y1="125.783" x2="161.026" y2="127.691"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FFF2D9" />
                        <stop offset="0.173769" stop-color="#FFF7DD" />
                        <stop offset="1" stop-color="#FFAF40" />
                    </linearGradient>
                    <radialGradient id="paint9_radial_7905_649" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
                        gradientTransform="translate(408.147 194.417) scale(10.1026)">
                        <stop stop-color="#FFDFBF" />
                        <stop offset="1" stop-color="#FFC171" />
                    </radialGradient>
                </defs>
            </svg>
        </div>
        <div class="content">
            <div>您的应用正在启动，请耐心等待</div>
            <button id="button" class="button" onclick="freshen()">刷新</button>
        </div>
    </div>
</body>
<script>
    let time = 11
    let timeoutFun = null
    // 倒计时/执行刷新
    function timeTimeout() {
        time--

        if (time < 0) {
            window.location.reload();
        } else {
            document.getElementById('button').innerHTML = '刷新 ' + '&nbsp;' + time.toString().padStart(2, "0")

            clearTimeout(timeoutFun)
            timeoutFun = null
            timeoutFun = setTimeout(() => {
                timeTimeout()
            }, 1000);
        }
    }
    timeTimeout()

    // 刷新按钮
    function freshen() {
        window.location.reload();
    } 
</script>

</html>