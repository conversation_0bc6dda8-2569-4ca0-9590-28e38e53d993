user  nobody;
worker_processes  auto;
error_log  /dev/stdout;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
    multi_accept on;
    use epoll;
}

http {
    lua_package_path "/usr/local/openresty/lualib/cy/?.lua;;";

    # 初始化共享内存字典用于缓存
    lua_shared_dict entity_url_cache 10m;

    ## 全局 SSL 优化（如果只做 HTTP 可删掉）
    ssl_protocols             TLSv1.2 TLSv1.3;
    ssl_ciphers               HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache         shared:SSL:10m;
    ssl_session_timeout       10m;

    include       mime.types;
    default_type  application/octet-stream;
    sendfile       on;
    keepalive_timeout 65;

    # 加载所有 vhost 配置
    include /usr/local/openresty/nginx/conf/conf.d/*.conf;
}
