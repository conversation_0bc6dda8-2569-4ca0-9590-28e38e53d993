# demo from production env
server {
    listen 443 ssl;
    server_name *.gz13.suanyun.cn *.gz13.chenyu.cn;
    
    # SSL证书配置
    ssl_certificate /www/server/nginx/cert/gz13.chenyu.cn.cer;
    ssl_certificate_key /www/server/nginx/cert/gz13.chenyu.cn.key;
    
    # 推荐的SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # SSL Session配置
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    
    proxy_http_version  1.1;  #1.1版本才支持wss
    proxy_max_temp_file_size 10240M;
    #启用支持websocket连接的配置
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_connect_timeout 60; #配置等待服务器响应时间
    proxy_read_timeout 600;
    proxy_send_timeout 600;
    proxy_redirect off;
    proxy_set_header Host "$http_host";
    proxy_set_header Origin "$scheme://$http_host";

    # 设置客户端请求的最大大小为 10MB
    client_max_body_size 800m;

    location /loadpodmapping {
      set_by_lua_block $instanceUuid {
          local pattern2 = "(.+).gz13.suanyun.cn"
          local pattern = "(.+).gz13.chenyu.cn"
          local result = string.match(ngx.var.host, pattern)
          if not result then
              result = string.match(ngx.var.host, pattern2)
          end
          if result then
                  local l = string.len(result)
                  if l>=16 then
                      return result
                  end
          end
          return ""
      }
      proxy_pass http://127.0.0.1:6001/api/sys/weburl2nginx?instance_uuid=$instanceUuid&action=add;
      proxy_set_header authorization "ac4f334sddbfe701dda3783124ef6822a";
      proxy_set_header Accept-Encoding "identity";

      #return 200 "Captured value: ";
    }
    
    location /setnginx {
      set_by_lua_block $instanceUuid {
          local pattern2 = "(.+).gz13.suanyun.cn"
          local pattern = "(.+).gz13.chenyu.cn"
          local result = string.match(ngx.var.host, pattern)
          if not result then
              result = string.match(ngx.var.host, pattern2)
          end
          if result then
                  local l = string.len(result)
                  if l>=16 then
                      return result
                  end
          end
          return ""
      }
      proxy_pass http://127.0.0.1:7007/api/node/docker/set_nginx?startup_mark_key=$instanceUuid&action=add;
      proxy_set_header authorization "ac4f334sddbfe701dda3783124ef6822a";
      proxy_set_header Accept-Encoding "identity";

      #return 200 "Captured value: $instanceUuid";
    }
    
    # 定义自定义错误页面
    error_page 502 /node_suanyun502.html;

    # 配置 502 错误页面的路径
    location = /node_suanyun502.html {
        root /www/server/nginx/html;
        internal;
    }
    
    location / {
        add_header Cache-Control no-cache;
        set $redirected_url "";
        
        
        set_by_lua_block $redirected_url {
          local a = 'http://***************:12088'
          return a
        }
      
        access_by_lua_block{
            local pattern2 = "(.+).gz13.suanyun.cn"
            local pattern = "(.+).gz13.chenyu.cn"
            local result = string.match(ngx.var.host, pattern)
            
            if not result then
                result = string.match(ngx.var.host, pattern2)
            end
            
            if result=="online" then
              ngx.var.redirected_url = "http://127.0.0.1:6002"
              return 
            end 
            
            if result=="node16" then
              ngx.var.redirected_url = "http://127.0.0.1:7021"
              return 
            end 
            
            local instanceUuid =''
            if result then
                if string.len(result)>=16 then
                    instanceUuid = result
                end
            end
            
            if instanceUuid=="" then
                ngx.header.content_type = "text/plain; charset=utf-8"
                ngx.status=404
                ngx.say("不是标准的Pod地址.")
                ngx.flush(true)
                ngx.exit(ngx.HTTP_OK)
            end
            
            local dict = ngx.shared.my_shared_data
            local v = dict:get(instanceUuid)
            if v then
                local lastChar = string.sub(v, -1)
                if lastChar == '/' then
                    v = string.sub(v, 1, -2)
                end
                -- 设置最后使用时间
                if string.len(instanceUuid) == 34 then
                    local truncatedUuid = string.sub(instanceUuid, 1, 32)
                    local lastusetimeKey = "lastusetime_" .. truncatedUuid
                    dict:set(lastusetimeKey, os.time())
                end
                ngx.var.redirected_url = v
                -- ngx.say(v)
                -- ngx.say("ok")
                -- ngx.flush(true)
            else
                local web_url1=''
                if web_url1=='' then 
                    ngx.header.content_type = "text/plain; charset=utf-8"
                    ngx.status=404
                    ngx.say("未获取到Pod地址,请尝试重启Pod.")
                    ngx.flush(true)
                    ngx.exit(ngx.HTTP_OK)
                end
            
                local res
                if string.len(result)>32 then
                    res = ngx.location.capture("/setnginx", { method = ngx.HTTP_GET})
                else
                    res = ngx.location.capture("/loadpodmapping", { method = ngx.HTTP_GET})
                end
                
                if res == nil then
                    ngx.status = 500
                    ngx.say("请求处理失败，请稍后重试.")
                    ngx.exit(ngx.status)
                end
                
                
              -- ngx.say(res.status)
                if res.status ~= ngx.HTTP_OK then
                    ngx.status=404
                    ngx.say("请求Pod信息失败，请刷新重试.")
                    ngx.flush(true)
                    ngx.exit(ngx.HTTP_OK)
                end
                  
                     local web_url=''
                     if string.find(res.body, "http") == nil then
                       -- ngx.say("res.body 中不包含 'http' 字符串")
                          ngx.header.content_type = "text/plain; charset=utf-8"
                          ngx.status=404
                          ngx.say("Pod不存在,请确认是否已启动.")
                          ngx.flush(true)
                          ngx.exit(ngx.HTTP_OK)
                     else
                          local start_pos, end_pos = string.find(res.body, '"web_url":"')
                          if start_pos and end_pos then
                            local url_start = end_pos + 1
                            local url_end = string.find(res.body, '"', url_start)
                            if url_end then
                                web_url = string.sub(res.body, url_start, url_end - 1)
                                dict:set(instanceUuid,web_url)
                                local lastChar = string.sub(web_url, -1)
                                if lastChar == '/' then
                                      web_url = string.sub(web_url, 1, -2)
                                end
                                ngx.var.redirected_url = web_url
                            end
                          end
                      end
                      if web_url=='' then 
                          ngx.header.content_type = "text/plain; charset=utf-8"
                          ngx.status=404
                          ngx.say("未获取到Pod地址,请尝试重新启动Pod.")
                          ngx.flush(true)
                          ngx.exit(ngx.HTTP_OK)
                      end
            end
        }
        
        if ($redirected_url ~ "^http") {
            
            proxy_pass $redirected_url;
        }

        if ($redirected_url !~ "^http") {
            # $redirected_url 不以 htt 开头
            # return 404 $redirected_url;
            # return 200 $redirected_url;
            # proxy_pass $redirected_url;
        }
    }
    access_log  /www/wwwlogs/gz13.suanyun.cn.log;
    error_log  /www/wwwlogs/gz13.suanyun.cn.error.log;

}
server
{
    listen 80;
    server_name gz13.suanyun.cn;
    index index.php index.html index.htm default.php default.htm default.html;
    root /www/wwwroot/gz13.suanyun.cn;
    #CERT-APPLY-CHECK--START
    # 用于SSL证书申请时的文件验证相关配置 -- 请勿删除
    include /www/server/panel/vhost/nginx/well-known/gz13.suanyun.cn.conf;
    #CERT-APPLY-CHECK--END

    #SSL-START SSL相关配置，请勿删除或修改下一行带注释的404规则
    #error_page 404/404.html;
    #SSL-END

    #ERROR-PAGE-START  错误页配置，可以注释、删除或修改
    error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #PHP-INFO-START  PHP引用配置，可以注释或修改
    include enable-php-00.conf;
    #PHP-INFO-END

    #REWRITE-START URL重写规则引用,修改后将导致面板设置的伪静态规则失效
    include /www/server/panel/vhost/rewrite/gz13.suanyun.cn.conf;
    #REWRITE-END

    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }

    #一键申请SSL证书验证目录相关设置
    location ~ \.well-known{
        allow all;
    }

    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      30d;
        error_log /dev/null;
        access_log /dev/null;
    }

    location ~ .*\.(js|css)?$
    {
        expires      12h;
        error_log /dev/null;
        access_log /dev/null;
    }
    access_log  /www/wwwlogs/gz13.suanyun.cn.log;
    error_log  /www/wwwlogs/gz13.suanyun.cn.error.log;
}