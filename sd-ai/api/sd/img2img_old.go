package sd

//
//import (
//	"bytes"
//	"center-ai/enums"
//	"center-ai/service"
//	"center-ai/utils/config"
//	"center-ai/utils/errmsg"
//	"center-ai/utils/logger"
//	"center-ai/utils/myimg"
//	"center-ai/utils/tools"
//	"encoding/base64"
//	"fmt"
//	"github.com/gin-gonic/gin"
//	"io/ioutil"
//	"net/http"
//	"os"
//)
//
//type Img2ImgRequest struct {
//	InitImages             []string `json:"init_images"`
//	ResizeMode             int      `json:"resize_mode"`        //调整模式 默认0 0-3只调整大小,裁剪和调整大小,调整和填充,只需调整大小
//	DenoisingStrength      float64  `json:"denoising_strength"` //重绘强度 0-1 默认0.78
//	ImageCfgScale          float64  `json:"image_cfg_scale"`    //图像的引导系数(参考系数) 0-3,默认1.5
//	Mask                   string   `json:"mask"`
//	MaskBlur               int      `json:"mask_blur"` //蒙版边缘模糊度
//	MaskBlurX              int      `json:"mask_blur_x"`
//	MaskBlurY              int      `json:"mask_blur_y"`
//	InpaintingFill         int      `json:"inpainting_fill"`          //仅在mask存在的时候使用 SD(Masked content) 蒙版区域内容处理 默认值 0 模版模式 0,1,2,3 1=>original 原图, 2=>latent noise 潜在噪声, 3=>latent nothing 无潜在空间
//	InpaintFullRes         bool     `json:"inpaint_full_res"`         //重绘区域 默认:true 全图 ,false:仅蒙板
//	InpaintFullResPadding  int      `json:"inpaint_full_res_padding"` //仅蒙版区域下边缘预留像素 被遮罩部分的像素值 正常sd默认给32
//	InpaintingMaskInvert   int      `json:"inpainting_mask_invert"`   //蒙版模式 重绘蒙版内容 重绘非蒙版内容 仅在mask存在的时候使用 蒙版反转,字面意思就是翻转:黑白换色,是SD的mask Model 默认绘制蒙板内容
//	InitialNoiseMultiplier int      `json:"initial_noise_multiplier"`
//	Prompt                 string   `json:"prompt"`
//	Styles                 []string `json:"styles"`
//	Seed                   int      `json:"seed"`
//	Subseed                int      `json:"subseed"`
//	SubseedStrength        int      `json:"subseed_strength"`
//	SeedResizeFromH        int      `json:"seed_resize_from_h"`
//	SeedResizeFromW        int      `json:"seed_resize_from_w"`
//	SamplerName            string   `json:"sampler_name"`
//	BatchSize              int      `json:"batch_size"`
//	NIter                  int      `json:"n_iter"`
//	Steps                  int      `json:"steps"`
//	CfgScale               int      `json:"cfg_scale"`
//	Width                  int      `json:"width"`
//	Height                 int      `json:"height"`
//	RestoreFaces           bool     `json:"restore_faces"`
//	Tiling                 bool     `json:"tiling"`
//	DoNotSaveSamples       bool     `json:"do_not_save_samples"`
//	DoNotSaveGrid          bool     `json:"do_not_save_grid"`
//	NegativePrompt         string   `json:"negative_prompt"`
//	Eta                    int      `json:"eta"` //该参数用于控制生成过程中噪声分布的形状。较大的值会产生更平滑的图像，较小的值会产生更噪声化的图像。默认值为 0, 想要点惊喜和变化,使用Euler a、DPM++ SDE、DPM++ SDE Karras、DPM2 a Karras（注意调正对应eta值）
//	SMinUncond             int      `json:"s_min_uncond"`
//	SChurn                 int      `json:"s_churn"`
//	STmax                  int      `json:"s_tmax"`
//	STmin                  int      `json:"s_tmin"`
//	SNoise                 int      `json:"s_noise"`
//	OverrideSettings       struct {
//	} `json:"override_settings"`
//	OverrideSettingsRestoreAfterwards bool          `json:"override_settings_restore_afterwards"`
//	ScriptArgs                        []interface{} `json:"script_args"`
//	SamplerIndex                      string        `json:"sampler_index"`
//	IncludeInitImages                 bool          `json:"include_init_images"`
//	ScriptName                        string        `json:"script_name"`
//	SendImages                        bool          `json:"send_images"`
//	SaveImages                        bool          `json:"save_images"`
//	AlwaysonScripts                   struct {
//	} `json:"alwayson_scripts"`
//}
//
//type SimpleImg2ImgRequest struct {
//	InitImages           []string                `json:"init_images"`
//	Prompt               string                  `json:"prompt"`
//	NegativePrompt       string                  `json:"negative_prompt"`
//	Mask                 string                  `json:"mask"`
//	InpaintFullRes       bool                    `json:"inpaint_full_res"`       //重绘区域  默认:true 全图 ,false:仅蒙板
//	InpaintingFill       int                     `json:"inpainting_fill"`        //蒙版区域内容处理 默认值 0 模版模式 0,1,2,3 1=>original 原图, 2=>latent noise 潜在噪声, 3=>latent nothing 无潜在空间
//	InpaintingMaskInvert int                     `json:"inpainting_mask_invert"` //1保留蒙版区域,重绘非版本区域   0重绘蒙版区域 (1反转蒙版)
//	DenoisingStrength    float64                 `json:"denoising_strength"`     //重绘强度 0-1 默认0.78
//	Width                int                     `json:"width"`
//	Height               int                     `json:"height"`
//	Seed                 int                     `json:"seed"`
//	Steps                int                     `json:"steps"`
//	CfgScale             int                     `json:"cfg_scale"`    //提示词权重,默认7
//	SamplerName          string                  `json:"sampler_name"` //采样器 :Euler …
//	OverrideSettings     service.OverrideSetting `json:"override_settings"`
//}
//
//func Img2img(c *gin.Context) {
//
//	var code int
//	var msg string
//
//	//claims := c.Value("claims").(*middleware.MyClaims)
//	//if claims.UserId <= 0 {
//	//	errmsg.Abort(c, errmsg.FAIL, "请先登录")
//	//	return
//	//}
//
//	customApp, _ := c.GetPostForm("custom_app")
//	customData, _ := c.GetPostForm("custom_data")
//	customPath, _ := c.GetPostForm("custom_path")
//	parameters, _ := c.GetPostForm("parameters")
//
//	if customApp != "shopshow" && customApp != "sdapitest" {
//		logger.Error("customApp 参数错误", customApp)
//		errmsg.Abort(c, errmsg.FAIL, "App参数错误")
//		return
//	}
//
//	var img2Img Img2ImgRequest
//	if err := tools.GetStructFromJson(&img2Img, parameters); err != nil {
//		logger.Error(err)
//		errmsg.Abort(c, errmsg.FAIL, "数据解析失败")
//		return
//	}
//
//	initImageFile, _, err := c.Request.FormFile("init_image") //c.FormFile("init_image")
//	if err != nil {
//		initImagePath, _ := c.GetPostForm("init_image_path")
//		if initImagePath != "" {
//			absolutePath := config.DiffusionFilePath + initImagePath
//			initImageFile, err = os.Open(absolutePath)
//			if err != nil {
//				logger.Error(err)
//				errmsg.Abort(c, errmsg.FAIL, "初始图路径文件不存在")
//				return
//			}
//		}
//		//logger.Error(err)
//		//errmsg.Abort(c, errmsg.FAIL, "初始图上传失败")
//		//return
//	}
//
//	if initImageFile != nil {
//		initImageBuf := bytes.NewBuffer(nil)
//		if _, err := initImageBuf.ReadFrom(initImageFile); err != nil {
//			logger.Error(err)
//		} else {
//			if img2Img.InitImages == nil || len(img2Img.InitImages) == 0 {
//				img2Img.InitImages = make([]string, 0)
//			}
//			// 使用 base64.StdEncoding 将 byte 切片编码为 base64 字符串
//			initImageBase64 := base64.StdEncoding.EncodeToString(initImageBuf.Bytes())
//			img2Img.InitImages = append(img2Img.InitImages, initImageBase64)
//
//			img, er := myimg.Base64ToImg(initImageBase64)
//			if er != nil {
//				logger.Error(er)
//				errmsg.Abort(c, errmsg.FAIL, "初始图尺寸获取失败")
//				return
//			}
//
//			img2Img.Width = img.Bounds().Size().X
//			img2Img.Height = img.Bounds().Size().Y
//
//			//img2Img.InitImages = append(img2Img.InitImages, "data:image/png;base64,"+base64)
//		}
//	}
//
//	maskImageFile, _, err := c.Request.FormFile("mask_image")
//	if err != nil {
//		maskImagePath, _ := c.GetPostForm("mask_image_path")
//		if maskImagePath != "" {
//			absolutePath := config.DiffusionFilePath + maskImagePath
//			maskImageFile, err = os.Open(absolutePath)
//			if err != nil {
//				logger.Error(err)
//				errmsg.Abort(c, errmsg.FAIL, "蒙版图路径文件不存在")
//				return
//			}
//		}
//		//logger.Error(err)
//		//errmsg.Abort(c, errmsg.FAIL, "蒙版图上传失败")
//		//return
//	}
//
//	if maskImageFile != nil {
//		maskImageBuf := bytes.NewBuffer(nil)
//		if _, err := maskImageBuf.ReadFrom(maskImageFile); err != nil {
//			logger.Error(err)
//		} else {
//			maskImageBase64 := base64.StdEncoding.EncodeToString(maskImageBuf.Bytes())
//			img2Img.Mask = maskImageBase64
//		}
//	}
//
//	overrideSettings := service.OverrideSetting{
//		//SdModelCheckpoint: `realistic\majicMIX realistic 麦橘写实_v6.safetensors [e4a30e4607]`,
//
//	}
//	simple := SimpleImg2ImgRequest{
//		InitImages:           img2Img.InitImages,
//		Prompt:               img2Img.Prompt,
//		NegativePrompt:       img2Img.NegativePrompt,
//		Mask:                 img2Img.Mask,
//		InpaintingFill:       img2Img.InpaintingFill,
//		InpaintingMaskInvert: img2Img.InpaintingMaskInvert,
//		DenoisingStrength:    img2Img.DenoisingStrength,
//		Width:                img2Img.Width,
//		Height:               img2Img.Height,
//		Seed:                 -1,
//		Steps:                30,
//		CfgScale:             7,       //提示词权重,默认7
//		SamplerName:          "Euler", //采样器 :Euler …
//		OverrideSettings:     overrideSettings,
//	}
//
//	//uuid := tools.GetUuid()
//	//input := service.SdInput{
//	//	CustomApp:  "sdapitest",
//	//	CustomData: "",
//	//	CustomPath: "img2img/" + uuid + ".png",
//	//	Sdapi:      enums.SdapiEnum.Img2img,
//	//	Parameters: tools.GetJsonFromStruct(simple),
//	//	TraceId:    tools.GetUuid(),
//	//}
//	input := service.SdInput{
//		CustomApp:  customApp,
//		CustomData: customData,
//		CustomPath: customPath,
//		Sdapi:      enums.SdapiEnum.Img2img,
//		Parameters: tools.GetJsonFromStruct(simple),
//		TraceId:    tools.GetUuid(),
//	}
//	size := service.SdService.Push(input)
//	if size == 0 {
//		logger.Error("img2img任务推送失败")
//		errmsg.Abort(c, errmsg.FAIL, "任务推送失败")
//		return
//	}
//	msg = fmt.Sprintf("绘图中，当前排在第%d位", size)
//	c.JSON(http.StatusOK, gin.H{
//		"code": code,
//		"msg":  msg,
//	})
//
//	//url := "http://117.187.188.4:7887/sdapi/v1/txt2img"
//	////m := make(map[string]interface{}, 0)
//	////m["prompt"] = "puppy dog"
//	////m["negative_prompt"] = "wrong hands"
//	////m["steps"] = 20
//	////oReq.Payload = tools.GetJsonFromMap(m)
//	//if s, err := Post(url, oReq.Payload); err != nil {
//	//	logger.Error(err)
//	//} else {
//	//	logger.Info(s)
//	//}
//}
//
//func GetImgFromUrl(url string) (string, error) {
//	resp, err := http.Get(url)
//	if err != nil {
//		logger.Error(err)
//		return "", err
//	}
//	defer resp.Body.Close()
//
//	data, err := ioutil.ReadAll(resp.Body)
//	if err != nil {
//		logger.Error(err)
//		return "", err
//	}
//
//	// 转换为 base64
//	str := base64.StdEncoding.EncodeToString(data)
//
//	return str, nil
//}
//
//func ScanImg2img(o *Img2ImgRequest) {
//	if o.Steps == 0 {
//		o.Steps = 50
//	}
//	if o.DenoisingStrength == 0 {
//		o.DenoisingStrength = 0.75
//	}
//	if o.MaskBlurX == 0 && o.MaskBlurY == 0 {
//		o.MaskBlurX = 4
//		o.MaskBlurY = 4
//	}
//	if o.Width == 0 && o.Height == 0 {
//		o.Width = 512
//		o.Height = 512
//	}
//	if o.Styles == nil {
//		o.Styles = make([]string, 0)
//	}
//	if o.ScriptArgs == nil {
//		o.ScriptArgs = make([]interface{}, 0)
//	}
//
//	if o.SamplerIndex == "" {
//		o.SamplerIndex = "Euler"
//	}
//
//	o.SendImages = true
//}
