package sd

import (
	"center-ai/enums"
	"center-ai/service"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
)

func Txt2img(c *gin.Context) {
	var code int
	var msg string
	var sdInput service.SdInput

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims.UserId <= 0 {
	//	errmsg.Abort(c, errmsg.FAIL, "请先登录")
	//	return
	//}

	if err := c.ShouldBindJSON(&sdInput); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	//input := service.SdInput{
	//	CustomApp:  customApp,
	//	CustomData: customData,
	//	CustomPath: customPath,
	//	Sdapi:      enums.SdapiEnum.Img2img,
	//	Parameters: tools.GetJsonFromStruct(simple),
	//	TraceId:    tools.GetUuid(),
	//}
	sdInput.Sdapi = enums.SdApiEnum.Txt2img
	sdInput.TraceId = tools.GetUuid()
	size := service.SdService.Push(sdInput)
	if size == 0 {
		logger.Error("img2img任务推送失败")
		errmsg.Abort(c, errmsg.FAIL, "任务推送失败")
		return
	}
	msg = fmt.Sprintf("绘图中，当前排在第%d位", size)
	c.JSON(http.StatusOK, gin.H{
		"code": code,
		"msg":  msg,
	})

	//result := make(map[string]interface{})
}
