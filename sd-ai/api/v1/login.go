package v1

import (
	"center-ai/enums"
	"center-ai/middleware"
	"center-ai/model"
	"center-ai/utils/config"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/myredis"
	"center-ai/utils/tools"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
	"time"
)

type loginReq struct {
	Username    string `json:"username"`
	Password    string `json:"password"`
	InvitedCode string `json:"invited_code"`
}

type loginSmsReq struct {
	Mobile      string `json:"mobile"`
	SmsCode     string `json:"sms_code"`
	InvitedCode string `json:"invited_code"`
	Plat        string `json:"plat"`
	Os          string `json:"os"`
	DeviceBrand string `json:"device_brand"`
	DeviceType  string `json:"device_type"`
	Channel     string `json:"channel"`
}

// Login 前台登录
func Login(c *gin.Context) {
	var code int
	var msg string
	var token string

	var oReq loginReq
	var user model.User

	er := c.ShouldBindJSON(&oReq)
	if er != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if len(oReq.Username) < 2 {
		errmsg.Abort(c, errmsg.FAIL, "用户名不正确")
		return
	}

	if len(oReq.Password) < 5 {
		errmsg.Abort(c, errmsg.FAIL, "密码不正确")
		return
	}

	if tools.IsMobile(oReq.Username) {
		user.GetByMobile(oReq.Username)
		if user.ID == 0 {
			errmsg.Abort(c, errmsg.FAIL, "用户不存在")
			return
		}
	} else {
		user.GetByUsername(oReq.Username)
		if user.ID == 0 {
			errmsg.Abort(c, errmsg.FAIL, "用户不存在")
			return
		}
	}

	err := user.CheckPassword(oReq.Password)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "密码错误")
		return
	}

	token, err = middleware.NewJWT().SetToken(user.ID, user.Username, user.Mobile)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "生成Token失败")
		return
	}

	avatarUrl := ""
	if user.Avatar != "" {
		avatarUrl = config.DiffusionDomain + user.Avatar
	}

	mobile := user.Mobile

	resp := UserInfoResp{
		UserId:    user.ID,
		Username:  user.Username,
		AvatarUrl: avatarUrl,
		Mobile:    tools.FormatMobileStar(mobile),
	}

	result := make(map[string]interface{})
	result["token"] = token
	result["user"] = resp
	result["is_new_user"] = false

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "登录成功",
			"result": result,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"result": nil,
			"msg":    msg,
		})
	}
}

func LoginSms(c *gin.Context) {
	var code int
	var msg string

	var loginReq loginSmsReq
	var user model.User

	er := c.ShouldBindJSON(&loginReq)
	if er != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if !tools.IsMobile(loginReq.Mobile) {
		errmsg.Abort(c, errmsg.FAIL, "手机号码不正确")
		return
	}

	if len(loginReq.SmsCode) != 4 {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	redisKey := enums.RedisKeyEnum.SmsLogin + loginReq.Mobile
	testCount := myredis.Get(redisKey + ":testcount")
	if testCount == "" {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请10分钟后重试")
		return
	}
	iTestCount, _ := strconv.Atoi(testCount)
	if iTestCount > 10 {
		errmsg.Abort(c, errmsg.FAIL, "验证码尝试次数过多，请10分钟后重试")
		return
	}
	iTestCount = iTestCount + 1
	if err := myredis.Set(redisKey+":testcount", strconv.Itoa(iTestCount), time.Minute*10); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "设置短信验证码尝试参数失败")
		return
	}

	v := myredis.Get(enums.RedisKeyEnum.SmsLogin + loginReq.Mobile)
	if len(v) == 0 {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请重试")
		return
	}
	if v != loginReq.SmsCode {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	if v == loginReq.SmsCode {
		//if true {
		if err := user.GetByMobile(loginReq.Mobile); err != nil {
			errmsg.Abort(c, errmsg.FAIL, "查询手机号码出错")
			return
		}
	}

	isNewUser := false
	var invitationUser model.User
	if loginReq.InvitedCode != "" {
		if err := invitationUser.GetByInvitationCode(loginReq.InvitedCode); err != nil {
			logger.Error(err)
		}
	}
	if user.ID == 0 {

		exists, err := user.ExistsMobile(loginReq.Mobile)
		if err != nil || exists {
			errmsg.Abort(c, errmsg.FAIL, "查询数据出错")
			return
		}

		visitInfo := model.UserEnv{
			Plat:        loginReq.Plat,
			OsName:      loginReq.Os,
			DeviceBrand: loginReq.DeviceBrand,
			DeviceType:  loginReq.DeviceType,
			Channel:     loginReq.Channel,
			Ip:          tools.GetClientIp(c.Request.Header),
		}
		user.RegInfo = tools.GetJsonFromStruct(visitInfo)

		user.Mobile = loginReq.Mobile
		err = user.Save()
		if err != nil || user.ID == 0 {
			errmsg.Abort(c, errmsg.FAIL, "生成记录失败")
			return
		}
		isNewUser = true
	}

	if user.ID == 0 {
		errmsg.Abort(c, errmsg.FAIL, "生成用户信息失败")
		return
	}

	if isNewUser {

	}

	token, err := middleware.NewJWT().SetToken(user.ID, user.Username, user.Mobile)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "生成Token失败")
		return
	}

	avatarUrl := ""
	if user.Avatar != "" {
		avatarUrl = config.DiffusionDomain + user.Avatar
	}

	resp := UserInfoResp{
		UserId:    user.ID,
		Username:  user.Username,
		AvatarUrl: avatarUrl,
		Mobile:    tools.FormatMobileStar(user.Mobile),
	}

	result := make(map[string]interface{})
	result["token"] = token
	result["user"] = resp
	result["is_new_user"] = isNewUser

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "登录成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
		return
	}

}
