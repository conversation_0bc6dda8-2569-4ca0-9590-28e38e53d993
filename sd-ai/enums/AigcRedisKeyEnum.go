package enums

import "reflect"

type aigcRedisKeyEnum_ struct {
	SdApiServer, SdTaskLockKey, SdApiServerIndex, SdApiServerQueue,
	SdTaskAutoNumber, SdTaskLastCompleteNumber,
	SdTaskPush, SdTaskPushTarget, SdTaskProgress, SdTaskPop,
	UpScalingIn, UpScalingOut, UpScalingState string
}

func (c aigcRedisKeyEnum_) Get(id string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if typeVo.Field(i).Name == id {
			return vo.Field(i).Interface().(string)
		}
	}
	return ""
}

var AigcRedisKeyEnum = aigcRedisKeyEnum_{
	SdApiServer:      "aigc-worker:sd:server:map",
	SdApiServerQueue: "aigc-worker:sd:server:queue",

	SdTaskLockKey:            "aigc-worker:sd:task:lock_key",             //任务锁，使用时需要加后缀
	SdTaskAutoNumber:         "aigc-worker:sd:task:auto_number",          //绘图任务自增号码
	SdTaskLastCompleteNumber: "aigc-worker:sd:task:last_complete_number", //绘图任务最后完成的号码

	SdTaskPush:       "aigc-worker:sd:task:queue",        //绘图队列
	SdTaskPushTarget: "aigc-worker:sd:task:queue_target", //绘图指定队列
	SdTaskPop:        "aigc-worker:sd:task:out",
	SdTaskProgress:   "aigc-worker:sd:task:progress",
	UpScalingIn:      "aigc-worker:list:up-scaling-in",
	UpScalingOut:     "aigc-worker:list:up-scaling-out:camera",
	UpScalingState:   "aigc-worker:list:up-scaling-state:camera",
}
