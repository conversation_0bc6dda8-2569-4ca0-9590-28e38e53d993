// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.22.0
// source: protobuf/dy.proto

package protobuf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommentTypeTag int32

const (
	CommentTypeTag_COMMENTTYPETAGUNKNOWN CommentTypeTag = 0
	CommentTypeTag_COMMENTTYPETAGSTAR    CommentTypeTag = 1
)

// Enum value maps for CommentTypeTag.
var (
	CommentTypeTag_name = map[int32]string{
		0: "COMMENTTYPETAGUNKNOWN",
		1: "COMMENTTYPETAGSTAR",
	}
	CommentTypeTag_value = map[string]int32{
		"COMMENTTYPETAGUNKNOWN": 0,
		"COMMENTTYPETAGSTAR":    1,
	}
)

func (x CommentTypeTag) Enum() *CommentTypeTag {
	p := new(CommentTypeTag)
	*p = x
	return p
}

func (x CommentTypeTag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommentTypeTag) Descriptor() protoreflect.EnumDescriptor {
	return file_protobuf_dy_proto_enumTypes[0].Descriptor()
}

func (CommentTypeTag) Type() protoreflect.EnumType {
	return &file_protobuf_dy_proto_enumTypes[0]
}

func (x CommentTypeTag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommentTypeTag.Descriptor instead.
func (CommentTypeTag) EnumDescriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{0}
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MessagesList      []*Message        `protobuf:"bytes,1,rep,name=messagesList,proto3" json:"messagesList,omitempty"`
	Cursor            string            `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	FetchInterval     uint64            `protobuf:"varint,3,opt,name=fetchInterval,proto3" json:"fetchInterval,omitempty"`
	Now               uint64            `protobuf:"varint,4,opt,name=now,proto3" json:"now,omitempty"`
	InternalExt       string            `protobuf:"bytes,5,opt,name=internalExt,proto3" json:"internalExt,omitempty"`
	FetchType         uint32            `protobuf:"varint,6,opt,name=fetchType,proto3" json:"fetchType,omitempty"`
	RouteParams       map[string]string `protobuf:"bytes,7,rep,name=routeParams,proto3" json:"routeParams,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	HeartbeatDuration uint64            `protobuf:"varint,8,opt,name=heartbeatDuration,proto3" json:"heartbeatDuration,omitempty"`
	NeedAck           bool              `protobuf:"varint,9,opt,name=needAck,proto3" json:"needAck,omitempty"`
	PushServer        string            `protobuf:"bytes,10,opt,name=pushServer,proto3" json:"pushServer,omitempty"`
	LiveCursor        string            `protobuf:"bytes,11,opt,name=liveCursor,proto3" json:"liveCursor,omitempty"`
	HistoryNoMore     bool              `protobuf:"varint,12,opt,name=historyNoMore,proto3" json:"historyNoMore,omitempty"`
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{0}
}

func (x *Response) GetMessagesList() []*Message {
	if x != nil {
		return x.MessagesList
	}
	return nil
}

func (x *Response) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *Response) GetFetchInterval() uint64 {
	if x != nil {
		return x.FetchInterval
	}
	return 0
}

func (x *Response) GetNow() uint64 {
	if x != nil {
		return x.Now
	}
	return 0
}

func (x *Response) GetInternalExt() string {
	if x != nil {
		return x.InternalExt
	}
	return ""
}

func (x *Response) GetFetchType() uint32 {
	if x != nil {
		return x.FetchType
	}
	return 0
}

func (x *Response) GetRouteParams() map[string]string {
	if x != nil {
		return x.RouteParams
	}
	return nil
}

func (x *Response) GetHeartbeatDuration() uint64 {
	if x != nil {
		return x.HeartbeatDuration
	}
	return 0
}

func (x *Response) GetNeedAck() bool {
	if x != nil {
		return x.NeedAck
	}
	return false
}

func (x *Response) GetPushServer() string {
	if x != nil {
		return x.PushServer
	}
	return ""
}

func (x *Response) GetLiveCursor() string {
	if x != nil {
		return x.LiveCursor
	}
	return ""
}

func (x *Response) GetHistoryNoMore() bool {
	if x != nil {
		return x.HistoryNoMore
	}
	return false
}

type Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method        string `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	Payload       []byte `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	MsgId         int64  `protobuf:"varint,3,opt,name=msgId,proto3" json:"msgId,omitempty"`
	MsgType       int32  `protobuf:"varint,4,opt,name=msgType,proto3" json:"msgType,omitempty"`
	Offset        int64  `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`
	NeedWrdsStore bool   `protobuf:"varint,6,opt,name=needWrdsStore,proto3" json:"needWrdsStore,omitempty"`
	WrdsVersion   int64  `protobuf:"varint,7,opt,name=wrdsVersion,proto3" json:"wrdsVersion,omitempty"`
	WrdsSubKey    string `protobuf:"bytes,8,opt,name=wrdsSubKey,proto3" json:"wrdsSubKey,omitempty"`
}

func (x *Message) Reset() {
	*x = Message{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{1}
}

func (x *Message) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *Message) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *Message) GetMsgId() int64 {
	if x != nil {
		return x.MsgId
	}
	return 0
}

func (x *Message) GetMsgType() int32 {
	if x != nil {
		return x.MsgType
	}
	return 0
}

func (x *Message) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *Message) GetNeedWrdsStore() bool {
	if x != nil {
		return x.NeedWrdsStore
	}
	return false
}

func (x *Message) GetWrdsVersion() int64 {
	if x != nil {
		return x.WrdsVersion
	}
	return 0
}

func (x *Message) GetWrdsSubKey() string {
	if x != nil {
		return x.WrdsSubKey
	}
	return ""
}

// 聊天
type ChatMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common               *Common              `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	User                 *User                `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Content              string               `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	VisibleToSender      bool                 `protobuf:"varint,4,opt,name=visibleToSender,proto3" json:"visibleToSender,omitempty"`
	BackgroundImage      *Image               `protobuf:"bytes,5,opt,name=backgroundImage,proto3" json:"backgroundImage,omitempty"`
	FullScreenTextColor  string               `protobuf:"bytes,6,opt,name=fullScreenTextColor,proto3" json:"fullScreenTextColor,omitempty"`
	BackgroundImageV2    *Image               `protobuf:"bytes,7,opt,name=backgroundImageV2,proto3" json:"backgroundImageV2,omitempty"`
	PublicAreaCommon     *PublicAreaCommon    `protobuf:"bytes,8,opt,name=publicAreaCommon,proto3" json:"publicAreaCommon,omitempty"`
	GiftImage            *Image               `protobuf:"bytes,9,opt,name=giftImage,proto3" json:"giftImage,omitempty"`
	AgreeMsgId           uint64               `protobuf:"varint,11,opt,name=agreeMsgId,proto3" json:"agreeMsgId,omitempty"`
	PriorityLevel        uint32               `protobuf:"varint,12,opt,name=priorityLevel,proto3" json:"priorityLevel,omitempty"`
	LandscapeAreaCommon  *LandscapeAreaCommon `protobuf:"bytes,13,opt,name=landscapeAreaCommon,proto3" json:"landscapeAreaCommon,omitempty"`
	EventTime            uint64               `protobuf:"varint,15,opt,name=eventTime,proto3" json:"eventTime,omitempty"`
	SendReview           bool                 `protobuf:"varint,16,opt,name=sendReview,proto3" json:"sendReview,omitempty"`
	FromIntercom         bool                 `protobuf:"varint,17,opt,name=fromIntercom,proto3" json:"fromIntercom,omitempty"`
	IntercomHideUserCard bool                 `protobuf:"varint,18,opt,name=intercomHideUserCard,proto3" json:"intercomHideUserCard,omitempty"`
	// repeated chatTagsList = 19;
	ChatBy                 string `protobuf:"bytes,20,opt,name=chatBy,proto3" json:"chatBy,omitempty"`
	IndividualChatPriority uint32 `protobuf:"varint,21,opt,name=individualChatPriority,proto3" json:"individualChatPriority,omitempty"`
	RtfContent             *Text  `protobuf:"bytes,22,opt,name=rtfContent,proto3" json:"rtfContent,omitempty"`
}

func (x *ChatMessage) Reset() {
	*x = ChatMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessage) ProtoMessage() {}

func (x *ChatMessage) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessage.ProtoReflect.Descriptor instead.
func (*ChatMessage) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{2}
}

func (x *ChatMessage) GetCommon() *Common {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *ChatMessage) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ChatMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ChatMessage) GetVisibleToSender() bool {
	if x != nil {
		return x.VisibleToSender
	}
	return false
}

func (x *ChatMessage) GetBackgroundImage() *Image {
	if x != nil {
		return x.BackgroundImage
	}
	return nil
}

func (x *ChatMessage) GetFullScreenTextColor() string {
	if x != nil {
		return x.FullScreenTextColor
	}
	return ""
}

func (x *ChatMessage) GetBackgroundImageV2() *Image {
	if x != nil {
		return x.BackgroundImageV2
	}
	return nil
}

func (x *ChatMessage) GetPublicAreaCommon() *PublicAreaCommon {
	if x != nil {
		return x.PublicAreaCommon
	}
	return nil
}

func (x *ChatMessage) GetGiftImage() *Image {
	if x != nil {
		return x.GiftImage
	}
	return nil
}

func (x *ChatMessage) GetAgreeMsgId() uint64 {
	if x != nil {
		return x.AgreeMsgId
	}
	return 0
}

func (x *ChatMessage) GetPriorityLevel() uint32 {
	if x != nil {
		return x.PriorityLevel
	}
	return 0
}

func (x *ChatMessage) GetLandscapeAreaCommon() *LandscapeAreaCommon {
	if x != nil {
		return x.LandscapeAreaCommon
	}
	return nil
}

func (x *ChatMessage) GetEventTime() uint64 {
	if x != nil {
		return x.EventTime
	}
	return 0
}

func (x *ChatMessage) GetSendReview() bool {
	if x != nil {
		return x.SendReview
	}
	return false
}

func (x *ChatMessage) GetFromIntercom() bool {
	if x != nil {
		return x.FromIntercom
	}
	return false
}

func (x *ChatMessage) GetIntercomHideUserCard() bool {
	if x != nil {
		return x.IntercomHideUserCard
	}
	return false
}

func (x *ChatMessage) GetChatBy() string {
	if x != nil {
		return x.ChatBy
	}
	return ""
}

func (x *ChatMessage) GetIndividualChatPriority() uint32 {
	if x != nil {
		return x.IndividualChatPriority
	}
	return 0
}

func (x *ChatMessage) GetRtfContent() *Text {
	if x != nil {
		return x.RtfContent
	}
	return nil
}

type LandscapeAreaCommon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShowHead            bool             `protobuf:"varint,1,opt,name=showHead,proto3" json:"showHead,omitempty"`
	ShowNickname        bool             `protobuf:"varint,2,opt,name=showNickname,proto3" json:"showNickname,omitempty"`
	ShowFontColor       bool             `protobuf:"varint,3,opt,name=showFontColor,proto3" json:"showFontColor,omitempty"`
	ColorValueList      []string         `protobuf:"bytes,4,rep,name=colorValueList,proto3" json:"colorValueList,omitempty"`
	CommentTypeTagsList []CommentTypeTag `protobuf:"varint,5,rep,packed,name=commentTypeTagsList,proto3,enum=douyin.CommentTypeTag" json:"commentTypeTagsList,omitempty"`
}

func (x *LandscapeAreaCommon) Reset() {
	*x = LandscapeAreaCommon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LandscapeAreaCommon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LandscapeAreaCommon) ProtoMessage() {}

func (x *LandscapeAreaCommon) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LandscapeAreaCommon.ProtoReflect.Descriptor instead.
func (*LandscapeAreaCommon) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{3}
}

func (x *LandscapeAreaCommon) GetShowHead() bool {
	if x != nil {
		return x.ShowHead
	}
	return false
}

func (x *LandscapeAreaCommon) GetShowNickname() bool {
	if x != nil {
		return x.ShowNickname
	}
	return false
}

func (x *LandscapeAreaCommon) GetShowFontColor() bool {
	if x != nil {
		return x.ShowFontColor
	}
	return false
}

func (x *LandscapeAreaCommon) GetColorValueList() []string {
	if x != nil {
		return x.ColorValueList
	}
	return nil
}

func (x *LandscapeAreaCommon) GetCommentTypeTagsList() []CommentTypeTag {
	if x != nil {
		return x.CommentTypeTagsList
	}
	return nil
}

type RoomUserSeqMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common                  *Common                          `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	RanksList               []*RoomUserSeqMessageContributor `protobuf:"bytes,2,rep,name=ranksList,proto3" json:"ranksList,omitempty"`
	Total                   int64                            `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	PopStr                  string                           `protobuf:"bytes,4,opt,name=popStr,proto3" json:"popStr,omitempty"`
	SeatsList               []*RoomUserSeqMessageContributor `protobuf:"bytes,5,rep,name=seatsList,proto3" json:"seatsList,omitempty"`
	Popularity              int64                            `protobuf:"varint,6,opt,name=popularity,proto3" json:"popularity,omitempty"`
	TotalUser               int64                            `protobuf:"varint,7,opt,name=totalUser,proto3" json:"totalUser,omitempty"`
	TotalUserStr            string                           `protobuf:"bytes,8,opt,name=totalUserStr,proto3" json:"totalUserStr,omitempty"`
	TotalStr                string                           `protobuf:"bytes,9,opt,name=totalStr,proto3" json:"totalStr,omitempty"`
	OnlineUserForAnchor     string                           `protobuf:"bytes,10,opt,name=onlineUserForAnchor,proto3" json:"onlineUserForAnchor,omitempty"`
	TotalPvForAnchor        string                           `protobuf:"bytes,11,opt,name=totalPvForAnchor,proto3" json:"totalPvForAnchor,omitempty"`
	UpRightStatsStr         string                           `protobuf:"bytes,12,opt,name=upRightStatsStr,proto3" json:"upRightStatsStr,omitempty"`
	UpRightStatsStrComplete string                           `protobuf:"bytes,13,opt,name=upRightStatsStrComplete,proto3" json:"upRightStatsStrComplete,omitempty"`
}

func (x *RoomUserSeqMessage) Reset() {
	*x = RoomUserSeqMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomUserSeqMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomUserSeqMessage) ProtoMessage() {}

func (x *RoomUserSeqMessage) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomUserSeqMessage.ProtoReflect.Descriptor instead.
func (*RoomUserSeqMessage) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{4}
}

func (x *RoomUserSeqMessage) GetCommon() *Common {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *RoomUserSeqMessage) GetRanksList() []*RoomUserSeqMessageContributor {
	if x != nil {
		return x.RanksList
	}
	return nil
}

func (x *RoomUserSeqMessage) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *RoomUserSeqMessage) GetPopStr() string {
	if x != nil {
		return x.PopStr
	}
	return ""
}

func (x *RoomUserSeqMessage) GetSeatsList() []*RoomUserSeqMessageContributor {
	if x != nil {
		return x.SeatsList
	}
	return nil
}

func (x *RoomUserSeqMessage) GetPopularity() int64 {
	if x != nil {
		return x.Popularity
	}
	return 0
}

func (x *RoomUserSeqMessage) GetTotalUser() int64 {
	if x != nil {
		return x.TotalUser
	}
	return 0
}

func (x *RoomUserSeqMessage) GetTotalUserStr() string {
	if x != nil {
		return x.TotalUserStr
	}
	return ""
}

func (x *RoomUserSeqMessage) GetTotalStr() string {
	if x != nil {
		return x.TotalStr
	}
	return ""
}

func (x *RoomUserSeqMessage) GetOnlineUserForAnchor() string {
	if x != nil {
		return x.OnlineUserForAnchor
	}
	return ""
}

func (x *RoomUserSeqMessage) GetTotalPvForAnchor() string {
	if x != nil {
		return x.TotalPvForAnchor
	}
	return ""
}

func (x *RoomUserSeqMessage) GetUpRightStatsStr() string {
	if x != nil {
		return x.UpRightStatsStr
	}
	return ""
}

func (x *RoomUserSeqMessage) GetUpRightStatsStrComplete() string {
	if x != nil {
		return x.UpRightStatsStrComplete
	}
	return ""
}

type CommonTextMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common *Common `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	User   *User   `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Scene  string  `protobuf:"bytes,3,opt,name=scene,proto3" json:"scene,omitempty"`
}

func (x *CommonTextMessage) Reset() {
	*x = CommonTextMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonTextMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonTextMessage) ProtoMessage() {}

func (x *CommonTextMessage) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonTextMessage.ProtoReflect.Descriptor instead.
func (*CommonTextMessage) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{5}
}

func (x *CommonTextMessage) GetCommon() *Common {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *CommonTextMessage) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *CommonTextMessage) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

type UpdateFanTicketMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common                 *Common `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	RoomFanTicketCountText string  `protobuf:"bytes,2,opt,name=roomFanTicketCountText,proto3" json:"roomFanTicketCountText,omitempty"`
	RoomFanTicketCount     uint64  `protobuf:"varint,3,opt,name=roomFanTicketCount,proto3" json:"roomFanTicketCount,omitempty"`
	ForceUpdate            bool    `protobuf:"varint,4,opt,name=forceUpdate,proto3" json:"forceUpdate,omitempty"`
}

func (x *UpdateFanTicketMessage) Reset() {
	*x = UpdateFanTicketMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateFanTicketMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFanTicketMessage) ProtoMessage() {}

func (x *UpdateFanTicketMessage) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFanTicketMessage.ProtoReflect.Descriptor instead.
func (*UpdateFanTicketMessage) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateFanTicketMessage) GetCommon() *Common {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *UpdateFanTicketMessage) GetRoomFanTicketCountText() string {
	if x != nil {
		return x.RoomFanTicketCountText
	}
	return ""
}

func (x *UpdateFanTicketMessage) GetRoomFanTicketCount() uint64 {
	if x != nil {
		return x.RoomFanTicketCount
	}
	return 0
}

func (x *UpdateFanTicketMessage) GetForceUpdate() bool {
	if x != nil {
		return x.ForceUpdate
	}
	return false
}

type RoomUserSeqMessageContributor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score            uint64 `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`
	User             *User  `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Rank             uint64 `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`
	Delta            uint64 `protobuf:"varint,4,opt,name=delta,proto3" json:"delta,omitempty"`
	IsHidden         bool   `protobuf:"varint,5,opt,name=isHidden,proto3" json:"isHidden,omitempty"`
	ScoreDescription string `protobuf:"bytes,6,opt,name=scoreDescription,proto3" json:"scoreDescription,omitempty"`
	ExactlyScore     string `protobuf:"bytes,7,opt,name=exactlyScore,proto3" json:"exactlyScore,omitempty"`
}

func (x *RoomUserSeqMessageContributor) Reset() {
	*x = RoomUserSeqMessageContributor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomUserSeqMessageContributor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomUserSeqMessageContributor) ProtoMessage() {}

func (x *RoomUserSeqMessageContributor) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomUserSeqMessageContributor.ProtoReflect.Descriptor instead.
func (*RoomUserSeqMessageContributor) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{7}
}

func (x *RoomUserSeqMessageContributor) GetScore() uint64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *RoomUserSeqMessageContributor) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *RoomUserSeqMessageContributor) GetRank() uint64 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *RoomUserSeqMessageContributor) GetDelta() uint64 {
	if x != nil {
		return x.Delta
	}
	return 0
}

func (x *RoomUserSeqMessageContributor) GetIsHidden() bool {
	if x != nil {
		return x.IsHidden
	}
	return false
}

func (x *RoomUserSeqMessageContributor) GetScoreDescription() string {
	if x != nil {
		return x.ScoreDescription
	}
	return ""
}

func (x *RoomUserSeqMessageContributor) GetExactlyScore() string {
	if x != nil {
		return x.ExactlyScore
	}
	return ""
}

// 礼物消息
type GiftMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common               *Common           `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	GiftId               uint64            `protobuf:"varint,2,opt,name=giftId,proto3" json:"giftId,omitempty"`
	FanTicketCount       uint64            `protobuf:"varint,3,opt,name=fanTicketCount,proto3" json:"fanTicketCount,omitempty"`
	GroupCount           uint64            `protobuf:"varint,4,opt,name=groupCount,proto3" json:"groupCount,omitempty"`
	RepeatCount          uint64            `protobuf:"varint,5,opt,name=repeatCount,proto3" json:"repeatCount,omitempty"`
	ComboCount           uint64            `protobuf:"varint,6,opt,name=comboCount,proto3" json:"comboCount,omitempty"`
	User                 *User             `protobuf:"bytes,7,opt,name=user,proto3" json:"user,omitempty"`
	ToUser               *User             `protobuf:"bytes,8,opt,name=toUser,proto3" json:"toUser,omitempty"`
	RepeatEnd            uint32            `protobuf:"varint,9,opt,name=repeatEnd,proto3" json:"repeatEnd,omitempty"`
	TextEffect           *TextEffect       `protobuf:"bytes,10,opt,name=textEffect,proto3" json:"textEffect,omitempty"`
	GroupId              uint64            `protobuf:"varint,11,opt,name=groupId,proto3" json:"groupId,omitempty"`
	IncomeTaskgifts      uint64            `protobuf:"varint,12,opt,name=incomeTaskgifts,proto3" json:"incomeTaskgifts,omitempty"`
	RoomFanTicketCount   uint64            `protobuf:"varint,13,opt,name=roomFanTicketCount,proto3" json:"roomFanTicketCount,omitempty"`
	Priority             *GiftIMPriority   `protobuf:"bytes,14,opt,name=priority,proto3" json:"priority,omitempty"`
	Gift                 *GiftStruct       `protobuf:"bytes,15,opt,name=gift,proto3" json:"gift,omitempty"`
	LogId                string            `protobuf:"bytes,16,opt,name=logId,proto3" json:"logId,omitempty"`
	SendType             uint64            `protobuf:"varint,17,opt,name=sendType,proto3" json:"sendType,omitempty"`
	PublicAreaCommon     *PublicAreaCommon `protobuf:"bytes,18,opt,name=publicAreaCommon,proto3" json:"publicAreaCommon,omitempty"`
	TrayDisplayText      *Text             `protobuf:"bytes,19,opt,name=trayDisplayText,proto3" json:"trayDisplayText,omitempty"`
	BannedDisplayEffects uint64            `protobuf:"varint,20,opt,name=bannedDisplayEffects,proto3" json:"bannedDisplayEffects,omitempty"`
	// GiftTrayInfo trayInfo = 21;
	// AssetEffectMixInfo assetEffectMixInfo = 22;
	DisplayForSelf   bool     `protobuf:"varint,25,opt,name=displayForSelf,proto3" json:"displayForSelf,omitempty"`
	InteractGiftInfo string   `protobuf:"bytes,26,opt,name=interactGiftInfo,proto3" json:"interactGiftInfo,omitempty"`
	DiyItemInfo      string   `protobuf:"bytes,27,opt,name=diyItemInfo,proto3" json:"diyItemInfo,omitempty"`
	MinAssetSetList  []uint64 `protobuf:"varint,28,rep,packed,name=minAssetSetList,proto3" json:"minAssetSetList,omitempty"`
	TotalCount       uint64   `protobuf:"varint,29,opt,name=totalCount,proto3" json:"totalCount,omitempty"`
	ClientGiftSource uint32   `protobuf:"varint,30,opt,name=clientGiftSource,proto3" json:"clientGiftSource,omitempty"`
	// AnchorGiftData anchorGift = 31;
	ToUserIdsList       []uint64 `protobuf:"varint,32,rep,packed,name=toUserIdsList,proto3" json:"toUserIdsList,omitempty"`
	SendTime            uint64   `protobuf:"varint,33,opt,name=sendTime,proto3" json:"sendTime,omitempty"`
	ForceDisplayEffects uint64   `protobuf:"varint,34,opt,name=forceDisplayEffects,proto3" json:"forceDisplayEffects,omitempty"`
	TraceId             string   `protobuf:"bytes,35,opt,name=traceId,proto3" json:"traceId,omitempty"`
	EffectDisplayTs     uint64   `protobuf:"varint,36,opt,name=effectDisplayTs,proto3" json:"effectDisplayTs,omitempty"`
}

func (x *GiftMessage) Reset() {
	*x = GiftMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftMessage) ProtoMessage() {}

func (x *GiftMessage) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftMessage.ProtoReflect.Descriptor instead.
func (*GiftMessage) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{8}
}

func (x *GiftMessage) GetCommon() *Common {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *GiftMessage) GetGiftId() uint64 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *GiftMessage) GetFanTicketCount() uint64 {
	if x != nil {
		return x.FanTicketCount
	}
	return 0
}

func (x *GiftMessage) GetGroupCount() uint64 {
	if x != nil {
		return x.GroupCount
	}
	return 0
}

func (x *GiftMessage) GetRepeatCount() uint64 {
	if x != nil {
		return x.RepeatCount
	}
	return 0
}

func (x *GiftMessage) GetComboCount() uint64 {
	if x != nil {
		return x.ComboCount
	}
	return 0
}

func (x *GiftMessage) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *GiftMessage) GetToUser() *User {
	if x != nil {
		return x.ToUser
	}
	return nil
}

func (x *GiftMessage) GetRepeatEnd() uint32 {
	if x != nil {
		return x.RepeatEnd
	}
	return 0
}

func (x *GiftMessage) GetTextEffect() *TextEffect {
	if x != nil {
		return x.TextEffect
	}
	return nil
}

func (x *GiftMessage) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GiftMessage) GetIncomeTaskgifts() uint64 {
	if x != nil {
		return x.IncomeTaskgifts
	}
	return 0
}

func (x *GiftMessage) GetRoomFanTicketCount() uint64 {
	if x != nil {
		return x.RoomFanTicketCount
	}
	return 0
}

func (x *GiftMessage) GetPriority() *GiftIMPriority {
	if x != nil {
		return x.Priority
	}
	return nil
}

func (x *GiftMessage) GetGift() *GiftStruct {
	if x != nil {
		return x.Gift
	}
	return nil
}

func (x *GiftMessage) GetLogId() string {
	if x != nil {
		return x.LogId
	}
	return ""
}

func (x *GiftMessage) GetSendType() uint64 {
	if x != nil {
		return x.SendType
	}
	return 0
}

func (x *GiftMessage) GetPublicAreaCommon() *PublicAreaCommon {
	if x != nil {
		return x.PublicAreaCommon
	}
	return nil
}

func (x *GiftMessage) GetTrayDisplayText() *Text {
	if x != nil {
		return x.TrayDisplayText
	}
	return nil
}

func (x *GiftMessage) GetBannedDisplayEffects() uint64 {
	if x != nil {
		return x.BannedDisplayEffects
	}
	return 0
}

func (x *GiftMessage) GetDisplayForSelf() bool {
	if x != nil {
		return x.DisplayForSelf
	}
	return false
}

func (x *GiftMessage) GetInteractGiftInfo() string {
	if x != nil {
		return x.InteractGiftInfo
	}
	return ""
}

func (x *GiftMessage) GetDiyItemInfo() string {
	if x != nil {
		return x.DiyItemInfo
	}
	return ""
}

func (x *GiftMessage) GetMinAssetSetList() []uint64 {
	if x != nil {
		return x.MinAssetSetList
	}
	return nil
}

func (x *GiftMessage) GetTotalCount() uint64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GiftMessage) GetClientGiftSource() uint32 {
	if x != nil {
		return x.ClientGiftSource
	}
	return 0
}

func (x *GiftMessage) GetToUserIdsList() []uint64 {
	if x != nil {
		return x.ToUserIdsList
	}
	return nil
}

func (x *GiftMessage) GetSendTime() uint64 {
	if x != nil {
		return x.SendTime
	}
	return 0
}

func (x *GiftMessage) GetForceDisplayEffects() uint64 {
	if x != nil {
		return x.ForceDisplayEffects
	}
	return 0
}

func (x *GiftMessage) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *GiftMessage) GetEffectDisplayTs() uint64 {
	if x != nil {
		return x.EffectDisplayTs
	}
	return 0
}

type GiftStruct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image    *Image `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	Describe string `protobuf:"bytes,2,opt,name=describe,proto3" json:"describe,omitempty"`
	Notify   bool   `protobuf:"varint,3,opt,name=notify,proto3" json:"notify,omitempty"`
	Duration uint64 `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	Id       uint64 `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	// GiftStructFansClubInfo fansclubInfo = 6;
	ForLinkmic         bool   `protobuf:"varint,7,opt,name=forLinkmic,proto3" json:"forLinkmic,omitempty"`
	Doodle             bool   `protobuf:"varint,8,opt,name=doodle,proto3" json:"doodle,omitempty"`
	ForFansclub        bool   `protobuf:"varint,9,opt,name=forFansclub,proto3" json:"forFansclub,omitempty"`
	Combo              bool   `protobuf:"varint,10,opt,name=combo,proto3" json:"combo,omitempty"`
	Type               uint32 `protobuf:"varint,11,opt,name=type,proto3" json:"type,omitempty"`
	DiamondCount       uint32 `protobuf:"varint,12,opt,name=diamondCount,proto3" json:"diamondCount,omitempty"`
	IsDisplayedOnPanel bool   `protobuf:"varint,13,opt,name=isDisplayedOnPanel,proto3" json:"isDisplayedOnPanel,omitempty"`
	PrimaryEffectId    uint64 `protobuf:"varint,14,opt,name=primaryEffectId,proto3" json:"primaryEffectId,omitempty"`
	GiftLabelIcon      *Image `protobuf:"bytes,15,opt,name=giftLabelIcon,proto3" json:"giftLabelIcon,omitempty"`
	Name               string `protobuf:"bytes,16,opt,name=name,proto3" json:"name,omitempty"`
	Region             string `protobuf:"bytes,17,opt,name=region,proto3" json:"region,omitempty"`
	Manual             string `protobuf:"bytes,18,opt,name=manual,proto3" json:"manual,omitempty"`
	ForCustom          bool   `protobuf:"varint,19,opt,name=forCustom,proto3" json:"forCustom,omitempty"`
	// specialEffectsMap = 20;
	Icon       *Image `protobuf:"bytes,21,opt,name=icon,proto3" json:"icon,omitempty"`
	ActionType uint32 `protobuf:"varint,22,opt,name=actionType,proto3" json:"actionType,omitempty"` // fixme 后面的就不写了还有几十个属性
}

func (x *GiftStruct) Reset() {
	*x = GiftStruct{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftStruct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftStruct) ProtoMessage() {}

func (x *GiftStruct) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftStruct.ProtoReflect.Descriptor instead.
func (*GiftStruct) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{9}
}

func (x *GiftStruct) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *GiftStruct) GetDescribe() string {
	if x != nil {
		return x.Describe
	}
	return ""
}

func (x *GiftStruct) GetNotify() bool {
	if x != nil {
		return x.Notify
	}
	return false
}

func (x *GiftStruct) GetDuration() uint64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *GiftStruct) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GiftStruct) GetForLinkmic() bool {
	if x != nil {
		return x.ForLinkmic
	}
	return false
}

func (x *GiftStruct) GetDoodle() bool {
	if x != nil {
		return x.Doodle
	}
	return false
}

func (x *GiftStruct) GetForFansclub() bool {
	if x != nil {
		return x.ForFansclub
	}
	return false
}

func (x *GiftStruct) GetCombo() bool {
	if x != nil {
		return x.Combo
	}
	return false
}

func (x *GiftStruct) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *GiftStruct) GetDiamondCount() uint32 {
	if x != nil {
		return x.DiamondCount
	}
	return 0
}

func (x *GiftStruct) GetIsDisplayedOnPanel() bool {
	if x != nil {
		return x.IsDisplayedOnPanel
	}
	return false
}

func (x *GiftStruct) GetPrimaryEffectId() uint64 {
	if x != nil {
		return x.PrimaryEffectId
	}
	return 0
}

func (x *GiftStruct) GetGiftLabelIcon() *Image {
	if x != nil {
		return x.GiftLabelIcon
	}
	return nil
}

func (x *GiftStruct) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GiftStruct) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GiftStruct) GetManual() string {
	if x != nil {
		return x.Manual
	}
	return ""
}

func (x *GiftStruct) GetForCustom() bool {
	if x != nil {
		return x.ForCustom
	}
	return false
}

func (x *GiftStruct) GetIcon() *Image {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *GiftStruct) GetActionType() uint32 {
	if x != nil {
		return x.ActionType
	}
	return 0
}

type GiftIMPriority struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QueueSizesList    []uint64 `protobuf:"varint,1,rep,packed,name=queueSizesList,proto3" json:"queueSizesList,omitempty"`
	SelfQueuePriority uint64   `protobuf:"varint,2,opt,name=selfQueuePriority,proto3" json:"selfQueuePriority,omitempty"`
	Priority          uint64   `protobuf:"varint,3,opt,name=priority,proto3" json:"priority,omitempty"`
}

func (x *GiftIMPriority) Reset() {
	*x = GiftIMPriority{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftIMPriority) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftIMPriority) ProtoMessage() {}

func (x *GiftIMPriority) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftIMPriority.ProtoReflect.Descriptor instead.
func (*GiftIMPriority) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{10}
}

func (x *GiftIMPriority) GetQueueSizesList() []uint64 {
	if x != nil {
		return x.QueueSizesList
	}
	return nil
}

func (x *GiftIMPriority) GetSelfQueuePriority() uint64 {
	if x != nil {
		return x.SelfQueuePriority
	}
	return 0
}

func (x *GiftIMPriority) GetPriority() uint64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

type TextEffect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Portrait  *TextEffectDetail `protobuf:"bytes,1,opt,name=portrait,proto3" json:"portrait,omitempty"`
	Landscape *TextEffectDetail `protobuf:"bytes,2,opt,name=landscape,proto3" json:"landscape,omitempty"`
}

func (x *TextEffect) Reset() {
	*x = TextEffect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextEffect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextEffect) ProtoMessage() {}

func (x *TextEffect) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextEffect.ProtoReflect.Descriptor instead.
func (*TextEffect) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{11}
}

func (x *TextEffect) GetPortrait() *TextEffectDetail {
	if x != nil {
		return x.Portrait
	}
	return nil
}

func (x *TextEffect) GetLandscape() *TextEffectDetail {
	if x != nil {
		return x.Landscape
	}
	return nil
}

type TextEffectDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text         *Text  `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	TextFontSize uint32 `protobuf:"varint,2,opt,name=textFontSize,proto3" json:"textFontSize,omitempty"`
	Background   *Image `protobuf:"bytes,3,opt,name=background,proto3" json:"background,omitempty"`
	Start        uint32 `protobuf:"varint,4,opt,name=start,proto3" json:"start,omitempty"`
	Duration     uint32 `protobuf:"varint,5,opt,name=duration,proto3" json:"duration,omitempty"`
	X            uint32 `protobuf:"varint,6,opt,name=x,proto3" json:"x,omitempty"`
	Y            uint32 `protobuf:"varint,7,opt,name=y,proto3" json:"y,omitempty"`
	Width        uint32 `protobuf:"varint,8,opt,name=width,proto3" json:"width,omitempty"`
	Height       uint32 `protobuf:"varint,9,opt,name=height,proto3" json:"height,omitempty"`
	ShadowDx     uint32 `protobuf:"varint,10,opt,name=shadowDx,proto3" json:"shadowDx,omitempty"`
	ShadowDy     uint32 `protobuf:"varint,11,opt,name=shadowDy,proto3" json:"shadowDy,omitempty"`
	ShadowRadius uint32 `protobuf:"varint,12,opt,name=shadowRadius,proto3" json:"shadowRadius,omitempty"`
	ShadowColor  string `protobuf:"bytes,13,opt,name=shadowColor,proto3" json:"shadowColor,omitempty"`
	StrokeColor  string `protobuf:"bytes,14,opt,name=strokeColor,proto3" json:"strokeColor,omitempty"`
	StrokeWidth  uint32 `protobuf:"varint,15,opt,name=strokeWidth,proto3" json:"strokeWidth,omitempty"`
}

func (x *TextEffectDetail) Reset() {
	*x = TextEffectDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextEffectDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextEffectDetail) ProtoMessage() {}

func (x *TextEffectDetail) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextEffectDetail.ProtoReflect.Descriptor instead.
func (*TextEffectDetail) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{12}
}

func (x *TextEffectDetail) GetText() *Text {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *TextEffectDetail) GetTextFontSize() uint32 {
	if x != nil {
		return x.TextFontSize
	}
	return 0
}

func (x *TextEffectDetail) GetBackground() *Image {
	if x != nil {
		return x.Background
	}
	return nil
}

func (x *TextEffectDetail) GetStart() uint32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *TextEffectDetail) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *TextEffectDetail) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *TextEffectDetail) GetY() uint32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *TextEffectDetail) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *TextEffectDetail) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *TextEffectDetail) GetShadowDx() uint32 {
	if x != nil {
		return x.ShadowDx
	}
	return 0
}

func (x *TextEffectDetail) GetShadowDy() uint32 {
	if x != nil {
		return x.ShadowDy
	}
	return 0
}

func (x *TextEffectDetail) GetShadowRadius() uint32 {
	if x != nil {
		return x.ShadowRadius
	}
	return 0
}

func (x *TextEffectDetail) GetShadowColor() string {
	if x != nil {
		return x.ShadowColor
	}
	return ""
}

func (x *TextEffectDetail) GetStrokeColor() string {
	if x != nil {
		return x.StrokeColor
	}
	return ""
}

func (x *TextEffectDetail) GetStrokeWidth() uint32 {
	if x != nil {
		return x.StrokeWidth
	}
	return 0
}

// 成员消息
type MemberMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common             *Common           `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	User               *User             `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	MemberCount        uint64            `protobuf:"varint,3,opt,name=memberCount,proto3" json:"memberCount,omitempty"`
	Operator           *User             `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	IsSetToAdmin       bool              `protobuf:"varint,5,opt,name=isSetToAdmin,proto3" json:"isSetToAdmin,omitempty"`
	IsTopUser          bool              `protobuf:"varint,6,opt,name=isTopUser,proto3" json:"isTopUser,omitempty"`
	RankScore          uint64            `protobuf:"varint,7,opt,name=rankScore,proto3" json:"rankScore,omitempty"`
	TopUserNo          uint64            `protobuf:"varint,8,opt,name=topUserNo,proto3" json:"topUserNo,omitempty"`
	EnterType          uint64            `protobuf:"varint,9,opt,name=enterType,proto3" json:"enterType,omitempty"`
	Action             uint64            `protobuf:"varint,10,opt,name=action,proto3" json:"action,omitempty"`
	ActionDescription  string            `protobuf:"bytes,11,opt,name=actionDescription,proto3" json:"actionDescription,omitempty"`
	UserId             uint64            `protobuf:"varint,12,opt,name=userId,proto3" json:"userId,omitempty"`
	EffectConfig       *EffectConfig     `protobuf:"bytes,13,opt,name=effectConfig,proto3" json:"effectConfig,omitempty"`
	PopStr             string            `protobuf:"bytes,14,opt,name=popStr,proto3" json:"popStr,omitempty"`
	EnterEffectConfig  *EffectConfig     `protobuf:"bytes,15,opt,name=enterEffectConfig,proto3" json:"enterEffectConfig,omitempty"`
	BackgroundImage    *Image            `protobuf:"bytes,16,opt,name=backgroundImage,proto3" json:"backgroundImage,omitempty"`
	BackgroundImageV2  *Image            `protobuf:"bytes,17,opt,name=backgroundImageV2,proto3" json:"backgroundImageV2,omitempty"`
	AnchorDisplayText  *Text             `protobuf:"bytes,18,opt,name=anchorDisplayText,proto3" json:"anchorDisplayText,omitempty"`
	PublicAreaCommon   *PublicAreaCommon `protobuf:"bytes,19,opt,name=publicAreaCommon,proto3" json:"publicAreaCommon,omitempty"`
	UserEnterTipType   uint64            `protobuf:"varint,20,opt,name=userEnterTipType,proto3" json:"userEnterTipType,omitempty"`
	AnchorEnterTipType uint64            `protobuf:"varint,21,opt,name=anchorEnterTipType,proto3" json:"anchorEnterTipType,omitempty"`
}

func (x *MemberMessage) Reset() {
	*x = MemberMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemberMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberMessage) ProtoMessage() {}

func (x *MemberMessage) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberMessage.ProtoReflect.Descriptor instead.
func (*MemberMessage) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{13}
}

func (x *MemberMessage) GetCommon() *Common {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *MemberMessage) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *MemberMessage) GetMemberCount() uint64 {
	if x != nil {
		return x.MemberCount
	}
	return 0
}

func (x *MemberMessage) GetOperator() *User {
	if x != nil {
		return x.Operator
	}
	return nil
}

func (x *MemberMessage) GetIsSetToAdmin() bool {
	if x != nil {
		return x.IsSetToAdmin
	}
	return false
}

func (x *MemberMessage) GetIsTopUser() bool {
	if x != nil {
		return x.IsTopUser
	}
	return false
}

func (x *MemberMessage) GetRankScore() uint64 {
	if x != nil {
		return x.RankScore
	}
	return 0
}

func (x *MemberMessage) GetTopUserNo() uint64 {
	if x != nil {
		return x.TopUserNo
	}
	return 0
}

func (x *MemberMessage) GetEnterType() uint64 {
	if x != nil {
		return x.EnterType
	}
	return 0
}

func (x *MemberMessage) GetAction() uint64 {
	if x != nil {
		return x.Action
	}
	return 0
}

func (x *MemberMessage) GetActionDescription() string {
	if x != nil {
		return x.ActionDescription
	}
	return ""
}

func (x *MemberMessage) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *MemberMessage) GetEffectConfig() *EffectConfig {
	if x != nil {
		return x.EffectConfig
	}
	return nil
}

func (x *MemberMessage) GetPopStr() string {
	if x != nil {
		return x.PopStr
	}
	return ""
}

func (x *MemberMessage) GetEnterEffectConfig() *EffectConfig {
	if x != nil {
		return x.EnterEffectConfig
	}
	return nil
}

func (x *MemberMessage) GetBackgroundImage() *Image {
	if x != nil {
		return x.BackgroundImage
	}
	return nil
}

func (x *MemberMessage) GetBackgroundImageV2() *Image {
	if x != nil {
		return x.BackgroundImageV2
	}
	return nil
}

func (x *MemberMessage) GetAnchorDisplayText() *Text {
	if x != nil {
		return x.AnchorDisplayText
	}
	return nil
}

func (x *MemberMessage) GetPublicAreaCommon() *PublicAreaCommon {
	if x != nil {
		return x.PublicAreaCommon
	}
	return nil
}

func (x *MemberMessage) GetUserEnterTipType() uint64 {
	if x != nil {
		return x.UserEnterTipType
	}
	return 0
}

func (x *MemberMessage) GetAnchorEnterTipType() uint64 {
	if x != nil {
		return x.AnchorEnterTipType
	}
	return 0
}

type PublicAreaCommon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserLabel             *Image `protobuf:"bytes,1,opt,name=userLabel,proto3" json:"userLabel,omitempty"`
	UserConsumeInRoom     uint64 `protobuf:"varint,2,opt,name=userConsumeInRoom,proto3" json:"userConsumeInRoom,omitempty"`
	UserSendGiftCntInRoom uint64 `protobuf:"varint,3,opt,name=userSendGiftCntInRoom,proto3" json:"userSendGiftCntInRoom,omitempty"`
}

func (x *PublicAreaCommon) Reset() {
	*x = PublicAreaCommon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublicAreaCommon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicAreaCommon) ProtoMessage() {}

func (x *PublicAreaCommon) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicAreaCommon.ProtoReflect.Descriptor instead.
func (*PublicAreaCommon) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{14}
}

func (x *PublicAreaCommon) GetUserLabel() *Image {
	if x != nil {
		return x.UserLabel
	}
	return nil
}

func (x *PublicAreaCommon) GetUserConsumeInRoom() uint64 {
	if x != nil {
		return x.UserConsumeInRoom
	}
	return 0
}

func (x *PublicAreaCommon) GetUserSendGiftCntInRoom() uint64 {
	if x != nil {
		return x.UserSendGiftCntInRoom
	}
	return 0
}

type EffectConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                     uint64            `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Icon                     *Image            `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	AvatarPos                uint64            `protobuf:"varint,3,opt,name=avatarPos,proto3" json:"avatarPos,omitempty"`
	Text                     *Text             `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	TextIcon                 *Image            `protobuf:"bytes,5,opt,name=textIcon,proto3" json:"textIcon,omitempty"`
	StayTime                 uint32            `protobuf:"varint,6,opt,name=stayTime,proto3" json:"stayTime,omitempty"`
	AnimAssetId              uint64            `protobuf:"varint,7,opt,name=animAssetId,proto3" json:"animAssetId,omitempty"`
	Badge                    *Image            `protobuf:"bytes,8,opt,name=badge,proto3" json:"badge,omitempty"`
	FlexSettingArrayList     []uint64          `protobuf:"varint,9,rep,packed,name=flexSettingArrayList,proto3" json:"flexSettingArrayList,omitempty"`
	TextIconOverlay          *Image            `protobuf:"bytes,10,opt,name=textIconOverlay,proto3" json:"textIconOverlay,omitempty"`
	AnimatedBadge            *Image            `protobuf:"bytes,11,opt,name=animatedBadge,proto3" json:"animatedBadge,omitempty"`
	HasSweepLight            bool              `protobuf:"varint,12,opt,name=hasSweepLight,proto3" json:"hasSweepLight,omitempty"`
	TextFlexSettingArrayList []uint64          `protobuf:"varint,13,rep,packed,name=textFlexSettingArrayList,proto3" json:"textFlexSettingArrayList,omitempty"`
	CenterAnimAssetId        uint64            `protobuf:"varint,14,opt,name=centerAnimAssetId,proto3" json:"centerAnimAssetId,omitempty"`
	DynamicImage             *Image            `protobuf:"bytes,15,opt,name=dynamicImage,proto3" json:"dynamicImage,omitempty"`
	ExtraMap                 map[string]string `protobuf:"bytes,16,rep,name=extraMap,proto3" json:"extraMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Mp4AnimAssetId           uint64            `protobuf:"varint,17,opt,name=mp4AnimAssetId,proto3" json:"mp4AnimAssetId,omitempty"`
	Priority                 uint64            `protobuf:"varint,18,opt,name=priority,proto3" json:"priority,omitempty"`
	MaxWaitTime              uint64            `protobuf:"varint,19,opt,name=maxWaitTime,proto3" json:"maxWaitTime,omitempty"`
	DressId                  string            `protobuf:"bytes,20,opt,name=dressId,proto3" json:"dressId,omitempty"`
	Alignment                uint64            `protobuf:"varint,21,opt,name=alignment,proto3" json:"alignment,omitempty"`
	AlignmentOffset          uint64            `protobuf:"varint,22,opt,name=alignmentOffset,proto3" json:"alignmentOffset,omitempty"`
}

func (x *EffectConfig) Reset() {
	*x = EffectConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EffectConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EffectConfig) ProtoMessage() {}

func (x *EffectConfig) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EffectConfig.ProtoReflect.Descriptor instead.
func (*EffectConfig) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{15}
}

func (x *EffectConfig) GetType() uint64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *EffectConfig) GetIcon() *Image {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *EffectConfig) GetAvatarPos() uint64 {
	if x != nil {
		return x.AvatarPos
	}
	return 0
}

func (x *EffectConfig) GetText() *Text {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *EffectConfig) GetTextIcon() *Image {
	if x != nil {
		return x.TextIcon
	}
	return nil
}

func (x *EffectConfig) GetStayTime() uint32 {
	if x != nil {
		return x.StayTime
	}
	return 0
}

func (x *EffectConfig) GetAnimAssetId() uint64 {
	if x != nil {
		return x.AnimAssetId
	}
	return 0
}

func (x *EffectConfig) GetBadge() *Image {
	if x != nil {
		return x.Badge
	}
	return nil
}

func (x *EffectConfig) GetFlexSettingArrayList() []uint64 {
	if x != nil {
		return x.FlexSettingArrayList
	}
	return nil
}

func (x *EffectConfig) GetTextIconOverlay() *Image {
	if x != nil {
		return x.TextIconOverlay
	}
	return nil
}

func (x *EffectConfig) GetAnimatedBadge() *Image {
	if x != nil {
		return x.AnimatedBadge
	}
	return nil
}

func (x *EffectConfig) GetHasSweepLight() bool {
	if x != nil {
		return x.HasSweepLight
	}
	return false
}

func (x *EffectConfig) GetTextFlexSettingArrayList() []uint64 {
	if x != nil {
		return x.TextFlexSettingArrayList
	}
	return nil
}

func (x *EffectConfig) GetCenterAnimAssetId() uint64 {
	if x != nil {
		return x.CenterAnimAssetId
	}
	return 0
}

func (x *EffectConfig) GetDynamicImage() *Image {
	if x != nil {
		return x.DynamicImage
	}
	return nil
}

func (x *EffectConfig) GetExtraMap() map[string]string {
	if x != nil {
		return x.ExtraMap
	}
	return nil
}

func (x *EffectConfig) GetMp4AnimAssetId() uint64 {
	if x != nil {
		return x.Mp4AnimAssetId
	}
	return 0
}

func (x *EffectConfig) GetPriority() uint64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *EffectConfig) GetMaxWaitTime() uint64 {
	if x != nil {
		return x.MaxWaitTime
	}
	return 0
}

func (x *EffectConfig) GetDressId() string {
	if x != nil {
		return x.DressId
	}
	return ""
}

func (x *EffectConfig) GetAlignment() uint64 {
	if x != nil {
		return x.Alignment
	}
	return 0
}

func (x *EffectConfig) GetAlignmentOffset() uint64 {
	if x != nil {
		return x.AlignmentOffset
	}
	return 0
}

type Text struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key           string       `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	DefaultPatter string       `protobuf:"bytes,2,opt,name=defaultPatter,proto3" json:"defaultPatter,omitempty"`
	DefaultFormat *TextFormat  `protobuf:"bytes,3,opt,name=defaultFormat,proto3" json:"defaultFormat,omitempty"`
	PiecesList    []*TextPiece `protobuf:"bytes,4,rep,name=piecesList,proto3" json:"piecesList,omitempty"`
}

func (x *Text) Reset() {
	*x = Text{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Text) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Text) ProtoMessage() {}

func (x *Text) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Text.ProtoReflect.Descriptor instead.
func (*Text) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{16}
}

func (x *Text) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Text) GetDefaultPatter() string {
	if x != nil {
		return x.DefaultPatter
	}
	return ""
}

func (x *Text) GetDefaultFormat() *TextFormat {
	if x != nil {
		return x.DefaultFormat
	}
	return nil
}

func (x *Text) GetPiecesList() []*TextPiece {
	if x != nil {
		return x.PiecesList
	}
	return nil
}

type TextPiece struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type            bool                 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Format          *TextFormat          `protobuf:"bytes,2,opt,name=format,proto3" json:"format,omitempty"`
	StringValue     string               `protobuf:"bytes,3,opt,name=stringValue,proto3" json:"stringValue,omitempty"`
	UserValue       *TextPieceUser       `protobuf:"bytes,4,opt,name=userValue,proto3" json:"userValue,omitempty"`
	GiftValue       *TextPieceGift       `protobuf:"bytes,5,opt,name=giftValue,proto3" json:"giftValue,omitempty"`
	HeartValue      *TextPieceHeart      `protobuf:"bytes,6,opt,name=heartValue,proto3" json:"heartValue,omitempty"`
	PatternRefValue *TextPiecePatternRef `protobuf:"bytes,7,opt,name=patternRefValue,proto3" json:"patternRefValue,omitempty"`
	ImageValue      *TextPieceImage      `protobuf:"bytes,8,opt,name=imageValue,proto3" json:"imageValue,omitempty"`
}

func (x *TextPiece) Reset() {
	*x = TextPiece{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextPiece) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextPiece) ProtoMessage() {}

func (x *TextPiece) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextPiece.ProtoReflect.Descriptor instead.
func (*TextPiece) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{17}
}

func (x *TextPiece) GetType() bool {
	if x != nil {
		return x.Type
	}
	return false
}

func (x *TextPiece) GetFormat() *TextFormat {
	if x != nil {
		return x.Format
	}
	return nil
}

func (x *TextPiece) GetStringValue() string {
	if x != nil {
		return x.StringValue
	}
	return ""
}

func (x *TextPiece) GetUserValue() *TextPieceUser {
	if x != nil {
		return x.UserValue
	}
	return nil
}

func (x *TextPiece) GetGiftValue() *TextPieceGift {
	if x != nil {
		return x.GiftValue
	}
	return nil
}

func (x *TextPiece) GetHeartValue() *TextPieceHeart {
	if x != nil {
		return x.HeartValue
	}
	return nil
}

func (x *TextPiece) GetPatternRefValue() *TextPiecePatternRef {
	if x != nil {
		return x.PatternRefValue
	}
	return nil
}

func (x *TextPiece) GetImageValue() *TextPieceImage {
	if x != nil {
		return x.ImageValue
	}
	return nil
}

type TextPieceImage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image       *Image  `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	ScalingRate float32 `protobuf:"fixed32,2,opt,name=scalingRate,proto3" json:"scalingRate,omitempty"`
}

func (x *TextPieceImage) Reset() {
	*x = TextPieceImage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextPieceImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextPieceImage) ProtoMessage() {}

func (x *TextPieceImage) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextPieceImage.ProtoReflect.Descriptor instead.
func (*TextPieceImage) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{18}
}

func (x *TextPieceImage) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *TextPieceImage) GetScalingRate() float32 {
	if x != nil {
		return x.ScalingRate
	}
	return 0
}

type TextPiecePatternRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key            string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	DefaultPattern string `protobuf:"bytes,2,opt,name=defaultPattern,proto3" json:"defaultPattern,omitempty"`
}

func (x *TextPiecePatternRef) Reset() {
	*x = TextPiecePatternRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextPiecePatternRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextPiecePatternRef) ProtoMessage() {}

func (x *TextPiecePatternRef) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextPiecePatternRef.ProtoReflect.Descriptor instead.
func (*TextPiecePatternRef) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{19}
}

func (x *TextPiecePatternRef) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *TextPiecePatternRef) GetDefaultPattern() string {
	if x != nil {
		return x.DefaultPattern
	}
	return ""
}

type TextPieceHeart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Color string `protobuf:"bytes,1,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *TextPieceHeart) Reset() {
	*x = TextPieceHeart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextPieceHeart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextPieceHeart) ProtoMessage() {}

func (x *TextPieceHeart) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextPieceHeart.ProtoReflect.Descriptor instead.
func (*TextPieceHeart) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{20}
}

func (x *TextPieceHeart) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

type TextPieceGift struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftId  uint64      `protobuf:"varint,1,opt,name=giftId,proto3" json:"giftId,omitempty"`
	NameRef *PatternRef `protobuf:"bytes,2,opt,name=nameRef,proto3" json:"nameRef,omitempty"`
}

func (x *TextPieceGift) Reset() {
	*x = TextPieceGift{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextPieceGift) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextPieceGift) ProtoMessage() {}

func (x *TextPieceGift) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextPieceGift.ProtoReflect.Descriptor instead.
func (*TextPieceGift) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{21}
}

func (x *TextPieceGift) GetGiftId() uint64 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *TextPieceGift) GetNameRef() *PatternRef {
	if x != nil {
		return x.NameRef
	}
	return nil
}

type PatternRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key            string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	DefaultPattern string `protobuf:"bytes,2,opt,name=defaultPattern,proto3" json:"defaultPattern,omitempty"`
}

func (x *PatternRef) Reset() {
	*x = PatternRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PatternRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PatternRef) ProtoMessage() {}

func (x *PatternRef) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PatternRef.ProtoReflect.Descriptor instead.
func (*PatternRef) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{22}
}

func (x *PatternRef) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *PatternRef) GetDefaultPattern() string {
	if x != nil {
		return x.DefaultPattern
	}
	return ""
}

type TextPieceUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User      *User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	WithColon bool  `protobuf:"varint,2,opt,name=withColon,proto3" json:"withColon,omitempty"`
}

func (x *TextPieceUser) Reset() {
	*x = TextPieceUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextPieceUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextPieceUser) ProtoMessage() {}

func (x *TextPieceUser) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextPieceUser.ProtoReflect.Descriptor instead.
func (*TextPieceUser) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{23}
}

func (x *TextPieceUser) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *TextPieceUser) GetWithColon() bool {
	if x != nil {
		return x.WithColon
	}
	return false
}

type TextFormat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Color              string `protobuf:"bytes,1,opt,name=color,proto3" json:"color,omitempty"`
	Bold               bool   `protobuf:"varint,2,opt,name=bold,proto3" json:"bold,omitempty"`
	Italic             bool   `protobuf:"varint,3,opt,name=italic,proto3" json:"italic,omitempty"`
	Weight             uint32 `protobuf:"varint,4,opt,name=weight,proto3" json:"weight,omitempty"`
	ItalicAngle        uint32 `protobuf:"varint,5,opt,name=italicAngle,proto3" json:"italicAngle,omitempty"`
	FontSize           uint32 `protobuf:"varint,6,opt,name=fontSize,proto3" json:"fontSize,omitempty"`
	UseHeighLightColor bool   `protobuf:"varint,7,opt,name=useHeighLightColor,proto3" json:"useHeighLightColor,omitempty"`
	UseRemoteClor      bool   `protobuf:"varint,8,opt,name=useRemoteClor,proto3" json:"useRemoteClor,omitempty"`
}

func (x *TextFormat) Reset() {
	*x = TextFormat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextFormat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextFormat) ProtoMessage() {}

func (x *TextFormat) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextFormat.ProtoReflect.Descriptor instead.
func (*TextFormat) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{24}
}

func (x *TextFormat) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *TextFormat) GetBold() bool {
	if x != nil {
		return x.Bold
	}
	return false
}

func (x *TextFormat) GetItalic() bool {
	if x != nil {
		return x.Italic
	}
	return false
}

func (x *TextFormat) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *TextFormat) GetItalicAngle() uint32 {
	if x != nil {
		return x.ItalicAngle
	}
	return 0
}

func (x *TextFormat) GetFontSize() uint32 {
	if x != nil {
		return x.FontSize
	}
	return 0
}

func (x *TextFormat) GetUseHeighLightColor() bool {
	if x != nil {
		return x.UseHeighLightColor
	}
	return false
}

func (x *TextFormat) GetUseRemoteClor() bool {
	if x != nil {
		return x.UseRemoteClor
	}
	return false
}

// 点赞
type LikeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common             *Common             `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	Count              uint64              `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Total              uint64              `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Color              uint64              `protobuf:"varint,4,opt,name=color,proto3" json:"color,omitempty"`
	User               *User               `protobuf:"bytes,5,opt,name=user,proto3" json:"user,omitempty"`
	Icon               string              `protobuf:"bytes,6,opt,name=icon,proto3" json:"icon,omitempty"`
	DoubleLikeDetail   *DoubleLikeDetail   `protobuf:"bytes,7,opt,name=doubleLikeDetail,proto3" json:"doubleLikeDetail,omitempty"`
	DisplayControlInfo *DisplayControlInfo `protobuf:"bytes,8,opt,name=displayControlInfo,proto3" json:"displayControlInfo,omitempty"`
	LinkmicGuestUid    uint64              `protobuf:"varint,9,opt,name=linkmicGuestUid,proto3" json:"linkmicGuestUid,omitempty"`
	Scene              string              `protobuf:"bytes,10,opt,name=scene,proto3" json:"scene,omitempty"`
	PicoDisplayInfo    *PicoDisplayInfo    `protobuf:"bytes,11,opt,name=picoDisplayInfo,proto3" json:"picoDisplayInfo,omitempty"`
}

func (x *LikeMessage) Reset() {
	*x = LikeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LikeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LikeMessage) ProtoMessage() {}

func (x *LikeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LikeMessage.ProtoReflect.Descriptor instead.
func (*LikeMessage) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{25}
}

func (x *LikeMessage) GetCommon() *Common {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *LikeMessage) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *LikeMessage) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *LikeMessage) GetColor() uint64 {
	if x != nil {
		return x.Color
	}
	return 0
}

func (x *LikeMessage) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *LikeMessage) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *LikeMessage) GetDoubleLikeDetail() *DoubleLikeDetail {
	if x != nil {
		return x.DoubleLikeDetail
	}
	return nil
}

func (x *LikeMessage) GetDisplayControlInfo() *DisplayControlInfo {
	if x != nil {
		return x.DisplayControlInfo
	}
	return nil
}

func (x *LikeMessage) GetLinkmicGuestUid() uint64 {
	if x != nil {
		return x.LinkmicGuestUid
	}
	return 0
}

func (x *LikeMessage) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *LikeMessage) GetPicoDisplayInfo() *PicoDisplayInfo {
	if x != nil {
		return x.PicoDisplayInfo
	}
	return nil
}

type SocialMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common           *Common           `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	User             *User             `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	ShareType        uint64            `protobuf:"varint,3,opt,name=shareType,proto3" json:"shareType,omitempty"`
	Action           uint64            `protobuf:"varint,4,opt,name=action,proto3" json:"action,omitempty"`
	ShareTarget      string            `protobuf:"bytes,5,opt,name=shareTarget,proto3" json:"shareTarget,omitempty"`
	FollowCount      uint64            `protobuf:"varint,6,opt,name=followCount,proto3" json:"followCount,omitempty"`
	PublicAreaCommon *PublicAreaCommon `protobuf:"bytes,7,opt,name=publicAreaCommon,proto3" json:"publicAreaCommon,omitempty"`
}

func (x *SocialMessage) Reset() {
	*x = SocialMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SocialMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocialMessage) ProtoMessage() {}

func (x *SocialMessage) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocialMessage.ProtoReflect.Descriptor instead.
func (*SocialMessage) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{26}
}

func (x *SocialMessage) GetCommon() *Common {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *SocialMessage) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *SocialMessage) GetShareType() uint64 {
	if x != nil {
		return x.ShareType
	}
	return 0
}

func (x *SocialMessage) GetAction() uint64 {
	if x != nil {
		return x.Action
	}
	return 0
}

func (x *SocialMessage) GetShareTarget() string {
	if x != nil {
		return x.ShareTarget
	}
	return ""
}

func (x *SocialMessage) GetFollowCount() uint64 {
	if x != nil {
		return x.FollowCount
	}
	return 0
}

func (x *SocialMessage) GetPublicAreaCommon() *PublicAreaCommon {
	if x != nil {
		return x.PublicAreaCommon
	}
	return nil
}

type PicoDisplayInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComboSumCount uint64 `protobuf:"varint,1,opt,name=comboSumCount,proto3" json:"comboSumCount,omitempty"`
	Emoji         string `protobuf:"bytes,2,opt,name=emoji,proto3" json:"emoji,omitempty"`
	EmojiIcon     *Image `protobuf:"bytes,3,opt,name=emojiIcon,proto3" json:"emojiIcon,omitempty"`
	EmojiText     string `protobuf:"bytes,4,opt,name=emojiText,proto3" json:"emojiText,omitempty"`
}

func (x *PicoDisplayInfo) Reset() {
	*x = PicoDisplayInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PicoDisplayInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PicoDisplayInfo) ProtoMessage() {}

func (x *PicoDisplayInfo) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PicoDisplayInfo.ProtoReflect.Descriptor instead.
func (*PicoDisplayInfo) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{27}
}

func (x *PicoDisplayInfo) GetComboSumCount() uint64 {
	if x != nil {
		return x.ComboSumCount
	}
	return 0
}

func (x *PicoDisplayInfo) GetEmoji() string {
	if x != nil {
		return x.Emoji
	}
	return ""
}

func (x *PicoDisplayInfo) GetEmojiIcon() *Image {
	if x != nil {
		return x.EmojiIcon
	}
	return nil
}

func (x *PicoDisplayInfo) GetEmojiText() string {
	if x != nil {
		return x.EmojiText
	}
	return ""
}

type DoubleLikeDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DoubleFlag  bool   `protobuf:"varint,1,opt,name=doubleFlag,proto3" json:"doubleFlag,omitempty"`
	SeqId       uint32 `protobuf:"varint,2,opt,name=seqId,proto3" json:"seqId,omitempty"`
	RenewalsNum uint32 `protobuf:"varint,3,opt,name=renewalsNum,proto3" json:"renewalsNum,omitempty"`
	TriggersNum uint32 `protobuf:"varint,4,opt,name=triggersNum,proto3" json:"triggersNum,omitempty"`
}

func (x *DoubleLikeDetail) Reset() {
	*x = DoubleLikeDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleLikeDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleLikeDetail) ProtoMessage() {}

func (x *DoubleLikeDetail) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleLikeDetail.ProtoReflect.Descriptor instead.
func (*DoubleLikeDetail) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{28}
}

func (x *DoubleLikeDetail) GetDoubleFlag() bool {
	if x != nil {
		return x.DoubleFlag
	}
	return false
}

func (x *DoubleLikeDetail) GetSeqId() uint32 {
	if x != nil {
		return x.SeqId
	}
	return 0
}

func (x *DoubleLikeDetail) GetRenewalsNum() uint32 {
	if x != nil {
		return x.RenewalsNum
	}
	return 0
}

func (x *DoubleLikeDetail) GetTriggersNum() uint32 {
	if x != nil {
		return x.TriggersNum
	}
	return 0
}

type DisplayControlInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShowText  bool `protobuf:"varint,1,opt,name=showText,proto3" json:"showText,omitempty"`
	ShowIcons bool `protobuf:"varint,2,opt,name=showIcons,proto3" json:"showIcons,omitempty"`
}

func (x *DisplayControlInfo) Reset() {
	*x = DisplayControlInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayControlInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayControlInfo) ProtoMessage() {}

func (x *DisplayControlInfo) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayControlInfo.ProtoReflect.Descriptor instead.
func (*DisplayControlInfo) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{29}
}

func (x *DisplayControlInfo) GetShowText() bool {
	if x != nil {
		return x.ShowText
	}
	return false
}

func (x *DisplayControlInfo) GetShowIcons() bool {
	if x != nil {
		return x.ShowIcons
	}
	return false
}

type EpisodeChatMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common         *Message `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	User           *User    `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Content        string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	VisibleToSende bool     `protobuf:"varint,4,opt,name=visibleToSende,proto3" json:"visibleToSende,omitempty"`
	// BackgroundImage backgroundImage = 5;
	// PublicAreaCommon publicAreaCommon = 6;
	GiftImage      *Image   `protobuf:"bytes,7,opt,name=giftImage,proto3" json:"giftImage,omitempty"`
	AgreeMsgId     uint64   `protobuf:"varint,8,opt,name=agreeMsgId,proto3" json:"agreeMsgId,omitempty"`
	ColorValueList []string `protobuf:"bytes,9,rep,name=colorValueList,proto3" json:"colorValueList,omitempty"`
}

func (x *EpisodeChatMessage) Reset() {
	*x = EpisodeChatMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EpisodeChatMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EpisodeChatMessage) ProtoMessage() {}

func (x *EpisodeChatMessage) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EpisodeChatMessage.ProtoReflect.Descriptor instead.
func (*EpisodeChatMessage) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{30}
}

func (x *EpisodeChatMessage) GetCommon() *Message {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *EpisodeChatMessage) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *EpisodeChatMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *EpisodeChatMessage) GetVisibleToSende() bool {
	if x != nil {
		return x.VisibleToSende
	}
	return false
}

func (x *EpisodeChatMessage) GetGiftImage() *Image {
	if x != nil {
		return x.GiftImage
	}
	return nil
}

func (x *EpisodeChatMessage) GetAgreeMsgId() uint64 {
	if x != nil {
		return x.AgreeMsgId
	}
	return 0
}

func (x *EpisodeChatMessage) GetColorValueList() []string {
	if x != nil {
		return x.ColorValueList
	}
	return nil
}

type MatchAgainstScoreMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common        *Common  `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	Against       *Against `protobuf:"bytes,2,opt,name=against,proto3" json:"against,omitempty"`
	MatchStatus   uint32   `protobuf:"varint,3,opt,name=matchStatus,proto3" json:"matchStatus,omitempty"`
	DisplayStatus uint32   `protobuf:"varint,4,opt,name=displayStatus,proto3" json:"displayStatus,omitempty"`
}

func (x *MatchAgainstScoreMessage) Reset() {
	*x = MatchAgainstScoreMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchAgainstScoreMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchAgainstScoreMessage) ProtoMessage() {}

func (x *MatchAgainstScoreMessage) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchAgainstScoreMessage.ProtoReflect.Descriptor instead.
func (*MatchAgainstScoreMessage) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{31}
}

func (x *MatchAgainstScoreMessage) GetCommon() *Common {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *MatchAgainstScoreMessage) GetAgainst() *Against {
	if x != nil {
		return x.Against
	}
	return nil
}

func (x *MatchAgainstScoreMessage) GetMatchStatus() uint32 {
	if x != nil {
		return x.MatchStatus
	}
	return 0
}

func (x *MatchAgainstScoreMessage) GetDisplayStatus() uint32 {
	if x != nil {
		return x.DisplayStatus
	}
	return 0
}

type Against struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeftName string `protobuf:"bytes,1,opt,name=leftName,proto3" json:"leftName,omitempty"`
	LeftLogo *Image `protobuf:"bytes,2,opt,name=leftLogo,proto3" json:"leftLogo,omitempty"`
	LeftGoal string `protobuf:"bytes,3,opt,name=leftGoal,proto3" json:"leftGoal,omitempty"`
	// LeftPlayersList leftPlayersList = 4;
	// LeftGoalStageDetail leftGoalStageDetail = 5;
	RightName string `protobuf:"bytes,6,opt,name=rightName,proto3" json:"rightName,omitempty"`
	RightLogo *Image `protobuf:"bytes,7,opt,name=rightLogo,proto3" json:"rightLogo,omitempty"`
	RightGoal string `protobuf:"bytes,8,opt,name=rightGoal,proto3" json:"rightGoal,omitempty"`
	// RightPlayersList rightPlayersList  = 9;
	// RightGoalStageDetail rightGoalStageDetail = 10;
	Timestamp          uint64 `protobuf:"varint,11,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Version            uint64 `protobuf:"varint,12,opt,name=version,proto3" json:"version,omitempty"`
	LeftTeamId         uint64 `protobuf:"varint,13,opt,name=leftTeamId,proto3" json:"leftTeamId,omitempty"`
	RightTeamId        uint64 `protobuf:"varint,14,opt,name=rightTeamId,proto3" json:"rightTeamId,omitempty"`
	DiffSei2AbsSecond  uint64 `protobuf:"varint,15,opt,name=diffSei2absSecond,proto3" json:"diffSei2absSecond,omitempty"`
	FinalGoalStage     uint32 `protobuf:"varint,16,opt,name=finalGoalStage,proto3" json:"finalGoalStage,omitempty"`
	CurrentGoalStage   uint32 `protobuf:"varint,17,opt,name=currentGoalStage,proto3" json:"currentGoalStage,omitempty"`
	LeftScoreAddition  uint32 `protobuf:"varint,18,opt,name=leftScoreAddition,proto3" json:"leftScoreAddition,omitempty"`
	RightScoreAddition uint32 `protobuf:"varint,19,opt,name=rightScoreAddition,proto3" json:"rightScoreAddition,omitempty"`
	LeftGoalInt        uint64 `protobuf:"varint,20,opt,name=leftGoalInt,proto3" json:"leftGoalInt,omitempty"`
	RightGoalInt       uint64 `protobuf:"varint,21,opt,name=rightGoalInt,proto3" json:"rightGoalInt,omitempty"`
}

func (x *Against) Reset() {
	*x = Against{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Against) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Against) ProtoMessage() {}

func (x *Against) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Against.ProtoReflect.Descriptor instead.
func (*Against) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{32}
}

func (x *Against) GetLeftName() string {
	if x != nil {
		return x.LeftName
	}
	return ""
}

func (x *Against) GetLeftLogo() *Image {
	if x != nil {
		return x.LeftLogo
	}
	return nil
}

func (x *Against) GetLeftGoal() string {
	if x != nil {
		return x.LeftGoal
	}
	return ""
}

func (x *Against) GetRightName() string {
	if x != nil {
		return x.RightName
	}
	return ""
}

func (x *Against) GetRightLogo() *Image {
	if x != nil {
		return x.RightLogo
	}
	return nil
}

func (x *Against) GetRightGoal() string {
	if x != nil {
		return x.RightGoal
	}
	return ""
}

func (x *Against) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *Against) GetVersion() uint64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *Against) GetLeftTeamId() uint64 {
	if x != nil {
		return x.LeftTeamId
	}
	return 0
}

func (x *Against) GetRightTeamId() uint64 {
	if x != nil {
		return x.RightTeamId
	}
	return 0
}

func (x *Against) GetDiffSei2AbsSecond() uint64 {
	if x != nil {
		return x.DiffSei2AbsSecond
	}
	return 0
}

func (x *Against) GetFinalGoalStage() uint32 {
	if x != nil {
		return x.FinalGoalStage
	}
	return 0
}

func (x *Against) GetCurrentGoalStage() uint32 {
	if x != nil {
		return x.CurrentGoalStage
	}
	return 0
}

func (x *Against) GetLeftScoreAddition() uint32 {
	if x != nil {
		return x.LeftScoreAddition
	}
	return 0
}

func (x *Against) GetRightScoreAddition() uint32 {
	if x != nil {
		return x.RightScoreAddition
	}
	return 0
}

func (x *Against) GetLeftGoalInt() uint64 {
	if x != nil {
		return x.LeftGoalInt
	}
	return 0
}

func (x *Against) GetRightGoalInt() uint64 {
	if x != nil {
		return x.RightGoalInt
	}
	return 0
}

type Common struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method     string `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	MsgId      uint64 `protobuf:"varint,2,opt,name=msgId,proto3" json:"msgId,omitempty"`
	RoomId     uint64 `protobuf:"varint,3,opt,name=roomId,proto3" json:"roomId,omitempty"`
	CreateTime uint64 `protobuf:"varint,4,opt,name=createTime,proto3" json:"createTime,omitempty"`
	Monitor    uint32 `protobuf:"varint,5,opt,name=monitor,proto3" json:"monitor,omitempty"`
	IsShowMsg  bool   `protobuf:"varint,6,opt,name=isShowMsg,proto3" json:"isShowMsg,omitempty"`
	Describe   string `protobuf:"bytes,7,opt,name=describe,proto3" json:"describe,omitempty"`
	// DisplayText displayText = 8;
	FoldType          uint64 `protobuf:"varint,9,opt,name=foldType,proto3" json:"foldType,omitempty"`
	AnchorFoldType    uint64 `protobuf:"varint,10,opt,name=anchorFoldType,proto3" json:"anchorFoldType,omitempty"`
	PriorityScore     uint64 `protobuf:"varint,11,opt,name=priorityScore,proto3" json:"priorityScore,omitempty"`
	LogId             string `protobuf:"bytes,12,opt,name=logId,proto3" json:"logId,omitempty"`
	MsgProcessFilterK string `protobuf:"bytes,13,opt,name=msgProcessFilterK,proto3" json:"msgProcessFilterK,omitempty"`
	MsgProcessFilterV string `protobuf:"bytes,14,opt,name=msgProcessFilterV,proto3" json:"msgProcessFilterV,omitempty"`
	User              *User  `protobuf:"bytes,15,opt,name=user,proto3" json:"user,omitempty"`
	// Room room = 16;
	AnchorFoldTypeV2   uint64 `protobuf:"varint,17,opt,name=anchorFoldTypeV2,proto3" json:"anchorFoldTypeV2,omitempty"`
	ProcessAtSeiTimeMs uint64 `protobuf:"varint,18,opt,name=processAtSeiTimeMs,proto3" json:"processAtSeiTimeMs,omitempty"`
	RandomDispatchMs   uint64 `protobuf:"varint,19,opt,name=randomDispatchMs,proto3" json:"randomDispatchMs,omitempty"`
	IsDispatch         bool   `protobuf:"varint,20,opt,name=isDispatch,proto3" json:"isDispatch,omitempty"`
	ChannelId          uint64 `protobuf:"varint,21,opt,name=channelId,proto3" json:"channelId,omitempty"`
	DiffSei2AbsSecond  uint64 `protobuf:"varint,22,opt,name=diffSei2absSecond,proto3" json:"diffSei2absSecond,omitempty"`
	AnchorFoldDuration uint64 `protobuf:"varint,23,opt,name=anchorFoldDuration,proto3" json:"anchorFoldDuration,omitempty"`
}

func (x *Common) Reset() {
	*x = Common{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Common) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Common) ProtoMessage() {}

func (x *Common) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Common.ProtoReflect.Descriptor instead.
func (*Common) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{33}
}

func (x *Common) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *Common) GetMsgId() uint64 {
	if x != nil {
		return x.MsgId
	}
	return 0
}

func (x *Common) GetRoomId() uint64 {
	if x != nil {
		return x.RoomId
	}
	return 0
}

func (x *Common) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Common) GetMonitor() uint32 {
	if x != nil {
		return x.Monitor
	}
	return 0
}

func (x *Common) GetIsShowMsg() bool {
	if x != nil {
		return x.IsShowMsg
	}
	return false
}

func (x *Common) GetDescribe() string {
	if x != nil {
		return x.Describe
	}
	return ""
}

func (x *Common) GetFoldType() uint64 {
	if x != nil {
		return x.FoldType
	}
	return 0
}

func (x *Common) GetAnchorFoldType() uint64 {
	if x != nil {
		return x.AnchorFoldType
	}
	return 0
}

func (x *Common) GetPriorityScore() uint64 {
	if x != nil {
		return x.PriorityScore
	}
	return 0
}

func (x *Common) GetLogId() string {
	if x != nil {
		return x.LogId
	}
	return ""
}

func (x *Common) GetMsgProcessFilterK() string {
	if x != nil {
		return x.MsgProcessFilterK
	}
	return ""
}

func (x *Common) GetMsgProcessFilterV() string {
	if x != nil {
		return x.MsgProcessFilterV
	}
	return ""
}

func (x *Common) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *Common) GetAnchorFoldTypeV2() uint64 {
	if x != nil {
		return x.AnchorFoldTypeV2
	}
	return 0
}

func (x *Common) GetProcessAtSeiTimeMs() uint64 {
	if x != nil {
		return x.ProcessAtSeiTimeMs
	}
	return 0
}

func (x *Common) GetRandomDispatchMs() uint64 {
	if x != nil {
		return x.RandomDispatchMs
	}
	return 0
}

func (x *Common) GetIsDispatch() bool {
	if x != nil {
		return x.IsDispatch
	}
	return false
}

func (x *Common) GetChannelId() uint64 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *Common) GetDiffSei2AbsSecond() uint64 {
	if x != nil {
		return x.DiffSei2AbsSecond
	}
	return 0
}

func (x *Common) GetAnchorFoldDuration() uint64 {
	if x != nil {
		return x.AnchorFoldDuration
	}
	return 0
}

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 uint64      `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ShortId            uint64      `protobuf:"varint,2,opt,name=shortId,proto3" json:"shortId,omitempty"`
	NickName           string      `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"`
	Gender             uint32      `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	Signature          string      `protobuf:"bytes,5,opt,name=Signature,proto3" json:"Signature,omitempty"`
	Level              uint32      `protobuf:"varint,6,opt,name=Level,proto3" json:"Level,omitempty"`
	Birthday           uint64      `protobuf:"varint,7,opt,name=Birthday,proto3" json:"Birthday,omitempty"`
	Telephone          string      `protobuf:"bytes,8,opt,name=Telephone,proto3" json:"Telephone,omitempty"`
	AvatarThumb        *Image      `protobuf:"bytes,9,opt,name=AvatarThumb,proto3" json:"AvatarThumb,omitempty"`
	AvatarMedium       *Image      `protobuf:"bytes,10,opt,name=AvatarMedium,proto3" json:"AvatarMedium,omitempty"`
	AvatarLarge        *Image      `protobuf:"bytes,11,opt,name=AvatarLarge,proto3" json:"AvatarLarge,omitempty"`
	Verified           bool        `protobuf:"varint,12,opt,name=Verified,proto3" json:"Verified,omitempty"`
	Experience         uint32      `protobuf:"varint,13,opt,name=Experience,proto3" json:"Experience,omitempty"`
	City               string      `protobuf:"bytes,14,opt,name=city,proto3" json:"city,omitempty"`
	Status             int32       `protobuf:"varint,15,opt,name=Status,proto3" json:"Status,omitempty"`
	CreateTime         uint64      `protobuf:"varint,16,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
	ModifyTime         uint64      `protobuf:"varint,17,opt,name=ModifyTime,proto3" json:"ModifyTime,omitempty"`
	Secret             uint32      `protobuf:"varint,18,opt,name=Secret,proto3" json:"Secret,omitempty"`
	ShareQrcodeUri     string      `protobuf:"bytes,19,opt,name=ShareQrcodeUri,proto3" json:"ShareQrcodeUri,omitempty"`
	IncomeSharePercent uint32      `protobuf:"varint,20,opt,name=IncomeSharePercent,proto3" json:"IncomeSharePercent,omitempty"`
	BadgeImageList     []*Image    `protobuf:"bytes,21,rep,name=BadgeImageList,proto3" json:"BadgeImageList,omitempty"`
	FollowInfo         *FollowInfo `protobuf:"bytes,22,opt,name=FollowInfo,proto3" json:"FollowInfo,omitempty"`
	// PayGrade PayGrade = 23;
	// FansClub FansClub = 24;
	// Border Border = 25;
	SpecialId         string   `protobuf:"bytes,26,opt,name=SpecialId,proto3" json:"SpecialId,omitempty"`
	AvatarBorder      *Image   `protobuf:"bytes,27,opt,name=AvatarBorder,proto3" json:"AvatarBorder,omitempty"`
	Medal             *Image   `protobuf:"bytes,28,opt,name=Medal,proto3" json:"Medal,omitempty"`
	RealTimeIconsList []*Image `protobuf:"bytes,29,rep,name=RealTimeIconsList,proto3" json:"RealTimeIconsList,omitempty"`
	DisplayId         string   `protobuf:"bytes,38,opt,name=displayId,proto3" json:"displayId,omitempty"`
	SecUid            string   `protobuf:"bytes,46,opt,name=secUid,proto3" json:"secUid,omitempty"`
	FanTicketCount    uint64   `protobuf:"varint,1022,opt,name=fanTicketCount,proto3" json:"fanTicketCount,omitempty"`
	IdStr             string   `protobuf:"bytes,1028,opt,name=idStr,proto3" json:"idStr,omitempty"`
	AgeRange          uint32   `protobuf:"varint,1045,opt,name=ageRange,proto3" json:"ageRange,omitempty"`
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{34}
}

func (x *User) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetShortId() uint64 {
	if x != nil {
		return x.ShortId
	}
	return 0
}

func (x *User) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *User) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *User) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *User) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *User) GetBirthday() uint64 {
	if x != nil {
		return x.Birthday
	}
	return 0
}

func (x *User) GetTelephone() string {
	if x != nil {
		return x.Telephone
	}
	return ""
}

func (x *User) GetAvatarThumb() *Image {
	if x != nil {
		return x.AvatarThumb
	}
	return nil
}

func (x *User) GetAvatarMedium() *Image {
	if x != nil {
		return x.AvatarMedium
	}
	return nil
}

func (x *User) GetAvatarLarge() *Image {
	if x != nil {
		return x.AvatarLarge
	}
	return nil
}

func (x *User) GetVerified() bool {
	if x != nil {
		return x.Verified
	}
	return false
}

func (x *User) GetExperience() uint32 {
	if x != nil {
		return x.Experience
	}
	return 0
}

func (x *User) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *User) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *User) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *User) GetModifyTime() uint64 {
	if x != nil {
		return x.ModifyTime
	}
	return 0
}

func (x *User) GetSecret() uint32 {
	if x != nil {
		return x.Secret
	}
	return 0
}

func (x *User) GetShareQrcodeUri() string {
	if x != nil {
		return x.ShareQrcodeUri
	}
	return ""
}

func (x *User) GetIncomeSharePercent() uint32 {
	if x != nil {
		return x.IncomeSharePercent
	}
	return 0
}

func (x *User) GetBadgeImageList() []*Image {
	if x != nil {
		return x.BadgeImageList
	}
	return nil
}

func (x *User) GetFollowInfo() *FollowInfo {
	if x != nil {
		return x.FollowInfo
	}
	return nil
}

func (x *User) GetSpecialId() string {
	if x != nil {
		return x.SpecialId
	}
	return ""
}

func (x *User) GetAvatarBorder() *Image {
	if x != nil {
		return x.AvatarBorder
	}
	return nil
}

func (x *User) GetMedal() *Image {
	if x != nil {
		return x.Medal
	}
	return nil
}

func (x *User) GetRealTimeIconsList() []*Image {
	if x != nil {
		return x.RealTimeIconsList
	}
	return nil
}

func (x *User) GetDisplayId() string {
	if x != nil {
		return x.DisplayId
	}
	return ""
}

func (x *User) GetSecUid() string {
	if x != nil {
		return x.SecUid
	}
	return ""
}

func (x *User) GetFanTicketCount() uint64 {
	if x != nil {
		return x.FanTicketCount
	}
	return 0
}

func (x *User) GetIdStr() string {
	if x != nil {
		return x.IdStr
	}
	return ""
}

func (x *User) GetAgeRange() uint32 {
	if x != nil {
		return x.AgeRange
	}
	return 0
}

type FollowInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FollowingCount    uint64 `protobuf:"varint,1,opt,name=followingCount,proto3" json:"followingCount,omitempty"`
	FollowerCount     uint64 `protobuf:"varint,2,opt,name=followerCount,proto3" json:"followerCount,omitempty"`
	FollowStatus      uint64 `protobuf:"varint,3,opt,name=followStatus,proto3" json:"followStatus,omitempty"`
	PushStatus        uint64 `protobuf:"varint,4,opt,name=pushStatus,proto3" json:"pushStatus,omitempty"`
	RemarkName        string `protobuf:"bytes,5,opt,name=remarkName,proto3" json:"remarkName,omitempty"`
	FollowerCountStr  string `protobuf:"bytes,6,opt,name=followerCountStr,proto3" json:"followerCountStr,omitempty"`
	FollowingCountStr string `protobuf:"bytes,7,opt,name=followingCountStr,proto3" json:"followingCountStr,omitempty"`
}

func (x *FollowInfo) Reset() {
	*x = FollowInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowInfo) ProtoMessage() {}

func (x *FollowInfo) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowInfo.ProtoReflect.Descriptor instead.
func (*FollowInfo) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{35}
}

func (x *FollowInfo) GetFollowingCount() uint64 {
	if x != nil {
		return x.FollowingCount
	}
	return 0
}

func (x *FollowInfo) GetFollowerCount() uint64 {
	if x != nil {
		return x.FollowerCount
	}
	return 0
}

func (x *FollowInfo) GetFollowStatus() uint64 {
	if x != nil {
		return x.FollowStatus
	}
	return 0
}

func (x *FollowInfo) GetPushStatus() uint64 {
	if x != nil {
		return x.PushStatus
	}
	return 0
}

func (x *FollowInfo) GetRemarkName() string {
	if x != nil {
		return x.RemarkName
	}
	return ""
}

func (x *FollowInfo) GetFollowerCountStr() string {
	if x != nil {
		return x.FollowerCountStr
	}
	return ""
}

func (x *FollowInfo) GetFollowingCountStr() string {
	if x != nil {
		return x.FollowingCountStr
	}
	return ""
}

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UrlListList     []string          `protobuf:"bytes,1,rep,name=urlListList,proto3" json:"urlListList,omitempty"`
	Uri             string            `protobuf:"bytes,2,opt,name=uri,proto3" json:"uri,omitempty"`
	Height          uint64            `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Width           uint64            `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`
	AvgColor        string            `protobuf:"bytes,5,opt,name=avgColor,proto3" json:"avgColor,omitempty"`
	ImageType       uint32            `protobuf:"varint,6,opt,name=imageType,proto3" json:"imageType,omitempty"`
	OpenWebUrl      string            `protobuf:"bytes,7,opt,name=openWebUrl,proto3" json:"openWebUrl,omitempty"`
	Content         *ImageContent     `protobuf:"bytes,8,opt,name=content,proto3" json:"content,omitempty"`
	IsAnimated      bool              `protobuf:"varint,9,opt,name=isAnimated,proto3" json:"isAnimated,omitempty"`
	FlexSettingList *NinePatchSetting `protobuf:"bytes,10,opt,name=FlexSettingList,proto3" json:"FlexSettingList,omitempty"`
	TextSettingList *NinePatchSetting `protobuf:"bytes,11,opt,name=TextSettingList,proto3" json:"TextSettingList,omitempty"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{36}
}

func (x *Image) GetUrlListList() []string {
	if x != nil {
		return x.UrlListList
	}
	return nil
}

func (x *Image) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *Image) GetHeight() uint64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Image) GetWidth() uint64 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Image) GetAvgColor() string {
	if x != nil {
		return x.AvgColor
	}
	return ""
}

func (x *Image) GetImageType() uint32 {
	if x != nil {
		return x.ImageType
	}
	return 0
}

func (x *Image) GetOpenWebUrl() string {
	if x != nil {
		return x.OpenWebUrl
	}
	return ""
}

func (x *Image) GetContent() *ImageContent {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *Image) GetIsAnimated() bool {
	if x != nil {
		return x.IsAnimated
	}
	return false
}

func (x *Image) GetFlexSettingList() *NinePatchSetting {
	if x != nil {
		return x.FlexSettingList
	}
	return nil
}

func (x *Image) GetTextSettingList() *NinePatchSetting {
	if x != nil {
		return x.TextSettingList
	}
	return nil
}

type NinePatchSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SettingListList []string `protobuf:"bytes,1,rep,name=settingListList,proto3" json:"settingListList,omitempty"`
}

func (x *NinePatchSetting) Reset() {
	*x = NinePatchSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NinePatchSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NinePatchSetting) ProtoMessage() {}

func (x *NinePatchSetting) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NinePatchSetting.ProtoReflect.Descriptor instead.
func (*NinePatchSetting) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{37}
}

func (x *NinePatchSetting) GetSettingListList() []string {
	if x != nil {
		return x.SettingListList
	}
	return nil
}

type ImageContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	FontColor       string `protobuf:"bytes,2,opt,name=fontColor,proto3" json:"fontColor,omitempty"`
	Level           uint64 `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	AlternativeText string `protobuf:"bytes,4,opt,name=alternativeText,proto3" json:"alternativeText,omitempty"`
}

func (x *ImageContent) Reset() {
	*x = ImageContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageContent) ProtoMessage() {}

func (x *ImageContent) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageContent.ProtoReflect.Descriptor instead.
func (*ImageContent) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{38}
}

func (x *ImageContent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ImageContent) GetFontColor() string {
	if x != nil {
		return x.FontColor
	}
	return ""
}

func (x *ImageContent) GetLevel() uint64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *ImageContent) GetAlternativeText() string {
	if x != nil {
		return x.AlternativeText
	}
	return ""
}

type PushFrame struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeqId           uint64         `protobuf:"varint,1,opt,name=seqId,proto3" json:"seqId,omitempty"`
	LogId           uint64         `protobuf:"varint,2,opt,name=logId,proto3" json:"logId,omitempty"`
	Service         uint64         `protobuf:"varint,3,opt,name=service,proto3" json:"service,omitempty"`
	Method          uint64         `protobuf:"varint,4,opt,name=method,proto3" json:"method,omitempty"`
	HeadersList     []*HeadersList `protobuf:"bytes,5,rep,name=headersList,proto3" json:"headersList,omitempty"`
	PayloadEncoding string         `protobuf:"bytes,6,opt,name=payloadEncoding,proto3" json:"payloadEncoding,omitempty"`
	PayloadType     string         `protobuf:"bytes,7,opt,name=payloadType,proto3" json:"payloadType,omitempty"`
	Payload         []byte         `protobuf:"bytes,8,opt,name=payload,proto3" json:"payload,omitempty"`
}

func (x *PushFrame) Reset() {
	*x = PushFrame{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushFrame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushFrame) ProtoMessage() {}

func (x *PushFrame) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushFrame.ProtoReflect.Descriptor instead.
func (*PushFrame) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{39}
}

func (x *PushFrame) GetSeqId() uint64 {
	if x != nil {
		return x.SeqId
	}
	return 0
}

func (x *PushFrame) GetLogId() uint64 {
	if x != nil {
		return x.LogId
	}
	return 0
}

func (x *PushFrame) GetService() uint64 {
	if x != nil {
		return x.Service
	}
	return 0
}

func (x *PushFrame) GetMethod() uint64 {
	if x != nil {
		return x.Method
	}
	return 0
}

func (x *PushFrame) GetHeadersList() []*HeadersList {
	if x != nil {
		return x.HeadersList
	}
	return nil
}

func (x *PushFrame) GetPayloadEncoding() string {
	if x != nil {
		return x.PayloadEncoding
	}
	return ""
}

func (x *PushFrame) GetPayloadType() string {
	if x != nil {
		return x.PayloadType
	}
	return ""
}

func (x *PushFrame) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

type Kk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	K uint32 `protobuf:"varint,14,opt,name=k,proto3" json:"k,omitempty"`
}

func (x *Kk) Reset() {
	*x = Kk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Kk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Kk) ProtoMessage() {}

func (x *Kk) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Kk.ProtoReflect.Descriptor instead.
func (*Kk) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{40}
}

func (x *Kk) GetK() uint32 {
	if x != nil {
		return x.K
	}
	return 0
}

type SendMessageBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConversationId      string     `protobuf:"bytes,1,opt,name=conversationId,proto3" json:"conversationId,omitempty"`
	ConversationType    uint32     `protobuf:"varint,2,opt,name=conversationType,proto3" json:"conversationType,omitempty"`
	ConversationShortId uint64     `protobuf:"varint,3,opt,name=conversationShortId,proto3" json:"conversationShortId,omitempty"`
	Content             string     `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Ext                 []*ExtList `protobuf:"bytes,5,rep,name=ext,proto3" json:"ext,omitempty"`
	MessageType         uint32     `protobuf:"varint,6,opt,name=messageType,proto3" json:"messageType,omitempty"`
	Ticket              string     `protobuf:"bytes,7,opt,name=ticket,proto3" json:"ticket,omitempty"`
	ClientMessageId     string     `protobuf:"bytes,8,opt,name=clientMessageId,proto3" json:"clientMessageId,omitempty"`
}

func (x *SendMessageBody) Reset() {
	*x = SendMessageBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMessageBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageBody) ProtoMessage() {}

func (x *SendMessageBody) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageBody.ProtoReflect.Descriptor instead.
func (*SendMessageBody) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{41}
}

func (x *SendMessageBody) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

func (x *SendMessageBody) GetConversationType() uint32 {
	if x != nil {
		return x.ConversationType
	}
	return 0
}

func (x *SendMessageBody) GetConversationShortId() uint64 {
	if x != nil {
		return x.ConversationShortId
	}
	return 0
}

func (x *SendMessageBody) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SendMessageBody) GetExt() []*ExtList {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *SendMessageBody) GetMessageType() uint32 {
	if x != nil {
		return x.MessageType
	}
	return 0
}

func (x *SendMessageBody) GetTicket() string {
	if x != nil {
		return x.Ticket
	}
	return ""
}

func (x *SendMessageBody) GetClientMessageId() string {
	if x != nil {
		return x.ClientMessageId
	}
	return ""
}

type ExtList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *ExtList) Reset() {
	*x = ExtList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtList) ProtoMessage() {}

func (x *ExtList) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtList.ProtoReflect.Descriptor instead.
func (*ExtList) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{42}
}

func (x *ExtList) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ExtList) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type Rsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A int32  `protobuf:"varint,1,opt,name=a,proto3" json:"a,omitempty"`
	B int32  `protobuf:"varint,2,opt,name=b,proto3" json:"b,omitempty"`
	C int32  `protobuf:"varint,3,opt,name=c,proto3" json:"c,omitempty"`
	D string `protobuf:"bytes,4,opt,name=d,proto3" json:"d,omitempty"`
	E int32  `protobuf:"varint,5,opt,name=e,proto3" json:"e,omitempty"`
	F *Rsp_F `protobuf:"bytes,6,opt,name=f,proto3" json:"f,omitempty"`
	G string `protobuf:"bytes,7,opt,name=g,proto3" json:"g,omitempty"`
	H uint64 `protobuf:"varint,10,opt,name=h,proto3" json:"h,omitempty"`
	I uint64 `protobuf:"varint,11,opt,name=i,proto3" json:"i,omitempty"`
	J uint64 `protobuf:"varint,13,opt,name=j,proto3" json:"j,omitempty"`
}

func (x *Rsp) Reset() {
	*x = Rsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rsp) ProtoMessage() {}

func (x *Rsp) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rsp.ProtoReflect.Descriptor instead.
func (*Rsp) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{43}
}

func (x *Rsp) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

func (x *Rsp) GetB() int32 {
	if x != nil {
		return x.B
	}
	return 0
}

func (x *Rsp) GetC() int32 {
	if x != nil {
		return x.C
	}
	return 0
}

func (x *Rsp) GetD() string {
	if x != nil {
		return x.D
	}
	return ""
}

func (x *Rsp) GetE() int32 {
	if x != nil {
		return x.E
	}
	return 0
}

func (x *Rsp) GetF() *Rsp_F {
	if x != nil {
		return x.F
	}
	return nil
}

func (x *Rsp) GetG() string {
	if x != nil {
		return x.G
	}
	return ""
}

func (x *Rsp) GetH() uint64 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *Rsp) GetI() uint64 {
	if x != nil {
		return x.I
	}
	return 0
}

func (x *Rsp) GetJ() uint64 {
	if x != nil {
		return x.J
	}
	return 0
}

type PreMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cmd             uint32           `protobuf:"varint,1,opt,name=cmd,proto3" json:"cmd,omitempty"`
	SequenceId      uint32           `protobuf:"varint,2,opt,name=sequenceId,proto3" json:"sequenceId,omitempty"`
	SdkVersion      string           `protobuf:"bytes,3,opt,name=sdkVersion,proto3" json:"sdkVersion,omitempty"`
	Token           string           `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	Refer           uint32           `protobuf:"varint,5,opt,name=refer,proto3" json:"refer,omitempty"`
	InboxType       uint32           `protobuf:"varint,6,opt,name=inboxType,proto3" json:"inboxType,omitempty"`
	BuildNumber     string           `protobuf:"bytes,7,opt,name=buildNumber,proto3" json:"buildNumber,omitempty"`
	SendMessageBody *SendMessageBody `protobuf:"bytes,8,opt,name=sendMessageBody,proto3" json:"sendMessageBody,omitempty"`
	// 字段名待定
	Aa             string         `protobuf:"bytes,9,opt,name=aa,proto3" json:"aa,omitempty"`
	DevicePlatform string         `protobuf:"bytes,11,opt,name=devicePlatform,proto3" json:"devicePlatform,omitempty"`
	Headers        []*HeadersList `protobuf:"bytes,15,rep,name=headers,proto3" json:"headers,omitempty"`
	AuthType       uint32         `protobuf:"varint,18,opt,name=authType,proto3" json:"authType,omitempty"`
	Biz            string         `protobuf:"bytes,21,opt,name=biz,proto3" json:"biz,omitempty"`
	Access         string         `protobuf:"bytes,22,opt,name=access,proto3" json:"access,omitempty"`
}

func (x *PreMessage) Reset() {
	*x = PreMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreMessage) ProtoMessage() {}

func (x *PreMessage) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreMessage.ProtoReflect.Descriptor instead.
func (*PreMessage) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{44}
}

func (x *PreMessage) GetCmd() uint32 {
	if x != nil {
		return x.Cmd
	}
	return 0
}

func (x *PreMessage) GetSequenceId() uint32 {
	if x != nil {
		return x.SequenceId
	}
	return 0
}

func (x *PreMessage) GetSdkVersion() string {
	if x != nil {
		return x.SdkVersion
	}
	return ""
}

func (x *PreMessage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *PreMessage) GetRefer() uint32 {
	if x != nil {
		return x.Refer
	}
	return 0
}

func (x *PreMessage) GetInboxType() uint32 {
	if x != nil {
		return x.InboxType
	}
	return 0
}

func (x *PreMessage) GetBuildNumber() string {
	if x != nil {
		return x.BuildNumber
	}
	return ""
}

func (x *PreMessage) GetSendMessageBody() *SendMessageBody {
	if x != nil {
		return x.SendMessageBody
	}
	return nil
}

func (x *PreMessage) GetAa() string {
	if x != nil {
		return x.Aa
	}
	return ""
}

func (x *PreMessage) GetDevicePlatform() string {
	if x != nil {
		return x.DevicePlatform
	}
	return ""
}

func (x *PreMessage) GetHeaders() []*HeadersList {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *PreMessage) GetAuthType() uint32 {
	if x != nil {
		return x.AuthType
	}
	return 0
}

func (x *PreMessage) GetBiz() string {
	if x != nil {
		return x.Biz
	}
	return ""
}

func (x *PreMessage) GetAccess() string {
	if x != nil {
		return x.Access
	}
	return ""
}

type HeadersList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *HeadersList) Reset() {
	*x = HeadersList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeadersList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeadersList) ProtoMessage() {}

func (x *HeadersList) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeadersList.ProtoReflect.Descriptor instead.
func (*HeadersList) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{45}
}

func (x *HeadersList) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *HeadersList) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type Rsp_F struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Q1 uint64 `protobuf:"varint,1,opt,name=q1,proto3" json:"q1,omitempty"`
	Q3 uint64 `protobuf:"varint,3,opt,name=q3,proto3" json:"q3,omitempty"`
	Q4 string `protobuf:"bytes,4,opt,name=q4,proto3" json:"q4,omitempty"`
	Q5 uint64 `protobuf:"varint,5,opt,name=q5,proto3" json:"q5,omitempty"`
}

func (x *Rsp_F) Reset() {
	*x = Rsp_F{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protobuf_dy_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rsp_F) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rsp_F) ProtoMessage() {}

func (x *Rsp_F) ProtoReflect() protoreflect.Message {
	mi := &file_protobuf_dy_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rsp_F.ProtoReflect.Descriptor instead.
func (*Rsp_F) Descriptor() ([]byte, []int) {
	return file_protobuf_dy_proto_rawDescGZIP(), []int{43, 0}
}

func (x *Rsp_F) GetQ1() uint64 {
	if x != nil {
		return x.Q1
	}
	return 0
}

func (x *Rsp_F) GetQ3() uint64 {
	if x != nil {
		return x.Q3
	}
	return 0
}

func (x *Rsp_F) GetQ4() string {
	if x != nil {
		return x.Q4
	}
	return ""
}

func (x *Rsp_F) GetQ5() uint64 {
	if x != nil {
		return x.Q5
	}
	return 0
}

var File_protobuf_dy_proto protoreflect.FileDescriptor

var file_protobuf_dy_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x22, 0x82, 0x04, 0x0a, 0x08,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x65, 0x74, 0x63, 0x68, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x6e,
	0x6f, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6e, 0x6f, 0x77, 0x12, 0x20, 0x0a,
	0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x66, 0x65, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x66, 0x65, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a,
	0x0b, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x68,
	0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x65, 0x64, 0x41, 0x63, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x6e, 0x65, 0x65, 0x64, 0x41, 0x63, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x75,
	0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x70, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x69,
	0x76, 0x65, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6c, 0x69, 0x76, 0x65, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x68, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x6f, 0x4d, 0x6f, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x6f, 0x4d, 0x6f, 0x72, 0x65,
	0x1a, 0x3e, 0x0a, 0x10, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xeb, 0x01, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d,
	0x73, 0x67, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x65, 0x65, 0x64, 0x57, 0x72,
	0x64, 0x73, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6e,
	0x65, 0x65, 0x64, 0x57, 0x72, 0x64, 0x73, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x77, 0x72, 0x64, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x77, 0x72, 0x64, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e,
	0x0a, 0x0a, 0x77, 0x72, 0x64, 0x73, 0x53, 0x75, 0x62, 0x4b, 0x65, 0x79, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x77, 0x72, 0x64, 0x73, 0x53, 0x75, 0x62, 0x4b, 0x65, 0x79, 0x22, 0xdf,
	0x06, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x26,
	0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x06,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x53,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x76, 0x69, 0x73,
	0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x0f,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x66, 0x75, 0x6c, 0x6c, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x66, 0x75, 0x6c, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x56, 0x32, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x52, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x56, 0x32, 0x12, 0x44, 0x0a, 0x10, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x41, 0x72,
	0x65, 0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x41, 0x72,
	0x65, 0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x10, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x09, 0x67, 0x69,
	0x66, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x09, 0x67, 0x69,
	0x66, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x4d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d,
	0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x4d, 0x0a,
	0x13, 0x6c, 0x61, 0x6e, 0x64, 0x73, 0x63, 0x61, 0x70, 0x65, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x64, 0x6f, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x4c, 0x61, 0x6e, 0x64, 0x73, 0x63, 0x61, 0x70, 0x65, 0x41, 0x72, 0x65,
	0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x13, 0x6c, 0x61, 0x6e, 0x64, 0x73, 0x63, 0x61,
	0x70, 0x65, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65,
	0x6e, 0x64, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x73, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x72,
	0x6f, 0x6d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x6f, 0x6d, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x6f, 0x6d, 0x12, 0x32,
	0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x6f, 0x6d, 0x48, 0x69, 0x64, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x61, 0x72, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x63, 0x6f, 0x6d, 0x48, 0x69, 0x64, 0x65, 0x55, 0x73, 0x65, 0x72, 0x43, 0x61,
	0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x74, 0x42, 0x79, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x42, 0x79, 0x12, 0x36, 0x0a, 0x16, 0x69, 0x6e,
	0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x43, 0x68, 0x61, 0x74, 0x50, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x16, 0x69, 0x6e, 0x64, 0x69,
	0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x43, 0x68, 0x61, 0x74, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x12, 0x2c, 0x0a, 0x0a, 0x72, 0x74, 0x66, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x0a, 0x72, 0x74, 0x66, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x22, 0xed, 0x01, 0x0a, 0x13, 0x4c, 0x61, 0x6e, 0x64, 0x73, 0x63, 0x61, 0x70, 0x65, 0x41, 0x72,
	0x65, 0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x68, 0x6f, 0x77,
	0x48, 0x65, 0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x73, 0x68, 0x6f, 0x77,
	0x48, 0x65, 0x61, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x68, 0x6f, 0x77, 0x4e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x68, 0x6f, 0x77,
	0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x68, 0x6f, 0x77,
	0x46, 0x6f, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x73, 0x68, 0x6f, 0x77, 0x46, 0x6f, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x26,
	0x0a, 0x0e, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x61, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x61, 0x67, 0x52, 0x13, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x61, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0xb4, 0x04, 0x0a, 0x12, 0x52, 0x6f, 0x6f, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x71,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12,
	0x43, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d,
	0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x71, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f,
	0x70, 0x53, 0x74, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6f, 0x70, 0x53,
	0x74, 0x72, 0x12, 0x43, 0x0a, 0x09, 0x73, 0x65, 0x61, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x52,
	0x6f, 0x6f, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x71, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x52, 0x09, 0x73, 0x65,
	0x61, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6f, 0x70, 0x75, 0x6c,
	0x61, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x6f, 0x70,
	0x75, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x55, 0x73, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x55, 0x73, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x55, 0x73,
	0x65, 0x72, 0x53, 0x74, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x53, 0x74, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x53, 0x74, 0x72, 0x12, 0x30, 0x0a, 0x13, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f,
	0x72, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x50, 0x76, 0x46, 0x6f, 0x72, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x76, 0x46, 0x6f, 0x72, 0x41, 0x6e, 0x63,
	0x68, 0x6f, 0x72, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x70, 0x52, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x53, 0x74, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x70,
	0x52, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74, 0x61, 0x74, 0x73, 0x53, 0x74, 0x72, 0x12, 0x38, 0x0a,
	0x17, 0x75, 0x70, 0x52, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74, 0x61, 0x74, 0x73, 0x53, 0x74, 0x72,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17,
	0x75, 0x70, 0x52, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74, 0x61, 0x74, 0x73, 0x53, 0x74, 0x72, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x22, 0x73, 0x0a, 0x11, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x06,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x64,
	0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x22, 0xca, 0x01, 0x0a,
	0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x61, 0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12,
	0x36, 0x0a, 0x16, 0x72, 0x6f, 0x6f, 0x6d, 0x46, 0x61, 0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x16, 0x72, 0x6f, 0x6f, 0x6d, 0x46, 0x61, 0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x54, 0x65, 0x78, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x72, 0x6f, 0x6f, 0x6d, 0x46,
	0x61, 0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x12, 0x72, 0x6f, 0x6f, 0x6d, 0x46, 0x61, 0x6e, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x63, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0xed, 0x01, 0x0a, 0x1d, 0x52, 0x6f,
	0x6f, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x71, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x20, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x12, 0x1a, 0x0a,
	0x08, 0x69, 0x73, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x69, 0x73, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x78, 0x61, 0x63, 0x74, 0x6c, 0x79,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x78, 0x61,
	0x63, 0x74, 0x6c, 0x79, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xc9, 0x09, 0x0a, 0x0b, 0x47, 0x69,
	0x66, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x64, 0x6f, 0x75, 0x79,
	0x69, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x61, 0x6e,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0e, 0x66, 0x61, 0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x06, 0x74, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x06, 0x74, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x45, 0x6e, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x45, 0x6e, 0x64, 0x12, 0x32, 0x0a, 0x0a, 0x74, 0x65, 0x78,
	0x74, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x45, 0x66, 0x66, 0x65, 0x63,
	0x74, 0x52, 0x0a, 0x74, 0x65, 0x78, 0x74, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x69, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x67, 0x69, 0x66, 0x74, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x67, 0x69, 0x66, 0x74,
	0x73, 0x12, 0x2e, 0x0a, 0x12, 0x72, 0x6f, 0x6f, 0x6d, 0x46, 0x61, 0x6e, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x72,
	0x6f, 0x6f, 0x6d, 0x46, 0x61, 0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x32, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x47, 0x69, 0x66,
	0x74, 0x49, 0x4d, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x26, 0x0a, 0x04, 0x67, 0x69, 0x66, 0x74, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x47, 0x69, 0x66,
	0x74, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x04, 0x67, 0x69, 0x66, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x6f, 0x67, 0x49, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x6f,
	0x67, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x44, 0x0a, 0x10, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x75, 0x79,
	0x69, 0x6e, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x10, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x41, 0x72, 0x65, 0x61, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x79, 0x44, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x54, 0x65, 0x78, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0f, 0x74, 0x72,
	0x61, 0x79, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x65, 0x78, 0x74, 0x12, 0x32, 0x0a,
	0x14, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x64, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x45, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x04, 0x52, 0x14, 0x62, 0x61, 0x6e,
	0x6e, 0x65, 0x64, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x73, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x46, 0x6f, 0x72, 0x53,
	0x65, 0x6c, 0x66, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x46, 0x6f, 0x72, 0x53, 0x65, 0x6c, 0x66, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x47, 0x69, 0x66, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x47, 0x69, 0x66,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x79, 0x49, 0x74, 0x65, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x79, 0x49,
	0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x53, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x1c, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x53, 0x65, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x2a, 0x0a, 0x10, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x47, 0x69, 0x66, 0x74, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x47, 0x69, 0x66, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x24, 0x0a,
	0x0d, 0x74, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x20,
	0x20, 0x03, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x21, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x30, 0x0a, 0x13, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x45,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x73, 0x18, 0x22, 0x20, 0x01, 0x28, 0x04, 0x52, 0x13, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x18, 0x23, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x65,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x73, 0x18, 0x24,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x54, 0x73, 0x22, 0xed, 0x04, 0x0a, 0x0a, 0x47, 0x69, 0x66, 0x74, 0x53, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x12, 0x23, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x6f, 0x72,
	0x4c, 0x69, 0x6e, 0x6b, 0x6d, 0x69, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x66,
	0x6f, 0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x6d, 0x69, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6f,
	0x64, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x64, 0x6f, 0x6f, 0x64, 0x6c,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x46, 0x61, 0x6e, 0x73, 0x63, 0x6c, 0x75, 0x62,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x66, 0x6f, 0x72, 0x46, 0x61, 0x6e, 0x73, 0x63,
	0x6c, 0x75, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x64, 0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x64, 0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x2e, 0x0a, 0x12, 0x69, 0x73, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x64,
	0x4f, 0x6e, 0x50, 0x61, 0x6e, 0x65, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69,
	0x73, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x64, 0x4f, 0x6e, 0x50, 0x61, 0x6e, 0x65,
	0x6c, 0x12, 0x28, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x45, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x70, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0d, 0x67,
	0x69, 0x66, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x52, 0x0d, 0x67, 0x69, 0x66, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x63, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x66, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x12, 0x21, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x0e, 0x47, 0x69, 0x66, 0x74, 0x49, 0x4d,
	0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x0e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x2c, 0x0a, 0x11, 0x73, 0x65, 0x6c, 0x66, 0x51, 0x75, 0x65, 0x75, 0x65, 0x50, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x73, 0x65, 0x6c,
	0x66, 0x51, 0x75, 0x65, 0x75, 0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22, 0x7a, 0x0a, 0x0a, 0x54, 0x65,
	0x78, 0x74, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x12, 0x34, 0x0a, 0x08, 0x70, 0x6f, 0x72, 0x74,
	0x72, 0x61, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x08, 0x70, 0x6f, 0x72, 0x74, 0x72, 0x61, 0x69, 0x74, 0x12, 0x36,
	0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x64, 0x73, 0x63, 0x61, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x45,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x09, 0x6c, 0x61, 0x6e,
	0x64, 0x73, 0x63, 0x61, 0x70, 0x65, 0x22, 0xc5, 0x03, 0x0a, 0x10, 0x54, 0x65, 0x78, 0x74, 0x45,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x20, 0x0a, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79,
	0x69, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x22, 0x0a,
	0x0c, 0x74, 0x65, 0x78, 0x74, 0x46, 0x6f, 0x6e, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x74, 0x65, 0x78, 0x74, 0x46, 0x6f, 0x6e, 0x74, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x2d, 0x0a, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x78,
	0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x44, 0x78, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x44, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x68, 0x61, 0x64,
	0x6f, 0x77, 0x44, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x68, 0x61, 0x64,
	0x6f, 0x77, 0x44, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x52, 0x61,
	0x64, 0x69, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x73, 0x68, 0x61, 0x64,
	0x6f, 0x77, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x68, 0x61, 0x64,
	0x6f, 0x77, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x68, 0x61, 0x64, 0x6f, 0x77, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74,
	0x72, 0x6f, 0x6b, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x74, 0x72, 0x6f, 0x6b, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x20, 0x0a, 0x0b,
	0x73, 0x74, 0x72, 0x6f, 0x6b, 0x65, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x6f, 0x6b, 0x65, 0x57, 0x69, 0x64, 0x74, 0x68, 0x22, 0x89,
	0x07, 0x0a, 0x0d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x26, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x08,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x08, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x53, 0x65, 0x74, 0x54,
	0x6f, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73,
	0x53, 0x65, 0x74, 0x54, 0x6f, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73,
	0x54, 0x6f, 0x70, 0x55, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x54, 0x6f, 0x70, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x72, 0x61, 0x6e,
	0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x6f, 0x70, 0x55, 0x73, 0x65,
	0x72, 0x4e, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x6f, 0x70, 0x55, 0x73,
	0x65, 0x72, 0x4e, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x38, 0x0a, 0x0c, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x65, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f,
	0x70, 0x53, 0x74, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6f, 0x70, 0x53,
	0x74, 0x72, 0x12, 0x42, 0x0a, 0x11, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x66, 0x66, 0x65, 0x63,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x11, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x37, 0x0a, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0f,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x3b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x56, 0x32, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x56, 0x32, 0x12, 0x3a, 0x0a, 0x11,
	0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x65, 0x78,
	0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x11, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x44, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x54, 0x65, 0x78, 0x74, 0x12, 0x44, 0x0a, 0x10, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x10, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x2a,
	0x0a, 0x10, 0x75, 0x73, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x54, 0x69, 0x70, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x75, 0x73, 0x65, 0x72, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x54, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x61, 0x6e,
	0x63, 0x68, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x54, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x54, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x22, 0xa3, 0x01, 0x0a, 0x10, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12,
	0x2b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x2c, 0x0a, 0x11,
	0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x6e, 0x52, 0x6f, 0x6f,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x49, 0x6e, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x34, 0x0a, 0x15, 0x75, 0x73,
	0x65, 0x72, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x69, 0x66, 0x74, 0x43, 0x6e, 0x74, 0x49, 0x6e, 0x52,
	0x6f, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x15, 0x75, 0x73, 0x65, 0x72, 0x53,
	0x65, 0x6e, 0x64, 0x47, 0x69, 0x66, 0x74, 0x43, 0x6e, 0x74, 0x49, 0x6e, 0x52, 0x6f, 0x6f, 0x6d,
	0x22, 0xbd, 0x07, 0x0a, 0x0c, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x50, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x50, 0x6f, 0x73, 0x12, 0x20, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x29, 0x0a, 0x08, 0x74, 0x65, 0x78, 0x74,
	0x49, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x08, 0x74, 0x65, 0x78, 0x74, 0x49,
	0x63, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x74, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x61, 0x6e, 0x69, 0x6d, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61, 0x6e, 0x69, 0x6d, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x05, 0x62, 0x61, 0x64, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x05, 0x62, 0x61, 0x64, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x66, 0x6c, 0x65, 0x78, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x04, 0x52, 0x14, 0x66, 0x6c, 0x65, 0x78, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x0f, 0x74, 0x65,
	0x78, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x79, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x0f, 0x74, 0x65, 0x78, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72,
	0x6c, 0x61, 0x79, 0x12, 0x33, 0x0a, 0x0d, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x61, 0x64, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0d, 0x61, 0x6e, 0x69, 0x6d, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x61, 0x64, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x68, 0x61, 0x73, 0x53,
	0x77, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x68, 0x61, 0x73, 0x53, 0x77, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x12, 0x3a,
	0x0a, 0x18, 0x74, 0x65, 0x78, 0x74, 0x46, 0x6c, 0x65, 0x78, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x18, 0x74, 0x65, 0x78, 0x74, 0x46, 0x6c, 0x65, 0x78, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x41, 0x6e, 0x69, 0x6d, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x41, 0x6e, 0x69,
	0x6d, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x64, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x4d, 0x61, 0x70, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x08, 0x65, 0x78, 0x74, 0x72, 0x61, 0x4d, 0x61, 0x70, 0x12, 0x26, 0x0a, 0x0e, 0x6d,
	0x70, 0x34, 0x41, 0x6e, 0x69, 0x6d, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0e, 0x6d, 0x70, 0x34, 0x41, 0x6e, 0x69, 0x6d, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12,
	0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x57, 0x61, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x57, 0x61, 0x69, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x6c, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x61, 0x6c, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x6c, 0x69,
	0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0f, 0x61, 0x6c, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x1a, 0x3b, 0x0a, 0x0d, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xab, 0x01, 0x0a, 0x04, 0x54, 0x65, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x24, 0x0a, 0x0d, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x12, 0x38, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x0d, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x70,
	0x69, 0x65, 0x63, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x50, 0x69, 0x65,
	0x63, 0x65, 0x52, 0x0a, 0x70, 0x69, 0x65, 0x63, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x8e,
	0x03, 0x0a, 0x09, 0x54, 0x65, 0x78, 0x74, 0x50, 0x69, 0x65, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x2a, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x20, 0x0a, 0x0b,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x33,
	0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x50,
	0x69, 0x65, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x33, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x50, 0x69, 0x65, 0x63, 0x65, 0x47, 0x69, 0x66, 0x74, 0x52, 0x09, 0x67,
	0x69, 0x66, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x68, 0x65, 0x61, 0x72,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64,
	0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x50, 0x69, 0x65, 0x63, 0x65, 0x48,
	0x65, 0x61, 0x72, 0x74, 0x52, 0x0a, 0x68, 0x65, 0x61, 0x72, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x45, 0x0a, 0x0f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x52, 0x65, 0x66, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x64, 0x6f, 0x75, 0x79,
	0x69, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x50, 0x69, 0x65, 0x63, 0x65, 0x50, 0x61, 0x74, 0x74,
	0x65, 0x72, 0x6e, 0x52, 0x65, 0x66, 0x52, 0x0f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x52,
	0x65, 0x66, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x6f,
	0x75, 0x79, 0x69, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x50, 0x69, 0x65, 0x63, 0x65, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x52, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x57, 0x0a, 0x0e, 0x54, 0x65, 0x78, 0x74, 0x50, 0x69, 0x65, 0x63, 0x65, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x23, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e,
	0x67, 0x52, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x73, 0x63, 0x61,
	0x6c, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x74, 0x65, 0x22, 0x4f, 0x0a, 0x13, 0x54, 0x65, 0x78, 0x74,
	0x50, 0x69, 0x65, 0x63, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x52, 0x65, 0x66, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x74, 0x74,
	0x65, 0x72, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x22, 0x26, 0x0a, 0x0e, 0x54, 0x65, 0x78,
	0x74, 0x50, 0x69, 0x65, 0x63, 0x65, 0x48, 0x65, 0x61, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x22, 0x55, 0x0a, 0x0d, 0x54, 0x65, 0x78, 0x74, 0x50, 0x69, 0x65, 0x63, 0x65, 0x47, 0x69,
	0x66, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x07, 0x6e, 0x61,
	0x6d, 0x65, 0x52, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x64, 0x6f,
	0x75, 0x79, 0x69, 0x6e, 0x2e, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x52, 0x65, 0x66, 0x52,
	0x07, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x66, 0x22, 0x46, 0x0a, 0x0a, 0x50, 0x61, 0x74, 0x74,
	0x65, 0x72, 0x6e, 0x52, 0x65, 0x66, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e,
	0x22, 0x4f, 0x0a, 0x0d, 0x54, 0x65, 0x78, 0x74, 0x50, 0x69, 0x65, 0x63, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x20, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x6c, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x77, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x6c, 0x6f,
	0x6e, 0x22, 0xfa, 0x01, 0x0a, 0x0a, 0x54, 0x65, 0x78, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x6c, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x62, 0x6f, 0x6c, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x74,
	0x61, 0x6c, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x74, 0x61, 0x6c,
	0x69, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x74,
	0x61, 0x6c, 0x69, 0x63, 0x41, 0x6e, 0x67, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x69, 0x74, 0x61, 0x6c, 0x69, 0x63, 0x41, 0x6e, 0x67, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x66, 0x6f, 0x6e, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x66, 0x6f, 0x6e, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x75, 0x73, 0x65, 0x48,
	0x65, 0x69, 0x67, 0x68, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x75, 0x73, 0x65, 0x48, 0x65, 0x69, 0x67, 0x68, 0x4c, 0x69,
	0x67, 0x68, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x52,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x75, 0x73, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x72, 0x22, 0xc2,
	0x03, 0x0a, 0x0b, 0x4c, 0x69, 0x6b, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x26,
	0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x06,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x20, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x44,
	0x0a, 0x10, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x4c, 0x69, 0x6b, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69,
	0x6e, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x4c, 0x69, 0x6b, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x10, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x4c, 0x69, 0x6b, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x4a, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x12, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x28, 0x0a, 0x0f, 0x6c, 0x69, 0x6e, 0x6b, 0x6d, 0x69, 0x63, 0x47, 0x75, 0x65, 0x73, 0x74,
	0x55, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x6c, 0x69, 0x6e, 0x6b, 0x6d,
	0x69, 0x63, 0x47, 0x75, 0x65, 0x73, 0x74, 0x55, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x65, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x12, 0x41, 0x0a, 0x0f, 0x70, 0x69, 0x63, 0x6f, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x6f, 0x75, 0x79,
	0x69, 0x6e, 0x2e, 0x50, 0x69, 0x63, 0x6f, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0f, 0x70, 0x69, 0x63, 0x6f, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x99, 0x02, 0x0a, 0x0d, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x20, 0x0a,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f,
	0x75, 0x79, 0x69, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x68, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x73, 0x68, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x68, 0x61, 0x72, 0x65, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x72,
	0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x66, 0x6f,
	0x6c, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x44, 0x0a, 0x10, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x10, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22,
	0x98, 0x01, 0x0a, 0x0f, 0x50, 0x69, 0x63, 0x6f, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x53, 0x75, 0x6d, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x62,
	0x6f, 0x53, 0x75, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x6f,
	0x6a, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x6f, 0x6a, 0x69, 0x12,
	0x2b, 0x0a, 0x09, 0x65, 0x6d, 0x6f, 0x6a, 0x69, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x52, 0x09, 0x65, 0x6d, 0x6f, 0x6a, 0x69, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09,
	0x65, 0x6d, 0x6f, 0x6a, 0x69, 0x54, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x65, 0x6d, 0x6f, 0x6a, 0x69, 0x54, 0x65, 0x78, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x10, 0x44,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x4c, 0x69, 0x6b, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x1e, 0x0a, 0x0a, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x65, 0x71, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x73, 0x65, 0x71, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c,
	0x73, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x72, 0x65, 0x6e, 0x65,
	0x77, 0x61, 0x6c, 0x73, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x73, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x73, 0x4e, 0x75, 0x6d, 0x22, 0x4e, 0x0a, 0x12, 0x44, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1a, 0x0a, 0x08, 0x73, 0x68, 0x6f, 0x77, 0x54, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x73, 0x68, 0x6f, 0x77, 0x54, 0x65, 0x78, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x68, 0x6f, 0x77, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x73, 0x68, 0x6f, 0x77, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x22, 0x96, 0x02, 0x0a, 0x12, 0x45, 0x70,
	0x69, 0x73, 0x6f, 0x64, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x27, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x54, 0x6f, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x76,
	0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x12, 0x2b, 0x0a,
	0x09, 0x67, 0x69, 0x66, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x09, 0x67, 0x69, 0x66, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0xb5, 0x01, 0x0a, 0x18, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x67, 0x61, 0x69,
	0x6e, 0x73, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x26, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x07, 0x61, 0x67, 0x61, 0x69, 0x6e,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69,
	0x6e, 0x2e, 0x41, 0x67, 0x61, 0x69, 0x6e, 0x73, 0x74, 0x52, 0x07, 0x61, 0x67, 0x61, 0x69, 0x6e,
	0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xf5, 0x04, 0x0a, 0x07, 0x41,
	0x67, 0x61, 0x69, 0x6e, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x29, 0x0a, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x65, 0x66, 0x74, 0x47, 0x6f, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x65, 0x66, 0x74, 0x47, 0x6f, 0x61, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x69, 0x67,
	0x68, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x09, 0x72, 0x69, 0x67, 0x68, 0x74,
	0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x09, 0x72, 0x69, 0x67, 0x68, 0x74,
	0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x69, 0x67, 0x68, 0x74, 0x47, 0x6f, 0x61,
	0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x69, 0x67, 0x68, 0x74, 0x47, 0x6f,
	0x61, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x65,
	0x66, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x6c, 0x65, 0x66, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x72, 0x69, 0x67, 0x68, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x11,
	0x64, 0x69, 0x66, 0x66, 0x53, 0x65, 0x69, 0x32, 0x61, 0x62, 0x73, 0x53, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x64, 0x69, 0x66, 0x66, 0x53, 0x65, 0x69,
	0x32, 0x61, 0x62, 0x73, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69,
	0x6e, 0x61, 0x6c, 0x47, 0x6f, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0e, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x47, 0x6f, 0x61, 0x6c, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x47, 0x6f, 0x61,
	0x6c, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x47, 0x6f, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2c,
	0x0a, 0x11, 0x6c, 0x65, 0x66, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x6c, 0x65, 0x66, 0x74, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12,
	0x72, 0x69, 0x67, 0x68, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x72, 0x69, 0x67, 0x68, 0x74, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b,
	0x6c, 0x65, 0x66, 0x74, 0x47, 0x6f, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0b, 0x6c, 0x65, 0x66, 0x74, 0x47, 0x6f, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x12, 0x22,
	0x0a, 0x0c, 0x72, 0x69, 0x67, 0x68, 0x74, 0x47, 0x6f, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x72, 0x69, 0x67, 0x68, 0x74, 0x47, 0x6f, 0x61, 0x6c, 0x49,
	0x6e, 0x74, 0x22, 0xe4, 0x05, 0x0a, 0x06, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x72, 0x6f, 0x6f,
	0x6d, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x1c, 0x0a,
	0x09, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x4d, 0x73, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x4d, 0x73, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x6f, 0x6c, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x66, 0x6f, 0x6c, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x46, 0x6f, 0x6c,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x61, 0x6e, 0x63,
	0x68, 0x6f, 0x72, 0x46, 0x6f, 0x6c, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0d, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x6f, 0x67, 0x49, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6c, 0x6f, 0x67, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x6d, 0x73, 0x67, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4b, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x6d, 0x73, 0x67, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x4b, 0x12, 0x2c, 0x0a, 0x11, 0x6d, 0x73, 0x67, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x56, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x6d, 0x73, 0x67, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x56, 0x12, 0x20, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x10, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x46,
	0x6f, 0x6c, 0x64, 0x54, 0x79, 0x70, 0x65, 0x56, 0x32, 0x18, 0x11, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x10, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x46, 0x6f, 0x6c, 0x64, 0x54, 0x79, 0x70, 0x65, 0x56,
	0x32, 0x12, 0x2e, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x74, 0x53, 0x65,
	0x69, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x74, 0x53, 0x65, 0x69, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x73, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x44, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x4d, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x72, 0x61, 0x6e,
	0x64, 0x6f, 0x6d, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x4d, 0x73, 0x12, 0x1e, 0x0a,
	0x0a, 0x69, 0x73, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x69, 0x73, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x12, 0x1c, 0x0a,
	0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x64,
	0x69, 0x66, 0x66, 0x53, 0x65, 0x69, 0x32, 0x61, 0x62, 0x73, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x64, 0x69, 0x66, 0x66, 0x53, 0x65, 0x69, 0x32,
	0x61, 0x62, 0x73, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x61, 0x6e, 0x63,
	0x68, 0x6f, 0x72, 0x46, 0x6f, 0x6c, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x46, 0x6f, 0x6c,
	0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb0, 0x08, 0x0a, 0x04, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x07, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x12, 0x1c, 0x0a, 0x09, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79,
	0x12, 0x1c, 0x0a, 0x09, 0x54, 0x65, 0x6c, 0x65, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x54, 0x65, 0x6c, 0x65, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x2f,
	0x0a, 0x0b, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x68, 0x75, 0x6d, 0x62, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x0b, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x68, 0x75, 0x6d, 0x62, 0x12,
	0x31, 0x0a, 0x0c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4d, 0x65, 0x64, 0x69,
	0x75, 0x6d, 0x12, 0x2f, 0x0a, 0x0b, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x61, 0x72, 0x67,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0b, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x61,
	0x72, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x51, 0x72, 0x63, 0x6f,
	0x64, 0x65, 0x55, 0x72, 0x69, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x55, 0x72, 0x69, 0x12, 0x2e, 0x0a, 0x12, 0x49,
	0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x53, 0x68, 0x61, 0x72, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x0e, 0x42,
	0x61, 0x64, 0x67, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x15, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x0e, 0x42, 0x61, 0x64, 0x67, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x32, 0x0a, 0x0a, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x46, 0x6f, 0x6c, 0x6c,
	0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x49, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x70, 0x65, 0x63, 0x69,
	0x61, 0x6c, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x42, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x05, 0x4d, 0x65, 0x64, 0x61, 0x6c,
	0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05, 0x4d, 0x65, 0x64, 0x61, 0x6c, 0x12, 0x3b, 0x0a, 0x11,
	0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x11, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65,
	0x49, 0x63, 0x6f, 0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x49, 0x64, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x55, 0x69,
	0x64, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x63, 0x55, 0x69, 0x64, 0x12,
	0x27, 0x0a, 0x0e, 0x66, 0x61, 0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0xfe, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x66, 0x61, 0x6e, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x05, 0x69, 0x64, 0x53, 0x74,
	0x72, 0x18, 0x84, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x64, 0x53, 0x74, 0x72, 0x12,
	0x1b, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x95, 0x08, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x61, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x98, 0x02, 0x0a,
	0x0a, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x66,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x66, 0x6f, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a,
	0x0a, 0x70, 0x75, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x70, 0x75, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a,
	0x0a, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a,
	0x10, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x12, 0x2c, 0x0a, 0x11, 0x66, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x22, 0x9b, 0x03, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x72, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x72, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x69, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x76, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x76, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x1c, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x6f, 0x70, 0x65, 0x6e, 0x57, 0x65, 0x62, 0x55, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x6e, 0x57, 0x65, 0x62, 0x55, 0x72, 0x6c, 0x12, 0x2e, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x69, 0x73, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x69, 0x73, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x12, 0x42, 0x0a,
	0x0f, 0x46, 0x6c, 0x65, 0x78, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x4e, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x0f, 0x46, 0x6c, 0x65, 0x78, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x42, 0x0a, 0x0f, 0x54, 0x65, 0x78, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x4e, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x0f, 0x54, 0x65, 0x78, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x3c, 0x0a, 0x10, 0x4e, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x80, 0x01, 0x0a, 0x0c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x6f, 0x6e, 0x74,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x6f, 0x6e,
	0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0f,
	0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x78, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x54, 0x65, 0x78, 0x74, 0x22, 0x86, 0x02, 0x0a, 0x09, 0x50, 0x75, 0x73, 0x68, 0x46,
	0x72, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x65, 0x71, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x05, 0x73, 0x65, 0x71, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x6f,
	0x67, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6c, 0x6f, 0x67, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x35, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e,
	0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0b, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x6e, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22,
	0x12, 0x0a, 0x02, 0x6b, 0x6b, 0x12, 0x0c, 0x0a, 0x01, 0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x01, 0x6b, 0x22, 0xb8, 0x02, 0x0a, 0x0f, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x68, 0x6f, 0x72, 0x74,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x13, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x45, 0x78,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x31,
	0x0a, 0x07, 0x45, 0x78, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0xe5, 0x01, 0x0a, 0x03, 0x52, 0x73, 0x70, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x0c, 0x0a, 0x01, 0x62, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x62, 0x12, 0x0c, 0x0a, 0x01, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x63, 0x12, 0x0c, 0x0a, 0x01, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x01,
	0x64, 0x12, 0x0c, 0x0a, 0x01, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x65, 0x12,
	0x1b, 0x0a, 0x01, 0x66, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x6f, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x52, 0x73, 0x70, 0x2e, 0x46, 0x52, 0x01, 0x66, 0x12, 0x0c, 0x0a, 0x01,
	0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x01, 0x67, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x01, 0x68, 0x12, 0x0c, 0x0a, 0x01, 0x69, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x01, 0x69, 0x12, 0x0c, 0x0a, 0x01, 0x6a, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x01, 0x6a, 0x1a, 0x43, 0x0a, 0x01, 0x46, 0x12, 0x0e, 0x0a, 0x02, 0x71, 0x31, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x71, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x71, 0x33, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x71, 0x33, 0x12, 0x0e, 0x0a, 0x02, 0x71, 0x34, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x71, 0x34, 0x12, 0x0e, 0x0a, 0x02, 0x71, 0x35, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x71, 0x35, 0x22, 0xba, 0x03, 0x0a, 0x0a, 0x50, 0x72,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x64,
	0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x73, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x66, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x72, 0x65, 0x66, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x62, 0x6f, 0x78, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x69, 0x6e, 0x62, 0x6f, 0x78,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x75, 0x69, 0x6c, 0x64,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0f, 0x73, 0x65, 0x6e, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x52, 0x0f, 0x73, 0x65, 0x6e, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x61, 0x61, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x61, 0x61, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x12, 0x2d, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0f, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x64, 0x6f, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x62, 0x69, 0x7a, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x69, 0x7a, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x35, 0x0a, 0x0b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x43, 0x0a,
	0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x61, 0x67, 0x12,
	0x19, 0x0a, 0x15, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x54, 0x59, 0x50, 0x45, 0x54, 0x41,
	0x47, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x4f,
	0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x54, 0x59, 0x50, 0x45, 0x54, 0x41, 0x47, 0x53, 0x54, 0x41, 0x52,
	0x10, 0x01, 0x42, 0x0c, 0x5a, 0x0a, 0x2e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protobuf_dy_proto_rawDescOnce sync.Once
	file_protobuf_dy_proto_rawDescData = file_protobuf_dy_proto_rawDesc
)

func file_protobuf_dy_proto_rawDescGZIP() []byte {
	file_protobuf_dy_proto_rawDescOnce.Do(func() {
		file_protobuf_dy_proto_rawDescData = protoimpl.X.CompressGZIP(file_protobuf_dy_proto_rawDescData)
	})
	return file_protobuf_dy_proto_rawDescData
}

var file_protobuf_dy_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_protobuf_dy_proto_msgTypes = make([]protoimpl.MessageInfo, 49)
var file_protobuf_dy_proto_goTypes = []interface{}{
	(CommentTypeTag)(0),                   // 0: douyin.CommentTypeTag
	(*Response)(nil),                      // 1: douyin.Response
	(*Message)(nil),                       // 2: douyin.Message
	(*ChatMessage)(nil),                   // 3: douyin.ChatMessage
	(*LandscapeAreaCommon)(nil),           // 4: douyin.LandscapeAreaCommon
	(*RoomUserSeqMessage)(nil),            // 5: douyin.RoomUserSeqMessage
	(*CommonTextMessage)(nil),             // 6: douyin.CommonTextMessage
	(*UpdateFanTicketMessage)(nil),        // 7: douyin.UpdateFanTicketMessage
	(*RoomUserSeqMessageContributor)(nil), // 8: douyin.RoomUserSeqMessageContributor
	(*GiftMessage)(nil),                   // 9: douyin.GiftMessage
	(*GiftStruct)(nil),                    // 10: douyin.GiftStruct
	(*GiftIMPriority)(nil),                // 11: douyin.GiftIMPriority
	(*TextEffect)(nil),                    // 12: douyin.TextEffect
	(*TextEffectDetail)(nil),              // 13: douyin.TextEffectDetail
	(*MemberMessage)(nil),                 // 14: douyin.MemberMessage
	(*PublicAreaCommon)(nil),              // 15: douyin.PublicAreaCommon
	(*EffectConfig)(nil),                  // 16: douyin.EffectConfig
	(*Text)(nil),                          // 17: douyin.Text
	(*TextPiece)(nil),                     // 18: douyin.TextPiece
	(*TextPieceImage)(nil),                // 19: douyin.TextPieceImage
	(*TextPiecePatternRef)(nil),           // 20: douyin.TextPiecePatternRef
	(*TextPieceHeart)(nil),                // 21: douyin.TextPieceHeart
	(*TextPieceGift)(nil),                 // 22: douyin.TextPieceGift
	(*PatternRef)(nil),                    // 23: douyin.PatternRef
	(*TextPieceUser)(nil),                 // 24: douyin.TextPieceUser
	(*TextFormat)(nil),                    // 25: douyin.TextFormat
	(*LikeMessage)(nil),                   // 26: douyin.LikeMessage
	(*SocialMessage)(nil),                 // 27: douyin.SocialMessage
	(*PicoDisplayInfo)(nil),               // 28: douyin.PicoDisplayInfo
	(*DoubleLikeDetail)(nil),              // 29: douyin.DoubleLikeDetail
	(*DisplayControlInfo)(nil),            // 30: douyin.DisplayControlInfo
	(*EpisodeChatMessage)(nil),            // 31: douyin.EpisodeChatMessage
	(*MatchAgainstScoreMessage)(nil),      // 32: douyin.MatchAgainstScoreMessage
	(*Against)(nil),                       // 33: douyin.Against
	(*Common)(nil),                        // 34: douyin.Common
	(*User)(nil),                          // 35: douyin.User
	(*FollowInfo)(nil),                    // 36: douyin.FollowInfo
	(*Image)(nil),                         // 37: douyin.Image
	(*NinePatchSetting)(nil),              // 38: douyin.NinePatchSetting
	(*ImageContent)(nil),                  // 39: douyin.ImageContent
	(*PushFrame)(nil),                     // 40: douyin.PushFrame
	(*Kk)(nil),                            // 41: douyin.kk
	(*SendMessageBody)(nil),               // 42: douyin.SendMessageBody
	(*ExtList)(nil),                       // 43: douyin.ExtList
	(*Rsp)(nil),                           // 44: douyin.Rsp
	(*PreMessage)(nil),                    // 45: douyin.PreMessage
	(*HeadersList)(nil),                   // 46: douyin.HeadersList
	nil,                                   // 47: douyin.Response.RouteParamsEntry
	nil,                                   // 48: douyin.EffectConfig.ExtraMapEntry
	(*Rsp_F)(nil),                         // 49: douyin.Rsp.F
}
var file_protobuf_dy_proto_depIdxs = []int32{
	2,  // 0: douyin.Response.messagesList:type_name -> douyin.Message
	47, // 1: douyin.Response.routeParams:type_name -> douyin.Response.RouteParamsEntry
	34, // 2: douyin.ChatMessage.common:type_name -> douyin.Common
	35, // 3: douyin.ChatMessage.user:type_name -> douyin.User
	37, // 4: douyin.ChatMessage.backgroundImage:type_name -> douyin.Image
	37, // 5: douyin.ChatMessage.backgroundImageV2:type_name -> douyin.Image
	15, // 6: douyin.ChatMessage.publicAreaCommon:type_name -> douyin.PublicAreaCommon
	37, // 7: douyin.ChatMessage.giftImage:type_name -> douyin.Image
	4,  // 8: douyin.ChatMessage.landscapeAreaCommon:type_name -> douyin.LandscapeAreaCommon
	17, // 9: douyin.ChatMessage.rtfContent:type_name -> douyin.Text
	0,  // 10: douyin.LandscapeAreaCommon.commentTypeTagsList:type_name -> douyin.CommentTypeTag
	34, // 11: douyin.RoomUserSeqMessage.common:type_name -> douyin.Common
	8,  // 12: douyin.RoomUserSeqMessage.ranksList:type_name -> douyin.RoomUserSeqMessageContributor
	8,  // 13: douyin.RoomUserSeqMessage.seatsList:type_name -> douyin.RoomUserSeqMessageContributor
	34, // 14: douyin.CommonTextMessage.common:type_name -> douyin.Common
	35, // 15: douyin.CommonTextMessage.user:type_name -> douyin.User
	34, // 16: douyin.UpdateFanTicketMessage.common:type_name -> douyin.Common
	35, // 17: douyin.RoomUserSeqMessageContributor.user:type_name -> douyin.User
	34, // 18: douyin.GiftMessage.common:type_name -> douyin.Common
	35, // 19: douyin.GiftMessage.user:type_name -> douyin.User
	35, // 20: douyin.GiftMessage.toUser:type_name -> douyin.User
	12, // 21: douyin.GiftMessage.textEffect:type_name -> douyin.TextEffect
	11, // 22: douyin.GiftMessage.priority:type_name -> douyin.GiftIMPriority
	10, // 23: douyin.GiftMessage.gift:type_name -> douyin.GiftStruct
	15, // 24: douyin.GiftMessage.publicAreaCommon:type_name -> douyin.PublicAreaCommon
	17, // 25: douyin.GiftMessage.trayDisplayText:type_name -> douyin.Text
	37, // 26: douyin.GiftStruct.image:type_name -> douyin.Image
	37, // 27: douyin.GiftStruct.giftLabelIcon:type_name -> douyin.Image
	37, // 28: douyin.GiftStruct.icon:type_name -> douyin.Image
	13, // 29: douyin.TextEffect.portrait:type_name -> douyin.TextEffectDetail
	13, // 30: douyin.TextEffect.landscape:type_name -> douyin.TextEffectDetail
	17, // 31: douyin.TextEffectDetail.text:type_name -> douyin.Text
	37, // 32: douyin.TextEffectDetail.background:type_name -> douyin.Image
	34, // 33: douyin.MemberMessage.common:type_name -> douyin.Common
	35, // 34: douyin.MemberMessage.user:type_name -> douyin.User
	35, // 35: douyin.MemberMessage.operator:type_name -> douyin.User
	16, // 36: douyin.MemberMessage.effectConfig:type_name -> douyin.EffectConfig
	16, // 37: douyin.MemberMessage.enterEffectConfig:type_name -> douyin.EffectConfig
	37, // 38: douyin.MemberMessage.backgroundImage:type_name -> douyin.Image
	37, // 39: douyin.MemberMessage.backgroundImageV2:type_name -> douyin.Image
	17, // 40: douyin.MemberMessage.anchorDisplayText:type_name -> douyin.Text
	15, // 41: douyin.MemberMessage.publicAreaCommon:type_name -> douyin.PublicAreaCommon
	37, // 42: douyin.PublicAreaCommon.userLabel:type_name -> douyin.Image
	37, // 43: douyin.EffectConfig.icon:type_name -> douyin.Image
	17, // 44: douyin.EffectConfig.text:type_name -> douyin.Text
	37, // 45: douyin.EffectConfig.textIcon:type_name -> douyin.Image
	37, // 46: douyin.EffectConfig.badge:type_name -> douyin.Image
	37, // 47: douyin.EffectConfig.textIconOverlay:type_name -> douyin.Image
	37, // 48: douyin.EffectConfig.animatedBadge:type_name -> douyin.Image
	37, // 49: douyin.EffectConfig.dynamicImage:type_name -> douyin.Image
	48, // 50: douyin.EffectConfig.extraMap:type_name -> douyin.EffectConfig.ExtraMapEntry
	25, // 51: douyin.Text.defaultFormat:type_name -> douyin.TextFormat
	18, // 52: douyin.Text.piecesList:type_name -> douyin.TextPiece
	25, // 53: douyin.TextPiece.format:type_name -> douyin.TextFormat
	24, // 54: douyin.TextPiece.userValue:type_name -> douyin.TextPieceUser
	22, // 55: douyin.TextPiece.giftValue:type_name -> douyin.TextPieceGift
	21, // 56: douyin.TextPiece.heartValue:type_name -> douyin.TextPieceHeart
	20, // 57: douyin.TextPiece.patternRefValue:type_name -> douyin.TextPiecePatternRef
	19, // 58: douyin.TextPiece.imageValue:type_name -> douyin.TextPieceImage
	37, // 59: douyin.TextPieceImage.image:type_name -> douyin.Image
	23, // 60: douyin.TextPieceGift.nameRef:type_name -> douyin.PatternRef
	35, // 61: douyin.TextPieceUser.user:type_name -> douyin.User
	34, // 62: douyin.LikeMessage.common:type_name -> douyin.Common
	35, // 63: douyin.LikeMessage.user:type_name -> douyin.User
	29, // 64: douyin.LikeMessage.doubleLikeDetail:type_name -> douyin.DoubleLikeDetail
	30, // 65: douyin.LikeMessage.displayControlInfo:type_name -> douyin.DisplayControlInfo
	28, // 66: douyin.LikeMessage.picoDisplayInfo:type_name -> douyin.PicoDisplayInfo
	34, // 67: douyin.SocialMessage.common:type_name -> douyin.Common
	35, // 68: douyin.SocialMessage.user:type_name -> douyin.User
	15, // 69: douyin.SocialMessage.publicAreaCommon:type_name -> douyin.PublicAreaCommon
	37, // 70: douyin.PicoDisplayInfo.emojiIcon:type_name -> douyin.Image
	2,  // 71: douyin.EpisodeChatMessage.common:type_name -> douyin.Message
	35, // 72: douyin.EpisodeChatMessage.user:type_name -> douyin.User
	37, // 73: douyin.EpisodeChatMessage.giftImage:type_name -> douyin.Image
	34, // 74: douyin.MatchAgainstScoreMessage.common:type_name -> douyin.Common
	33, // 75: douyin.MatchAgainstScoreMessage.against:type_name -> douyin.Against
	37, // 76: douyin.Against.leftLogo:type_name -> douyin.Image
	37, // 77: douyin.Against.rightLogo:type_name -> douyin.Image
	35, // 78: douyin.Common.user:type_name -> douyin.User
	37, // 79: douyin.User.AvatarThumb:type_name -> douyin.Image
	37, // 80: douyin.User.AvatarMedium:type_name -> douyin.Image
	37, // 81: douyin.User.AvatarLarge:type_name -> douyin.Image
	37, // 82: douyin.User.BadgeImageList:type_name -> douyin.Image
	36, // 83: douyin.User.FollowInfo:type_name -> douyin.FollowInfo
	37, // 84: douyin.User.AvatarBorder:type_name -> douyin.Image
	37, // 85: douyin.User.Medal:type_name -> douyin.Image
	37, // 86: douyin.User.RealTimeIconsList:type_name -> douyin.Image
	39, // 87: douyin.Image.content:type_name -> douyin.ImageContent
	38, // 88: douyin.Image.FlexSettingList:type_name -> douyin.NinePatchSetting
	38, // 89: douyin.Image.TextSettingList:type_name -> douyin.NinePatchSetting
	46, // 90: douyin.PushFrame.headersList:type_name -> douyin.HeadersList
	43, // 91: douyin.SendMessageBody.ext:type_name -> douyin.ExtList
	49, // 92: douyin.Rsp.f:type_name -> douyin.Rsp.F
	42, // 93: douyin.PreMessage.sendMessageBody:type_name -> douyin.SendMessageBody
	46, // 94: douyin.PreMessage.headers:type_name -> douyin.HeadersList
	95, // [95:95] is the sub-list for method output_type
	95, // [95:95] is the sub-list for method input_type
	95, // [95:95] is the sub-list for extension type_name
	95, // [95:95] is the sub-list for extension extendee
	0,  // [0:95] is the sub-list for field type_name
}

func init() { file_protobuf_dy_proto_init() }
func file_protobuf_dy_proto_init() {
	if File_protobuf_dy_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protobuf_dy_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LandscapeAreaCommon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomUserSeqMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonTextMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateFanTicketMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomUserSeqMessageContributor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftStruct); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftIMPriority); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextEffect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextEffectDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemberMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublicAreaCommon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EffectConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Text); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextPiece); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextPieceImage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextPiecePatternRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextPieceHeart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextPieceGift); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PatternRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextPieceUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextFormat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LikeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SocialMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PicoDisplayInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoubleLikeDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisplayControlInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EpisodeChatMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchAgainstScoreMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Against); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Common); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NinePatchSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushFrame); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Kk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMessageBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeadersList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protobuf_dy_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rsp_F); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protobuf_dy_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   49,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_protobuf_dy_proto_goTypes,
		DependencyIndexes: file_protobuf_dy_proto_depIdxs,
		EnumInfos:         file_protobuf_dy_proto_enumTypes,
		MessageInfos:      file_protobuf_dy_proto_msgTypes,
	}.Build()
	File_protobuf_dy_proto = out.File
	file_protobuf_dy_proto_rawDesc = nil
	file_protobuf_dy_proto_goTypes = nil
	file_protobuf_dy_proto_depIdxs = nil
}
