package main

import (
	"log"
	"net/http"
	"net/url"

	"github.com/gorilla/websocket"
)

func main111() {
	//wss://webcast5-ws-web-lf.douyin.com/webcast/im/push/v2/?
	u := url.URL{Scheme: "wss", Host: "webcast5-ws-web-lf.douyin.com", Path: "/webcast/im/push/v2/", RawQuery: "app_name=douyin_web&version_code=180800&webcast_sdk_version=1.0.8&update_version_code=1.0.8&compress=gzip&device_platform=web&cookie_enabled=true&screen_width=1792&screen_height=1120&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0%20(Macintosh;%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/*********%20Safari/537.36&browser_online=true&tz_name=Asia/Shanghai&cursor=h-1_t-1693278248912_r-1_d-1_u-1&internal_ext=internal_src:dim|wss_push_room_id:7272565214057646903|wss_push_did:7262176106497050112|dim_log_id:20230829110408F486BFAB212C9F00B6B2|first_req_ms:1693278248854|fetch_time:1693278248912|seq:1|wss_info:0-1693278248912-0-0|wrds_kvs:HighlightContainerSyncData-1_WebcastRoomRankMessage-1693278211290146604_WebcastRoomStreamAdaptationMessage-1693278244169205154_WebcastRoomStatsMessage-1693278247352867775_LotteryInfoSyncData-1693278248702070470&host=https://live.douyin.com&aid=6383&live_id=1&did_rule=3&endpoint=live_pc&support_wrds=1&user_unique_id=&im_path=/webcast/im/fetch/&identity=audience&room_id=7272565214057646903&heartbeatDuration=0&signature=WpmAtd3P5EfBZBqm"}

	log.Printf("connecting to %s", u.String())

	headers := http.Header{}
	headers.Add("Host", "webcast5-ws-web-lf.douyin.com")
	//headers.Add("Connection", "Upgrade")
	headers.Add("Pragma", "no-cache")
	headers.Add("Cache-Control", "no-cache")
	headers.Add("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	//headers.Add("Upgrade", "websocket")
	//headers.Add("Origin", "http://localhost:5005")
	//headers.Add("Sec-WebSocket-Version", "13")
	headers.Add("Accept-Encoding", "gzip, deflate, br")
	headers.Add("Accept-Language", "zh-CN,zh;q=0.9")
	//headers.Add("Sec-WebSocket-Key", "d0Mx5t34oAcKElPTxkshEQ==")
	//headers.Add("Sec-WebSocket-Extensions", "permessage-deflate; client_max_window_bits")

	conn, _, err := websocket.DefaultDialer.Dial("wss://webcast5-ws-web-lf.douyin.com/webcast/im/push/v2/?app_name=douyin_web&version_code=180800&webcast_sdk_version=1.0.8&update_version_code=1.0.8&compress=gzip&device_platform=web&cookie_enabled=true&screen_width=1792&screen_height=1120&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0%20(Macintosh;%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/*********%20Safari/537.36&browser_online=true&tz_name=Asia/Shanghai&cursor=h-1_t-1693278248912_r-1_d-1_u-1&internal_ext=internal_src:dim|wss_push_room_id:7272565214057646903|wss_push_did:7262176106497050112|dim_log_id:20230829110408F486BFAB212C9F00B6B2|first_req_ms:1693278248854|fetch_time:1693278248912|seq:1|wss_info:0-1693278248912-0-0|wrds_kvs:HighlightContainerSyncData-1_WebcastRoomRankMessage-1693278211290146604_WebcastRoomStreamAdaptationMessage-1693278244169205154_WebcastRoomStatsMessage-1693278247352867775_LotteryInfoSyncData-1693278248702070470&host=https://live.douyin.com&aid=6383&live_id=1&did_rule=3&endpoint=live_pc&support_wrds=1&user_unique_id=&im_path=/webcast/im/fetch/&identity=audience&room_id=7272565214057646903&heartbeatDuration=0&signature=WpmAtd3P5EfBZBqm", headers)
	if err != nil {
		log.Fatal(err)
	}
	defer conn.Close()

	c, _, err := websocket.DefaultDialer.Dial(u.String(), headers)
	if err != nil {
		log.Fatal("dial:", err)
	}
	defer c.Close()

	// 启动一个goroutine来接收来自WebSocket服务器的消息
	go func() {
		for {
			_, message, err := c.ReadMessage()
			if err != nil {
				log.Println("read:", err)
				return
			}
			log.Printf("recv: %s", message)
		}
	}()

	// 发送消息到WebSocket服务器
	err = c.WriteMessage(websocket.TextMessage, []byte("Hello, WebSocket server!"))
	if err != nil {
		log.Println("write:", err)
		return
	}

	// 保持主程序运行，防止退出
	select {}
}
