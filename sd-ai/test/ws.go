package main

import (
	"crypto/tls"
	"fmt"
	"golang.org/x/net/publicsuffix"
	"log"
	"net/http"
	"net/http/cookiejar"
	"time"

	"github.com/gorilla/websocket"
)

func main112() {
	//var dialer *websocket.Dialer

	// 创建一个 CookieJar
	jar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
	if err != nil {
		fmt.Println(err)
		return
	}
	dialer := websocket.Dialer{
		// 如果服务器使用自签名证书，取消对服务器证书的验证
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		Jar:             jar,
	}

	headers := http.Header{}
	headers.Add("Host", "webcast5-ws-web-lf.douyin.com")
	//headers.Add("Connection", "Upgrade")
	headers.Add("Pragma", "no-cache")
	headers.Add("Cache-Control", "no-cache")
	headers.Add("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	//headers.Add("Upgrade", "websocket")
	headers.Add("Origin", "https://live.douyin.com")
	//headers.Add("Sec-WebSocket-Version", "13")
	headers.Add("Accept-Encoding", "gzip, deflate, br")
	headers.Add("Accept-Language", "zh-CN,zh;q=0.9")
	//headers.Add("Sec-WebSocket-Key", "d0Mx5t34oAcKElPTxkshEQ==")
	//headers.Add("Sec-WebSocket-Extensions", "permessage-deflate; client_max_window_bits")

	wssUrl := "wss://webcast5-ws-web-lq.douyin.com/webcast/im/push/v2/?app_name=douyin_web&version_code=180800&webcast_sdk_version=1.0.8&update_version_code=1.0.8&compress=gzip&device_platform=web&cookie_enabled=true&screen_width=1792&screen_height=1120&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0%20(Macintosh;%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/*********%20Safari/537.36&browser_online=true&tz_name=Asia/Shanghai&cursor=t-1693368193220_r-1_d-1_u-1_h-1&internal_ext=internal_src:dim|wss_push_room_id:7272953108366510885|wss_push_did:7262176106497050112|dim_log_id:202308301203131295AA4A006FD0003EBA|first_req_ms:1693368193156|fetch_time:1693368193220|seq:1|wss_info:0-1693368193220-0-0|wrds_kvs:LotteryInfoSyncData-1693368192303866656_WebcastRoomStreamAdaptationMessage-1693368184864468212_WebcastRoomStatsMessage-1693368188929625124_WebcastRoomRankMessage-1693368182914496210&host=https://live.douyin.com&aid=6383&live_id=1&did_rule=3&endpoint=live_pc&support_wrds=1&user_unique_id=&im_path=/webcast/im/fetch/&identity=audience&room_id=7272953108366510885&heartbeatDuration=0&signature=RpvsHe6gj0fl2AML"
	ttwid := "1%7CPlF7QTQgFw_ZsJh9z8uJbQfuxk5-bHIcqRgFEI0C_zA%7C1690857158%7C5154e8723b5cb069ef48fd5c0d4cec1726007d53c1d8192ed6a854c5cfab1287"

	cookie := &http.Cookie{
		Name:  "ttwid",
		Value: ttwid,
	}
	headers.Add("Cookie", cookie.String())

	conn, _, err := dialer.Dial(wssUrl, headers)
	if err != nil {
		log.Fatal("Error connecting to WebSocket server:", err)
	}
	defer conn.Close()

	done := make(chan struct{})

	// goroutine to handle incoming messages
	go func() {
		defer close(done)
		for {
			_, message, err := conn.ReadMessage()
			if err != nil {
				log.Println("Error in receive:", err)
				return
			}
			log.Println("Received Message: ", string(message))
		}
	}()

	// Send message to server, similar to ws.send in JavaScript
	err = conn.WriteMessage(websocket.TextMessage, []byte("Hello WebSockets!"))
	if err != nil {
		log.Println("Error in send:", err)
		return
	}

	// Wait for receiving goroutine to stop
	<-done

	// Close the connection when you're done with it
	err = conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
	if err != nil {
		log.Println("Error in closing:", err)
		return
	}

	time.Sleep(time.Second)
}
