package config

import (
	"fmt"
	"gopkg.in/ini.v1"
)

var (
	AppMode  string
	Env      string
	RunTimer bool
	HttpPort string
	JwtKey   string

	Bundle       string
	CenterServer string
	SaveToDb     bool

	StaticFilePath    string
	DiffusionFilePath string
	DiffusionDomain   string
	Domain            string

	AccessKeyId     string
	AccessKeySecret string

	WeixinAppId  string
	WeixinSecret string

	Db            string
	DbHost        string
	DbPort        string
	DbUser        string
	DbPassWord    string
	DbName        string
	RedisAddr     string
	RedisPassWord string
)

func init() {
	file, err := ini.Load("./config/config.ini")
	if err != nil {
		fmt.Println("配置文件读取错误", err)
	} else {
		LoadServer(file)
		LoadDatabase(file)
	}
}

func LoadServer(file *ini.File) {
	Bundle = file.Section("server").Key("Bundle").MustString("")
	CenterServer = file.Section("server").Key("CenterServer").MustString("")
	SaveToDb = file.Section("server").Key("SaveToDb").MustBool(false)
	AppMode = file.Section("server").Key("AppMode").MustString("")
	Env = file.Section("server").Key("Env").MustString("")
	RunTimer = file.Section("server").Key("RunTimer").MustBool(false)
	HttpPort = file.Section("server").Key("HttpPort").MustString("")
	JwtKey = file.Section("server").Key("JwtKey").MustString("")

	AccessKeyId = file.Section("server").Key("AccessKeyId").MustString("")
	AccessKeySecret = file.Section("server").Key("AccessKeySecret").MustString("")

	StaticFilePath = file.Section("server").Key("StaticFilePath").MustString("")
	DiffusionFilePath = file.Section("server").Key("DiffusionFilePath").MustString("")
	DiffusionDomain = file.Section("server").Key("DiffusionDomain").MustString("")
	Domain = file.Section("server").Key("Domain").MustString("")
}

func LoadDatabase(file *ini.File) {
	Db = file.Section("database").Key("Db").MustString("")
	DbHost = file.Section("database").Key("DbHost").MustString("")
	DbPort = file.Section("database").Key("DbPort").MustString("")
	DbUser = file.Section("database").Key("DbUser").MustString("")
	DbPassWord = file.Section("database").Key("DbPassWord").MustString("")
	DbName = file.Section("database").Key("DbName").MustString("")
	RedisAddr = file.Section("database").Key("RedisAddr").MustString("")
	RedisPassWord = file.Section("database").Key("RedisPassWord").MustString("")
}
