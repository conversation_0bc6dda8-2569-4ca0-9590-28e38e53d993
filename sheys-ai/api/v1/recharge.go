package v1

import (
	"errors"
	"fmt"
	"net/http"

	"sheys-ai/enums"
	"sheys-ai/middleware"
	"sheys-ai/model"
	"sheys-ai/pay/alipay"
	"sheys-ai/pay/wechatpay"
	"sheys-ai/utils/errmsg"
	"sheys-ai/utils/logger"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

type rechargeApi struct {
}

type productListReq struct {
	Store     string `json:"store"`
	GroupName string `json:"group_name"`
}

// type rechargeReq struct {
// 	Amount     int    `json:"amount"`
// 	PayChannel string `json:"pay_channel"`
// 	Gateway    string `json:"gateway"`
// 	Coin       int    `json:"coin"`
// 	Price      string `json:"price"`
// }

type rechargeReq struct {
	ProductId  uint   `json:"product_id"`
	PayChannel string `json:"pay_channel"`
	Gateway    string `json:"gateway"`
}

type rechargeListReq struct {
	KW       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type rechargeListItemResp struct {
	UserId     uint            `json:"user_id"`
	CreatedAt  model.JsonTime  `json:"created_at"'`
	OutTradeNo string          `json:"out_trade_no"`
	Amount     decimal.Decimal `json:"amount"`
}

type rechargeProductResp struct {
	ID    uint            `json:"id"`
	Name  string          `json:"name"`
	Price decimal.Decimal `json:"price"`
}

func (obj rechargeApi) GetRechargeList(c *gin.Context) {
	var code int
	var recharge model.RechargeProduct

	arr, err := recharge.GetList("com.cyuai.sheys", "", "")
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "未查询到产品数据")
		return
	}

	resArr := make([]rechargeProductResp, 0)
	for _, product := range arr {
		resArr = append(resArr, rechargeProductResp{
			ID:    product.ID,
			Name:  product.ShowTitle,
			Price: product.Price,
		})
	}

	result := make(map[string]interface{})
	result["products"] = resArr
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "")
	}

}

func (obj rechargeApi) Launch(c *gin.Context) {

	var code int
	var oReq rechargeReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	outTradeNo, _ := model.Order.GenerateOrder(0)

	if outTradeNo == "" {
		logger.Error(errors.New("生成单号失败"), oReq)
		errmsg.Abort(c, errmsg.FAIL, "生成单号失败")
		return
	}

	var rechargeProd model.RechargeProduct
	rechargeProd.GetByID(oReq.ProductId)

	amount := rechargeProd.Amount
	if amount.LessThanOrEqual(decimal.Zero) {
		logger.Error(claims.UserId, " 充值金额不正确 ", oReq)
		errmsg.Abort(c, errmsg.FAIL, "充值金额不正确")
		return
	}

	fmt.Println(amount)

	if tmpKey := enums.PayGatewayEnum.GetKey(oReq.Gateway); tmpKey == "" {
		logger.Error("支付网关参数错误", oReq)
		errmsg.Abort(c, errmsg.FAIL, "支付网关参数错误")
		return
	}

	if tmpKey := enums.PayChannelEnum.GetKey(oReq.PayChannel); tmpKey == "" {
		logger.Error("支付渠道参数错误", oReq)
		errmsg.Abort(c, errmsg.FAIL, "支付渠道参数错误")
		return
	}

	recharge := model.Recharge{
		UserId:                claims.UserId,
		OutTradeNo:            outTradeNo,
		Amount:                amount,
		PayChannel:            oReq.PayChannel,
		Gateway:               oReq.Gateway,
		PayCallbackJson:       "{}",
		PayRefundCallbackJson: "{}",
	}

	var user model.User
	if user.GetByID(claims.UserId) != nil {
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	if err := recharge.Save(); err != nil || recharge.ID == 0 {
		errmsg.Abort(c, errmsg.FAIL, "充值订单生成失败")
		return
	}

	result := make(map[string]interface{})
	title := "自定义充值"
	if oReq.PayChannel == enums.PayChannelEnum.WechatPay {
		if oReq.Gateway == enums.PayGatewayEnum.H5 {
			h5Rsp, err := wechatpay.PayService.TradePayH5(recharge.OutTradeNo, rechargeProd.Price, title)
			if err != nil {
				logger.Error(err, h5Rsp)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_url"] = h5Rsp.Response.H5Url
		} else {
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		}
		//if oReq.Gateway == enums.PayGatewayEnum.Jsapi {
		//	/*
		//		resp, err := wechatpay.PayService.TradePayJsapi(user.Openid, recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
		//		if err != nil {
		//			logger.Error(err)
		//			errmsg.Abort(c, errmsg.FAIL, "发送充值订单失败")
		//			return
		//		}
		//		result["pay"] = resp*/
		//	logger.Info("weixin.PayService.PrepayWithRequestPayment")
		//	resp, _, errCreateOrder := weixin.PayService.PrepayWithRequestPayment(recharge.OutTradeNo, recharge.AmountCharge, user.Openid, rechargeShow.Remark, "")
		//	if errCreateOrder != nil {
		//		logger.Error(errCreateOrder)
		//		errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
		//		return
		//	}
		//
		//	if err := recharge.SetPrepayId(*resp.PrepayId); err != nil {
		//		logger.Error(errCreateOrder)
		//		errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
		//		return
		//	}
		//	result["pay"] = resp
		//} else if oReq.Gatway == enums.PayGatewayEnum.App {
		//	resp, err := wechatpay.PayService.TradePayApp(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
		//	if err != nil {
		//		logger.Error(err)
		//		errmsg.Abort(c, errmsg.FAIL, "发送充值订单失败")
		//		return
		//	}
		//	result["pay"] = resp
		//} else if oReq.Gatway == "native" {
		//	resp, err := wechatpay.PayService.TradePayNative(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
		//	if err != nil {
		//		logger.Error(err)
		//		errmsg.Abort(c, errmsg.FAIL, "发送充值订单失败")
		//		return
		//	}
		//	result["pay"] = resp
		//} else if oReq.Gatway == "h5" {
		//	resp, err := wechatpay.PayService.TradePayH5(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
		//	if err != nil {
		//		logger.Error(err)
		//		errmsg.Abort(c, errmsg.FAIL, "发送充值订单失败")
		//		return
		//	}
		//	result["pay"] = resp
		//} else {
		//	resp, _, errCreateOrder := weixin.PayService.PrepayWithRequestPayment(recharge.OutTradeNo, recharge.AmountCharge, user.Openid, rechargeShow.Remark, "")
		//	if errCreateOrder != nil {
		//		logger.Error(errCreateOrder)
		//		errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
		//		return
		//	}
		//
		//	if err := recharge.SetPrepayId(*resp.PrepayId); err != nil {
		//		logger.Error(errCreateOrder)
		//		errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
		//		return
		//	}
		//	result["pay"] = resp
		//}
	} else if oReq.PayChannel == enums.PayChannelEnum.AliPay {
		if oReq.Gateway == enums.PayGatewayEnum.Wap {
			payUrl, err := alipay.PayService.TradeWapPay(recharge.OutTradeNo, rechargeProd.Price, title)
			if err != nil {
				logger.Error(err, payUrl)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_url"] = payUrl
		} else if oReq.Gateway == enums.PayGatewayEnum.Web {
			payUrl, err := alipay.PayService.TradePagePay(recharge.OutTradeNo, rechargeProd.Price, title)
			if err != nil {
				logger.Error(err, payUrl)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_url"] = payUrl
		} else if oReq.Gateway == enums.PayGatewayEnum.Native {
			payRsp, err := alipay.PayService.TradePrecreate(recharge.OutTradeNo, rechargeProd.Price, title)
			if err != nil {
				logger.Error(err, payRsp)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_rsp"] = payRsp
		} else {
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		}

	}

	if err := recharge.GetByID(recharge.ID); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "充值订单生成失败")
		return
	}
	logger.Info("len(result)", len(result))
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "请充值",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "")
	}

}

func (obj rechargeApi) GetBalance(c *gin.Context) {
	var code int
	var msg string
	var listReq rechargeListReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if er := c.ShouldBindJSON(&listReq); er != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	if listReq.PageSize < 1 {
		listReq.PageSize = 1
	}
	if listReq.Page < 1 {
		listReq.Page = 1
	}

	var recharge model.Recharge
	ary := make([]rechargeListItemResp, 0)
	total, err := recharge.GetList(&ary, claims.UserId, listReq.KW, enums.RechargeStateEnum.TRADE_SUCCESS, listReq.Page, listReq.PageSize)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询出错")
		return
	}

	result := make(map[string]interface{})
	result["items"] = ary
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

//
//func (obj rechargeApi) GetList(c *gin.Context) {
//	var code int
//	var msg string
//	var listReq req.RechargeListReq
//
//	claims := c.Value("claims").(*middleware.MyClaims)
//	if claims.UserId <= 0 {
//		errmsg.Abort(c, errmsg.FAIL, "请先登录")
//		return
//	}
//
//	er := c.ShouldBindJSON(&listReq)
//	if er != nil {
//		code = errmsg.FAIL
//		msg = "数据获取失败"
//		errmsg.Abort(c, code, msg)
//		return
//	}
//
//	if listReq.PageSize < 1 {
//		listReq.PageSize = 1
//	}
//	if listReq.Page < 1 {
//		listReq.Page = 1
//	}
//
//	var o model.Recharge
//	data, total, err := o.GetList(claims.UserId, listReq.KW, 1, listReq.Page, listReq.PageSize)
//	if err != nil {
//		errmsg.Abort(c, errmsg.FAIL, "查询出错")
//		return
//	}
//
//	result := make(map[string]interface{})
//	result["items"] = data
//	result["total"] = total
//
//	if code == errmsg.SUCCESS {
//		c.JSON(http.StatusOK, gin.H{
//			"code":    code,
//			"message": "",
//			"msg":     "",
//			"result":  result,
//		})
//	} else {
//		errmsg.Abort(c, errmsg.FAIL, msg)
//	}
//}

var RechargeApi rechargeApi
