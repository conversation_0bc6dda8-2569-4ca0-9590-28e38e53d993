package main

//CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o sheys_ai.linux
import (
	"sheys-ai/enums"
	"sheys-ai/model"
	"sheys-ai/routes"
	"sheys-ai/service"
	"sheys-ai/utils/config"
	"sheys-ai/utils/logger"
	"sheys-ai/utils/myredis"

	"github.com/gin-gonic/gin"
)

func main() {
	defer func() {
		if e := recover(); e != nil {
			//logger.Error("main奔溃:", e)
		}
	}()

	gin.SetMode(config.AppMode)
	if enums.EnvEnum.GetKey(config.Env) == "" {
		logger.Fatal("运行环境参数值错误：", config.Env)
	}
	model.InitDb()
	myredis.InitRedis()

	logger.Info("启动main")
	if config.RunTimer == true {
		logger.Info("启动RunTimer")
		service.RunTimer()
		logger.Info("启动RunTimer完成")
	}
	routes.InitRouter()
	//logger.Info("main启动完成")
}
