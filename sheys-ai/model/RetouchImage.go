package model

import (
	"sheys-ai/enums"
	"time"

	"gorm.io/gorm"
)

type ImageResult struct {
	Filename      string    `json:"file_name"`
	State         int       `json:"state"`
	Width         int       `json:"width"`
	Height        int       `json:"height"`
	Size          int64     `json:"size"`
	Md5           string    `json:"md5"`
	CreatedAt     time.Time `json:"upload_time"`
	CompleteTime  time.Time `json:"complete_time"`
	DownloadCount int       `json:"download_count"`
}

type RetouchImage struct {
	gorm.Model
	Folder uint   `json:"folder" gorm:"type:varchar(50);not null;default:0;comment:文件夹id"`
	Md5    string `json:"md5" gorm:"type:varchar(50);not null;default:'';comment:图片md5"`
	// Uuid          string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:唯一字符串任务uuid"`
	UserId        uint      `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	MasterId      uint      `json:"master_id" gorm:"type:bigint;not null;default:0;comment:主账号ID"`
	Filename      string    `json:"file_name" gorm:"type:varchar(50);not null;default:'';comment:图片名称"`
	InputImgPath  string    `json:"input_img_path" gorm:"type:varchar(255);not null;default:'';comment:原始图片路径"`
	OutputImgPath string    `json:"output_img_path" gorm:"type:varchar(255);not null;default:'';comment:输出图片路径"`
	SmallImgPath  string    `json:"small_img_path" gorm:"type:varchar(255);not null;default:'';comment:输出图片小图路径"`
	Size          int64     `json:"size" gorm:"type:int;not null;default:0;comment:图片文件大小"`
	Width         int       `json:"width" gorm:"type:int;not null;default:0;comment:图片宽度"`
	Height        int       `json:"height" gorm:"type:int;not null;default:0;comment:图片高度"`
	PushJson      string    `json:"push_json" gorm:"type:json;';comment:推送给后端的绘图JSON模板"`
	State         int       `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 1上传成功 2校验失败 3非法图片 4校验通过 5修图中 200修图完成"`
	DownloadCount int       `json:"download_count" gorm:"type:int;not null;default:0;comment:下载次数"`
	CompleteTime  time.Time `json:"complete_time" gorm:"type:datetime(3);default:null;comment:修图完成时间"`
}

func (RetouchImage) TableName() string {
	return "T_RetouchImage"
}

func (o *RetouchImage) GetByID(id uint) error {
	return db.First(o, id).Error
}

func (o *RetouchImage) Save() error {
	return db.Debug().Save(o).Error
}

func (o *RetouchImage) GetByMd5(md5 string) error {
	return db.First(o, "md5 = ?", md5).Error
}

func (o *RetouchImage) SetPushJson(json string) error {
	return db.Model(o).Updates(RetouchImage{PushJson: json, State: enums.RetouchStateEnum.Drawing}).Error
}

func (o *RetouchImage) GetListByFolder(folder uint, page int, pageSize int) ([]ImageResult, int64, error) {
	// retouchImages := make([]RetouchImage,0)
	arr := make([]ImageResult, 0)
	var total int64
	tx := db.Debug().Model(o).Where("folder=?", folder)
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return nil, 0, err
		}
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(&arr)

	return arr, total, tx.Error
}

func (o *RetouchImage) GetCountByUser(userId uint) (int64, error) {
	var total int64
	tx := db.Debug().Model(o).Where("user_id=?", userId)
	if err := tx.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil

}
