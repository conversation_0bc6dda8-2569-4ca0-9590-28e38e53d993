package myimg

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"github.com/nfnt/resize"
	"image"
	"image/jpeg"
	"image/png"
	"io/ioutil"
	"log"
	"os"
)

type myimg_ struct {
}

func resizeImg() {
	// open "test.jpg"
	file, err := os.Open("test.jpg")
	if err != nil {
		log.Println(err)
	}
	// decode jpeg into image.Image
	img, err := jpeg.Decode(file)
	if err != nil {
		log.Println(err)
	}
	file.Close()
	// resize to width 1000 using Lanczos resampling
	//and preserve aspect ratio
	m := resize.Resize(1000, 0, img, resize.Lanczos3)
	out, err := os.Create("test_resized.jpg")
	if err != nil {
		log.Println(err)
	}
	defer out.Close()
	// write new image to file
	jpeg.Encode(out, m, nil)
}

func img2base64() (string, error) {
	f, err := os.Open("ubuntu.png")
	if err != nil {
		panic(err)
	}
	all, _ := ioutil.ReadAll(f)
	str := base64.StdEncoding.EncodeToString(all)
	fmt.Printf("%s", str)
	return str, nil
}

func readImage() {
	f, err := os.Open("ubuntu.png")
	if err != nil {
		panic(err)
	}
	// decode图片
	m, err := png.Decode(f)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%v", m.Bounds()) // 图片长宽

	fmt.Printf("%v", m.ColorModel()) // 图片颜色模型

	fmt.Printf("%v", m.At(100, 100)) // 该像素点的颜色

}

func Base64ToFile(base64Str string, filepath string) error { //base64Str 前面不需要加data:image/png;
	ddd, err := base64.StdEncoding.DecodeString(base64Str) //成图片文件并把文件写入到buffer
	err = ioutil.WriteFile(filepath, ddd, 0666)            //"./output.jpg"
	return err
}

func Base64ToBuffer(base64Str string) (*bytes.Buffer, error) {
	ddd, err := base64.StdEncoding.DecodeString(base64Str) //成图片文件并把文件写入到buffer
	bbb := bytes.NewBuffer(ddd)                            // 必须加一个buffer 不然没有read方法就会报错
	return bbb, err
}

func BufferToImg(bbb *bytes.Buffer) (image.Image, string, error) {
	m, str, err := image.Decode(bbb) // 图片文件解码
	return m, str, err
}

func Base64ToImg(base64Str string) (image.Image, error) {
	bbb, err := Base64ToBuffer(base64Str)
	if err != nil {
		return nil, err
	}
	img, _, err := BufferToImg(bbb)
	return img, err
}

func ImgToFile(subImg image.Image, filepath string) error {
	emptyBuff := bytes.NewBuffer(nil) //开辟一个新的空buff
	err := jpeg.Encode(emptyBuff, subImg, nil)
	err = ioutil.WriteFile(filepath, emptyBuff.Bytes(), 0666)
	return err
}

func ImgToBase64(subImg image.Image) (string, error) {

	/*
		emptyBuff := bytes.NewBuffer(nil)                  //开辟一个新的空buff
		err := jpeg.Encode(emptyBuff, subImg, nil)         //img写入到buff
		dist := make([]byte, 50000)                        //开辟存储空间
		base64.StdEncoding.Encode(dist, emptyBuff.Bytes()) //buff转成base64
		//fmt.Println(string(dist))                           //输出图片base64(type = []byte)
		//_ = ioutil.WriteFile("./base64pic.txt", dist, 0666) //buffer输出到jpg文件中（不做处理，直接写到文件）
		return string(dist), err*/

	var b bytes.Buffer
	err := png.Encode(&b, subImg)
	str := base64.StdEncoding.EncodeToString(b.Bytes())
	return str, err
}

func FileToBase64(filename string) (string, error) {
	b, err := ioutil.ReadFile(filename) //我还是喜欢用这个快速读文件
	if err != nil {
		return "", err
	}
	str := base64.StdEncoding.EncodeToString(b)
	return str, err
}

func FileToImg(filename string) (image.Image, string, error) {
	b, err := ioutil.ReadFile(filename) //我还是喜欢用这个快速读文件
	if err != nil {
		return nil, "", err
	}
	return BufferToImg(bytes.NewBuffer(b))
}

/*
func ImgFileToBase64(filename string) (string, error) {
	ff, err := ioutil.ReadFile(filename) //我还是喜欢用这个快速读文件
	if err != nil {
		return "", err
	}
	bufstore := make([]byte, 5000000)                         //数据缓存
	base64.StdEncoding.Encode(bufstore, ff)                   // 文件转base64
	_ = ioutil.WriteFile("./output2.jpg.txt", bufstore, 0666) //直接写入到文件就ok完活了。
	return string(bufstore), nil
}
*/

func CutImage(bbb *bytes.Buffer) (image.Image, error) { //裁剪图片
	m, _, err := image.Decode(bbb) // 图片文件解码
	rgbImg := m.(*image.YCbCr)
	subImg := rgbImg.SubImage(image.Rect(0, 0, 200, 200)).(*image.YCbCr) //图片裁剪x0 y0 x1 y1
	return subImg, err
}

func ResizeImg(w int, h int, subImg image.Image, full bool) image.Image {
	x := subImg.Bounds().Size().X
	y := subImg.Bounds().Size().Y

	floatX := float64(x)
	floatY := float64(y)
	floatW := float64(w)
	floatH := float64(h)

	nW := w
	nH := int(floatY / (floatX / floatW))
	if full {
		if nH < h {
			nH = h
			nW = int(floatX / (floatY / floatH))
		}
	}
	//logger.Info("resizeimg", x, y, w, h)
	return resize.Resize(uint(nW), uint(nH), subImg, resize.NearestNeighbor)
}
