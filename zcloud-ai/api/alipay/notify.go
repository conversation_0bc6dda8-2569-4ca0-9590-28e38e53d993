package alipay

import (
	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/alipay"
	"net/http"
	"time"
	"zcloud-ai/model"
	"zcloud-ai/service"
	"zcloud-ai/utils"
	"zcloud-ai/utils/logger"
)

func Notify(c *gin.Context) {

	//defer func() {
	//	if e := recover(); e != nil {
	//		logger.Error("Notify:", e)
	//	}
	//}()

	logger.Info("已经接收到支付宝回调v1.3")

	bm, err := alipay.ParseNotifyToBodyMap(c.Request)
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Info("notifyReq:", bm)

	ok, err := service.AlipayService.VerifySign(bm)
	if err != nil {
		logger.Error("err:", err)
		return
	}
	logger.Info("支付宝验签是否通过:", ok)

	tradeStatus := bm["trade_status"].(string)
	if tradeStatus == "TRADE_SUCCESS" {
		outTradeNo := bm["out_trade_no"].(string)
		tradeNo := bm["trade_no"].(string)

		payTime, err := time.ParseInLocation("2006-01-02 15:04:05", bm["gmt_payment"].(string), time.Local) //这里按照当前时区转
		logger.Info("payTime ", bm["gmt_payment"].(string), "  ", payTime)
		if err != nil {
			logger.Error(err)
			return
		}

		var recharge model.Recharge
		if err := recharge.GetByOutTradeNo(outTradeNo); err != nil || recharge.ID <= 0 {
			logger.Error(outTradeNo, err)
			return
		}
		//if !recharge.AmountCharge.Equal(totalAmount) {
		//	logger.Error(outTradeNo, " 订单金额不匹配 ", notifyStr)
		//	return
		//}

		if len(recharge.PayTradeId) == 0 {
			recharge.PayTradeId = tradeNo
		}
		recharge.PayTime = payTime
		recharge.PayCallbackJson = utils.GetJsonFromStruct(bm)
		err = model.Transactions.RechargeSuccess(&recharge)
		if err != nil {
			logger.Error(err)
			return
		} else {
			c.String(http.StatusOK, "%s", "success")
			//c.Writer.WriteString("success")
			return
		}
	} else {
		logger.Error("其它的支付状态:", tradeStatus, " bm: ", bm)
	}

}
