package alipay

import (
	"context"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/alipay"
	"github.com/shopspring/decimal"
	"zcloud-ai/utils/logger"
)

type payService_ struct {
	ctx             context.Context
	client          *alipay.Client
	appId           string //应用APPID
	privateKey      string //应用私钥
	alipayPublicKey string //支付宝平台获取的支付宝公钥
}

func (o *payService_) setUp() {
	if o.client == nil {
		o.ctx = context.Background()
		o.appId = "2021001191690325"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              // 商户号
		o.privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQChLfAt94P8gtvWoaNtPnW2Rvrma8NLV8ZQWGLbV2FY915mu8QSywnWFQwz/rE8kXZWW12GTs8OocgQmTOXCCasn0LkxXYdn5TLgZqZJKSB9+5DcOyy5ejWYb2nTliuR9aSP6/QpJKHqqAXvPU1EVUjC1OVZahabKWpN0e0BNS56RtfgdXgJiuAj5R0gFvcaAp8aXnCbjtnmLsCk8rIW4vzEiKbhx7DFNoIVeMPqoN5XGZF8/zx9w6WFyo3HcWdAD44OOz+//amr6B1dR9ZUlraJ1G79CIdjLYBoj15DHw1hF4mDvNm5nDa6UtPkM1EuMirHaPSyguxKawh1Ux+rnpnAgMBAAECggEAV6MUvsmgOEImqlME+zdE5bE+Zt8dNvAo4jLMvZqLUcZey9k4SUegt4iGNBc940LsZxQSjB55sukAURYb2PkTnjSSOLWs/Xpxex2jI7WqxQg7wuu5p6Qer8JBTqDo0wo4fxilzfpNufpqRb32Tb543i8gaVD28wO0J4fpMtaVm+doiAY/fI7TJfgZBAuDUcgp+13L59jExKyz868Pj8uYre2k/ebkkyFIjAXd9qdVHbviWSDtofio7NhXAJXzBpztmmVWlnnduX4TaPw6Wa3z/E89sLRJE/RWVK8k/yVfAEzuqXOPbwL4FVRdU8LlkVyEBXv/cU3r0sgN+95r6z8DUQKBgQDP5JKcH8zSmVKO89xWe1NhkfpZgduf/sX3RWqzSZGRD1nLpuSm2zqgDTiR60aBxkcb8qAKqwfs7uCfgY6u50dUZaNM4rmeGHgP4Z/TZ6htppkgD0jZ8P/bnqDR4BJR4VWkUY3+P67iCacvYDzXgdAzk6uAGTLX8fDdxyITMW2lewKBgQDGehhzplrB6lyYj59/428CZ6fJEWWO2wa+nvrUzkt6ZbGCFABgTGNyMdckbJxcWxlQGCK+bgc0z0bzl1Xn2c0rzoLwh/nvEW4HS4yC+aosWSJUyn9yILNX/LFRvzVnoFlvmcdmJ+AEsVt+QZloJUJJwjLXLPj/weUxAfG2jXkNBQKBgC/mPlFykkLDyN3fGhli+Oc2NzSAUxYeIYbDm74LaVq+6rWdknlPccaGxU35o5krEh9CW9WZao4Qy/h0ZWevKiQr9uA0LQhcIu2l0qlm7Mu24vkcDHEKsijr96njstTi0lS15b169tRl3PtxdRNCebIoiA9PvwM6X4D57MP3U4gZAoGBAKQO8H9IFuULcBa7QUBZIr1+KPbO5n+7FEwk5XlaPJGj1aKMHfN7sQgHoqp9DJJaywWlrbJzCInie+eGRIAtTKbVQO7JtxKh+a0WCcOJ09xsoTAtYkNYPGaSfY/w2Zgat1LEbkpG06mNYO9wEfpAMhb960Bp8DiYcXadAMvBDTpxAoGBAI8fiixBj3OIbB6qtMrb2UeMZnhDCJ/ojgNHxKoJmZKgbCs6yLPVq+7QtYYoORLXkS2E5BQ+qu70jFGiQAoU93hPQsT10ZzViQuS8eV3rvSIyoxTxBWLX4TYHttDI73298BahPzG5HMgz/DkSYgWUqPo7j+CXEA/Jxj7gpiu62ke" // 商户证书序列号
		o.alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnXBM9zrCYmTNCMZRd55j+jvmm4y+mDK3jJgKsviNi2kIOm2jM8a3cGQTCBZSbmZ/Uuu5xLD8T5MzbsHzvC0ToiqvtlcJHuOBfdKu2MXr69OTpg9jXc/zExGfcVly4hXw1ZIwPCJAcJ3Q0olOb7kRPr9UZGwq6069xM9KUw+ewRKEVcsKhw317lDm0fgU4k2JD62vNc8JtU4WMpy+G74eVnc2zZK8z7FqoOvYXFNzxpdK0DpWSMOF7mu29xYOqcKnjOxIvE1LOkqo22E9Ul5GyX1Bf3CcECD237oxsPlzIOsyrvRBoQe7/mGgtuPLqiyIlhP2ziQ/wkK4/XNypsXmxQIDAQAB"
		//初始化支付宝客户端
		//    appId：应用ID
		//    privateKey：应用秘钥
		//    isProd：是否是正式环境
		client, err := alipay.NewClient(o.appId, o.privateKey, true)
		if err != nil {
			logger.Error(err)
			return
		}
		// 打开Debug开关，输出日志，默认关闭
		client.DebugSwitch = gopay.DebugOn

		// 设置支付宝请求 公共参数
		//    注意：具体设置哪些参数，根据不同的方法而不同，此处列举出所有设置参数
		client.SetLocation(alipay.LocationShanghai). // 设置时区，不设置或出错均为默认服务器时间
								SetCharset(alipay.UTF8).                                       // 设置字符编码，不设置默认 utf-8
								SetSignType(alipay.RSA2).                                      // 设置签名类型，不设置默认 RSA2
								SetReturnUrl("https://aigc.cyuai.com/api/v1/alipay/callback"). // 设置返回URL
								SetNotifyUrl("https://aigc.cyuai.com/api/v1/alipay/notify")    // 设置异步通知URL
		//SetAppAuthToken()                           // 设置第三方应用授权

		client.AutoVerifySign([]byte(o.alipayPublicKey))
		o.client = client
	}
}

func (o *payService_) TradePagePay(outTradeNo string, amount decimal.Decimal, desc string) (string, error) {
	o.setUp()
	//请求参数
	bm := make(gopay.BodyMap)
	bm.Set("subject", desc) //电脑网站测试支付
	bm.Set("out_trade_no", outTradeNo)
	bm.Set("total_amount", amount.String())
	bm.Set("product_code", "FAST_INSTANT_TRADE_PAY")
	////电脑网站支付请求
	payUrl, err := o.client.TradePagePay(o.ctx, bm)
	if err != nil {
		logger.Error("err:", err)
	}
	return payUrl, err
}

func (o *payService_) TradeWapPay(outTradeNo string, amount decimal.Decimal, desc string) (string, error) {
	o.setUp()
	//请求参数
	bm := make(gopay.BodyMap)
	bm.Set("subject", desc) //手机网站测试支付
	bm.Set("out_trade_no", outTradeNo)
	//bm.Set("quit_url", "https://www.fmm.ink")
	bm.Set("total_amount", amount.String())
	bm.Set("product_code", "QUICK_WAP_WAY")
	//手机网站支付请求
	payUrl, err := o.client.TradeWapPay(o.ctx, bm)
	if err != nil {
		logger.Error("err:", err)
	}
	return payUrl, err
	//https://openapi.alipay.com/gateway.do?app_id=2021001191690325&biz_content=%7B%22out_trade_no%22%3A%22r2022121615331500000029%22%2C%22product_code%22%3A%22QUICK_WAP_WAY%22%2C%22subject%22%3A%22%E6%94%AF%E4%BB%98%E6%B5%8B%E8%AF%95%22%2C%22total_amount%22%3A%220.01%22%7D&charset=utf-8&format=JSON&method=alipay.trade.wap.pay&sign=QjuSgEBe7mVgJQejpDZgYuTzGv5DPJvJIiLkoF2Ql5aPI%2BYi99Ip1FXs5A7T18IVA%2FXk9s9FjyAM5jdIlrMZLplOx6KGkKH81lnHA88BX7dTHbyNjGQnIZTpw2O8SGRc4DP%2Fls%2BPJ4V52zeEvoao25nVqrwAUrSFpE%2FVyujED57AVfgOG1e8SWa480Dxf%2BROKNmApTAL1WRTRe637kf1kTUCTK2r%2FJI8sinR%2BCFY6n8X%2Bf90RzEN%2BMgnIi5C1pj3JSQkeve5oabrdwBEg%2BDAKMxAk6f%2Fn1uGtJwN9LOfIwnM%2BHgE6FY2kvJCy4aiYrQg%2FjAVlso2Q%2FdzGqjs2Azpbg%3D%3D&sign_type=RSA2&timestamp=2022-12-16+15%3A33%3A47&version=1.0
}

func (o *payService_) VerifySign(notifyBean interface{}) (bool, error) {
	ok, err := alipay.VerifySign(o.alipayPublicKey, notifyBean)
	if err != nil {
		logger.Error("VerifySign(%+v),error:%+v", notifyBean, err)
	}
	return ok, err
}

var PayService payService_
