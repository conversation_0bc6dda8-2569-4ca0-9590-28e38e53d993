package auth

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"zcloud-ai/enums"
	"zcloud-ai/global"
	"zcloud-ai/middleware"
	"zcloud-ai/model"
	"zcloud-ai/utils/config"
	"zcloud-ai/utils/errmsg"
	"zcloud-ai/utils/logger"
)

type handrawApi struct {
}

type handrawListReq struct {
	UserId    uint `json:"user_id"`
	HandrawId uint `json:"handraw_id"`
	Page      int  `json:"page"`
	PageSize  int  `json:"page_size"`
}

type handrawListItem struct {
	ID            uint            `json:"id"`
	CreatedAt     global.JsonTime `json:"created_at"`
	UserId        uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	HandUuid      string          `json:"hand_uuid" gorm:"type:varchar(50);not null;default:'';comment:唯一字符串"`
	HandFrom      int             `json:"hand_from" gorm:"type:int;not null;default:0;comment:来源 1手绘 2儿童版 3AI猜画"`
	HandFromTxt   string          `json:"hand_from_txt"`
	InputImgMd5   string          `json:"input_img_md5" gorm:"type:varchar(50);not null;default:'';comment:手绘图片索引"`
	InputImgPath  string          `json:"-" gorm:"type:varchar(100);not null;default:'';comment:手绘图片路径"`
	InputImgUrl   string          `json:"input_img_url"`
	Width         int             `json:"width" gorm:"type:int;not null;default:0;comment:参考图宽度"`
	Height        int             `json:"height" gorm:"type:int;not null;default:0;comment:参考图高度"`
	DrawType      int             `json:"draw_type" gorm:"type:int;not null;default:0;comment:绘画类型 0路径 1图片"`
	DrawTypeTxt   string          `json:"draw_type_txt"`
	Prompt        string          `json:"prompt" gorm:"type:varchar(200);not null;default:'';comment:简要画面描述"`
	GuessUserId   uint            `json:"guess_user_id" gorm:"type:bigint;not null;default:0;comment:画面描述用户ID"`
	BatchCount    int             `json:"batch_count" gorm:"type:int;not null;default:0;comment:生成的图片张数"`
	PushJson      string          `json:"push_json" gorm:"type:json;';comment:推送的Json字符串"`
	OutputImgMd5  string          `json:"output_img_md5" gorm:"type:varchar(50);not null;default:'';comment:生成图片索引"`
	OutputImgPath string          `json:"-" gorm:"type:varchar(100);not null;default:'';comment:手绘图片输出路径"`
	OutputImgUrl  string          `json:"output_img_url"`
	PriceCoin     int             `json:"price_coin" gorm:"type:int;not null;default:0;comment:所需Coin"`
	OrderNo       string          `json:"order_no" gorm:"type:varchar(50);comment:订单编号"`
	State         int             `json:"state" gorm:"type:int;not null;default:0;comment:状态(1手绘图上传成功 2生成中 3生成完成)"`
	StateTxt      string          `json:"state_txt"`
}

func (obj handrawApi) GetList(c *gin.Context) {
	var code int
	var req handrawListReq
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var handraw model.Handraw
	var arr = make([]handrawListItem, 0)
	total, err := handraw.GetListManage(&arr, req.UserId, req.HandrawId, req.Page, req.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
		return
	}
	for i := 0; i < len(arr); i++ {
		if arr[i].InputImgPath != "" {
			arr[i].InputImgUrl = config.DiffusionDomain + arr[i].InputImgPath
		}
		if arr[i].OutputImgPath != "" {
			arr[i].OutputImgUrl = config.DiffusionDomain + arr[i].OutputImgPath
		}
		if arr[i].HandFrom == 1 {
			arr[i].HandFromTxt = "手绘"
		} else if arr[i].HandFrom == 2 {
			arr[i].HandFromTxt = "儿童版"
		} else if arr[i].HandFrom == 3 {
			arr[i].HandFromTxt = "AI猜画"
		}

		if arr[i].DrawType == 0 {
			arr[i].DrawTypeTxt = "路径"
		} else if arr[i].DrawType == 1 {
			arr[i].DrawTypeTxt = "图片"
		}
		arr[i].StateTxt = enums.HandrawStateEnum.GetKey(arr[i].State)
	}
	result := make(map[string]interface{})
	result["items"] = arr
	result["total"] = total
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

var HandrawApi handrawApi
