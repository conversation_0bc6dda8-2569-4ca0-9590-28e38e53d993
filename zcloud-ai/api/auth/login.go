package auth

import (
	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
	"zcloud-ai/middleware"
	"zcloud-ai/model"
	"zcloud-ai/model/req"
	"zcloud-ai/utils"
	"zcloud-ai/utils/errmsg"
)

type loginReq struct {
	Username  string `json:"username"`
	Password  string `json:"password"`
	Type      string `json:"type"`
	AutoLogin bool   `json:"autoLogin"`
}

// Login 后台登录
func Login(c *gin.Context) {
	var code int
	var msg string
	var token string

	var oReq loginReq
	var user model.User

	er := c.ShouldBindJSON(&oReq)
	if er != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if len(oReq.Username) < 2 {
		errmsg.Abort(c, errmsg.FAIL, "用户名不正确")
		return
	}

	if len(oReq.Password) < 5 {
		errmsg.Abort(c, errmsg.FAIL, "密码不正确")
		return
	}

	if utils.IsMobile(oReq.Username) {
		user.GetByMobile(oReq.Username)
		if user.ID == 0 {
			errmsg.Abort(c, errmsg.FAIL, "用户不存在")
			return
		}
	} else {
		user.GetByUsername(oReq.Username)
		if user.ID == 0 {
			errmsg.Abort(c, errmsg.FAIL, "用户不存在")
			return
		}
	}

	err := user.CheckPassword(oReq.Password)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "密码错误")
		return
	}

	if user.ID != 1 {
		errmsg.Abort(c, errmsg.FAIL, "账号密码错误")
		return
	}

	token, code, msg = setToken(user)

	loginResp := req.LoginResp{
		UserId:   user.ID,
		Username: user.Username,
		Token:    token,
		Mobile:   utils.FormatMobileStar(user.Mobile),
	}
	//{"status":"ok","type":"account","currentAuthority":"admin"}
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":             code,
			"message":          "ok",
			"msg":              "ok",
			"result":           loginResp,
			"status":           "ok",
			"type":             "account",
			"currentAuthority": "admin",
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":             code,
			"result":           nil,
			"message":          msg,
			"msg":              msg,
			"status":           "error",
			"type":             "account",
			"currentAuthority": "guest",
		})
	}
}

// token生成函数
func setToken(user model.User) (string, int, string) {
	j := middleware.NewJWT()
	claims := middleware.MyClaims{
		UserId:   user.ID,
		Username: user.Username,
		Mobile:   utils.FormatMobileStar(user.Mobile),
		StandardClaims: jwt.StandardClaims{
			NotBefore: time.Now().Unix() - 100,
			ExpiresAt: time.Now().Unix() + ***********,
			Issuer:    "zcloud",
		},
	}

	token, err := j.CreateToken(claims)
	if err != nil {
		return "", errmsg.FAIL, "生成token失败"
	}

	return token, errmsg.SUCCESS, ""
}
