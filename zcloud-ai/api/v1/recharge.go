package v1

import (
	"encoding/base64"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/skip2/go-qrcode"
	"net/http"
	"strings"
	"zcloud-ai/api/wechatpay"
	"zcloud-ai/api/weixin"
	"zcloud-ai/enums"
	"zcloud-ai/middleware"
	"zcloud-ai/model"
	"zcloud-ai/model/req"
	"zcloud-ai/service"
	"zcloud-ai/utils/errmsg"
	"zcloud-ai/utils/logger"
)

type rechargeApi struct {
}

type priceListReq struct {
	Plat string `json:"plat"`
}

type queryOrderReq struct {
	OutTradeNo string `json:"out_trade_no"`
}

func (obj rechargeApi) GetPriceList(c *gin.Context) {
	var code int

	var oReq priceListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
	}

	ary := service.RechargeService.GetListByPlat(oReq.Plat)
	c.JSON(http.StatusOK, gin.H{
		"code":   code,
		"msg":    "充值价格表",
		"result": ary,
	})
}

func (obj rechargeApi) Recharge(c *gin.Context) {

	//defer func() {
	//	if e := recover(); e != nil {
	//		logger.Error("Recharge:", e)
	//	}
	//}()

	var code int
	var oReq req.RechargeReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	oReq.Gatway = strings.ToLower(oReq.Gatway)
	checkJsonKey := ""
	if oReq.PayType == enums.PayTypeEnum.AppleIap {
		checkJsonKey = "ios"
	}

	rechargeShow := req.RechargeShow{}
	if oReq.ProductId == "CM002" || oReq.ProductId == "CY002" {
		oReq.Category = enums.ProductCategoryEnum.Subscription
		tmp := service.RechargeService.GetByProductId("jsonChildrenSubStr", oReq.ProductId)
		if tmp == nil {
			logger.Error("未找到充值产品 productId:", oReq.ProductId)
			errmsg.Abort(c, errmsg.FAIL, "充值产品为找到")
			return
		}
		rechargeShow = *tmp
	} else {

		tmp, errRechargeShow := service.RechargeService.Check(checkJsonKey, oReq.Coin, oReq.Price)
		if errRechargeShow != nil {
			logger.Error(claims.UserId, " 充值参数错误 ", oReq)
			errmsg.Abort(c, errmsg.FAIL, "充值参数错误")
			return
		}
		rechargeShow = tmp
	}

	tradeNo, errOrderNo := service.OrderNo.NewByOrderType(enums.OrderTypeEnum.RechargeBuy, 0)
	if errOrderNo != nil || len(tradeNo) <= 0 {
		logger.Error(errOrderNo)
		errmsg.Abort(c, errmsg.FAIL, "充值单号生成失败")
		return
	}

	amount, err := decimal.NewFromString(oReq.Price)
	if err != nil || amount.LessThanOrEqual(decimal.Zero) {
		logger.Error(claims.UserId, " 充值金额不正确 ", oReq)
		errmsg.Abort(c, errmsg.FAIL, "充值金额不正确")
		return
	}

	if len(oReq.PayType) == 0 {
		oReq.PayType = "1"
	}

	if oReq.Category == "" {
		oReq.Category = enums.ProductCategoryEnum.Consumable
	}

	if enums.ProductCategoryEnum.GetKey(oReq.Category) == "" {
		logger.Error(claims.UserId, " 产品类型不正确 ", oReq)
		errmsg.Abort(c, errmsg.FAIL, "产品类型不正确")
	}

	//if oReq.PayType != enums.PayTypeEnum.AliPay && oReq.PayType != enums.PayTypeEnum.WechatPay {
	//	errmsg.Abort(c, errmsg.FAIL, "支付渠道参数错误")
	//	return
	//}

	if tmpKey := enums.PayTypeEnum.GetKey(oReq.PayType); tmpKey == "" {
		errmsg.Abort(c, errmsg.FAIL, "支付渠道参数错误")
		return
	}

	if oReq.PayType == enums.PayTypeEnum.WechatPay && len(oReq.Gatway) == 0 {
		oReq.Gatway = enums.PayGatewayEnum.Jsapi
	}

	if len(enums.PayGatewayEnum.GetKey(oReq.Gatway)) == 0 {
		errmsg.Abort(c, errmsg.FAIL, "支付网关参数错误")
		return
	}

	recharge := model.Recharge{
		UserId:                claims.UserId,
		OutTradeNo:            tradeNo,
		CoinCharge:            oReq.Coin,
		AmountCharge:          amount,
		PayType:               oReq.PayType,
		Gatway:                oReq.Gatway,
		ProductCategory:       oReq.Category,
		ProductId:             oReq.ProductId,
		PayCallbackJson:       "{}",
		PayRefundCallbackJson: "{}",
	}

	var user model.User
	if user.GetByID(claims.UserId) != nil {
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	if err := recharge.Save(); err != nil || recharge.ID == 0 {
		errmsg.Abort(c, errmsg.FAIL, "充值订单生成失败")
		return
	}

	result := make(map[string]interface{})
	if oReq.PayType == enums.PayTypeEnum.WechatPay {
		if oReq.Gatway == enums.PayGatewayEnum.Web {
			logger.Info("发起微信native支付")
			nativeRsp, err := service.WechatpayService.TradePayNative(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
			if err != nil {
				logger.Error(err, nativeRsp)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_url"] = nativeRsp.Response.CodeUrl

			qrCode, err := qrcode.New(nativeRsp.Response.CodeUrl, qrcode.Medium)
			if err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "生成二维码失败")
				return
			}
			pngBytes, err := qrCode.PNG(256)
			if err != nil {
				fmt.Println(err)
				return
			}
			result["qr_code"] = "data:image/png;base64," + base64.StdEncoding.EncodeToString(pngBytes)
			result["trade_no"] = recharge.OutTradeNo
			result["out_trade_no"] = recharge.OutTradeNo
		} else if oReq.Gatway == enums.PayGatewayEnum.Jsapi {
			/*
				resp, err := wechatpay.PayService.TradePayJsapi(user.Openid, recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
				if err != nil {
					logger.Error(err)
					errmsg.Abort(c, errmsg.FAIL, "发送充值订单失败")
					return
				}
				result["pay"] = resp*/
			logger.Info("weixin.PayService.PrepayWithRequestPayment")
			resp, _, errCreateOrder := weixin.PayService.PrepayWithRequestPayment(recharge.OutTradeNo, recharge.AmountCharge, user.Openid, rechargeShow.Remark, "")
			if errCreateOrder != nil {
				logger.Error(errCreateOrder)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}

			if err := recharge.SetPrepayId(*resp.PrepayId); err != nil {
				logger.Error(errCreateOrder)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay"] = resp
		} else if oReq.Gatway == enums.PayGatewayEnum.App {
			//resp, err := wechatpay.PayService.TradePayApp(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
			//if err != nil {
			//	logger.Error(err)
			//	errmsg.Abort(c, errmsg.FAIL, "发送充值订单失败")
			//	return
			//}
			//result["pay"] = resp

			prepayRsp, err := service.WechatpayService.TradePayApp(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
			if err != nil {
				logger.Error(err, prepayRsp)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["prepayid"] = prepayRsp.Response.PrepayId
			payParams, err := service.WechatpayService.PaySignOfApp(prepayRsp.Response.PrepayId)
			if err != nil {
				logger.Error(err)
			}
			result["payParams"] = payParams
		} else if oReq.Gatway == "native" {
			resp, err := wechatpay.PayService.TradePayNative(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
			if err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "发送充值订单失败")
				return
			}
			result["pay"] = resp
		} else if oReq.Gatway == "h5" {
			//resp, err := wechatpay.PayService.TradePayH5(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
			//if err != nil {
			//	logger.Error(err)
			//	errmsg.Abort(c, errmsg.FAIL, "发送充值订单失败")
			//	return
			//}
			//result["pay"] = resp
			h5Rsp, err := service.WechatpayService.TradePayH5(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
			if err != nil {
				logger.Error(err, h5Rsp)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_url"] = h5Rsp.Response.H5Url
		} else {
			resp, _, errCreateOrder := weixin.PayService.PrepayWithRequestPayment(recharge.OutTradeNo, recharge.AmountCharge, user.Openid, rechargeShow.Remark, "")
			if errCreateOrder != nil {
				logger.Error(errCreateOrder)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}

			if err := recharge.SetPrepayId(*resp.PrepayId); err != nil {
				logger.Error(errCreateOrder)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay"] = resp
		}
	} else if oReq.PayType == enums.PayTypeEnum.AliPay {
		if oReq.Gatway == enums.PayGatewayEnum.Wap {
			//payUrl, err := alipay.PayService.TradeWapPay(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
			//if err != nil {
			//	logger.Error(err, payUrl)
			//	errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
			//	return
			//}
			//result["pay_url"] = payUrl

			payUrl, err := service.AlipayService.TradeWapPay(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
			if err != nil {
				logger.Error(err, payUrl)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_url"] = payUrl

		} else if oReq.Gatway == enums.PayGatewayEnum.Web {
			//payUrl, err := alipay.PayService.TradePagePay(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
			//if err != nil {
			//	logger.Error(err, payUrl)
			//	errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
			//	return
			//}
			//result["pay_url"] = payUrl

			payUrl, err := service.AlipayService.TradePagePay(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
			if err != nil {
				logger.Error(err, payUrl)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_url"] = payUrl

		} else if oReq.Gatway == enums.PayGatewayEnum.App {
			payParam, err := service.AlipayService.TradeAppPay(recharge.OutTradeNo, recharge.AmountCharge, rechargeShow.Remark)
			if err != nil {
				logger.Error(err, payParam)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["pay_param"] = payParam
		}

	} else if oReq.PayType == enums.PayTypeEnum.AppleIap {
		result["out_trade_no"] = recharge.OutTradeNo
	}

	respRecharge, errResult := recharge.Get(recharge.ID)

	if errResult != nil || respRecharge.ID <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "充值订单生成失败")
		return
	}

	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "请充值",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "")
	}

}

func (obj rechargeApi) QueryByOutTradeNo(c *gin.Context) {
	var code int

	var oReq queryOrderReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if er := c.ShouldBindJSON(&oReq); er != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var recharge model.Recharge
	if err := recharge.GetByOutTradeNo(oReq.OutTradeNo); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询订单出错")
		return
	}
	if recharge.UserId != claims.UserId {
		errmsg.Abort(c, errmsg.FAIL, "查询支付订单失败")
		return
	}

	result := make(map[string]interface{})
	result["out_trade_no"] = recharge.OutTradeNo
	result["state"] = recharge.State
	result["state_txt"] = enums.RechargeStateEnum.GetShowTitle(recharge.State)

	c.JSON(http.StatusOK, gin.H{
		"code":   code,
		"msg":    "订单信息",
		"result": result,
	})

}

func (obj rechargeApi) GetList(c *gin.Context) {
	var code int
	var msg string
	var listReq req.RechargeListReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	er := c.ShouldBindJSON(&listReq)
	if er != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if listReq.PageSize < 1 {
		listReq.PageSize = 1
	}
	if listReq.Page < 1 {
		listReq.Page = 1
	}

	var o model.Recharge
	data, total, err := o.GetList(claims.UserId, listReq.KW, 1, listReq.Page, listReq.PageSize)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询出错")
		return
	}

	result := make(map[string]interface{})
	result["items"] = data
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"message": "",
			"msg":     "",
			"result":  result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

var RechargeApi rechargeApi
