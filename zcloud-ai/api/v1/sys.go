package v1

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"zcloud-ai/enums"
	"zcloud-ai/middleware"
	"zcloud-ai/model/req"
	"zcloud-ai/service"
	"zcloud-ai/utils/errmsg"
	"zcloud-ai/utils/logger"
	"zcloud-ai/utils/myredis"
)

type sysApi struct {
}

func (obj sysApi) RefreshWeixinAccessToken(c *gin.Context) {
	var code int

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	service.AccessTokenUpdate.Run(true)
	c.JSON(http.StatusOK, gin.H{
		"code":   code,
		"msg":    "已强制更新",
		"result": "",
	})
}

func (obj sysApi) AnimeToTxt(c *gin.Context) {
	var code int
	var msg string
	var oReq req.IdReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	var aa uint
	aa, err = service.AnimeToTxt2img.CostAndCreateTxt2img(oReq.Id)

	if err != nil {
		errmsg.Abort(c, 1, err.Error())
		return
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": aa,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  msg,
		})
	}
}

func (obj sysApi) TxtToImg(c *gin.Context) {
	var code int
	var msg string
	var oReq req.IdReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	var aa uint
	aa, err = service.Diffusion.Txt2Img(oReq.Id, true)

	if err != nil {
		errmsg.Abort(c, 1, err.Error())
		return
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": aa,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  msg,
		})
	}
}

func (obj sysApi) DiffusionOut(c *gin.Context) {
	var code int
	var msg string
	var oReq req.IdReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}
	/*
		value, err := service.DiffusionOut.PopRedisQueue()
		if err != nil {
			if len(value) > 0 {
				service.DiffusionOut.PushRedisQueueErr(value)
			}
			logger.Error("DiffusionOut.Run resp:", err)
		}
		logger.Info(value)*/

	value := `{"output_md5":"6703ca354926bb379d069f0c0ba64394","output_path":"/root/stable-diffusion-webui/aigc-output/20221218/6703ca354926bb379d069f0c0ba64394.png","batch_num":0,"out_id":"1732"}`

	if err := service.DiffusionOut.SaveToImage(value); err != nil {
		logger.Error(err)
		errmsg.Abort(c, 1, err.Error())
		return
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": value,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  msg,
		})
	}
}

func (obj sysApi) RedoComplete(c *gin.Context) {
	var code int
	var msg string
	var oReq req.IdReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	txt2imgId := oReq.Id
	str, err := service.DiffusionOut.RedoComplete(txt2imgId)

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": str,
			"error":  err,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  msg,
		})
	}
}

func (obj sysApi) GetSysData(c *gin.Context) {
	var code int
	var msg string
	var oReq req.IdReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	result := make(map[string]interface{})
	result["AnimeToTxt_Count"] = myredis.LLen(enums.RedisKeyEnum.AnimeToTxt)
	result["Txt2Img_Count"] = myredis.LLen(enums.RedisKeyEnum.Txt2Img)

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  msg,
		})
	}
}

var SysApi sysApi
