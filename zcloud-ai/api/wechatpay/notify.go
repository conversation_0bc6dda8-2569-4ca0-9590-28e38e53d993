package wechatpay

import (
	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/wechat/v3"
	"net/http"
	"time"
	"zcloud-ai/model"
	"zcloud-ai/service"
	"zcloud-ai/utils"
	"zcloud-ai/utils/logger"
)

func Notify(c *gin.Context) {
	//
	//defer func() {
	//	if e := recover(); e != nil {
	//		logger.Error("Notify:", e)
	//	}
	//}()

	logger.Info("已经接收到微信回调v1.3")

	// 从 HTTP 请求头中获取加密算法类型和加密使用的证书序列号
	//nonce := c.Request.Header.Get("Wechatpay-Nonce")
	//serial := c.Request.Header.Get("Wechatpay-Serial")

	//logger.Info("nonce:", nonce, "  ", "serial:", serial)

	notifyData, err := wechat.V3ParseNotify(c.Request)
	if err != nil {
		logger.Error(err)
		return
	}
	//logger.Info("解密前：", notifyData.Resource.Nonce, " ", notifyData.Resource.AssociatedData, " ", "  ", notifyData.Resource.Ciphertext, "  ")

	// 解密加密数据
	result, err := service.WechatpayService.V3DecryptNotifyCipherText(notifyData.Resource.Ciphertext, notifyData.Resource.Nonce, notifyData.Resource.AssociatedData)
	if err != nil {
		// 解密失败，处理错误
		c.String(http.StatusBadRequest, "解密支付通知失败：%v", err)
		return
	}
	logger.Info("解密完成")
	logger.Info("解密支付 result:", utils.GetJsonFromStruct(result))
	logger.Info(result.TransactionId, "  ", result.TradeState, " ", result.OutTradeNo)

	if result.TradeState == "SUCCESS" {

		outTradeNo := result.OutTradeNo
		tradeNo := result.TransactionId

		//payTime, err := time.ParseInLocation("2006-01-02 15:04:05", result.SuccessTime, time.Local) //这里按照当前时区转
		payTime, err := time.Parse(time.RFC3339, result.SuccessTime)
		if err != nil {
			logger.Error(err)
			return
		}
		logger.Info("payTime ", result.SuccessTime, "  ", payTime)
		if err != nil {
			logger.Error(err)
			return
		}

		var recharge model.Recharge
		if err := recharge.GetByOutTradeNo(outTradeNo); err != nil || recharge.ID <= 0 {
			logger.Error(outTradeNo, err)
			return
		}
		//if !recharge.AmountCharge.Equal(totalAmount) {
		//	logger.Error(outTradeNo, " 订单金额不匹配 ", notifyStr)
		//	return
		//}

		if len(recharge.PayTradeId) == 0 {
			recharge.PayTradeId = tradeNo
		}
		recharge.PayTime = payTime
		recharge.PayCallbackJson = utils.GetJsonFromStruct(result)
		err = model.Transactions.RechargeSuccess(&recharge)
		if err != nil {
			logger.Error(err)
			return
		} else {
			//c.String(http.StatusOK, "%s", "success")
			c.String(http.StatusOK, "success")
			//c.Writer.WriteString("success")
			return
		}

	} else {
		logger.Error("result:", utils.GetJsonFromStruct(result))
	}

	//c.JSON(http.StatusOK, &wechat.V3NotifyRsp{Code: gopay.SUCCESS, Message: "成功"})
	//logger.Info("返回success")
	//c.String(http.StatusOK, "success")

}
