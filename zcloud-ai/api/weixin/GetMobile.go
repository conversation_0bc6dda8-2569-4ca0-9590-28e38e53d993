package weixin

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"zcloud-ai/middleware"
	"zcloud-ai/model"
	"zcloud-ai/utils"
	"zcloud-ai/utils/errmsg"
	"zcloud-ai/utils/logger"
)

func GetMobile(c *gin.Context) {
	var code int
	var msg string

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	var regReq PhoneInfoReq
	var user model.User

	er := c.ShouldBindJSON(&regReq)
	if er != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if len(regReq.Code) < 5 {
		logger.Error("regReq.Code:", regReq.Code)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	phoneNumberResp, res, err := GetPhoneNumber(regReq.Code)

	if phoneNumberResp.ErrCode == 0 {
		logger.Info("手机号码获取成功：", res)
	} else {
		logger.Info("手机号码获取失败：", res)
	}
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取手机号码失败")
		return
	}

	if err = user.GetByID(claims.UserId); err != nil {
		logger.Error(err, " userid:", claims.UserId)
		errmsg.Abort(c, errmsg.FAIL, "查询用户失败")
		return
	}

	if err := user.SetPhoneInfo(res); err != nil {
		logger.Error("更新手机号码信息失败:", err, " userid:", claims.UserId)
	}

	phoneNumber := phoneNumberResp.PhoneInfo.PhoneNumber
	if len(phoneNumber) == 0 {
		logger.Error("userId:", claims.UserId, " res:", res)
		if phoneNumberResp.ErrCode == 0 && phoneNumberResp.ErrMsg == "ok" {

		}
		errmsg.Abort(c, errmsg.FAIL, "获取手机号码字段无数据")
		return
	}

	if !utils.IsMobile(phoneNumber) {
		logger.Error("获取的手机号码不标准 phoneNumber:", phoneNumber)
		errmsg.Abort(c, errmsg.FAIL, "获取的手机号码不标准")
		return
	}

	exists, err := user.ExistsMobile(phoneNumber)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "查询手机号码是否存在失败")
		return
	}

	if exists == false {
		if user.Openid != "" && user.Mobile == "" {
			if err := user.SetMobile(phoneNumber); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "设置手机号码失败")
				return
			}
		}
	}

	//err = user.GetByMobile(phoneNumber)
	//if err != nil {
	//	errmsg.Abort(c, errmsg.FAIL, "查询手机号码失败")
	//	return
	//}

	//if user.ID == 0 { //手机号码数据库中没有
	//	err = user.GetByID(claims.UserId)
	//	if err != nil {
	//		errmsg.Abort(c, errmsg.FAIL, "查询用户失败")
	//		return
	//	}
	//	if user.Openid == regReq.Openid && len(user.Mobile) == 0 {
	//		err = user.SetMobile(phoneNumber)
	//		if err != nil {
	//			errmsg.Abort(c, errmsg.FAIL, "更手机号码失败")
	//			return
	//		}
	//	}
	//}

	result := make(map[string]interface{})
	result["mobile"] = phoneNumber

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"message": "成功",
			"msg":     "成功",
			"result":  result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}
