package weixin

import (
	"context"
	"crypto/x509"
	"github.com/shopspring/decimal"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/h5"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"os"
	"zcloud-ai/utils/config"
	"zcloud-ai/utils/logger"
)

type payService_ struct {
	Client                     *core.Client
	mchId                      string
	mchCertificateSerialNumber string
	mchAPIv3Key                string
	cert                       *x509.Certificate
}

func (o *payService_) MchAPIv3Key() string {
	o.setUp()
	return o.mchAPIv3Key
}
func (o *payService_) Cert() *x509.Certificate {
	o.setUp()
	return o.cert
}

func (o *payService_) setUp() {
	if o.Client == nil {
		o.mchId = "1635958245"                                                    // 商户号
		o.mchCertificateSerialNumber = "180AA7D0285165A8299952C92F9F9C28B0B39C25" // 商户证书序列号
		o.mchAPIv3Key = "cyuai888888888888888888888888888"                        // 商户APIv3密钥

		// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
		//mchPrivateKey, err := utils.LoadPrivateKeyWithPath("/path/to/merchant/apiclient_key.pem")

		path := "./config/apiclient_key.pem"
		certPath := "./config/apiclient_cert.pem"
		_, err := os.Stat(path)
		if err != nil {
			path = "/Users/<USER>/Downloads/1635958245_20221212_cert/apiclient_key.pem"
			certPath = "/Users/<USER>/Downloads/1635958245_20221212_cert/apiclient_cert.pem"
		}

		mchPrivateKey, err := utils.LoadPrivateKeyWithPath(path)
		if err != nil {
			logger.Error("load merchant private key error path:", path)
		} else {
			logger.Info("load merchant private key success path:", path)
		}

		cert, errCert := utils.LoadCertificateWithPath(certPath)
		if errCert != nil {
			logger.Error("load merchant cert error path:", certPath, errCert)
		} else {
			o.cert = cert
			logger.Info("load merchant cert success path:", certPath)
		}

		ctx := context.Background()
		// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
		opts := []core.ClientOption{
			option.WithWechatPayAutoAuthCipher(o.mchId, o.mchCertificateSerialNumber, mchPrivateKey, o.mchAPIv3Key),
		}
		client, err := core.NewClient(ctx, opts...)
		if err != nil {
			logger.Fatalf("new wechat pay client err:%s", err)
		}
		o.Client = client
	}
}

func (o *payService_) PrepayWithRequestPayment(outTradeNo string, amount decimal.Decimal, openId string, desc string, attach string) (*jsapi.PrepayWithRequestPaymentResponse, *core.APIResult, error) {
	o.setUp()
	oneHundred, _ := decimal.NewFromString("100")
	amountFen := amount.Mul(oneHundred)
	amountInt := amountFen.BigInt().Int64()

	svc := jsapi.JsapiApiService{Client: o.Client}
	ctx := context.Background()
	// 得到prepay_id，以及调起支付所需的参数和签名
	resp, result, err := svc.PrepayWithRequestPayment(ctx,
		jsapi.PrepayRequest{
			Appid:       core.String(config.WeixinAppId),
			Mchid:       core.String(o.mchId),
			Description: core.String(desc),
			OutTradeNo:  core.String(outTradeNo), //1217752501201407033233368018
			Attach:      core.String(attach),     //自定义数据说明
			//NotifyUrl:   core.String("https://aigc.zcloudai.cn/api/v1/weixin/notify"), //https://www.weixin.qq.com/wxpay/pay.php
			NotifyUrl: core.String(config.Domain + "api/v1/weixin/notify"),
			Amount: &jsapi.Amount{
				Total: core.Int64(amountInt),
			},
			Payer: &jsapi.Payer{
				Openid: core.String(openId), //oUpF8uMuAJO_M2pxb1Q9zNjWeS6o
			},
		},
	)
	if err == nil {
		logger.Info(resp, result)
	} else {
		logger.Error(err)
	}
	return resp, result, err
}

func (o *payService_) PrepayH5(outTradeNo string, amount decimal.Decimal, desc string, attach string) (*h5.PrepayResponse, *core.APIResult, error) {
	o.setUp()
	oneHundred, _ := decimal.NewFromString("100")
	amountFen := amount.Mul(oneHundred)
	amountInt := amountFen.BigInt().Int64()

	svc := h5.H5ApiService{Client: o.Client}
	ctx := context.Background()
	// 得到prepay_id，以及调起支付所需的参数和签名
	resp, result, err := svc.Prepay(ctx,
		h5.PrepayRequest{
			Appid:       core.String(config.WeixinAppId),
			Mchid:       core.String(o.mchId),
			Description: core.String(desc),
			OutTradeNo:  core.String(outTradeNo),                                      //1217752501201407033233368018
			Attach:      core.String(attach),                                          //自定义数据说明
			NotifyUrl:   core.String("https://aigc.zcloudai.cn/api/v1/weixin/notify"), //https://www.weixin.qq.com/wxpay/pay.php
			Amount: &h5.Amount{
				Total: core.Int64(amountInt),
			},
		},
	)
	if err == nil {
		logger.Info(resp, result)
	} else {
		logger.Error(err)
	}
	return resp, result, err
}

func (o *payService_) QueryOrderById(outTradeNo string) {
	o.setUp()
	svc := jsapi.JsapiApiService{Client: o.Client}
	ctx := context.Background()
	resp, result, err := svc.QueryOrderById(ctx,
		jsapi.QueryOrderByIdRequest{
			TransactionId: core.String(outTradeNo),
			Mchid:         core.String(o.mchId),
		},
	)

	if err == nil {
		logger.Info(resp, result)
	} else {
		logger.Error(err)
	}
}

func (o *payService_) QueryOrderByOutTradeNo(outTradeNo string) {
	o.setUp()
	svc := jsapi.JsapiApiService{Client: o.Client}
	ctx := context.Background()
	resp, result, err := svc.QueryOrderByOutTradeNo(ctx,
		jsapi.QueryOrderByOutTradeNoRequest{
			OutTradeNo: core.String(outTradeNo),
			Mchid:      core.String(o.mchId),
		},
	)

	if err == nil {
		logger.Info(resp, result)
	} else {
		logger.Error(err)
	}
}

var PayService payService_
