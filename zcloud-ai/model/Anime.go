package model

import (
	"errors"
	"zcloud-ai/model/req"

	"gorm.io/gorm"
)

type Anime struct {
	gorm.Model
	UserId       uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	Sex          int    `json:"sex" gorm:"type:tinyint;not null;default:0;comment:1男孩 2女孩"`
	Prompt       string `json:"prompt" gorm:"type:varchar(500);not null;default:'';comment:画面描述"`
	StyleId      int    `json:"style_id" gorm:"type:tinyint;not null;default:0;comment:画面风格(壁画、油画、铅笔画、涂鸦)"`
	ImgScale     string `json:"img_scale" gorm:"type:varchar(10);not null;default:'';comment:图片比例(4:3、3:4、1:1)"`
	Width        int    `json:"width" gorm:"type:int;not null;default:0;comment:图像宽度"`
	Height       int    `json:"height" gorm:"type:int;not null;default:0;comment:图像高度"`
	BatchCount   int    `json:"batch_count" gorm:"type:int;not null;default:0;comment:生成的图片批次数量"`
	BatchSize    int    `json:"batch_size" gorm:"type:int;not null;default:1;comment:每次生成的图片数量"`
	CostCoin     int    `json:"cost_coin" gorm:"type:int;not null;default:0;comment:消耗钱币"`
	PriceCoin    int    `json:"price_coin" gorm:"type:int;not null;default:0;comment:需要钱币"`
	PriceDetail  string `json:"price_detail" gorm:"type:json;';comment:钱币组合详情"`
	OrderNo      string `json:"order_no" gorm:"type:varchar(50);comment:订单编号"`
	State        int    `json:"state" gorm:"type:int;not null;default:0;comment:状态 0等待 1正在生成模型 2模型生成完成"`
	InitImages   string `json:"init_images" gorm:"type:json;';comment:"`
	OutputImages string `json:"output_images" gorm:"type:json;';comment:"`
	InitImageMd5 string `json:"init_image_md5" gorm:"type:varchar(50);comment:参考图md5"`
	PushJson     string `json:"push_json" gorm:"type:json;';comment:推送给后端的绘图JSON模板"`
}

func (Anime) TableName() string {
	return "T_Anime"
}

func (o *Anime) Get(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *Anime) GetByID(id uint) (req.AnimeResp, error) {
	var resp req.AnimeResp
	err := db.First(o, id).Scan(&resp).Error
	return resp, err
}

func (o *Anime) GetByID11(id uint) error {
	err := db.Where("id = ?", id).Find(o).Error
	return err
}

func (o *Anime) CountByInitImageMd5(md5 string) (int64, error) {
	var count int64
	err := db.Model(o).Where("init_image_md5 = ?", md5).Count(&count).Error
	return count, err
}

func (o *Anime) GetListManage(dest interface{}, animeId uint, userId uint, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o)
	if animeId > 0 {
		tx.Where("id=?", animeId)
	}
	if userId > 0 {
		tx.Where("user_id=?", userId)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	if err = tx.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, tx.Error
}

func (o *Anime) GetList(userId uint, kw string, state int, page int, pageSize int) ([]req.AnimeListResp, int64, error) {
	var ary []req.AnimeListResp
	var total int64

	//tx := db.Debug().Table(o.TableName()).Where("user_id=?", userId)
	tx := db.Debug().Model(o).Where("user_id=?", userId).Order("id desc")
	if len(kw) > 0 {
		tx.Where("prompt like ?", "%"+kw+"%")
	}

	if state > -1 {
		tx.Where("state = ?", state)
	}
	if err = tx.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(&ary)
	return ary, total, tx.Error
}

func (o *Anime) Save() error {
	return db.Save(o).Error
}
func (o *Anime) SetPushJson(json string) error {
	return db.Debug().Model(o).Updates(Anime{PushJson: json}).Error
}

func (o *Anime) SaveAndAddTxt2Img(oo *Txt2Img) error {
	// 开启事务
	tx := db.Begin()
	if o.State != 2 {
		return errors.New("状态不正确")
	}
	err := tx.Save(o).Error
	if err == nil {
		oo.OrigId = o.ID
		if oo.Styles == "" {
			oo.Styles = "[]"
		}
		if oo.OverrideSettings == "" {
			oo.OverrideSettings = "{}"
		}
		err = tx.Save(oo).Error
	}

	if err != nil {
		tx.Rollback()
	} else {
		tx.Commit()
	}
	return err
}

func (o *Anime) CreateTxt2Img(oo *Txt2Img) error {
	// 开启事务
	tx := db.Begin()

	err := db.Model(o).Updates(Anime{State: 1}).Error
	if err == nil {
		err = oo.Save()
	}

	if err != nil {
		tx.Rollback()
	} else {
		tx.Commit()
	}
	return err
}
