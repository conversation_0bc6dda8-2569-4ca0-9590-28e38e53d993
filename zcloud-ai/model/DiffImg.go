package model

import (
	"errors"
	"gorm.io/gorm"
	"time"
)

type DiffImg struct {
	gorm.Model
	UserId     uint      `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	OrigWhere  int       `json:"orig_where" gorm:"type:int;not null;default:0;comment:哪里来的数据"`
	OrigId     uint      `json:"orig_id" gorm:"type:bigint;not null;default:0;comment:来源记录ID"`
	DiffWhere  int       `json:"diff_where" gorm:"type:tinyint;not null;default:0;comment:1txt2img表"`
	DiffId     uint      `json:"diff_id" gorm:"type:bigint;not null;default:0;comment:生成记录ID"`
	BatchNum   int       `json:"batch_num"  gorm:"type:int;not null;default:0;comment:"`
	Md5        string    `json:"md5" gorm:"type:varchar(50);not null;default:'';comment:图片md5"`
	Path       string    `json:"path" gorm:"type:varchar(100);not null;default:'';comment:图片路径"`
	Path1      string    `json:"path1" gorm:"type:varchar(100);not null;default:'';comment:高分图片路径1"`
	Path2      string    `json:"path2" gorm:"type:varchar(100);not null;default:'';comment:高分图片路径2"`
	ScaleAt    time.Time `json:"scale_at" gorm:"type:datetime;default:'1900-01-01';comment:高分开始时间,高分成功后置为null"`
	Scales     string    `json:"scales" gorm:"type:varchar(50);not null;default:'';comment:高分倍数，用,隔开"`
	Parameters string    `json:"override_settings" gorm:"type:json;comment:图片生成最终参数"`
	State      int       `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 3已完成"`
}

func (DiffImg) TableName() string {
	return "T_DiffImg"
}

func (o *DiffImg) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}
func (o *DiffImg) GetByMd5(md5 string) error {
	err := db.First(o, "md5 = ?", md5).Error
	return err
}

func (o *DiffImg) GetByAnimeIdAndNum(animeId uint, num int) error {
	err := db.First(o, "orig_where = ? and orig_id=? and batch_num=?", 1, animeId, num).Error
	return err
}

func (o *DiffImg) GetLastCompleted() error {
	err := db.First(o, "path <>?", "").Order("id desc").Error
	return err
}

func (o *DiffImg) GetListByAnimeId(userId uint, animeId uint) ([]DiffImg, error) {
	ary := make([]DiffImg, 0)
	tx := db.Debug().Model(&DiffImg{}).Where("user_id = ? and orig_where=? and orig_id=?", userId, 1, animeId).Order("batch_num asc").Scan(&ary)
	return ary, tx.Error
}
func (o *DiffImg) GetListByTxt2imgId(txt2imgId uint) ([]DiffImg, error) {
	ary := make([]DiffImg, 0)
	tx := db.Model(o).Where("diff_where=? and diff_id=?", 1, txt2imgId).Order("batch_num asc").Scan(&ary)
	return ary, tx.Error
}

func (o *DiffImg) GetMaxIdByCreateAt(createAd time.Time) error {
	err := db.Debug().Order("id desc").First(o, "created_at < ?", createAd).Error
	return err
}

func (o *DiffImg) GetListForRepush(maxId uint, limit int) ([]DiffImg, error) {
	ary := make([]DiffImg, 0)
	tx := db.Debug().Model(&DiffImg{}).Where("id<? and orig_where=? and path=?", maxId, 1, "").Order("id desc").Limit(limit).Scan(&ary)
	return ary, tx.Error
}

func (o *DiffImg) BatchCreate(value []DiffImg) error {
	return db.Create(&value).Error
}

func (o *DiffImg) Save() error {
	return db.Save(o).Error
}

/*
func (o *DiffImg) SetUpscalePath(level int, path string) error {
	if level == 1 {
		return db.Model(o).Updates(DiffImg{Path1: path, o.UpdatedAt: nil}).Error
	}

}*/

func (o *DiffImg) SetPath(path string, status, level int) error {
	if level > 0 {
		return db.Model(o).Updates(DiffImg{Path: path, State: status, ScaleAt: time.Now()}).Error
	} else {
		return db.Model(o).Updates(DiffImg{Path: path, State: status}).Error
	}
}

func (o *DiffImg) SetUpscalePath(id uint, level int, path string) error {
	if level == 1 {
		return db.Model(o).Where("id = ?", id).Updates(map[string]interface{}{"path1": path, "scale_at": nil}).Error
	} else if level == 2 {
		return db.Model(o).Where("id = ?", id).Updates(map[string]interface{}{"path2": path, "scale_at": nil}).Error
	} else {
		return errors.New("未找到level对应字段")
	}
}

func (o *DiffImg) SetUpscaleAt() error {
	return db.Model(o).Updates(DiffImg{ScaleAt: time.Now()}).Error
}

func (o *DiffImg) Del(tx *gorm.DB, id uint, userId uint) error {
	tx.Debug().Where("id=? and user_id=? ", id, userId).Delete(o)
	return tx.Error
}

func (o *DiffImg) DelByOrig(tx *gorm.DB, userId uint, origWhere int, origId uint) error {
	tx.Debug().Where("user_id=? and orig_where=? and orig_id=?", userId, origWhere, origId).Delete(o)
	return tx.Error
}

func (o *DiffImg) GetListManage(dest interface{}, userId uint, origWhere int, origId uint, page int, pageSize int) (int64, error) {

	var total int64
	tx := db.Debug().Model(o).Where("orig_where=? ", origWhere)
	if userId > 0 {
		tx.Where("user_id=?", userId)
	}
	if origId > 0 {
		tx.Where("orig_id=?", origId)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}
