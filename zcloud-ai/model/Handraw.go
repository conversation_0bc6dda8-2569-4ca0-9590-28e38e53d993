package model

import (
	"gorm.io/gorm"
	"zcloud-ai/enums"
)

type Handraw struct {
	gorm.Model
	UserId        uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	HandUuid      string `json:"hand_uuid" gorm:"type:varchar(50);not null;default:'';comment:唯一字符串"`
	HandFrom      int    `json:"hand_from" gorm:"type:int;not null;default:0;comment:来源 1手绘 2儿童版 3AI猜画"`
	InputImgMd5   string `json:"input_img_md5" gorm:"type:varchar(50);not null;default:'';comment:手绘图片索引"`
	InputImgPath  string `json:"input_img_path" gorm:"type:varchar(100);not null;default:'';comment:手绘图片路径"`
	Width         int    `json:"width" gorm:"type:int;not null;default:0;comment:参考图宽度"`
	Height        int    `json:"height" gorm:"type:int;not null;default:0;comment:参考图高度"`
	DrawPath      string `json:"draw_path" gorm:"type:json;';comment:绘画路径"`
	DrawType      int    `json:"draw_type" gorm:"type:int;not null;default:0;comment:绘画类型 0路径 1图片"`
	Prompt        string `json:"prompt" gorm:"type:varchar(200);not null;default:'';comment:简要画面描述"`
	GuessUserId   uint   `json:"guess_user_id" gorm:"type:bigint;not null;default:0;comment:画面描述用户ID"`
	ParentId      uint   `json:"parent_id" gorm:"type:bigint;not null;default:0;comment:父级手绘ID"`
	BatchCount    int    `json:"batch_count" gorm:"type:int;not null;default:0;comment:生成的图片张数"`
	PushJson      string `json:"push_json" gorm:"type:json;';comment:推送的Json字符串"`
	OutputImgMd5  string `json:"output_img_md5" gorm:"type:varchar(50);not null;default:'';comment:生成图片索引"`
	OutputImgPath string `json:"output_img_path" gorm:"type:varchar(100);not null;default:'';comment:手绘图片输出路径"`
	PriceCoin     int    `json:"price_coin" gorm:"type:int;not null;default:0;comment:所需Coin"`
	OrderNo       string `json:"order_no" gorm:"type:varchar(50);comment:订单编号"`
	State         int    `json:"state" gorm:"type:int;not null;default:0;comment:状态(1手绘图上传成功 2生成中 3生成完成)"`
}

func (Handraw) TableName() string {
	return "T_Handraw"
}

func (o *Handraw) Get() error {
	err := db.First(o, o.ID).Error
	return err
}

func (o *Handraw) GetById(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *Handraw) GetByUuid(uuid string) error {
	err := db.First(o, "hand_uuid = ?", uuid).Error
	return err
}

func (o *Handraw) GetListManage(dest interface{}, userId uint, handrawId uint, page int, pageSize int) (int64, error) {

	var total int64
	tx := db.Debug().Model(o)
	if userId > 0 {
		tx.Where("user_id=?", userId)
	}
	if handrawId > 0 {
		tx.Where("id=?", handrawId)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Handraw) Save() error {
	return db.Save(o).Error
}
func (o *Handraw) SetPushJson(json string) error {
	return db.Model(o).Updates(Handraw{PushJson: json, State: enums.HandrawStateEnum.Drawing}).Error
}
func (o *Handraw) SetOutputImg(outputImgPath string) error {
	return db.Model(o).Updates(Handraw{OutputImgPath: outputImgPath, State: enums.HandrawStateEnum.DrawComplete}).Error
}
func (o *Handraw) SetOutputImgMd5(outputImgMd5 string) error {
	return db.Model(o).Updates(Handraw{OutputImgMd5: outputImgMd5}).Error
}
func (o *Handraw) SetState(state int) error {
	return db.Model(o).Updates(map[string]interface{}{"state": state}).Error
}

func (o *Handraw) SetStateAndSize(state int, width int, height int) error {
	return db.Model(o).Updates(map[string]interface{}{"state": state, "width": width, "height": height}).Error
}

//func (o *ArtStyle) GetListBySex(dest interface{}, sex int, state int, page int, pageSize int) (int64, error) {
//	var total int64
//	tx := db.Debug().Model(o).Where("sex=? and state=?", sex, state).Order("order_index asc")
//	tx.Count(&total)
//	tx = tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
//	return total, tx.Error
//}
