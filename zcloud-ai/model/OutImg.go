package model

import (
	"errors"
	"gorm.io/gorm"
	"time"
)

type OutImg struct {
	gorm.Model
	UserId      uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	OrigWhere   int    `json:"orig_where" gorm:"type:int;not null;default:0;comment:哪里来的数据"`
	OrigId      uint   `json:"orig_id" gorm:"type:bigint;not null;default:0;comment:来源记录ID"`
	Prompt      string `json:"prompt" gorm:"type:varchar(200);not null;default:'';comment:简要画面描述"`
	GuessUserId uint   `json:"guess_user_id" gorm:"type:bigint;not null;default:0;comment:画面描述用户ID"`
	Md5         string `json:"md5" gorm:"type:varchar(50);not null;default:'';comment:图片md5"`
	Path        string `json:"path" gorm:"type:varchar(100);not null;default:'';comment:图片路径"`
	PriceCoin   int    `json:"price_coin" gorm:"type:int;not null;default:0;comment:所需Coin"`
	OrderNo     string `json:"order_no" gorm:"type:varchar(50);comment:订单编号"`
	State       int    `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 3已完成"`
	Share       int    `json:"share" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 1禁止分享 2申请分享 3审核通过"`
}

func (OutImg) TableName() string {
	return "T_OutImg"
}

func (o *OutImg) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}
func (o *OutImg) GetByMd5(md5 string) error {
	err := db.First(o, "md5 = ?", md5).Error
	return err
}

func (o *OutImg) GetShareListFall(origWhere int, share int, lastId uint, limit int) ([]OutImg, error) {
	ary := make([]OutImg, 0)
	tx := db.Debug().Model(o).Where("orig_where=?", origWhere)
	if share >= 0 {
		tx = tx.Where("share>=?", share)
	} else {
		return ary, nil
	}
	if lastId > 0 {
		tx = tx.Where("id<?", lastId)
	}
	tx = tx.Order("id desc").Limit(limit).Scan(&ary)
	return ary, tx.Error
}

func (o *OutImg) GetList(userId uint, origWhere int, origId uint, share int, page int, pageSize int) ([]OutImg, int64, error) {
	ary := make([]OutImg, 0)
	var total int64
	tx := db.Debug().Model(o).Where("user_id = ? and orig_where=? ", userId, origWhere)
	if userId == 0 {
		tx = db.Debug().Model(o).Where("orig_where=? ", origWhere)
	}
	if origId > 0 {
		tx.Where("orig_id=?", origId)
	}
	if share > -1 {
		tx.Where("share>=?", share)
	}
	if err = tx.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(&ary)
	return ary, total, tx.Error
}

func (o *OutImg) GetByTxt2imgId(txt2imgId uint) ([]DiffImg, error) {
	var ary []DiffImg
	tx := db.Model(o).Where("diff_type=? and diff_id", txt2imgId).Order("batch_num asc").Scan(&ary)
	return ary, tx.Error
}

func (o *OutImg) BatchCreate(value []OutImg) error {
	return db.Create(&value).Error
}

func (o *OutImg) Save() error {
	return db.Save(o).Error
}

func (o *OutImg) SetShare(share int) error {
	return db.Model(o).Updates(map[string]interface{}{"share": share}).Error
}

func (o *OutImg) SetPath(path string) error {
	return db.Model(o).Updates(OutImg{Path: path}).Error
}

/*
func (o *DiffImg) SetUpscalePath(level int, path string) error {
	if level == 1 {
		return db.Model(o).Updates(DiffImg{Path1: path, o.UpdatedAt: nil}).Error
	}

}*/

func (o *OutImg) SetUpscalePath(id uint, level int, path string) error {
	if level == 1 {
		return db.Model(o).Where("id = ?", id).Updates(map[string]interface{}{"path1": path, "scale_at": nil}).Error
	} else if level == 2 {
		return db.Model(o).Where("id = ?", id).Updates(map[string]interface{}{"path2": path, "scale_at": nil}).Error
	} else {
		return errors.New("未找到level对应字段")
	}
}

func (o *OutImg) SetUpscaleAt() error {
	return db.Model(o).Updates(DiffImg{ScaleAt: time.Now()}).Error
}

//func (o *OutImg) Del(tx *gorm.DB, id uint, userId uint) error {
//	tx.Debug().Where("id=? and user_id=? ", userId, id, userId).Delete(o)
//	return tx.Error
//}

func (o *OutImg) Del() error {
	return db.Delete(o).Error
}

func (o *OutImg) DelByOrig(tx *gorm.DB, userId uint, origWhere int, origId uint) error {
	tx.Debug().Where("user_id=? and orig_where=? and orig_id=?", userId, origWhere, origId).Delete(o)
	return tx.Error
}
