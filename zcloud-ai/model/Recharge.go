package model

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
	"zcloud-ai/model/req"
)

type Recharge struct {
	gorm.Model
	UserId                uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	ProductId             string          `json:"product_id" gorm:"type:varchar(50);not null;default:'';comment:产品id eg:apple订阅"`
	ProductCategory       string          `json:"product_category" gorm:"type:varchar(50);not null;default:'';comment:产品类型 对应ProductCategory枚举"`
	OutTradeNo            string          `json:"out_trade_no" gorm:"type:varchar(50);not null;default:'';comment:商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一"`
	DatetimeCancel        time.Time       `json:"datetime_cancel" gorm:"type:datetime;default:'1900-01-01';comment:订单取消时间"`
	CoinCharge            int             `json:"coin_charge" gorm:"type:int;not null;default:0;comment:购买云算豆数量"`
	AmountCharge          decimal.Decimal `json:"amount_charge" gorm:"type:decimal(16,2);not null;default:0;comment:充值金额"`
	Description           string          `json:"description" gorm:"type:varchar(50);not null;default:'';comment:商品描述，Image形象店-深圳腾大-QQ公仔"`
	Gatway                string          `json:"gatway" gorm:"type:varchar(10);not null;default:'';comment:支付方式 对应枚举PayGatewayEnum"`
	PayType               string          `json:"pay_type" gorm:"type:varchar(50);not null;default:'';comment:收款渠道"`
	PayTime               time.Time       `json:"pay_time" gorm:"type:datetime;default:'1900-01-01';comment:支付平台回调时间"`
	PayTradeId            string          `json:"pay_trade_id" gorm:"type:varchar(50);not null;default:'';comment:支付平台的交易单号"`
	PayCallbackJson       string          `json:"pay_callback_json" gorm:"type:json;comment:支付平台回调数据"`
	PayRefundTime         time.Time       `json:"pay_refund_time" gorm:"type:datetime;default:'1900-01-01';comment:支付平台退款回调时间"`
	PayRefundCallbackJson string          `json:"pay_refund_callback_json" gorm:"type:json;comment:支付平台退款回调数据"`
	PayRefundTradeId      string          `json:"pay_refund_trade_id" gorm:"type:varchar(50);not null;default:'';comment:支付平台退款的交易单号"`
	State                 int             `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0新创建 1已付款 2已退款 9已取消"`
}

func (Recharge) TableName() string {
	return "T_Recharge"
}

func (o *Recharge) Get(id uint) (req.RechargeResp, error) {
	var resp req.RechargeResp
	err := db.First(o, id).Scan(&resp).Error
	return resp, err
}

func (o *Recharge) GetByOutTradeNo(outTradeNo string) error {
	err := db.Debug().Where("out_trade_no = ?", outTradeNo).First(o).Error
	return err
}

func (o *Recharge) GetByPayTradeId(payType string, tradeId string) error {
	return db.Debug().First(o, "pay_type=? and  pay_trade_id = ?", payType, tradeId).Error
}

func (o *Recharge) GetList(userId uint, kw string, state int, page int, pageSize int) ([]req.RechargeResp, int64, error) {
	var ary []req.RechargeResp
	var total int64
	tx := db.Model(o).Where("user_id=?", userId).Order("id desc")
	if state >= 0 {
		tx.Where("state=?", state)
	}
	if len(kw) > 0 {
		tx.Where("Description like ?", "%"+kw+"%")
	}
	if err = tx.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(&ary)
	return ary, total, tx.Error
}

func (o *Recharge) GetListByManager(dest interface{}, userId uint, state int, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o)
	if userId > 0 {
		tx.Where("user_id=? ", userId)
	}
	if state > -1 {
		tx.Where("state=? ", state)
	}
	if err = tx.Count(&total).Error; err != nil {
		return 0, err
	}

	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Recharge) Save() error {
	return db.Debug().Save(o).Error
}

func (o *Recharge) ExistsPayTradeId(payType string, tradeId string) (bool, error) {
	var recharge Recharge
	if err := db.Debug().First(&recharge, "pay_type=? and  pay_trade_id = ?", payType, tradeId).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *Recharge) SetPrepayId(prepayId string) error {
	return db.Model(o).Updates(Recharge{PayTradeId: prepayId}).Error
}

func (o *Recharge) SetAppleVerifyInfo(jsonStr string) error {
	return db.Model(o).Updates(Recharge{PayCallbackJson: jsonStr}).Error
}

func (o *Recharge) SetAppleIapSuccess(tx *gorm.DB, tradeNo string, successTime time.Time) error {
	return tx.Model(o).Updates(Recharge{PayTradeId: tradeNo, PayTime: successTime, State: 1}).Error
}

func (o *Recharge) SetPaySuccess(tx *gorm.DB, successTime time.Time, jsonStr string) error {
	return tx.Model(o).Updates(Recharge{PayTime: successTime, PayCallbackJson: jsonStr, State: 1}).Error
}

func (o *Recharge) SetAliPaySuccess(tx *gorm.DB, tradeNo string, successTime time.Time, jsonStr string) error {
	return tx.Model(o).Updates(Recharge{PayTradeId: tradeNo, PayTime: successTime, PayCallbackJson: jsonStr, State: 1}).Error
}
