package req

import (
	"time"
)

type ArtStyleReq struct {
	ID         int    `json:"id"`
	Sex        int    `json:"sex"`
	Prompt     string `json:"prompt"`
	RefImg     string `json:"ref_img"`
	OrderIndex int    `json:"order_index"`
	Title      string `json:"title"`
	StyleCode  string `json:"style_code"`
}

type ArtStyleResp struct {
	ID         int       `json:"id"`
	Sex        int       `json:"sex"`
	RefImg     string    `json:"-"`
	RefUrl     string    `json:"ref_url"`
	OrderIndex int       `json:"order_index"`
	Title      string    `json:"title"`
	StyleCode  string    `json:"-"`
	IsHot      int       `json:"is_hot"`
	IsDefault  int       `json:"is_default"`
	MainBody   int       `json:"main_body"`
	Path       string    `json:"path"`
	OtherParm  string    `json:"other_parm"`
	UpdatedAt  time.Time `json:"-"`
}
