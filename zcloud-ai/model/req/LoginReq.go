package req

type LoginReq struct {
	Username    string `json:"username"`
	Password    string `json:"password"`
	InvitedCode string `json:"invited_code"`
}

type LoginSmsReq struct {
	Mobile      string `json:"mobile"`
	SmsCode     string `json:"sms_code"`
	InvitedCode string `json:"invited_code"`
	Plat        string `json:"plat"`
	UserSystem  string `json:"user_system"`
}

type RoleResp struct {
	RoleName string `json:"roleName"`
	Value    string `json:"value"`
}

type LoginResp struct {
	UserId         uint   `json:"user_id"`
	Username       string `json:"username"`
	Token          string `json:"token"`
	Mobile         string `json:"mobile"`
	Role           int    `json:"role"`
	Openid         string `json:"openid"`
	InvitationCode string `json:"invitation_code"`
}
