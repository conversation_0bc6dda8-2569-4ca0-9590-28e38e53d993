package service

import (
	"errors"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"zcloud-ai/utils/config"
)

type alisms_ struct {
}

/**
 * 使用AK&SK初始化账号Client
 * @param accessKeyId
 * @param accessKeySecret
 * @return Client
 * @throws Exception
 */

func (obj alisms_) CreateClient(accessKeyId *string, accessKeySecret *string) (_result *dysmsapi20170525.Client, _err error) {
	config := &openapi.Config{
		// 必填，您的 AccessKey ID
		AccessKeyId: accessKeyId,
		// 必填，您的 AccessKey Secret
		AccessKeySecret: accessKeySecret,
	}
	// 访问的域名
	config.Endpoint = tea.String("dysmsapi.aliyuncs.com")
	_result = &dysmsapi20170525.Client{}
	_result, _err = dysmsapi20170525.NewClient(config)
	return _result, _err
}

func (obj alisms_) SendSmsCode(mould int, mobile string, code string) error {

	mouldCode, err := obj.GetAliSmsMouldCode(mould)
	if err != nil {
		return err
	}

	client, _err := obj.CreateClient(tea.String(config.AccessKeyId), tea.String(config.AccessKeySecret))
	if _err != nil {
		return _err
	}
	sendSmsRequest := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers: tea.String(mobile),
		//SignName:      tea.String("泽云智联"),
		SignName:      tea.String("晨羽智云"),
		TemplateCode:  tea.String(mouldCode),
		TemplateParam: tea.String("{\"code\":\"" + code + "\"}"),
	}
	runtime := &util.RuntimeOptions{}
	tryErr := func() (_e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		// 复制代码运行请自行打印 API 的返回值
		_, _err = client.SendSmsWithOptions(sendSmsRequest, runtime)
		if _err != nil {
			return _err
		}

		return nil
	}()

	if tryErr != nil {
		var error = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			error = _t
		} else {
			error.Message = tea.String(tryErr.Error())
		}
		// 如有需要，请打印 error
		_, _err = util.AssertAsString(error.Message)
		if _err != nil {
			return _err
		}
	}
	return _err
}

func (obj alisms_) GetAliSmsMouldCode(mould int) (string, error) {
	return "SMS_274665268", nil
	switch mould {
	case 1:
		return "SMS_201740256", nil
	case 2:
		return "SMS_201740258", nil
	case 3:
		return "SMS_201740255", nil
	}
	return "", errors.New("未找到匹配模板")
}

var AliSms alisms_
