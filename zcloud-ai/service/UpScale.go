package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zcloud-ai/enums"
	"zcloud-ai/model"
	"zcloud-ai/utils/logger"
	"zcloud-ai/utils/myredis"
)

type upscale_ struct {
}

func (d *upscale_) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("UpScaleService奔溃:", e)
		}
	}()
	logger.Info("UpScaleService.Run 开始循环获取")
	testCount := -1
	for {
		value, err := d.PopRedisQueue()

		if err != nil {
			logger.Error("UpScaleService.Run PopRedisQueue to Retry:", value, err)
			continue
		}
		if value == "" {
			if testCount == -1 {
				logger.Info("UpScaleOut:", "空字符串，获取下一个数据", " len:", len(value), testCount)
			}

			testCount += 1
			if testCount == 12 {
				logger.Info("UpScaleOut:", "空字符串，获取下一个数据", " len:", len(value), testCount)
				testCount = 0
			}
			continue
		} else {
			logger.Info("UpScaleOut:", value, " len:", len(value))
		}
		logger.Info("接收到超分回传数据:", value)
		if err := d.UpdateToImage(value); err != nil {
			logger.Error(err)
		}
	}
}

func (d *upscale_) UpdateToImage(value string) error {

	var resp upScaleResp_
	if err := json.Unmarshal([]byte(value), &resp); err != nil {
		logger.Error("Json数据解析失败", value)
		return errors.New("解析数据失败")
	}

	customData := CustomData_{}
	if resp.CustomData != "" {
		if err := json.Unmarshal([]byte(resp.CustomData), &customData); err != nil {
			logger.Error(err)
			return err
		}
	}

	imgId := customData.ImgId
	level := customData.Level
	path := resp.OutputRelativePath
	if imgId == 0 || level == 0 || len(path) == 0 {
		logger.Error("Json解析出来的数据不正确", value, " ", imgId, " ", level, " ", path)
		return errors.New("解析出来的数据不正确")
	}
	diffImg := model.DiffImg{}
	if err := diffImg.SetUpscalePath(imgId, level, path); err != nil {
		logger.Error("")
		return err
	}
	if err := d.RemoveRedisUpscaleState(imgId, level); err != nil {
		logger.Error(err)
	} else {
		logger.Info("移除RemoveRedisUpscaleState imgId:", imgId, " level:", level)
	}
	return nil
}

func (d *upscale_) GetUpScaleJson(path string, imgId uint, level int, upScaleW int, upScaleH int) (string, error) {
	mapObj := make(map[string]interface{})
	mapObj["sd_api"] = "extra-single"
	mapObj["path"] = path
	mapObj["width"] = upScaleW
	mapObj["height"] = upScaleH

	customData := make(map[string]interface{})
	customData["img_id"] = imgId
	customData["level"] = level

	bytesCustomData, err := json.Marshal(customData)
	if err != nil {
		logger.Error("json.Marshal(m) error:", err)
		return "", err
	}
	mapObj["custom_data"] = string(bytesCustomData)

	bytesData, err := json.Marshal(mapObj)
	if err != nil {
		logger.Error("json.Marshal(m) error:", err)
		return "", err
	}
	return string(bytesData), nil
}

func (d *upscale_) PushRedisQueue(json string) (int64, error) { //Push到超分队列
	size, err := myredis.LPush(enums.RedisKeyEnum.DiffusionExtraQueue, json)
	if err != nil {
		logger.Error(err)
	}
	return size, err
}

func (d *upscale_) PopRedisQueue() (string, error) { //获取超分回传数据
	value, err := myredis.BRPop(enums.RedisKeyEnum.DiffusionExtraQueueOut)
	return value, err
}

func (d *upscale_) PushRedisUpscaleState(imgId uint, level int) error { //Push到超分队列
	field := fmt.Sprintf("%d_%d", imgId, level)
	value := time.Now().Format("2006-01-02 15:04:05")
	_, err := myredis.HSet(enums.RedisKeyEnum.DiffImgUpscale, field, value)
	if err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func (d *upscale_) GetRedisUpscaleState(imgId uint, level int) (time.Time, error) {
	field := fmt.Sprintf("%d_%d", imgId, level)

	value, err := myredis.HGet(enums.RedisKeyEnum.DiffImgUpscale, field)
	if err != nil && err.Error() != "redis: nil" {
		return time.Time{}, err
	}
	if value != "" {

		format := "2006-01-02 15:04:05"
		t, err := time.ParseInLocation(format, value, time.FixedZone("Asia/Shanghai", 8*60*60))
		if err != nil {
			logger.Error(err)
			return time.Time{}, err
		}
		return t, nil
	}
	return time.Time{}, nil
}

func (d *upscale_) RemoveRedisUpscaleState(imgId uint, level int) error { //获取超分回传数据
	field := fmt.Sprintf("%d_%d", imgId, level)
	n, err := myredis.HDel(enums.RedisKeyEnum.DiffImgUpscale, field)
	if err != nil {
		logger.Error(err, n)
		return err
	}
	return nil
}

type upScaleResp_ struct {
	OutputMd5          string `json:"output_md5"`
	OutputRelativePath string `json:"output_relative_path"`
	CustomData         string `json:"custom_data"`
}

type CustomData_ struct {
	ImgId uint `json:"img_id"`
	Level int  `json:"level"`
}

var UpScaleService upscale_
