package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"path"
	"strings"
	"time"
	"zcloud-ai/enums"
	"zcloud-ai/model"
	"zcloud-ai/utils/logger"
	"zcloud-ai/utils/myredis"
)

type upscale_v2 struct {
}

func (d *upscale_v2) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("UpScaleService奔溃:", e)
		}
	}()
	logger.Info("UpScaleService.Run 开始循环获取")
	for {
		value, err := d.PopRedisQueue()
		if err != nil {
			logger.Error("UpScaleService.Run PopRedisQueue to Retry:", value, err)
			continue
		}
		if value == "" {
			continue
		}
		logger.Info("接收到超分回传数据:", value)
		if err := d.UpdateToImage(value); err != nil {
			logger.Error(err)
		}
	}
}

type upscaleOutData_ struct {
	CustomApp   string `json:"custom_app"`
	CustomData  string `json:"custom_data"`
	SisPath     string `json:"sis_path"`
	ExecuteTime int    `json:"execute_time"`
}

type upscaleCustomData_ struct {
	ImgId uint `json:"img_id"`
	Level int  `json:"level"`
}

func (d *upscale_v2) UpdateToImage(value string) error {

	var resp upscaleOutData_
	if err := json.Unmarshal([]byte(value), &resp); err != nil {
		logger.Error("Json数据解析失败", value)
		return errors.New("解析数据失败")
	}

	customData := upscaleCustomData_{}
	if resp.CustomData != "" {
		if err := json.Unmarshal([]byte(resp.CustomData), &customData); err != nil {
			logger.Error(err)
			return err
		}
	}

	imgId := customData.ImgId
	level := customData.Level
	path := resp.SisPath
	if imgId == 0 || level == 0 || len(path) == 0 {
		logger.Error("Json解析出来的数据不正确", value, " ", imgId, " ", level, " ", path)
		return errors.New("解析出来的数据不正确")
	}
	diffImg := model.DiffImg{}
	if err := diffImg.SetUpscalePath(imgId, level, path); err != nil {
		logger.Error("")
		return err
	}
	if err := d.RemoveRedisUpscaleState(imgId, level); err != nil {
		logger.Error(err)
	} else {
		logger.Info("移除RemoveRedisUpscaleState imgId:", imgId, " level:", level)
	}
	return nil
}

func (d *upscale_v2) GetUpScaleJson(imgPath string, imgId uint, level int, upScaleW int, upScaleH int) (string, error) {
	mapObj := make(map[string]interface{})
	//mapObj["sd_api"] = "extra-single"
	//mapObj["path"] = path
	//mapObj["width"] = upScaleW
	//mapObj["height"] = upScaleH

	mapObj["custom_app"] = "shenbixiaoai"
	mapObj["image_path"] = imgPath
	mapObj["upscaling_resize_w"] = upScaleW
	mapObj["upscaling_resize_h"] = upScaleH
	mapObj["resize_mode"] = 1 //按比列缩放

	ext := path.Ext(imgPath)
	mapObj["custom_path"] = strings.Replace(imgPath, ext, fmt.Sprintf("_%dx%d.jpg", upScaleW, upScaleH), -1)
	mapObj["custom_path"] = strings.Replace(mapObj["custom_path"].(string), "shenbixiaoai/", "", -1)

	customData := make(map[string]interface{})
	customData["img_id"] = imgId
	customData["level"] = level

	bytesCustomData, err := json.Marshal(customData)
	if err != nil {
		logger.Error("json.Marshal(m) error:", err)
		return "", err
	}
	mapObj["custom_data"] = string(bytesCustomData)

	bytesData, err := json.Marshal(mapObj)
	if err != nil {
		logger.Error("json.Marshal(m) error:", err)
		return "", err
	}
	return string(bytesData), nil
}

func (d *upscale_v2) PushRedisQueue(json string) (int64, error) { //Push到超分队列
	size, err := myredis.LPush(enums.AigcRedisKeyEnum.UpScalingIn, json)
	if err != nil {
		logger.Error(err)
	}
	return size, err
}

func (d *upscale_v2) PopRedisQueue() (string, error) { //获取超分回传数据
	//value, err := myredis.BRPop(enums.RedisKeyEnum.DiffusionExtraQueueOut)
	value, err := myredis.BRPop(enums.AigcRedisKeyEnum.UpScalingOut)
	return value, err
}

func (d *upscale_v2) PushRedisUpscaleState(imgId uint, level int) error { //Push到超分队列
	field := fmt.Sprintf("%d_%d", imgId, level)
	value := time.Now().Format("2006-01-02 15:04:05")
	_, err := myredis.HSet(enums.AigcRedisKeyEnum.UpScalingState, field, value)
	if err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func (d *upscale_v2) GetRedisUpscaleState(imgId uint, level int) (time.Time, error) {
	field := fmt.Sprintf("%d_%d", imgId, level)

	value, err := myredis.HGet(enums.AigcRedisKeyEnum.UpScalingState, field)
	if err != nil && err.Error() != "redis: nil" {
		return time.Time{}, err
	}
	if value != "" {

		format := "2006-01-02 15:04:05"
		t, err := time.ParseInLocation(format, value, time.FixedZone("Asia/Shanghai", 8*60*60))
		if err != nil {
			logger.Error(err)
			return time.Time{}, err
		}
		return t, nil
	}
	return time.Time{}, nil
}

func (d *upscale_v2) RemoveRedisUpscaleState(imgId uint, level int) error { //获取超分回传数据
	field := fmt.Sprintf("%d_%d", imgId, level)
	n, err := myredis.HDel(enums.AigcRedisKeyEnum.UpScalingState, field)
	if err != nil {
		logger.Error(err, n)
		return err
	}
	return nil
}

var UpScaleServiceV2 upscale_v2
