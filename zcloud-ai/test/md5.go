package main

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"time"
)

func mainmd() {

	str := "hello world!"

	//包括冒号前面的下标，但不包括冒号后面的。下面截取的是字符串从第零位开始到第四位
	str = str[1:5]
	//输出结果:ello
	fmt.Println(str)

	oMd5Str := fmt.Sprintf("%d,%s", 1, time.Now().Format("2006-01-02 15:04:05.000"))
	has := md5.Sum([]byte(oMd5Str))
	md5Str := hex.EncodeToString(has[:])

	fmt.Println(md5Str)

	//获取方法1 %X大写 %x小写
	m := fmt.Sprintf("%X", has)
	fmt.Println(m)

	//获取方法2
	//b := has[:]
	hex_string_data := hex.EncodeToString(has[:])
	fmt.Println(hex_string_data)

	/*
		md5h := md5.New()
		io.Copy(md5h, pFile)
		md5Str := hex.EncodeToString(md5h.Sum(nil))*/
}
