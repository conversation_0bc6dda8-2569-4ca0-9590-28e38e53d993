package config

import (
	"fmt"
	"gopkg.in/ini.v1"
)

var (
	Bundle       string
	CenterServer string
	Version      float64
	AppMode      string
	Env          string
	RunTimer     bool
	HttpPort     string
	JwtKey       string

	DiffusionApi    string
	Txt2ImgFilePath string
	TempImgFilePath string

	StaticFilePath    string
	DiffusionFilePath string
	DiffusionDomain   string
	Domain            string

	TmpFilePath  string
	SaveFilePath string

	AccessKeyId     string
	AccessKeySecret string

	WeixinAppId  string
	WeixinSecret string

	Db            string
	DbHost        string
	DbPort        string
	DbUser        string
	DbPassWord    string
	DbName        string
	RedisAddr     string
	RedisPassWord string
)

func init() {
	file, err := ini.Load("./config/config.ini")
	if err != nil {
		fmt.Println("配置文件读取错误", err)
	}
	LoadServer(file)
	LoadDatabase(file)
}

func LoadServer(file *ini.File) {
	Bundle = file.Section("server").Key("Bundle").MustString("")
	CenterServer = file.Section("server").Key("CenterServer").MustString("")
	Version = file.Section("server").Key("Version").MustFloat64(1.0)
	Env = file.Section("server").Key("Env").MustString("")
	AppMode = file.Section("server").Key("AppMode").MustString("")
	RunTimer = file.Section("server").Key("RunTimer").MustBool(false)
	HttpPort = file.Section("server").Key("HttpPort").MustString("")
	JwtKey = file.Section("server").Key("JwtKey").MustString("")

	AccessKeyId = file.Section("server").Key("AccessKeyId").MustString("")
	AccessKeySecret = file.Section("server").Key("AccessKeySecret").MustString("")

	WeixinAppId = file.Section("server").Key("WeixinAppId").MustString("")
	WeixinSecret = file.Section("server").Key("WeixinSecret").MustString("")

	DiffusionApi = file.Section("server").Key("DiffusionApi").MustString("")
	Txt2ImgFilePath = file.Section("server").Key("Txt2ImgFilePath").MustString("./txt2img/")
	TempImgFilePath = file.Section("server").Key("TempImgFilePath").MustString("./tempimg/")

	StaticFilePath = file.Section("server").Key("StaticFilePath").MustString("/stable-diffusion-webui/aigc-static/")
	DiffusionFilePath = file.Section("server").Key("DiffusionFilePath").MustString("/stable-diffusion-webui/aigc-output/")
	DiffusionDomain = file.Section("server").Key("DiffusionDomain").MustString("https://aigc.cyuai.com/aigc-output/")
	Domain = file.Section("server").Key("Domain").MustString("https://aigc.cyuai.com/")

	TmpFilePath = file.Section("server").Key("TmpFilePath").MustString("./tmp/")
	SaveFilePath = file.Section("server").Key("SaveFilePath").MustString("./")
}

func LoadDatabase(file *ini.File) {
	Db = file.Section("database").Key("Db").MustString("")
	DbHost = file.Section("database").Key("DbHost").MustString("")
	DbPort = file.Section("database").Key("DbPort").MustString("")
	DbUser = file.Section("database").Key("DbUser").MustString("")
	DbPassWord = file.Section("database").Key("DbPassWord").MustString("")
	DbName = file.Section("database").Key("DbName").MustString("")
	RedisAddr = file.Section("database").Key("RedisAddr").MustString("")
	RedisPassWord = file.Section("database").Key("RedisPassWord").MustString("")
}
