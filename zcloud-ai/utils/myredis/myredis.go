package myredis

import (
	"errors"
	"github.com/go-redis/redis"
	"log"
	"sync"
	"time"
	"zcloud-ai/utils/config"
	"zcloud-ai/utils/logger"
)

var redisdb *redis.Client
var mutex sync.Mutex

func InitRedis() (err error) {
	redisdb = redis.NewClient(&redis.Options{
		Addr:     config.RedisAddr, // 指定
		Password: config.RedisPassWord,
		DB:       0, // redis一共16个库，指定其中一个库即可
	})
	_, err = redisdb.Ping().Result()
	if err != nil {
		logger.Fatal("redis 初始化失败", err)
	}
	return
}

// GetRedis 获取连接
func GetRedis() *redis.Client {
	return redisdb
}

func Set(key string, value interface{}, expiration time.Duration) error {

	return redisdb.Set(key, value, expiration).Err()
}

func Get(key string) string {
	v := redisdb.Get(key)
	return v.Val()
}

func Del(key string) error {
	return redisdb.Del(key).Err()
}

func Incr(key string) (int64, error) {
	return redisdb.Incr(key).Result()
}

func LPush(key string, value string) (int64, error) {
	size, err := redisdb.LPush(key, value).Result()
	if err != nil {
		return 0, err
	}
	return size, err
}

func RPush(key string, value string) (int64, error) {
	size, err := redisdb.RPush(key, value).Result()
	if err != nil {
		return 0, err
	}
	return size, err
}

func Publish(key string, value string) (int64, error) {
	size, err := redisdb.Publish(key, value).Result()
	if err != nil {
		return 0, err
	}
	return size, err
}

func BRPop(key string) (string, error) {
	value, err := redisdb.BRPop(5*time.Second, key).Result()
	if err == redis.Nil {
		// 查询不到数据
		return "", nil
	}
	if len(value) == 2 {
		return value[1], nil
	}
	if err != nil {
		// 查询出错
		return "", err
	}
	return "", errors.New("未知错误")
}

func LLen(key string) int64 {
	cmd := redisdb.LLen(key)
	i := cmd.Val()
	return i
}

func HSet(key string, field string, value string) (bool, error) {
	return redisdb.HSet(key, field, value).Result()
}

func HGet(key string, field string) (string, error) {
	return redisdb.HGet(key, field).Result()
}

func HDel(key string, field string) (int64, error) {
	return redisdb.HDel(key, field).Result()
}

func HExist(key string, field string) (bool, error) {
	return redisdb.HExists(key, field).Result()
}

func HKeys(key string) ([]string, error) {
	return redisdb.HKeys(key).Result()
}

func HGetAll(key string) (map[string]string, error) {
	return redisdb.HGetAll(key).Result()
}

// 加锁
func Lock(key string) bool {
	// ex:设置默认过期时间10秒，防止死锁
	ex := 10 * time.Second
	mutex.Lock()
	defer mutex.Unlock()
	bool, err := redisdb.SetNX(key, `{"lock":1}`, ex).Result()
	if err != nil {
		return bool
	}
	return bool
}

// 解锁
func UnLock(key string) int64 {
	nums, err := redisdb.Del(key).Result()
	if err != nil {
		log.Println(err.Error())
		return 0
	}
	return nums
}
