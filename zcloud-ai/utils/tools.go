package utils

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
	"zcloud-ai/utils/logger"
)

func Struct2Map(obj interface{}) map[string]interface{} {
	t := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		data[field.Tag.Get("json")] = v.Field(i).Interface()
		data[t.Field(i).Name] = v.Field(i).Interface()
	}
	return data
}

func StructJson2Map(obj interface{}) map[string]interface{} {
	t := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < t.NumField(); i++ {
		data[t.Field(i).Tag.Get("json")] = v.Field(i).Interface()
	}
	return data
}

func StructJson2MapForField(obj interface{}, out interface{}) map[string]interface{} {
	t := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)

	outT := reflect.TypeOf(out)

	var data = make(map[string]interface{})
	for i := 0; i < outT.NumField(); i++ {
		name := outT.Field(i).Name
		field, b := t.FieldByName(name)
		if b == false {
			continue
		}
		jsonName := outT.Field(i).Tag.Get("json")
		if len(jsonName) == 0 {
			jsonName = field.Tag.Get("json")
			if len(jsonName) == 0 {
				jsonName = name
			}
		}
		jsonValue := v.FieldByName(name).Interface()
		ty := reflect.TypeOf(jsonValue)
		if ty.Name() == "Time" {
			var tmp time.Time = jsonValue.(time.Time)
			data[jsonName] = tmp.Format("2006-01-02 15:04:05")
		} else {
			data[jsonName] = jsonValue
		}
	}

	return data
}

func GetInvitationCode() string {
	rand.Seed(time.Now().UnixNano())
	const charset = "ABCDEFGHIJKLMNPQRSTUVWXYZ123456789"
	b := make([]byte, 6)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	invitationCode := string(b)
	return invitationCode
}

func IsMobile(mobile string) bool {
	result, _ := regexp.MatchString(`^(1[3|4|5|6|7|8|9][0-9]\d{4,8})$`, mobile)
	if result {
		return true
	} else {
		return false
	}
}

//判断是否为整数
func IsInt(s string) bool {
	match, _ := regexp.MatchString(`^[\+-]?\d+$`, s)
	return match
}

func ParseUint(s string) uint {
	num, err := strconv.ParseUint(s, 10, 32)
	if err != nil {
		logger.Error(err)
		return 0
	}
	return uint(num)
}

func GetRandom(min int, max int) int {
	return rand.Intn(max-min) + min
}

func GetRandUsername(size int) (string, error) {
	charStart := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	charStr := "1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+=-><:}{?/"
	outStr := ""

	for i := 0; i < 1; i++ {
		num := rand.Intn(len(charStart))
		outStr += fmt.Sprintf("%c", charStr[num])
	}

	for i := 0; i < size-1; i++ {
		num := rand.Intn(len(charStr))
		outStr += fmt.Sprintf("%c", charStr[num])
	}
	return outStr, nil
}

func FormatMobileStar(mobile string) string {
	if len(mobile) <= 10 {
		return mobile
	}
	return mobile[:3] + "****" + mobile[7:]
}

func CreateCaptcha(num int) string {
	str := "1"
	for i := 0; i < num; i++ {
		str += strconv.Itoa(0)
	}
	str10 := str
	int10, err := strconv.ParseInt(str10, 10, 32)
	if err != nil {
		fmt.Println(err)
		return ""
	} else {
		j := int32(int10)
		return fmt.Sprintf("%0"+strconv.Itoa(num)+"v", rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(j))
	}
}

func Now() string {
	return time.Now().Format("2006-01-02 15:04:05")
}

func DefaultTime() time.Time {

	// 获取 Asia/Shanghai 时区的 Location 对象
	loc, _ := time.LoadLocation("Asia/Shanghai")

	// 创建默认时间，使用 loc 时区
	return time.Date(1900, time.January, 1, 0, 0, 0, 0, loc)
}

func ParseTime(timeStr string) time.Time {
	// 获取对应时区的 Location 对象
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return time.Time{}
	}
	//t, err := time.Parse("2006-01-02 15:04:05", timeStr)
	t, err := time.ParseInLocation("2006-01-02 15:04:05", timeStr, loc)
	if err != nil {
		logger.Error(err)
	}
	return t
}

func ParseTimeInChina(timeStr string) time.Time {

	locationName := "Asia/Shanghai"

	// 解析时间字符串
	t, err := time.Parse("2006-01-02 15:04:05 Etc/GMT", timeStr)
	if err != nil {
		return time.Time{}
	}

	// 获取对应时区的 Location 对象
	loc, err := time.LoadLocation(locationName)
	if err != nil {
		return time.Time{}
	}

	// 转换时区
	t = t.In(loc)

	return t
}

//PathExists 判断一个文件或文件夹是否存在
//输入文件路径，根据返回的bool值来判断文件或文件夹是否存在
func PathFileExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

func GetJsonFromStruct(m interface{}) string {
	jsonbyte, err := json.Marshal(m)
	if err != nil {
		logger.Error(err)
		return ""
	}
	return string(jsonbyte)
}

func GetStructFromJson(dest interface{}, jsonStr string) error { //传进来的dest 要加&
	err := json.Unmarshal([]byte(jsonStr), dest)
	if err != nil {
		logger.Error(err, jsonStr)
		return err
	}
	return nil
}

func GetClientIp(header http.Header) string {
	xForwardedFor := header.Get("X-Forwarded-For")
	ary := strings.Split(xForwardedFor, ",")
	for _, value := range ary {
		if !strings.HasPrefix(value, "192") {
			return value
		}
	}
	return ""
}

func GetTimeDifferenceOfMinutes(startTime, endTime time.Time) int {
	duration := endTime.Sub(startTime)
	minutes := int(duration.Minutes())
	return minutes
}

func IsToday(t time.Time) bool {
	tStr := t.Format("2006-01-02")
	todayStr := time.Now().Format("2006-01-02")
	if tStr != "" && tStr == todayStr {
		return true
	}
	return false
}

func ReplaceStringIgnoreCase(str, old, new string) string {
	regex := regexp.MustCompile("(?i)" + regexp.QuoteMeta(old))
	result := regex.ReplaceAllString(str, new)
	return result
}

func ReplaceWordIgnoreCase(text, word, replacement string) string {
	// 使用不区分大小写的正则表达式进行单词替换
	//regex := regexp.MustCompile(`(?i)^[a-zA-Z]` + word + `^[a-zA-Z]`)
	regex := regexp.MustCompile(`(?i)\b` + regexp.QuoteMeta(word) + `\b`)
	result := regex.ReplaceAllString(text, replacement)
	return result

	// 使用不区分大小写的正则表达式进行单词替换
	//regex := regexp.MustCompile(`(?i)\b` + regexp.QuoteMeta(word) + `\b`)
	//result := regex.ReplaceAllStringFunc(text, func(match string) string {
	//	if match == word || strings.ToLower(match) == word {
	//		return match // 保留完整的单词和非匹配大小写的单词
	//	}
	//	return replacement
	//})
	//return result
}

func FindFile(root, fileName string) (string, error) {
	var targetPath string

	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && info.Name() == fileName {
			targetPath = path
			return fmt.Errorf("file found") // 使用错误返回值作为中断标志
		}

		return nil
	})

	if err != nil && err.Error() != "file found" {
		return "", err
	}

	return targetPath, nil
}
